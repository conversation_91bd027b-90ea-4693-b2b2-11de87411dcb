import sqlglot
from sqlglot import expressions as exp
from collections import defaultdict
from typing import Dict, Set, List


class SQLSchemaExtractor:
    def __init__(self):
        self.table_schemas = defaultdict(set)
    
    def extract_columns_from_expression(self, node):
        """从表达式中提取所有列引用"""
        columns = []
        
        # 使用sqlglot的find_all方法来查找所有Column节点
        for column in node.find_all(exp.Column):
            column_name = column.name
            table_name = None
            if column.table:
                table_name = column.table
            columns.append((column_name, table_name))
        
        return columns
    
    def process_insert_select(self, sql: str):
        """处理单个INSERT...SELECT语句"""
        try:
            # 解析SQL
            parsed = sqlglot.parse_one(sql)
            
            if not isinstance(parsed, exp.Insert):
                print(f"警告: 不是INSERT语句: {sql}")
                return
            
            # 获取目标表信息 - 修复获取表名的逻辑
            target_table = None
            if parsed.this:
                if isinstance(parsed.this, exp.Table):
                    target_table = parsed.this.name
                elif hasattr(parsed.this, 'name'):
                    target_table = parsed.this.name
            
            if not target_table:
                print(f"警告: 无法获取目标表名: {sql}")
                return
            
            print(f"处理表: {target_table}")
            
            # 获取INSERT的列名
            insert_columns = []
            if 'columns' in parsed.args and parsed.args['columns']:
                for col in parsed.args['columns']:
                    if isinstance(col, exp.Column):
                        insert_columns.append(col.name)
                    elif isinstance(col, exp.Identifier):
                        insert_columns.append(col.name)
            
            # 获取SELECT部分
            select_stmt = parsed.expression
            if not select_stmt or not isinstance(select_stmt, exp.Select):
                print(f"警告: 无法找到SELECT语句: {sql}")
                return
            
            # 收集所有涉及的表
            all_tables = set()
            
            # 从FROM子句获取表
            if select_stmt.find(exp.From):
                for table in select_stmt.find_all(exp.Table):
                    all_tables.add(table.name)
            
            # 从JOIN子句获取表
            for join in select_stmt.find_all(exp.Join):
                if isinstance(join.this, exp.Table):
                    all_tables.add(join.this.name)
            
            print(f"  源表: {list(all_tables)}")
            
            # 添加目标表的schema信息
            if insert_columns:
                for col in insert_columns:
                    self.table_schemas[target_table].add(col)
                print(f"  目标表 {target_table} 字段: {insert_columns}")
            
            # 提取SELECT子句中的列
            select_columns = []
            for expr in select_stmt.expressions:
                cols = self.extract_columns_from_expression(expr)
                select_columns.extend(cols)
            
            # 提取WHERE子句中的列
            where_columns = []
            if select_stmt.find(exp.Where):
                where_clause = select_stmt.find(exp.Where)
                where_columns = self.extract_columns_from_expression(where_clause)
            
            # 提取JOIN条件中的列
            join_columns = []
            for join in select_stmt.find_all(exp.Join):
                if join.on:
                    join_columns.extend(self.extract_columns_from_expression(join.on))
            
            # 合并所有列引用
            all_columns = select_columns + where_columns + join_columns
            print(f"  发现的列引用: {all_columns}")
            
            # 为每个表分配字段
            for col_name, table_name in all_columns:
                if table_name:
                    # 如果列有表前缀，直接分配
                    self.table_schemas[table_name].add(col_name)
                else:
                    # 如果没有表前缀，分配给所有源表
                    for table in all_tables:
                        self.table_schemas[table].add(col_name)
            
            # 确保所有源表都在结果中
            for table in all_tables:
                if table not in self.table_schemas:
                    self.table_schemas[table] = set()
                    
        except Exception as e:
            print(f"解析SQL时出错: {sql}")
            print(f"错误信息: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def process_sql_list(self, sql_list: List[str]):
        """处理多个SQL语句"""
        for i, sql in enumerate(sql_list, 1):
            sql = sql.strip()
            if sql:
                print(f"\n--- 处理第{i}个SQL ---")
                self.process_insert_select(sql)
    
    def get_schema_info(self) -> Dict[str, List[str]]:
        """获取所有表的schema信息"""
        result = {}
        for table, columns in self.table_schemas.items():
            result[table] = sorted(list(columns))
        return result
    
    def print_schema_info(self):
        """打印schema信息"""
        print("\n" + "="*50)
        print("=== 表Schema信息 ===")
        for table, columns in sorted(self.table_schemas.items()):
            print(f"\n表名: {table}")
            if columns:
                print("字段:")
                for col in sorted(columns):
                    print(f"  - {col}")
            else:
                print("  (未发现字段信息)")


def main():
    # 示例SQL语句
    sql_examples = [
        "INSERT INTO tbl (col1, col2) SELECT c1, c2 FROM tbl2 WHERE c1 > 0",
        "INSERT INTO users (id, name, email) SELECT user_id, username, user_email FROM temp_users WHERE status = 'active'",
        "INSERT INTO orders (order_id, customer_id, total) SELECT o.id, o.cust_id, o.amount FROM raw_orders o JOIN customers c ON o.cust_id = c.id WHERE o.date > '2024-01-01'"
    ]
    
    # 创建提取器实例
    extractor = SQLSchemaExtractor()
    
    # 处理SQL语句
    print("开始处理SQL语句...")
    extractor.process_sql_list(sql_examples)
    
    # 输出结果
    extractor.print_schema_info()
    
    # 也可以获取字典格式的结果
    schema_dict = extractor.get_schema_info()
    print(f"\n=== 字典格式结果 ===")
    for table, columns in schema_dict.items():
        print(f"{table}: {columns}")


if __name__ == "__main__":
    main()