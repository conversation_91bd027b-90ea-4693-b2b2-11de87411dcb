import sqlglot
from sqlglot import parse, exp, parse_one, transpile
from typing import List, Optional, Dict, Set, Any
import sys
from pathlib import Path
# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))
from lineage_core.logger import logger

# 默认SQL方言
SQL_DIALECT = "sqlite"

def get_dialect() -> str:
    """获取SQL方言"""
    return SQL_DIALECT

def parse_statement(sql_statement: str) -> Optional[exp.Expression]:
    """
    解析单条SQL语句。

    Args:
        sql_statement: 要解析的SQL语句字符串。

    Returns:
        解析成功则返回sqlglot表达式对象，否则返回None。
    """
    if not sql_statement or not sql_statement.strip():
        return None
    try:
        # parse_one 更适合解析单条语句
        parsed_expression = parse_one(sql_statement, dialect=get_dialect())
        return parsed_expression
    except sqlglot.errors.ParseError as e:
        logger.error(f"解析单条SQL语句时发生sqlglot.ParseError: {e}. SQL: {sql_statement[:200]}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"解析单条SQL语句时发生未知错误: {e}. SQL: {sql_statement[:200]}", exc_info=True)
        return None

def parse_all_sqls(sql_content: str) -> List[Optional[exp.Expression]]:
    """
    解析包含多条SQL语句的字符串或列表。DDL和DML语句也会被解析。

    Args:
        sql_content: 包含一条或多条SQL语句的字符串或字符串列表。

    Returns:
        解析后的sqlglot表达式对象列表。如果某条语句解析失败，则对应位置为None。
        如果整个块解析失败，可能返回包含单个None的列表。
    """
    # 处理列表输入，将其转换为单个字符串
    if isinstance(sql_content, list):
        sql_content = "\n".join(sql_content)
        
    logger.debug(f"开始解析SQL内容块。前100字节: {sql_content[:100].replace('\\n', ' ')}") 
    if not sql_content or not sql_content.strip():
        logger.info("接收到空的SQL内容块进行解析，返回空列表。") 
        return []

    try:
        # 直接使用sqlglot.parse解析多条语句
        parsed_expressions_direct = parse(sql_content, dialect=get_dialect())
        if parsed_expressions_direct:
            logger.info(f"解析得到 {len(parsed_expressions_direct)} 条SQL语句。")
            expressions: List[Optional[exp.Expression]] = []
            for i, expr in enumerate(parsed_expressions_direct):
                if expr:
                    logger.debug(f"解析表达式 #{i+1}: 类型={type(expr)}, SQL={str(expr.sql(dialect=get_dialect()))[:150].replace('\\n', ' ')}...")
                    expressions.append(expr)
                else:
                    logger.debug(f"解析表达式 #{i+1}: None")
                    expressions.append(None)
            return expressions
        else:
            logger.warning(f"sqlglot.parse未能从SQL内容中解析出任何语句。SQL前缀: {sql_content[:300].replace('\\n', ' ')}")
            return []

    except sqlglot.errors.ParseError as e:
        logger.error(f"解析SQL内容块时发生sqlglot.ParseError: {e}. SQL前缀: {sql_content[:500].replace('\\n', ' ')}", exc_info=True)
        return [None]
    except Exception as e:
        logger.error(f"解析SQL内容块时发生未知严重错误: {e}. SQL前缀: {sql_content[:500].replace('\\n', ' ')}", exc_info=True)
        return [None]


def get_tables_from_sql(sql_query: str) -> List[str]:
    """
    从SQL查询中提取所有涉及的表名（包括FROM子句和JOIN子句中的表）。

    Args:
        sql_query: SQL查询字符串。

    Returns:
        查询中涉及的表名列表 (小写, 带schema)。
    """
    table_names: Set[str] = set()
    try:
        parsed_expressions = parse(sql_query, dialect=get_dialect())
        if parsed_expressions:
            for expression in parsed_expressions:
                if expression is None:
                    continue
                # 遍历表达式树以查找所有表
                for table_exp in expression.find_all(exp.Table):
                    # 构建完整的表名，包括schema（数据库名）和表名本身
                    db_name = table_exp.text("db")
                    table_name_part = table_exp.text("this")
                    
                    full_name_parts = []
                    if db_name:
                        full_name_parts.append(db_name.lower())
                    if table_name_part:
                        full_name_parts.append(table_name_part.lower())
                    
                    if full_name_parts:
                        table_names.add(".".join(full_name_parts))
            return list(table_names)
        else:
            return []
    except sqlglot.errors.ParseError as e:
        logger.error(f"从SQL中提取表名时发生sqlglot.ParseError: {e}. SQL: {sql_query[:200]}", exc_info=True)
        return []
    except Exception as e:
        logger.error(f"从SQL中提取表名时发生未知错误: {e}. SQL: {sql_query[:200]}", exc_info=True)
        return []

def load_and_parse_sql_file(file_path: str) -> List[exp.Expression]:
    """
    从文件加载SQL内容并解析成sqlglot表达式列表。

    Args:
        file_path: SQL文件的路径。

    Returns:
        sqlglot.Expression对象的列表。
        
    Raises:
        FileNotFoundError: 当指定的SQL文件不存在时
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if not content.strip():
            logger.warning(f"文件 {file_path} 内容为空。")
            return []

        # 调用本模块的parse_all_sqls函数解析内容
        parsed_expressions = parse_all_sqls(content)
        
        return parsed_expressions
    except FileNotFoundError:
        logger.error(f"错误：文件 {file_path} 未找到。")
        raise
    except Exception as e:
        logger.error(f"读取或解析文件 {file_path} 时发生错误：{e}")
        return []
