# 自动化SQL血缘关系分析系统设计文档 (针对 INSERT...SELECT)

## 1. 系统目标

本系统旨在提供一个自动化的解决方案，用于分析包含一系列 `INSERT INTO ... SELECT ...` SQL语句的文件。核心目标是判断在给定的SQL操作序列中，任意两个表之间（特别是最终目标表与最原始源表之间）是否存在**实际的数据流依赖关系（即表级血缘）**。这种依赖关系的判断主要基于构成表之间数据流动的**控制流条件（如`WHERE`子句、`JOIN ON`条件、`HAVING`子句）**以及这些条件所依赖的列的计算逻辑。

（请注意，实际应用中应该还有一个前置步骤，要将SQL构建表级关系图，然后通过图上路径的遍历，获取源表和目标表之间的加工SQL，作为当前系统的输入。）

为此，系统采用两阶段处理方法：
1.  **阶段一：相关性与类型分析** - 全局扫描SQL序列，从所有SQL语句的控制流条件（`WHERE`/`JOIN`/`HAVING`）反向追溯，识别出所有真正影响表级数据流动的"相关"字段，并推断其数据类型。
2.  **阶段二：Z3转换与血缘分析** - 仅针对"相关"字段，并使用其推断出的类型构建Z3约束模型，利用SMT求解器推断表级依赖关系。

## 2. 输入

*   **SQL文件**:
    *   一个或多个纯文本文件，包含一条或多条 `INSERT INTO ... SELECT ...` 形式的SQL语句。其他形式的语句可以忽略。
    *   SQL语句之间使用 `;\n` (分号加换行符) 作为分隔符。
    *   **重要假设**: SQL语句按照数据血缘从目标端到源端顺序排列。
*   **初始模式信息 (Initial Schemas)**:
    *   一个描述最原始输入表（即不是由前面SQL生成的表）的结构和类型信息，例如 `{"TableName1": {"colA": "Int", "colB": "String"}, "TableName2": {"colC": "Date"}}`。此信息可选，如果提供，可用于校验或辅助类型推断。

## 3. 总体处理流程

系统将按以下两个主要阶段运行：

### 阶段一：相关性与类型分析 (Relevance and Type Analysis)

此阶段的目标是识别出SQL序列中所有对最终**表级血缘判定**有贡献的列，并为这些相关列推断出数据类型。

1.  **解析所有SQL语句**: 将输入文件中的所有SQL语句解析为`sqlglot`的AST对象列表
    a. 先通过 ";\n" 进行分割，在逐一对每个sql段落进行解析，得到AST。
    b. 对每个 AST 进行预处理，建立子查询的别名与真实表名的对应关系，然后将字段前面所有别名替换为真实表名
2.  **识别初始相关列**:
    a.  **控制流列**: 遍历所有SQL语句（包括CTE定义内部）的AST，将出现在任何 `WHERE` 子句、`JOIN ON` 条件或 `HAVING` 子句中的所有列（及其所属表）标记为**初始相关列**。这些是直接影响表间数据流动的列。
3.  **相关性反向传播**:
    a.  维护一个全局的"相关列集合"（例如，`relevant_columns = {'table_name': {'col1', 'col2'}}`）。此集合用于存储所有被识别为影响表级血缘的列。
    b.  从步骤2中识别的**所有初始相关列（即控制流列）**开始，进行迭代反向传播：
        i.  如果表 `T_curr` 的列 `col_curr` 在 `relevant_columns` 中（因为它是一个控制流列，或通过反向传播被标记为相关），并且 `T_curr.col_curr` 是由前一条SQL语句 `SQL_prev: INSERT INTO T_curr(...) SELECT ... expr_prev AS col_alias ... FROM T_prev1, T_prev2 ...` 生成的（其中 `col_alias` 可能是 `col_curr`，或者 `col_curr` 是通过 `col_alias` 引用得到的）。
        ii. 那么，解析 `expr_prev`，将其在 `T_prev1`, `T_prev2` 等源表中直接或间接引用的所有列也添加到 `relevant_columns` 中对应表的集合下。这确保了构成控制流列计算所需的上游数据也被认为是相关的。
    c.  重复此传播过程，直到没有新的列可以被添加到 `relevant_columns` 中为止。**注意：此阶段的目标不是追踪所有数据列的流动，而是仅追踪那些最终会影响控制流决策的列及其来源。**
4.  **构建相关字段依赖图与初始类型收集**: 
    a.  遍历所有SQL语句的AST。对于在 `relevant_columns` 中的每一个字段：
        i.  记录其直接依赖关系：例如，在 `T1.a = T2.b + T3.c` 中，`T1.a` 依赖于 `T2.b` 和 `T3.c`。在 `T1.x = 'constant'` 中，`T1.x` 依赖于常量 `'constant'`。
        ii. 如果一个相关字段直接与常量进行操作（赋值、比较），记录该常量及其类型：
            *   字符串字面量 (`'abc'`) -> String (并尝试解析为Date)
            *   整数字面量 (`123`) -> Int
            *   浮点数字面量 (`3.14`) -> Float
            *   布尔字面量 (`TRUE`, `FALSE`) -> Bool
            *   **Date推断**: 对识别出的字符串常量，尝试使用预定义的格式列表（包括 'YYYY-MM-DD', 'YYYYMMDD' 两种）进行解析。如果成功，则该常量的类型记为 Date，否则为 String。
            *   最终不能确定的类型，统一设置为 String
    b.  构建一个初步的字段依赖图，节点为相关字段和常量，边表示依赖或运算关系。
5.  **数据类型推断与传播**: (暂时废除， 我们认为每个字段的类型都已经给出) 
    a.  初始化 `column_types = {'tablename.columnname': 'Unknown'}` 对应所有相关列。
    b.  **常量类型赋值**: 将步骤4.a.ii中从常量直接获得的类型赋给 `column_types` 中对应的相关列。如果存在冲突（一个列从不同常量推断出不同类型）， 则按照 Date > String > Float > Int > Bool 的优先级顺序，选择高优先级的类型。
    c.  **类型传播**: 基于依赖图和已知的类型（来自常量或 `initial_schemas`），迭代传播类型：
        i.  **赋值**: 若 `T1.a = T2.b`，且 `T2.b` 类型已知，则 `T1.a` 类型赋为 `T2.b` 的类型。
        ii. **运算**: 若 `T1.a = T2.b op T3.c`，根据 `op` 的语义及 `T2.b`, `T3.c` 的已知类型推断 `T1.a` 的类型（例如，Int + Int -> Int; Int + Float -> Float）。如果一个操作数类型已知而另一个为 `Unknown`，尝试将已知类型或运算结果类型赋给 `Unknown` 列（例如，若 `C = Unknown_Col + Int_Col` 且结果应为 `Int`，则 `Unknown_Col` 可能被推断为 `Int`）。
        iii. **比较**: 参与比较的字段通常应具有兼容或相同的类型。如果一方类型已知，可传递给另一方。
        iv. **函数**: 根据已知函数的签名（例如 `SUBSTRING` 返回 String，`COUNT` 返回 Int）推断结果类型。
    d.  重复传播直到没有相关列的类型可以被更新。
    e.  **最终处理**: 所有在类型传播完成后类型仍为 `Unknown` 的相关列，将其类型统一设置为 `String`。
6.  **输出**: 
    *   `table_schema: Dict[str, Dict[str, str]], 例如：{table1: {col1:"Int", col2:"String"}}`

### 阶段二：Z3转换与血缘分析 (Based on Relevant Columns and Types)

此阶段利用阶段一识别出的"相关列"及其"推断类型"，并结合 `sql_to_z3` 库来构建Z3模型并进行血缘分析。

1.  **初始化 `SqlToZ3` 实例**:
    *   创建一个 `SqlToZ3` 对象：`z3_sql = SqlToZ3()`。这个对象将管理所有的表、行、列的Z3表示以及约束。

2.  **定义表结构 (对应 SQL `CREATE TABLE`)**:
    *   根据 `initial_schemas` (如果提供) 以及后续SQL语句中定义的表，使用 `column_types` (来自阶段一的类型推断结果) 为每个相关表创建Z3表结构。
    *   对每个表 `T` 及其相关列 `col_i` (类型为 `type_i`)：
        *   调用 `table_T_obj = z3_sql.create_table("T", {"col_1": Z3Sort_1, "col_2": Z3Sort_2, ...})`。
        *   `Z3Sort_i` 根据 `type_i` 确定：
            *   `'Int'` -> `IntSort()`
            *   `'String'` -> `StringSort()`
            *   `'Bool'` -> `BoolSort()`
            *   `'Float'` -> `RealSort()` (Z3用Real表示浮点数)
            *   `'Date'`/`'Datetime'` -> 使用 `lineage_core/z3_datetype.py` 中定义的自定义DateSort (例如 `DateSort()`)。
    *   这一步处理了所有初始输入表和在SQL序列中通过 `CREATE TABLE` (或隐式通过 `INSERT INTO new_table SELECT ...`) 创建的中间表和最终目标表。

3.  **处理SQL数据操作与转换逻辑 (对应 SQL `INSERT`, `SELECT`, `WHERE`, `JOIN`)**:
    *   按顺序遍历（已解析的）SQL语句AST列表（主要关注 `INSERT INTO ... SELECT ...` 语句）。
    *   对于每条 `INSERT INTO T_curr SELECT ... FROM S1, S2, ... WHERE cond_w JOIN cond_j` 语句：

        a.  **识别源表和目标表对象**: `T_curr_obj`, `S1_obj`, `S2_obj` 等应已在步骤2中通过 `create_table` 创建。

        b.  **转换 `SELECT` 表达式**:

            *   对于目标表 `T_curr_obj` 中的每个相关列 `col_curr_i`，其值来源于 `SELECT` 列表中的表达式 `expr_i`。
            *   将 `expr_i` 转换为Z3表达式 `z3_expr_i`。这涉及到：
                *   引用源表列：`S1_obj.column_name` (这将是Z3函数应用，例如 `z3_sql.column_functions["S1_column_name"](row_var_S1)`)。
                *   常量：`IntVal(10)`, `StringVal("text")`, `True/False`, `RealVal(1.23)`.
                *   运算符和函数：`+`, `-`, `*`, `/`, `And`, `Or`, `Not`, `If`, `Concat`, `SubString`, etc. (转换为对应的Z3函数)。

        c.  **生成 `INSERT` 约束**:

            *   使用 `T_curr_obj.insert({ "col_curr_1": z3_expr_1, "col_curr_2": z3_expr_2, ... }, for_all_rows=True_or_appropriate_row_vars)`。
            *   `insert` 方法会为 `T_curr_obj` 创建新的行（或行变量），并添加约束，使得新行中各列的值等于对应的 `z3_expr_i`。
            *   `for_all_rows=True` 可用于表示 `SELECT` 结果的每一行都插入。对于更复杂的逻辑（如 `field_lineage_example.py` 中的多步骤转换），可能需要显式管理行变量并为每个源行组合调用 `insert`。

        d.  **转换 `WHERE` 和 `JOIN` 条件**:

            *   将SQL的 `WHERE` 子句 `cond_w` 和 `JOIN` 子句 `cond_j` 转换为Z3布尔表达式 `z3_cond_w` 和 `z3_cond_j`。
            *   这些条件通常涉及源表 `S1_obj`, `S2_obj` 等的列。
            *   使用 `z3_sql.add(z3_cond_w)` 和 `z3_sql.add(z3_cond_j)` 将这些约束添加到求解器。
            *   `sql_to_z3` 库支持简化的JOIN语法，如 `S1_obj.key == S2_obj.foreign_key`，它会自动生成必要的行变量和约束。

4.  **表级血缘关系分析**:

    a.  确定最初的源表列表（来自 `initial_schemas` 或SQL序列的早期表）和最终的目标表（序列中最后一个主要 `INSERT` 的目标表）。

    b.  对每一个最初始的源表 `S_orig_obj` 和最终的目标表 `T_final_obj`，判断 `T_final_obj` 是否实际依赖于 `S_orig_obj`：

        i.  遍历 `S_orig_obj` 中所有在 `relevant_columns` 中的列 `s_col_name`，以及 `T_final_obj` 中所有在 `relevant_columns` 中的列 `t_col_name`。
        ii. 对于每一对相关的 `(s_col_name, t_col_name)`，执行Z3可满足性检查以判断列级血缘（如先前文档3.4节所述的通过比较两场景下值的变化来判断影响，但现在使用 `sql_to_z3` API）：

            1.  `z3_sql.push()`
            2.  选择或声明源表 `S_orig_obj` 的一个（或符号化的）行变量 `s_row_var` 和目标表 `T_final_obj` 的一个（或符号化的）行变量 `t_row_var`。确保这些行变量通过转换逻辑相关联。
            3.  获取列函数: `s_col_func = z3_sql.column_functions[f"{S_orig_obj.name}_{s_col_name}"]`, `t_col_func = z3_sql.column_functions[f"{T_final_obj.name}_{t_col_name}"]`.
            4.  施加约束 `s_col_func(s_row_var) == test_value1`。
            5.  检查 `z3_sql.check() == sat`。若满足，获取模型 `model1 = z3_sql.get_model()` 并计算 `result1 = model1.eval(t_col_func(t_row_var))`。
            6.  `z3_sql.pop()`
            7.  `z3_sql.push()`
            8.  施加约束 `s_col_func(s_row_var) == test_value2` (其中 `test_value2 != test_value1`)。
            9.  检查 `z3_sql.check() == sat`。若满足，获取模型 `model2 = z3_sql.get_model()` 并计算 `result2 = model2.eval(t_col_func(t_row_var))`。
            10. `z3_sql.pop()`
            11. 如果两个场景都可满足且 `result1 != result2`，则判定存在从 `s_col_name` 到 `t_col_name` 的列级血缘。

        iii. 如果**任何一对**相关的 `(s_col_name, t_col_name)` 显示存在列级血缘关系，则判定表 `T_final_obj` **实际依赖**于表 `S_orig_obj`，并记录此表级依赖。然后可以停止对 `S_orig_obj` 与 `T_final_obj` 之间其他列对的检查。

    c.  输出结果。

## 4. 核心组件

*   **SQL解析器 (SQL Parser)**: (同前)
*   **相关性与类型分析器 (Relevance & Type Analyzer)**:
    *   功能: 实现阶段一的逻辑，包括解析所有SQL、识别初始相关列、执行反向相关性传播、构建字段依赖图以及执行数据类型推断与传播算法。
    *   输出: `relevant_columns` 字典和 `column_types` 字典。
*   **AST 到 Z3 转换器 (AST to Z3 Translator - Relevance & Type Aware)**:
    *   功能: 实现阶段二的核心转换逻辑。根据 `relevant_columns` 和 `column_types`，仅为相关的列创建具有正确Sort的Z3变量，并将SQL的`SELECT`表达式、`WHERE`/`JOIN`条件转换为类型一致的Z3约束。
    *   上下文管理: 维护 `z3_vars_context`（仅含相关Z3变量）并在SQL语句间传递。
*   **Z3约束求解器与血缘判定器 (Z3 Solver & Lineage Decider)**:
    *   功能: 接收累积的Z3约束。对给定的（相关源列，相关目标列）对执行可满足性查询以判定列级影响。汇总列级结果以判定表级依赖。

## 5. 输出 (Revised for Table-Level Dependency)

*   **血缘关系报告**: 指出最终目标表是否依赖于每个最初始的源表。
    *   示例: `{"FinalTargetTableX": {"InitialSourceTableA": true, "InitialSourceTableB": false}}`

## 6. 关键模块与函数设计 (Revised for Type Inference)

*   `load_and_split_sql_file(file_path: str) -> List[str]`
*   `parse_all_sqls(sql_statements: List[str]) -> List[sqlglot.Expression]`
*   `analyze_relevance(parsed_sqls: List[sqlglot.Expression], initial_schemas: Optional[Dict[str, List[str]]] = None) -> Tuple[Dict[str, Set[str]], List[sqlglot.Expression]]`:
    *   负责对解析后的SQL AST进行列名限定（可选用`initial_schemas`）。
    *   识别**所有语句中**的控制流相关列（`WHERE`, `JOIN ON`, `HAVING`）。
    *   执行相关性反向传播算法，以找到构成这些控制流列所需的所有上游源列。
    *   返回 `relevant_columns` 字典 (包含所有影响表级血缘的列) 和 `qualified_sqls`列表 (列名限定后的AST列表，供类型分析器使用)。
*   `analyze_column_types(qualified_sqls: List[sqlglot.Expression], relevant_columns: Dict[str, Set[str]], initial_schemas: Optional[Dict[str, List[str]]] = None) -> Dict[str, str]`:
    *   接收列名限定后的SQL AST列表和相关列映射。
    *   基于相关列，构建字段依赖关系。
    *   从常量和（可选的）`initial_schemas`中收集初始数据类型。
    *   执行数据类型推断与传播算法。
    *   返回 `column_types` 字典 (相关列的类型映射，值为 'String', 'Int', 'Float', 'Bool', 'Date')。
*   `extract_insert_select_components(insert_ast: sqlglot.Expression) -> Dict` (同前)
*   `convert_sql_expr_to_z3(sql_expr_ast: sqlglot.Expression, z3_vars_context: Dict[str, z3.ExprRef], column_types: Dict[str, str], relevant_columns: Dict[str, Set[str]], current_table_aliases: Dict) -> z3.ExprRef`:
    *   转换时使用 `column_types` 确保Z3操作的类型正确性。
*   `build_z3_model_for_sequence(parsed_sqls: List[sqlglot.Expression], initial_schemas: Dict[str, List[str]], relevant_columns: Dict[str, Set[str]], column_types: Dict[str, str]) -> Tuple[List[z3.BoolRef], Dict[str, z3.ExprRef], List[str], str]`:
    *   接收 `column_types` 用于创建带类型的Z3变量。
*   `determine_table_level_dependencies(all_constraints: List[z3.BoolRef], z3_vars_context: Dict[str, z3.ExprRef], relevant_columns: Dict[str, Set[str]], initial_source_names: List[str], final_target_name: str, initial_schemas: Dict[str, List[str]], final_target_schema_cols: List[str]) -> Dict[str, Dict[str, bool]]`:
    *   实现阶段二的表级血缘判定逻辑（步骤4）。
    *   `final_target_schema_cols` 是最终目标表中所有相关列的列表。

## 7. 数据结构

*   **AST**: `sqlglot` 表达式树。
*   **Relevant Columns Map**: `Dict[str, Set[str]]` e.g., `{'tableA': {'col1', 'col3'}, 'tableB': {'col2'}}`.
*   **Column Types Map**: `Dict[str, str]` (e.g., `{'tableA.col1': 'Int', 'tableB.col2': 'String'}`)
*   **Z3变量上下文 (`z3_vars_context`)**: `{"tablename.columnname": z3_variable}` (仅含相关列的变量)。
*   **血缘关系报告**: (同第5节)。

## 8. 挑战与待解决问题

*   **相关性传播算法的正确性与完备性**: 确保能准确追踪所有间接相关的字段，特别是跨复杂表达式和多层SQL时。
*   **数据类型推断的复杂性与准确性**:
    *   处理类型冲突（例如，一个字段同时与字符串和数字常量操作）。
    *   隐式类型转换规则的定义与实现。
    *   用户自定义函数（UDFs）或复杂内置函数的输出类型推断。
    *   Date类型解析的鲁棒性（支持多种格式，处理无效日期字符串）。
    *   类型传播中的循环依赖。
*   **复杂SQL表达式到类型感知Z3的转换**: (同前)
*   **别名处理**: 在相关性分析和Z3转换中准确处理表和列别名。
*   **性能**: 相关性分析和类型推断阶段的计算成本。

## 9. 后续扩展

*   支持更丰富的类型系统和更复杂的类型推断规则。
*   允许用户提供自定义类型定义或函数签名以辅助推断。