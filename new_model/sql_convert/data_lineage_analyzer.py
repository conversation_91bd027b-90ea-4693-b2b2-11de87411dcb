"""
数据血缘分析主模块

本模块整合了数据血缘系统的两个主要阶段：
1. 相关性与类型分析
2. Z3转换与血缘分析

它提供了一个完整的接口，用于分析SQL文件中的表级血缘关系。
"""

from typing import Dict, Optional, Set
import os

# 导入所需模块
from new_model.sql_convert.relevance_analyzer import analyze_relevance
from new_model.sql_convert.z3_analyzer import analyze_z3_lineage
from lineage_core.logger import logger


def analyze_sql_lineage(
    sql_file_path: Optional[str] = None,
    initial_schemas: Optional[Dict[str, Dict[str, str]]] = None,
    final_target_table_name: Optional[str] = None,
    sql: Optional[str] = None
) -> Dict[str, Dict[str, bool]]:
    """
    分析SQL文件或SQL字符串中的表级血缘关系

    Args:
        sql_file_path: SQL文件路径
        sql: 直接传入的SQL字符串（优先级高于sql_file_path）
        initial_schemas: 初始表模式信息，格式为 {'tablename': {'col1': 'type1'}}
        final_target_table_name: 最终目标表名（可选）

    Returns:
        表级依赖关系字典，格式为 {'target_table': {'source_table1': True, 'source_table2': False}}
    
    Raises:
        FileNotFoundError: 当指定的SQL文件不存在时
        ValueError: 当既没有提供SQL文件路径也没有提供SQL字符串时
    """
    # 阶段一：相关性与类型分析
    logger.info("开始阶段一：相关性与类型分析")

    if sql is not None:
        from new_model.sql_convert.sql_parser import parse_all_sqls
        parsed_sqls = parse_all_sqls(sql)
    elif sql_file_path is not None:
        # 检查文件是否存在
        if not os.path.exists(sql_file_path):
            raise FileNotFoundError(f"SQL文件 {sql_file_path} 不存在")
        from new_model.sql_convert.sql_parser import load_and_parse_sql_file
        parsed_sqls = load_and_parse_sql_file(sql_file_path)
    else:
        raise ValueError("必须提供SQL文件路径或SQL字符串")

    # 分析相关列
    relevant_columns, qualified_sqls = analyze_relevance(
        parsed_sqls,
        initial_schemas=initial_schemas,
        final_target_table_name=final_target_table_name
    )

    logger.info(f"阶段一完成，找到 {sum(len(cols) for cols in relevant_columns.values())} 个相关列")
    logger.info(f"相关列: {relevant_columns}")

    # 阶段二：Z3转换与血缘分析
    logger.info("开始阶段二：Z3转换与血缘分析")

    # 使用Z3分析表级血缘关系
    dependencies = analyze_z3_lineage(
        parsed_sqls,
        qualified_sqls,
        relevant_columns,
        initial_schemas,
        final_target_table_name
    )

    return dependencies