"""
Z3血缘分析器模块

本模块实现了数据血缘系统设计文档中描述的阶段二：Z3转换与血缘分析。
它使用阶段一得到的相关列及其类型信息，结合sql_to_z3库构建Z3模型并进行血缘分析。

主要功能：
1. 初始化SqlToZ3实例
2. 根据表模式信息创建Z3表结构
3. 处理SQL数据操作与转换逻辑
4. 执行表级血缘关系分析
"""

from typing import Dict, List, Set, Optional, Any, Tuple
import z3
from sqlglot import Expression
from sqlglot import expressions as exp

from new_model.core import SqlToZ3, Table
from lineage_core.logger import logger
from lineage_core.z3_datetype import is_date_str, DateType
                


class Z3LineageAnalyzer:
    """Z3血缘分析器，用于执行表级血缘关系分析"""

    def __init__(self):
        """初始化Z3血缘分析器"""
        self.z3_sql = SqlToZ3()
        self.table_objects: Dict[str, Table] = {}  # 存储物理表对象的字典
        self.initial_source_tables: Set[str] = set()  # 初始物理源表集合
        self.final_target_table: Optional[str] = None  # 最终目标物理表

    def create_table_structures(self,
                               initial_schemas: Dict[str, Dict[str, str]],
                               relevant_columns: Dict[str, Set[str]]
                               ) -> None:
        """
        根据列类型信息创建Z3表结构 (基于物理表名)

        Args:
            initial_schemas: 列类型字典，格式为 {'tablename': {'columnname': 'type'}}
            relevant_columns: 相关列字典，格式为 {'tablename': {'col1', 'col2'}}。仅这些表和列将被考虑创建Z3结构。
        """
        tables_to_ensure_creation = set(relevant_columns.keys())

        valid_tables_for_creation = set()
        for table_name in tables_to_ensure_creation:
            if table_name not in initial_schemas:
                logger.warning(f"表 '{table_name}' (来自relevant_columns.keys()) 在initial_schemas中未定义。跳过此表的Z3结构创建。")
            else:
                valid_tables_for_creation.add(table_name)

        logger.info(f"最终用于Z3结构创建的表（仅从relevant_columns派生且存在于initial_schemas中）: {list(valid_tables_for_creation)}")

        for table_name in valid_tables_for_creation:
            table_col_types_from_schema = initial_schemas[table_name]
            
            z3_schema_for_this_table = {} 
            
            # 仅使用relevant_columns中为此表指定的列
            cols_to_consider_for_z3_definition = relevant_columns[table_name]
            logger.debug(f"表 '{table_name}'，使用relevant_columns中指定的列进行Z3结构创建: {cols_to_consider_for_z3_definition}")

            for col_name in cols_to_consider_for_z3_definition: 
                if col_name in table_col_types_from_schema:
                    col_type_str = table_col_types_from_schema[col_name]
                    z3_sort = None
                    if col_type_str == 'Int':
                        z3_sort = z3.IntSort()
                    elif col_type_str == 'String':
                        z3_sort = z3.StringSort()
                    elif col_type_str == 'Bool':
                        z3_sort = z3.BoolSort()
                    elif col_type_str == 'Float': 
                        z3_sort = z3.RealSort()
                    elif col_type_str == 'Date':
                        date_type = DateType()
                        z3_sort = date_type.int_sort 
                    else:
                        logger.warning(f"不支持的列类型 '{col_type_str}' (表 {table_name}.{col_name})。默认使用StringSort。")
                        z3_sort = z3.StringSort()
                    
                    z3_schema_for_this_table[col_name] = z3_sort
                else:
                    logger.warning(f"列 '{col_name}' (来自表 '{table_name}' 的relevant_columns) 在模式定义(initial_schemas)中未找到。跳过此列的Z3结构创建。")
            
            # 即使z3_schema_for_this_table为空，也始终创建Table对象
            if table_name in self.table_objects:
                 logger.debug(f"表 '{table_name}' 的对象已存在。跳过通过create_table调用重新创建，应该没问题。")
            else:
                logger.debug(f"创建Z3表 '{table_name}'，有效Z3模式: {{ { {k: str(v) for k, v in z3_schema_for_this_table.items()} } }}")
                table_obj = self.z3_sql.create_table(table_name, z3_schema_for_this_table)
                self.table_objects[table_name] = table_obj
            
            current_table_obj = self.table_objects.get(table_name)
            if not current_table_obj:
                 logger.error(f"在create_table_structures循环中无法创建或检索表 '{table_name}' 的对象。")

        logger.info(f"已创建 {len(self.table_objects)} 个表的Z3结构。创建/确保的表对象: {list(self.table_objects.keys())}")

    def process_sql_statements(self,
                               raw_sql_list: List[Expression],
                               qualified_sql_list: List[Expression],
                               relevant_columns: Optional[Dict[str, Set[str]]] = None) -> None:
        """
        处理SQL语句，将其转换为Z3约束

        Args:
            raw_sql_list: 解析后的原始SQL AST列表 (保留原始别名)
            qualified_sql_list: 解析后并经过限定的SQL AST列表
            relevant_columns: 相关列字典，格式为 {'tablename': {'col1', 'col2'}} (基于物理表名)
        """
        # 动态识别初始源物理表和所有涉及的物理表
        all_source_physical_tables = set()
        all_target_physical_tables = set()
        
        for rs_expr in raw_sql_list:
            # 从FROM和JOIN子句中收集所有源表，使用其物理名称
            select_node_rs = rs_expr.find(exp.Select)
            if select_node_rs:
                from_clause_node_rs = select_node_rs.args.get('from')
                if from_clause_node_rs and isinstance(from_clause_node_rs.this, exp.Table):
                    physical_name = from_clause_node_rs.this.name.strip('\'`"')
                    all_source_physical_tables.add(physical_name)

                join_nodes_rs = select_node_rs.args.get('joins', [])
                for join_node_rs in join_nodes_rs:
                    if isinstance(join_node_rs.this, exp.Table):
                        physical_name = join_node_rs.this.name.strip('\'`"')
                        all_source_physical_tables.add(physical_name)

            # 从INSERT语句中收集所有目标表，使用其物理名称
            if isinstance(rs_expr, exp.Insert):
                target_table_node_rs = rs_expr.this
                actual_target_table_ast_node = None
                if isinstance(target_table_node_rs, exp.Schema):
                    actual_target_table_ast_node = target_table_node_rs.this
                else:
                    actual_target_table_ast_node = target_table_node_rs
                
                if isinstance(actual_target_table_ast_node, exp.Table):
                    all_target_physical_tables.add(str(actual_target_table_ast_node.name).strip('\'`"'))
                elif isinstance(actual_target_table_ast_node, exp.Identifier):
                    all_target_physical_tables.add(str(actual_target_table_ast_node.this).strip('\'`"'))

        self.initial_source_tables.clear()
        for phys_table in all_source_physical_tables:
            if phys_table not in all_target_physical_tables:
                self.initial_source_tables.add(phys_table)
        
        logger.info(f"动态识别的初始源物理表（在任何INSERT中是源但不是目标的表）: {list(self.initial_source_tables)}")
        logger.info(f"在原始SQL中找到的所有物理源表: {list(all_source_physical_tables)}")
        logger.info(f"在原始SQL中找到的所有物理目标表（来自INSERT）: {list(all_target_physical_tables)}")

        for i, (raw_sql_expr, qualified_sql_expr) in enumerate(zip(raw_sql_list, qualified_sql_list)):
            try:
                if isinstance(raw_sql_expr, exp.Insert) and isinstance(qualified_sql_expr, exp.Insert):
                    self._process_insert_statement(raw_sql_expr, qualified_sql_expr, relevant_columns)
                    
                    # 记录最后一个INSERT语句的目标表为最终目标表
                    if i == len(qualified_sql_list) - 1:
                        target_table_node_q = qualified_sql_expr.this
                        final_target_name = None
                        if isinstance(target_table_node_q, exp.Schema):
                            final_target_name = str(target_table_node_q.this.name).strip('\'`"')
                        elif isinstance(target_table_node_q, exp.Table):
                            final_target_name = str(target_table_node_q.name).strip('\'`"')
                        elif isinstance(target_table_node_q, exp.Identifier):
                             final_target_name = str(target_table_node_q.this).strip('\'`"')

                        if final_target_name:
                            self.final_target_table = final_target_name
                            logger.info(f"根据最后一条INSERT语句，将final_target_table设置为: {self.final_target_table}")
                        else:
                            logger.warning(f"无法从最后一条INSERT语句确定最终目标表: {qualified_sql_expr}")
            except Exception as e:
                logger.error(f"处理SQL语句 (raw: {raw_sql_expr.sql()}, qualified: {qualified_sql_expr.sql()}) 时出错: {e}")
                import traceback
                logger.error(f"错误详细信息: {traceback.format_exc()}")

    def _convert_sql_expr_to_z3(self,
                                sql_expr: Expression, # Expression from QUALIFIED AST
                                source_row_vars_by_alias: Dict[str, z3.ExprRef],
                                alias_to_physical_map: Dict[str, str],
                                current_context_alias: Optional[str] = None # The alias governing the current context
                                ) -> Optional[z3.ExprRef]:
        """
        将SQL表达式(来自限定AST)转换为Z3表达式，使用基于别名的预定义统一源行变量。

        Args:
            sql_expr: SQL表达式 (来自限定AST)
            source_row_vars_by_alias: 字典，包含当前INSERT上下文中每个源表别名对应的统一Z3行变量。
                                     格式: {'alias_name': z3_row_variable}
            alias_to_physical_map: 字典，从别名映射到物理表名。 {'alias_name': 'physical_table_name'}
            current_context_alias: 当列引用没有明确的、可识别的别名限定符时，使用的当前上下文的表别名。

        Returns:
            Z3表达式；如果无法转换，则返回None
        """
        # 处理别名表达式
        if isinstance(sql_expr, exp.Alias):
            return self._convert_sql_expr_to_z3(sql_expr.this, source_row_vars_by_alias, alias_to_physical_map, current_context_alias)
        elif isinstance(sql_expr, exp.Where):
            return self._convert_sql_expr_to_z3(sql_expr.this, source_row_vars_by_alias, alias_to_physical_map, current_context_alias)

        # 处理列引用
        if isinstance(sql_expr, exp.Column):
            # sql_expr is from the QUALIFIED AST. Its '.table' might be a physical name or sometimes an alias.
            raw_column_qualifier = str(sql_expr.table).strip('\'`"') if sql_expr.table else None
            column_name = str(sql_expr.name).strip('\'`"') # This should be the simple column name

            effective_alias_for_lookup: Optional[str] = None
            
            # 处理列引用时，尝试更灵活地查找有效别名
            # 首先尝试直接匹配别名
            if raw_column_qualifier and raw_column_qualifier in source_row_vars_by_alias:
                effective_alias_for_lookup = raw_column_qualifier
                logger.debug(f"列 '{sql_expr.sql()}' 直接匹配到别名 '{effective_alias_for_lookup}'")
            # 然后尝试匹配物理表名到别名的映射
            elif raw_column_qualifier:
                matching_aliases = [
                    alias for alias, physical_name in alias_to_physical_map.items()
                    if physical_name == raw_column_qualifier and alias in source_row_vars_by_alias
                ]
                if len(matching_aliases) == 1:
                    # 只有一个匹配，可以确定使用这个别名
                    effective_alias_for_lookup = matching_aliases[0]
                    logger.debug(f"列 '{sql_expr.sql()}' 的物理表限定符 '{raw_column_qualifier}' 映射到唯一别名 '{effective_alias_for_lookup}'")
                elif len(matching_aliases) > 1:
                    # 多个匹配，可能是自连接，尝试使用上下文别名
                    if current_context_alias and current_context_alias in matching_aliases:
                        effective_alias_for_lookup = current_context_alias
                        logger.debug(f"列 '{sql_expr.sql()}' 在自连接中使用上下文别名 '{effective_alias_for_lookup}'")
                    else:
                        # 无法确定具体使用哪个别名，尝试根据列名和上下文做更智能的匹配
                        # 例如，如果我们在处理 a.id = b.old_id，并且当前上下文是 WHERE 子句的一部分
                        # 可以尝试使用更多的上下文信息来确定正确的别名
                        
                        # 如果有上下文别名，检查它是否在匹配列表中
                        if current_context_alias:
                            logger.debug(f"列 '{sql_expr.sql()}' 的物理表限定符 '{raw_column_qualifier}' 匹配到多个别名 {matching_aliases}，尝试使用上下文别名 '{current_context_alias}'")
                            # 如果上下文别名在匹配列表中，使用它
                            if current_context_alias in matching_aliases:
                                effective_alias_for_lookup = current_context_alias
                            else:
                                # 否则使用第一个匹配
                                logger.warning(f"列 '{sql_expr.sql()}' 的物理表限定符 '{raw_column_qualifier}' 匹配到多个别名 {matching_aliases}，上下文别名 '{current_context_alias}' 不在其中。使用第一个匹配 '{matching_aliases[0]}'。")
                                effective_alias_for_lookup = matching_aliases[0]
                        else:
                            # 没有上下文别名，使用第一个匹配
                            logger.warning(f"列 '{sql_expr.sql()}' 的物理表限定符 '{raw_column_qualifier}' 匹配到多个别名 {matching_aliases}，无上下文别名。使用第一个匹配 '{matching_aliases[0]}'。")
                            effective_alias_for_lookup = matching_aliases[0]
            # 最后尝试使用上下文别名
            elif current_context_alias and current_context_alias in source_row_vars_by_alias:
                effective_alias_for_lookup = current_context_alias
                logger.debug(f"列 '{sql_expr.sql()}' 没有限定符，使用上下文别名 '{effective_alias_for_lookup}'")

            if not effective_alias_for_lookup:
                logger.error(f"无法确定列 '{sql_expr.sql()}' (原始限定符: '{raw_column_qualifier}', 列名: '{column_name}') 的有效别名。 当前上下文别名: '{current_context_alias}'. 已知源别名: {list(source_row_vars_by_alias.keys())}")
                return None

            physical_table_name = alias_to_physical_map.get(effective_alias_for_lookup)
            if not physical_table_name:
                logger.error(f"无法找到别名 '{effective_alias_for_lookup}' 的物理表名。别名到物理表映射: {alias_to_physical_map}")
                return None

            if column_name:
                unified_row_var = source_row_vars_by_alias[effective_alias_for_lookup]
                column_func_name = f"{physical_table_name}_{column_name}"

                if column_func_name in self.z3_sql.column_functions:
                    z3_expr = self.z3_sql.column_functions[column_func_name](unified_row_var)
                    logger.debug(f"列 '{sql_expr.sql()}' 转换为 Z3 表达式: {z3_expr} (使用别名 '{effective_alias_for_lookup}', 物理表 '{physical_table_name}', 行变量 {unified_row_var})")
                    return z3_expr
                else:
                    logger.error(f"列函数 {column_func_name} (基于别名 '{effective_alias_for_lookup}' -> 物理表 '{physical_table_name}') 不存在于z3_sql.column_functions中")
                    # Try to get column type from the physical table object
                    table_obj = self.table_objects.get(physical_table_name)
                    if table_obj:
                        z3_sort_type = table_obj.columns_with_types.get(column_name) # This uses Z3 Sorts
                        if z3_sort_type == z3.IntSort(): return z3.IntVal(0, self.z3_sql.solver.ctx)
                        if z3_sort_type == z3.RealSort(): return z3.RealVal(0.0, self.z3_sql.solver.ctx)
                        if z3_sort_type == z3.StringSort(): return z3.StringVal("", self.z3_sql.solver.ctx)
                        if z3_sort_type == z3.BoolSort(): return z3.BoolVal(False, self.z3_sql.solver.ctx)
                        # Add DateType check if necessary (DateType().int_sort)
                        logger.warning(f"列 '{column_func_name}' 未找到对应Z3函数且类型未知或不支持 ({z3_sort_type})，返回对应类型的默认值。")
                    else:
                        logger.warning(f"物理表对象 '{physical_table_name}' 未找到，无法确定列 '{column_func_name}' 的默认类型。")
                    # 返回 None 而不是特定的占位符，防止生成错误的约束
                    return None  # 返回None比返回一个永远无法满足的值更安全
            else: # column_name is None or empty
                logger.error(f"无法从表达式 '{sql_expr.sql()}' 解析列名 (有效别名: '{effective_alias_for_lookup}')")
                return None

        # 处理常量
        elif isinstance(sql_expr, exp.Literal):
            value = sql_expr.this
            is_string = getattr(sql_expr, 'is_string', False)
            
            if is_string:
                fmt = is_date_str(str(value))
                if fmt:
                    date_type = DateType()
                    date_expr_obj = date_type.parse_date(str(value), fmt)
                    return date_expr_obj.date_expr if hasattr(date_expr_obj, 'date_expr') else date_expr_obj
                else:
                    return z3.StringVal(str(value), self.z3_sql.solver.ctx)
            else:
                try:
                    float_val = float(value)
                    if float_val.is_integer():
                        return z3.IntVal(int(float_val), self.z3_sql.solver.ctx)
                    else:
                        return z3.RealVal(float_val, self.z3_sql.solver.ctx)
                except (ValueError, TypeError):
                    logger.warning(f"无法将字面量 {value} 转换为数值，返回默认整数0")
                    return z3.IntVal(0, self.z3_sql.solver.ctx)

        # 处理二元操作
        elif isinstance(sql_expr, exp.Binary):
            # 递归转换左右子表达式，传递 source_row_vars_by_alias 和 current_context_alias
            # current_context_alias 在二元操作中可能需要更细致地确定，
            # 例如，如果左边是 ColA，右边是 TableB.ColB，那么current_context_alias对右边无效。
            # sqlglot 的 AST 应该已经解析了表名，所以 current_context_alias 主要用于顶层无前缀列。
            left_z3 = self._convert_sql_expr_to_z3(sql_expr.left, source_row_vars_by_alias, alias_to_physical_map, current_context_alias)
            right_z3 = self._convert_sql_expr_to_z3(sql_expr.right, source_row_vars_by_alias, alias_to_physical_map, current_context_alias)

            if left_z3 is None or right_z3 is None:
                # 如果任一侧表达式转换失败，不要尝试生成二元表达式，直接返回None
                logger.warning(f"二元操作 '{type(sql_expr).__name__}' 的左侧或右侧表达式转换失败: {sql_expr.sql()}")
                return None

            if isinstance(sql_expr, exp.EQ): return left_z3 == right_z3
            elif isinstance(sql_expr, exp.NEQ): return left_z3 != right_z3
            elif isinstance(sql_expr, exp.GT): return left_z3 > right_z3
            elif isinstance(sql_expr, exp.GTE): return left_z3 >= right_z3
            elif isinstance(sql_expr, exp.LT): return left_z3 < right_z3
            elif isinstance(sql_expr, exp.LTE): return left_z3 <= right_z3
            elif isinstance(sql_expr, exp.And):
                # 确保逻辑操作的子表达式都是BoolRef类型
                if not (isinstance(left_z3, z3.BoolRef) and isinstance(right_z3, z3.BoolRef)):
                    logger.warning(f"AND操作的子表达式不是布尔类型: 左: {type(left_z3)}, 右: {type(right_z3)}")
                    # 避免类型错误，返回一个默认布尔值
                    return z3.BoolVal(True, self.z3_sql.solver.ctx)
                return z3.And(left_z3, right_z3)
            elif isinstance(sql_expr, exp.Or):
                # 确保逻辑操作的子表达式都是BoolRef类型
                if not (isinstance(left_z3, z3.BoolRef) and isinstance(right_z3, z3.BoolRef)):
                    logger.warning(f"OR操作的子表达式不是布尔类型: 左: {type(left_z3)}, 右: {type(right_z3)}")
                    # 避免类型错误，返回一个默认布尔值
                    return z3.BoolVal(True, self.z3_sql.solver.ctx)
                return z3.Or(left_z3, right_z3)
            elif isinstance(sql_expr, exp.Add): return left_z3 + right_z3
            elif isinstance(sql_expr, exp.Sub): return left_z3 - right_z3
            elif isinstance(sql_expr, exp.Mul): return left_z3 * right_z3
            elif isinstance(sql_expr, exp.Div): return left_z3 / right_z3
            else:
                logger.warning(f"不支持的二元操作: {type(sql_expr)}")
                return None

        # 处理函数调用
        elif isinstance(sql_expr, exp.Anonymous): # SQL UDFs or built-in functions like UPPER(), COUNT() etc.
            func_name = sql_expr.this.lower()
            z3_args = []
            for arg_expr_q in sql_expr.expressions:
                # When converting arguments of a function, the current_context_alias might not always apply directly
                # if arguments are like other_alias.column. The column conversion logic should handle it.
                arg_z3 = self._convert_sql_expr_to_z3(arg_expr_q, source_row_vars_by_alias, alias_to_physical_map, current_context_alias)
                if arg_z3 is None:
                    logger.warning(f"函数 '{func_name}' 的参数 '{arg_expr_q.sql()}' 转换失败。")
                    return None
                z3_args.append(arg_z3)
            
            # TODO: 实现更多内置函数的Z3转换
            if func_name == 'count':
                # COUNT(*) 或 COUNT(column) 的简化处理，可以返回一个符号整数或常量1
                return z3.IntVal(1, self.z3_sql.solver.ctx) # 简化：认为总是有至少一行或固定计数
            elif func_name == 'sum':
                if z3_args: return z3_args[0] # 简化：SUM(col) 返回 col 本身 (应为累加)
                return z3.IntVal(0, self.z3_sql.solver.ctx)
            elif func_name == 'upper':
                 if z3_args and z3.is_string(z3_args[0]):
                    # Z3 本身没有直接的 UPPER/LOWER 字符串函数，需要用 z3.SeqToRe 和正则表达式或自己实现
                    # 简化：返回原字符串
                    return z3_args[0]
                 elif z3_args: # 类型不匹配
                    logger.warning(f"UPPER函数期望字符串参数，但得到 {type(z3_args[0])}")
                    return z3_args[0] # 返回原参数
                 return z3.StringVal("", self.z3_sql.solver.ctx) # 无参数
            # ... 其他函数类似处理 ...
            elif func_name == 'cast':
                target_type_str = None
                if 'to' in sql_expr.args and isinstance(sql_expr.args['to'], exp.DataType):
                    target_type_str = str(sql_expr.args['to'].this).lower()

                if z3_args and target_type_str:
                    val_to_cast = z3_args[0]
                    # Z3 类型转换逻辑 (简化)
                    if target_type_str in ['integer', 'int']:
                        if z3.is_real(val_to_cast): return z3.ToInt(val_to_cast)
                        # if z3.is_string(val_to_cast): return z3.StrToInt(val_to_cast) # Z3 4.8.8+
                        return val_to_cast # Assume already int or compatible
                    elif target_type_str in ['real', 'float', 'double']:
                        if z3.is_int(val_to_cast): return z3.ToReal(val_to_cast)
                        return val_to_cast # Assume already real or compatible
                    # Add more casts as needed
                    return val_to_cast # Default: no cast or unsupported cast
                return z3.IntVal(0, self.z3_sql.solver.ctx) # CAST 失败或参数不足

            else:
                logger.warning(f"不支持的SQL函数: {func_name}")
                # 返回一个占位符或None
                return z3.IntVal(0, self.z3_sql.solver.ctx) # 默认返回0

        # 移除旧的 _create_simple_func, _create_arg_func, _create_cast_func
        # 因为现在直接返回Z3表达式

        else:
            logger.warning(f"彻底不支持的SQL表达式类型: {type(sql_expr)} ({sql_expr})")
            return None


    def _process_insert_statement(self,
                                original_insert_ast: exp.Insert,
                                qualified_insert_ast: exp.Insert,
                                relevant_columns: Dict[str, Set[str]] = None) -> None:
        """
        处理单个INSERT语句，提取目标表、源表和条件

        Args:
            original_insert_ast: 原始INSERT语句的AST (用于别名识别)
            qualified_insert_ast: 限定后的INSERT语句的AST (用于类型和结构，以及某些表达式转换)
            relevant_columns: 相关列字典，格式为 {'physical_tablename': {'col1', 'col2'}}
        """
        # Determine target table from qualified AST (physical name)
        target_table_ast_q = qualified_insert_ast.this # exp.Schema or exp.Table or exp.Identifier
        target_table_name_physical: Optional[str] = None
        if isinstance(target_table_ast_q, exp.Schema):
            target_table_name_physical = str(target_table_ast_q.this.name).strip('\'`"')
        elif isinstance(target_table_ast_q, exp.Table):
            target_table_name_physical = str(target_table_ast_q.name).strip('\'`"')
        elif isinstance(target_table_ast_q, exp.Identifier):
             target_table_name_physical = str(target_table_ast_q.this).strip('\'`"')

        if not target_table_name_physical:
            logger.error(f"无法从限定INSERT AST中确定目标表名: {qualified_insert_ast.sql()}")
            return
            
        target_table_obj = self.table_objects.get(target_table_name_physical)
        if not target_table_obj:
            logger.warning(f"INSERT 语句的目标物理表 '{target_table_name_physical}' 未在 Z3LineageAnalyzer 中创建或定义。将仅处理其 SELECT 子句中的 WHERE/JOIN 条件 (如果存在)，但不会执行实际的Z3插入。")

        original_select_ast = original_insert_ast.find(exp.Select)
        qualified_select_ast = qualified_insert_ast.find(exp.Select)

        if not original_select_ast or not qualified_select_ast:
            logger.warning(f"INSERT语句 (raw: {original_insert_ast.sql()}, qualified: {qualified_insert_ast.sql()}) 中未找到SELECT部分，跳过")
            return

        # --- Alias-aware source table and row variable mapping ---
        source_row_vars_by_alias: Dict[str, z3.ExprRef] = {}
        alias_to_physical_map: Dict[str, str] = {}
        
        # Helper to process table expressions from original AST
        def process_table_expr_for_alias_map(table_expr_node: exp.Table):
            if isinstance(table_expr_node, exp.Table):
                alias = table_expr_node.alias_or_name.strip('\'`"')
                physical_name = table_expr_node.name.strip('\'`"')
                
                if alias not in source_row_vars_by_alias:
                    source_row_vars_by_alias[alias] = z3.Int(f"{alias}_unified_row_{self.z3_sql._get_unique_var_suffix()}")
                    logger.debug(f"为源表别名 '{alias}' (物理表: '{physical_name}') 创建Z3行变量: {source_row_vars_by_alias[alias]}")
                alias_to_physical_map[alias] = physical_name

        # Process FROM clause from original AST
        from_clause_node_orig = original_select_ast.args.get('from')
        default_from_alias: Optional[str] = None
        if from_clause_node_orig and isinstance(from_clause_node_orig.this, exp.Table):
            process_table_expr_for_alias_map(from_clause_node_orig.this)
            default_from_alias = from_clause_node_orig.this.alias_or_name.strip('\'`"')
            
        # Process JOIN clauses from original AST
        join_nodes_orig = original_select_ast.args.get('joins', [])
        for join_node_orig in join_nodes_orig:
            if isinstance(join_node_orig.this, exp.Table):
                process_table_expr_for_alias_map(join_node_orig.this)
        
        logger.debug(f"构建的 source_row_vars_by_alias: { {k: str(v) for k,v in source_row_vars_by_alias.items()} }")
        logger.debug(f"构建的 alias_to_physical_map: {alias_to_physical_map}")
        # --- End of alias mapping ---

        # 对自连接情况增加处理灵活度，允许表的多次引用
        # Constrain unified source row variables for INTERMEDIATE source tables (linking by physical name)
        # Now handle potential self-join (same physical table multiple references)
        all_source_constraints_satisfied = True  # 跟踪所有源表约束是否都满足
        
        # 跟踪每个物理表被引用的次数，用于自连接处理
        physical_table_reference_count = {}
        for alias_name, physical_name in alias_to_physical_map.items():
            physical_table_reference_count[physical_name] = physical_table_reference_count.get(physical_name, 0) + 1
        
        # 打印出被多次引用的表（自连接情况）
        self_joined_tables = [table for table, count in physical_table_reference_count.items() if count > 1]
        if self_joined_tables:
            logger.info(f"检测到自连接表: {self_joined_tables}。对这些表的多个引用将使用独立的行ID变量。")
            
        for alias_name, unified_row_variable in source_row_vars_by_alias.items():
            physical_source_name = alias_to_physical_map.get(alias_name)
            if not physical_source_name:
                logger.error(f"逻辑错误: 别名 '{alias_name}' 在 alias_to_physical_map 中没有条目。跳过行变量约束。")
                continue

            # 使用辅助方法处理源表行变量链接
            constraint_satisfied = self.process_source_table_row_linking(alias_name, physical_source_name, unified_row_variable)
            if not constraint_satisfied:
                all_source_constraints_satisfied = False

        # 如果有任何源表约束不满足，添加警告日志但不阻止处理
        if not all_source_constraints_satisfied:
            logger.warning(f"一个或多个源表约束无法满足。这可能影响分析结果的准确性，但将继续处理。")
            # 不再添加 False 约束，而是继续处理
        
        # Target columns specified in INSERT statement (e.g., INSERT INTO tbl (col1, col2))
        # Use qualified_insert_ast for this as target table structure is physical
        target_columns_specified_in_insert = []
        if isinstance(qualified_insert_ast.this, exp.Schema):
             column_expression_nodes = qualified_insert_ast.this.expressions
             if isinstance(column_expression_nodes, list):
                for col_node_expr in column_expression_nodes:
                    if isinstance(col_node_expr, exp.Identifier):
                         target_columns_specified_in_insert.append(str(col_node_expr.this).strip('\'`"'))
                    elif isinstance(col_node_expr, exp.Column):
                         target_columns_specified_in_insert.append(str(col_node_expr.name).strip('\'`"'))
                    else:
                        logger.warning(f"目标表 '{target_table_name_physical}' 的INSERT目标模式中出现意外节点类型 {type(col_node_expr)}: {col_node_expr}")
        
        logger.debug(f"对于 INSERT 到 '{target_table_name_physical}', INSERT模式指定的目标列: {target_columns_specified_in_insert}")

        # Column mappings: target_col_name (physical) -> source_sqlglot_expr (from qualified_select_ast)
        column_mappings: Dict[str, Expression] = {}
        
        select_expressions_q = qualified_select_ast.expressions # Use qualified select expressions for conversion
        
        for i, expr_node_q in enumerate(select_expressions_q):
            target_col_name_physical: Optional[str] = None
            logger.debug(f"  处理来自限定查询的 SELECT 表达式 #{i}: {expr_node_q.sql()} (类型: {type(expr_node_q)})")

            if i < len(target_columns_specified_in_insert):
                target_col_name_physical = target_columns_specified_in_insert[i]
            elif isinstance(expr_node_q, exp.Alias):
                target_col_name_physical = str(expr_node_q.alias).strip('\'`"')
            elif isinstance(expr_node_q, exp.Column): # Should be output name if not aliased
                target_col_name_physical = str(expr_node_q.name).strip('\'`"')
            else:
                logger.warning(f"    无法从限定SELECT表达式 '{expr_node_q.sql()}' 确定目标列名。")

            if not target_col_name_physical:
                logger.warning(f"  无法确定限定SELECT表达式 '{expr_node_q.sql()}' 的目标物理列名，在INSERT到 '{target_table_name_physical}' 时跳过此表达式。")
                continue
            
            logger.debug(f"  最终确定的目标物理列名 '{target_col_name_physical}' (对于源表达式 '{expr_node_q.sql()}')，准备检查Z3模式。")

            if target_table_obj and target_col_name_physical in target_table_obj.columns_with_types:
                column_mappings[target_col_name_physical] = expr_node_q
                logger.debug(f"    成功映射源表达式(限定) '{expr_node_q.sql()}' 到目标列 '{target_table_name_physical}.{target_col_name_physical}'")
            elif target_table_obj:
                logger.warning(f"    跳过映射: 目标物理列名 '{target_col_name_physical}' (对于源表达式 '{expr_node_q.sql()}') 不在目标物理表 '{target_table_name_physical}' 的Z3定义中。Z3列: {list(target_table_obj.columns_with_types.keys())}.")
            else:
                logger.error(f"  在处理列映射时，目标物理表对象 '{target_table_name_physical}' 未找到。")

        # Process WHERE clause from qualified_select_ast, using default_from_alias for context
        where_clause_q = qualified_select_ast.args.get('where')
        where_clause_orig = original_select_ast.args.get('where')
        if where_clause_q:
            # 使用原始 SQL 的 WHERE 子句来获取更准确的别名信息
            # 先分析原始 WHERE 子句中涉及的表别名
            involved_aliases = set()
            if where_clause_orig:
                # 递归收集原始 WHERE 子句中的所有表别名
                def collect_aliases(node):
                    if isinstance(node, exp.Column) and node.table:
                        involved_aliases.add(str(node.table).strip('\'`"'))
                    for child in node.args.values():
                        if isinstance(child, Expression):
                            collect_aliases(child)
                        elif isinstance(child, list):
                            for item in child:
                                if isinstance(item, Expression):
                                    collect_aliases(item)
                
                collect_aliases(where_clause_orig.this)
            
            # 如果找到了涉及的别名，使用第一个作为上下文；否则使用默认的 FROM 别名
            where_context_alias = next(iter(involved_aliases)) if involved_aliases else default_from_alias
            logger.debug(f"WHERE 子句中涉及的表别名: {involved_aliases}，使用 '{where_context_alias}' 作为上下文")
            
            z3_where_condition = self._convert_sql_expr_to_z3(
                where_clause_q.this, # condition expression from qualified AST
                source_row_vars_by_alias,
                alias_to_physical_map,
                where_context_alias # 使用分析得到的上下文别名
            )
            if z3_where_condition is not None:
                if isinstance(z3_where_condition, z3.BoolRef):
                    # 检查是否存在恒假表达式，如 z3.BoolVal(False)
                    if z3.is_false(z3_where_condition):
                        logger.warning(f"WHERE条件 '{where_clause_q.this.sql()}' 转换为Z3恒假表达式。可能某些列不存在或转换错误。")
                    else:
                        self.z3_sql.add(z3_where_condition)
                        logger.info(f"添加WHERE条件约束到Z3求解器: {z3_where_condition} (上下文别名: {where_context_alias})")
                else:
                    logger.error(f"WHERE条件转换为非布尔Z3表达式: {z3_where_condition} (类型: {type(z3_where_condition)})")
            else:
                logger.warning(f"无法转换WHERE条件：{where_clause_q.this.sql()}，此条件将被忽略。")
                # 不添加任何约束，而不是添加False约束
        
        # Process JOIN conditions
        # Iterate original joins to get alias context, but convert conditions from qualified AST
        # Assuming original_select_ast.args.get('joins') and qualified_select_ast.args.get('joins') correspond
        original_joins = original_select_ast.args.get('joins', [])
        qualified_joins = qualified_select_ast.args.get('joins', [])

        if len(original_joins) != len(qualified_joins):
            logger.error(f"原始JOIN子句数量 ({len(original_joins)}) 与限定JOIN子句数量 ({len(qualified_joins)}) 不匹配。JOIN处理可能不准确。")
            # Potentially fall back to only qualified joins or skip join processing if this is critical

        for oj_idx, original_join_node in enumerate(original_joins):
            if oj_idx >= len(qualified_joins): break # Safety break

            qualified_join_node = qualified_joins[oj_idx]
            
            original_join_on_condition = original_join_node.args.get('on')
            qualified_join_on_condition_expr = qualified_join_node.args.get('on')

            if not original_join_on_condition or not qualified_join_on_condition_expr:
                logger.debug(f"JOIN (原始: {original_join_node.sql()}) 无 ON 条件，跳过。")
                continue

            # 分析 JOIN ON 条件中涉及的表别名
            join_involved_aliases = set()
            
            # 递归收集原始 JOIN ON 条件中的所有表别名
            def collect_join_aliases(node):
                if isinstance(node, exp.Column) and node.table:
                    join_involved_aliases.add(str(node.table).strip('\'`"'))
                for child in node.args.values():
                    if isinstance(child, Expression):
                        collect_join_aliases(child)
                    elif isinstance(child, list):
                        for item in child:
                            if isinstance(item, Expression):
                                collect_join_aliases(item)
            
            collect_join_aliases(original_join_on_condition.this)
            
            # 获取被JOIN的表的别名
            join_table_alias = original_join_node.this.alias_or_name.strip('\'`"') if isinstance(original_join_node.this, exp.Table) else None
            
            logger.debug(f"JOIN ON 条件中涉及的表别名: {join_involved_aliases}，被JOIN的表别名: {join_table_alias}")

            # 对于自连接，我们需要特别处理
            is_self_join = False
            if join_table_alias and join_table_alias in join_involved_aliases:
                is_self_join = True
                logger.info(f"检测到自连接: 表 '{alias_to_physical_map.get(join_table_alias)}' 被引用为别名 '{join_table_alias}'")

            z3_join_condition = None
            # 尝试使用更智能的方法处理 JOIN ON 条件
            if isinstance(qualified_join_on_condition_expr, exp.EQ) and isinstance(original_join_on_condition, exp.EQ):
                # 分析原始 JOIN ON 条件中的左右两侧表别名
                left_orig_alias = None
                right_orig_alias = None
                
                if isinstance(original_join_on_condition.left, exp.Column) and original_join_on_condition.left.table:
                    left_orig_alias = str(original_join_on_condition.left.table).strip('\'`"')
                
                if isinstance(original_join_on_condition.right, exp.Column) and original_join_on_condition.right.table:
                    right_orig_alias = str(original_join_on_condition.right.table).strip('\'`"')
                
                # 确保我们有明确的别名用于左右两侧
                if not left_orig_alias:
                    left_orig_alias = default_from_alias
                
                if not right_orig_alias:
                    right_orig_alias = join_table_alias
                
                logger.debug(f"JOIN ON EQ 条件: 左侧别名 '{left_orig_alias}', 右侧别名 '{right_orig_alias}'")
                
                # 转换左侧表达式，使用左侧的上下文别名
                z3_left = self._convert_sql_expr_to_z3(
                    qualified_join_on_condition_expr.left,
                    source_row_vars_by_alias,
                    alias_to_physical_map,
                    left_orig_alias
                )
                
                # 转换右侧表达式，使用右侧的上下文别名
                z3_right = self._convert_sql_expr_to_z3(
                    qualified_join_on_condition_expr.right,
                    source_row_vars_by_alias,
                    alias_to_physical_map,
                    right_orig_alias
                )

                if z3_left is not None and z3_right is not None:
                    z3_join_condition = (z3_left == z3_right)
                    logger.info(f"JOIN条件(EQ)转换: 左 ({qualified_join_on_condition_expr.left.sql()}) 上下文 '{left_orig_alias}' -> {z3_left}, 右 ({qualified_join_on_condition_expr.right.sql()}) 上下文 '{right_orig_alias}' -> {z3_right}")
                else:
                    if z3_left is None:
                        logger.warning(f"JOIN ON 条件左侧转换失败: {qualified_join_on_condition_expr.left.sql()} (上下文: {left_orig_alias})")
                    if z3_right is None:
                        logger.warning(f"JOIN ON 条件右侧转换失败: {qualified_join_on_condition_expr.right.sql()} (上下文: {right_orig_alias})")
            else:
                # 对于非 EQ 结构的 JOIN ON 条件，尝试使用更通用的方法
                # 如果有多个表别名，尝试使用第一个作为上下文
                join_context_alias = next(iter(join_involved_aliases)) if join_involved_aliases else default_from_alias
                logger.warning(f"JOIN ON 条件不是标准的 EQ 结构: {qualified_join_on_condition_expr.sql()}. 尝试使用 '{join_context_alias}' 作为上下文。")
                
                z3_join_condition = self._convert_sql_expr_to_z3(
                    qualified_join_on_condition_expr,
                    source_row_vars_by_alias,
                    alias_to_physical_map,
                    join_context_alias
                )

            if z3_join_condition is not None:
                if isinstance(z3_join_condition, z3.BoolRef):
                    # 检查是否存在恒假表达式
                    if z3.is_false(z3_join_condition):
                        logger.warning(f"JOIN条件 '{qualified_join_on_condition_expr.sql()}' 转换为Z3恒假表达式。可能某些列不存在或转换错误。")
                    else:
                        self.z3_sql.add(z3_join_condition)
                        logger.info(f"添加JOIN条件约束到Z3求解器: {z3_join_condition} (来自限定: {qualified_join_on_condition_expr.sql()})")
                else:
                    logger.error(f"JOIN条件转换为非布尔Z3表达式: {z3_join_condition} (类型: {type(z3_join_condition)})")
            else:
                logger.warning(f"无法转换JOIN条件：{qualified_join_on_condition_expr.sql()} (原始: {original_join_on_condition.sql()})，此条件将被忽略。")
                # 不添加任何约束，而不是添加False约束
        
        # Collect final Z3 value expressions for insert
        insert_z3_values = {}
        target_z3_cols_physical = list(target_table_obj.columns_with_types.keys()) if target_table_obj else []

        if target_table_obj:
            for target_col_name_phys, source_sql_expr_q in column_mappings.items():
                if target_col_name_phys in target_z3_cols_physical:
                    z3_value_expr = self._convert_sql_expr_to_z3(source_sql_expr_q, source_row_vars_by_alias, alias_to_physical_map, default_from_alias)
                    if z3_value_expr is not None:
                        insert_z3_values[target_col_name_phys] = z3_value_expr
                        logger.info(f"为目标物理列 '{target_table_name_physical}.{target_col_name_phys}' 从源表达式(限定) '{source_sql_expr_q.sql()}' 生成Z3值表达式: {z3_value_expr} (上下文别名: {default_from_alias})")
                    else:
                        logger.warning(f"无法为目标物理列 '{target_table_name_physical}.{target_col_name_phys}' 从源表达式(限定) '{source_sql_expr_q.sql()}' 创建Z3值表达式。")
                else:
                    logger.debug(f"目标物理列 '{target_col_name_phys}' 在物理表 '{target_table_name_physical}' 的Z3结构中意外不存在，跳过赋值。")
        else: # target_table_obj is None
            logger.error(f"尝试为物理表 '{target_table_name_physical}' 生成Z3插入值时，表对象未找到或无效。")
            # Early exit if target table object doesn't exist, as we can't insert.
            if not column_mappings: # if there were no mappings anyway
                 logger.info(f"对于INSERT到 '{target_table_name_physical}'，没有列映射可处理（且表对象缺失）。")
            else:
                 logger.warning(f"对于INSERT到 '{target_table_name_physical}'，虽有列映射但表对象缺失，无法插入。")
            return

        if not insert_z3_values and column_mappings:
            logger.warning(f"对于INSERT到 '{target_table_name_physical}'，虽有列映射 {list(column_mappings.keys())}，但未能生成任何有效的Z3值。目标表Z3列: {target_z3_cols_physical}。不执行插入。")
            return 
        elif not insert_z3_values:
            logger.info(f"对于INSERT到 '{target_table_name_physical}'，没有有效的Z3值可插入。")
            return

        # MODIFICATION: Handle multi-row inserts if source is an intermediate table with multiple concrete row possibilities.
        # This logic assumes a single primary source table determines the multiplicity of inserts.
        # If multiple source tables are joined, this might need to be more complex (e.g. Cartesian product of possibilities).
        # For now, we check if the `default_from_alias` (if it exists and is an intermediate table) has multiple rows.

        primary_source_alias_for_multi_insert = default_from_alias
        multi_insert_iterations = 1
        concrete_source_row_ids_to_iterate = None # List of concrete row IDs
        source_var_to_substitute = None

        if primary_source_alias_for_multi_insert and primary_source_alias_for_multi_insert in source_row_vars_by_alias:
            phys_name_of_primary_src = alias_to_physical_map.get(primary_source_alias_for_multi_insert)
            if phys_name_of_primary_src and phys_name_of_primary_src not in self.initial_source_tables:
                primary_src_obj = self.table_objects.get(phys_name_of_primary_src)
                if primary_src_obj and primary_src_obj.rows and len(primary_src_obj.rows) > 1:
                    # Source is intermediate and has multiple concrete rows in its Z3 representation.
                    # We need to perform one insert into the target table for each concrete row of this primary source.
                    multi_insert_iterations = len(primary_src_obj.rows)
                    concrete_source_row_ids_to_iterate = primary_src_obj.rows
                    source_var_to_substitute = source_row_vars_by_alias[primary_source_alias_for_multi_insert]
                    logger.info(f"主源别名 '{primary_source_alias_for_multi_insert}' (物理表 '{phys_name_of_primary_src}') 有 {multi_insert_iterations} 个具体行。将为目标表 '{target_table_name_physical}' 执行多次插入。")

        try:
            # 注意：使用 is not None 判断而不是直接布尔判断，以避免Z3表达式隐式转换为布尔值
            if concrete_source_row_ids_to_iterate is not None and source_var_to_substitute is not None:
                for i in range(multi_insert_iterations):
                    actual_source_row_id_val = concrete_source_row_ids_to_iterate[i]
                    substituted_insert_z3_values = {}
                    is_substitution_complete = True
                    for col, val_expr in insert_z3_values.items():
                        # Substitute the specific source_var_to_substitute with its concrete row_id for this iteration.
                        # Other source variables in val_expr (e.g., from JOINs) remain symbolic.
                        sub_val = z3.substitute(val_expr, (source_var_to_substitute, z3.IntVal(actual_source_row_id_val)))
                        substituted_insert_z3_values[col] = sub_val
                        if z3.is_expr(sub_val) and z3.is_const(sub_val) and sub_val.decl().kind() == z3.Z3_OP_UNINTERPRETED and source_var_to_substitute.decl().name() in str(sub_val):
                             logger.warning(f"  迭代 {i+1}/{multi_insert_iterations} (源行ID: {actual_source_row_id_val}): 列 '{col}' 的值 {val_expr} 替换后仍为 {sub_val}，可能替换未完全生效。")
                    
                    logger.debug(f"  迭代 {i+1}/{multi_insert_iterations} (源行ID: {actual_source_row_id_val}): 替换后的插入值: { {k:str(v) for k,v in substituted_insert_z3_values.items()} }")
                    try:
                        # Each iteration represents one concrete row from the primary source, so for_all_rows=False for this specific insert.
                        target_table_obj.insert(substituted_insert_z3_values, for_all_rows=False)
                        logger.info(f"  向物理表 '{target_table_name_physical}' 中插入了具体化Z3表达式值 (迭代 {i+1}/{multi_insert_iterations}，源 '{primary_source_alias_for_multi_insert}' 行ID {actual_source_row_id_val})，for_all_rows=False")
                    except Exception as e:
                        import traceback
                        logger.error(f"  迭代 {i+1}/{multi_insert_iterations} 中插入Z3表达式值到物理表 '{target_table_name_physical}' 时出错: {e}")
                        logger.error(f"  错误详情: {traceback.format_exc()}")
            else:
                # Standard single symbolic insert if not iterating or no primary source for iteration identified.
                use_for_all_rows = bool(source_row_vars_by_alias) # If there are source aliases, implies dependency
                try:
                    target_table_obj.insert(insert_z3_values, for_all_rows=use_for_all_rows)
                    logger.info(f"向物理表 '{target_table_name_physical}' 中插入了Z3表达式值，for_all_rows={use_for_all_rows} (基于源别名的存在，无特定多行迭代)")
                except Exception as e:
                    import traceback
                    logger.error(f"插入Z3表达式值到物理表 '{target_table_name_physical}' 时出错: {e}")
                    logger.error(f"错误详情: {traceback.format_exc()}")
        except Exception as e:
            # 捕获整个多行插入过程中的任何错误
            import traceback
            logger.error(f"处理插入到物理表 '{target_table_name_physical}' 的过程中发生错误: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    def process_source_table_row_linking(self, alias_name, physical_source_name, unified_row_variable):
        """
        处理源表行变量的链接，更安全地添加约束
        
        Args:
            alias_name: 表别名
            physical_source_name: 物理表名
            unified_row_variable: 统一行变量
        
        Returns:
            布尔值，表示是否成功处理链接而不添加False约束
        """
        if physical_source_name in self.initial_source_tables:
            # 初始源表保持自由行变量
            logger.debug(f"别名 '{alias_name}' (物理表: '{physical_source_name}') 是初始源表。其统一变量 '{unified_row_variable}' 保持自由。")
            return True
        else:
            # 中间表需要链接到其现有行
            source_table_obj_for_linking = self.table_objects.get(physical_source_name)
            if source_table_obj_for_linking:
                if source_table_obj_for_linking.rows:
                    # 为中间表的多次引用创建更宽松的约束
                    # 不要求严格相等，而是允许统一行变量为任何有效的行ID
                    disjunction = [unified_row_variable == existing_row_id for existing_row_id in source_table_obj_for_linking.rows]
                    if not disjunction:  # 防御性检查
                        logger.warning(f"中间源表 '{physical_source_name}' (别名 '{alias_name}') 有行但为其统一变量 '{unified_row_variable}' 创建了空析取式。")
                        return False  # 不添加False约束，仅返回失败状态
                    else:
                        try:
                            self.z3_sql.add(z3.Or(disjunction))
                            logger.info(f"链接中间源表 '{physical_source_name}' (别名 '{alias_name}') 的统一变量 '{unified_row_variable}' 到其现有Z3行: {source_table_obj_for_linking.rows}")
                            return True
                        except Exception as e:
                            logger.error(f"添加中间表行变量联系时出错: {e}")
                            # 不添加错误约束，只返回状态
                            return False
                else:
                    # 中间表有Z3对象但.rows为空
                    logger.warning(f"中间源表 '{physical_source_name}' (别名 '{alias_name}') 在其Z3表示中没有行。这意味着它在此上下文中为空。")
                    # 不立即添加False约束
                    return False
            else:
                # 中间表缺少Z3对象
                logger.warning(f"中间表 '{physical_source_name}' (别名 '{alias_name}') 没有对应的Z3表对象。它可能未被包含在 relevant_columns 中，或未对最终目标表产生贡献。")
                # 不立即添加False约束
                return False

    def analyze_table_level_dependencies(self) -> bool:
        """
        分析表级血缘关系
        """
        # 添加日志，显示最终的约束情况
        logger.info("开始分析表级血缘关系...")
        logger.info(f"初始源表: {list(self.initial_source_tables)}")
        logger.info(f"最终目标表: {self.final_target_table}")
        
        # 检查求解器中的约束数量
        constraints = self.z3_sql.solver.assertions()
        logger.info(f"求解器中共有 {len(constraints)} 个约束")
        
        # 打印关键约束信息
        for i, constraint in enumerate(constraints):
            if i < 10 or i > len(constraints) - 10:  # 只打印前10个和后10个约束
                logger.debug(f"约束 #{i}: {constraint}")
        
        # 执行可满足性检查
        result = self.z3_sql.check()
        
        if result == z3.sat:
            logger.info("血缘分析结果: 可满足 (存在血缘关系)")
            # 尝试获取模型，展示一个满足约束的具体例子
            try:
                model = self.z3_sql.solver.model()
                logger.debug(f"满足约束的模型示例: {model}")
            except:
                logger.debug("无法获取满足约束的模型示例")
        else:
            logger.info("血缘分析结果: 不可满足 (不存在血缘关系)")
            # 尝试获取不可满足核心，帮助诊断问题
            try:
                unsat_core = self.z3_sql.solver.unsat_core()
                logger.debug(f"不可满足核心约束: {unsat_core}")
            except:
                logger.debug("无法获取不可满足核心约束")
        
        return result == z3.sat


def analyze_z3_lineage(
    raw_parsed_sqls: List[Expression], # Added: Original parsed SQLs with aliases
    qualified_parsed_sqls: List[Expression], # Renamed from parsed_sqls
    relevant_columns: Dict[str, Set[str]], # Keyed by physical table name
    initial_schemas: Dict[str, Dict[str, str]], # Renamed from column_types
    final_target_table_name: Optional[str] = None # Physical name
) -> bool:
    """
    使用Z3分析SQL语句的表级血缘关系

    Args:
        raw_parsed_sqls: 原始解析后的SQL AST列表 (保留原始别名信息)
        qualified_parsed_sqls: 经过限定和预处理的SQL AST列表
        relevant_columns: 相关列字典，格式为 {'physical_tablename': {'col1', 'col2'}}
        initial_schemas: 列类型字典，格式为 {'physical_tablename': {'columnname': 'type'}}
        final_target_table_name: 最终目标物理表名（可选）

    Returns:
        布尔值，表示最终目标表是否依赖于初始源表
    """
    analyzer = Z3LineageAnalyzer()
    
    # 检查是否有自连接情况
    table_references = {}
    for sql in raw_parsed_sqls:
        # 收集每个表被引用的次数
        def collect_table_refs(node):
            if isinstance(node, exp.Table):
                physical_name = node.name.strip('\'`"')
                if physical_name not in table_references:
                    table_references[physical_name] = 0
                table_references[physical_name] += 1
            
            # 递归处理子节点
            for child in node.args.values():
                if isinstance(child, Expression):
                    collect_table_refs(child)
                elif isinstance(child, list):
                    for item in child:
                        if isinstance(item, Expression):
                            collect_table_refs(item)
        
        collect_table_refs(sql)
    
    # 检查是否有表被多次引用（自连接）
    self_joined_tables = [table for table, count in table_references.items() if count > 1]
    if self_joined_tables:
        logger.info(f"检测到自连接表: {self_joined_tables}。这些表在分析中将使用独立的行ID变量。")
    
    # 创建表结构
    analyzer.create_table_structures(initial_schemas, relevant_columns)
    
    # 处理SQL语句
    analyzer.process_sql_statements(raw_parsed_sqls, qualified_parsed_sqls, relevant_columns)

    # 设置最终目标表
    if final_target_table_name:
        analyzer.final_target_table = final_target_table_name

    # 分析血缘关系
    dependencies = analyzer.analyze_table_level_dependencies()
    return dependencies