"""
表达式计算示例
展示SQL到Z3约束转换中的表达式计算功能
"""

import sys
import unittest
import codecs # 导入codecs模块
from pathlib import Path
from z3 import *

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入SQL到Z3模块
from new_model.core import SqlToZ3

class TestExpressionExample(unittest.TestCase):

    def _decode_z3_string(self, z3_str_val):
        """辅助方法：解码Z3字符串值（可能包含 \\\\u{...} 转义）"""
        if hasattr(z3_str_val, 'as_string'):
            raw_str = z3_str_val.as_string()
            if '\\\\u{' in raw_str: # 注意双反斜杠匹配文字 '\u{'
                processed_str = raw_str.replace('\\\\u{', '\\\\u').replace('}', '')
                try:
                    return codecs.decode(processed_str.encode('utf-8'), 'unicode_escape')
                except UnicodeDecodeError:
                    return raw_str 
            return raw_str 
        return str(z3_str_val)

    def setUp(self):
        """每个测试方法运行前，可以初始化一个通用的z3_sql实例，但表和数据最好在测试方法内创建以保证独立性"""
        self.z3_sql = SqlToZ3()
        # print("New TestExpressionExample test started.")

    def _create_and_populate_tables(self, z3_sql_instance):
        """辅助方法：创建并填充products和orders表"""
        products = z3_sql_instance.create_table("products", {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort(),
            "stock": IntSort()
        })
        
        orders = z3_sql_instance.create_table("orders", {
            "id": IntSort(),
            "product_id": IntSort(),
            "quantity": IntSort(),
            "discount": RealSort()
        })
        
        products.insert({"id": 1, "name": "Laptop", "price": 5000.0, "stock": 10})
        products.insert({"id": 2, "name": "Phone", "price": 3000.0, "stock": 20})
        products.insert({"id": 3, "name": "Headphones", "price": 500.0, "stock": 50})
        
        orders.insert({"id": 101, "product_id": 1, "quantity": 2, "discount": 0.1})
        orders.insert({"id": 102, "product_id": 2, "quantity": 1, "discount": 0.05})
        orders.insert({"id": 103, "product_id": 3, "quantity": 5, "discount": 0.2})
        return products, orders

    def test_case1_order_total_and_stock(self):
        """测试案例1: 可满足的查询 - 计算订单总价和剩余库存
        
        对应SQL:
        SELECT 
            p.name, 
            p.price * o.quantity * (1 - o.discount) AS total_price,
            p.stock - o.quantity AS remaining_stock
        FROM 
            products p 
            JOIN orders o ON p.id = o.product_id
        """
        print("\n运行测试: test_case1_order_total_and_stock")
        print("-" * 50)
        z3_sql = SqlToZ3() # 每个测试使用独立的实例
        products, orders = self._create_and_populate_tables(z3_sql)
        
        results = z3_sql.create_table("results", {
            "product_name": StringSort(),
            "total_price": RealSort(),
            "remaining_stock": IntSort()
        })
        
        print("对应SQL: SELECT p.name, p.price * o.quantity * (1 - o.discount) AS total_price, ...")
        
        join_condition_tuple = products.id == orders.product_id # (p_row_var, "products", o_row_var, "orders")
        z3_sql.add(join_condition_tuple) # SqlToZ3.add should handle this tuple correctly by creating the constraint

        # 获取JOIN操作产生的行变量
        p_row_var = join_condition_tuple[0]
        o_row_var = join_condition_tuple[2]

        def create_total_price_expr(product_row_expr, order_row_expr):
            # 使用列名直接访问列对象，然后调用它们以获取其Z3函数表示形式，并应用于行变量表达式
            return z3_sql.column_functions["products_price"](product_row_expr) * \
                   z3_sql.column_functions["orders_quantity"](order_row_expr) * \
                   (RealVal(1.0) - z3_sql.column_functions["orders_discount"](order_row_expr))

        def create_remaining_stock_expr(product_row_expr, order_row_expr):
            return z3_sql.column_functions["products_stock"](product_row_expr) - \
                   z3_sql.column_functions["orders_quantity"](order_row_expr)

        check_res = z3_sql.check()
        self.assertEqual(check_res, sat, "案例1: JOIN查询应有解")

        if check_res == sat:
            model = z3_sql.get_model()
            # 从模型中评估JOIN选择的行 (这些已经是 Z3 Row ID / IntVal)
            product_row_id = model.eval(p_row_var) 
            order_row_id = model.eval(o_row_var)
            
            # 手动添加结果 (模拟SELECT INTO)
            # result_row_id = results.insert({}) # 假设insert返回一个行ID (IntVal)

            product_name_func = z3_sql.column_functions["products_name"]
            # result_name_func = z3_sql.column_functions["results_product_name"]
            # result_price_func = z3_sql.column_functions["results_total_price"]
            # result_stock_func = z3_sql.column_functions["results_remaining_stock"]
            
            # z3_sql.add(result_name_func(result_row_id) == product_name_func(product_row_id))
            # z3_sql.add(result_price_func(result_row_id) == create_total_price_expr(product_row_id, order_row_id))
            # z3_sql.add(result_stock_func(result_row_id) == create_remaining_stock_expr(product_row_id, order_row_id))
            
            # 为了断言，我们直接评估表达式的值
            eval_product_name = self._decode_z3_string(model.eval(product_name_func(product_row_id)))
            eval_total_price = model.eval(create_total_price_expr(product_row_id, order_row_id))
            eval_remaining_stock = model.eval(create_remaining_stock_expr(product_row_id, order_row_id))

            print(f"查询结果: 商品名称: {eval_product_name}, 订单总价: {eval_total_price}, 剩余库存: {eval_remaining_stock}")

            # 断言 - 根据数据，可能的结果有多个，我们检查找到的任何一个是否有效
            # Laptop (id=1), Order (id=101, pid=1, q=2, d=0.1)
            # Price: 5000 * 2 * (1-0.1) = 10000 * 0.9 = 9000
            # Stock: 10 - 2 = 8
            if eval_product_name == "Laptop":
                self.assertEqual(str(eval_total_price), "9000") # RealVal比较
                self.assertEqual(eval_remaining_stock.as_long(), 8)
            # Phone (id=2), Order (id=102, pid=2, q=1, d=0.05)
            # Price: 3000 * 1 * (1-0.05) = 3000 * 0.95 = 2850
            # Stock: 20 - 1 = 19
            elif eval_product_name == "Phone":
                self.assertEqual(str(eval_total_price), "2850")
                self.assertEqual(eval_remaining_stock.as_long(), 19)
            # Headphones (id=3), Order (id=103, pid=3, q=5, d=0.2)
            # Price: 500 * 5 * (1-0.2) = 2500 * 0.8 = 2000
            # Stock: 50 - 5 = 45
            elif eval_product_name == "Headphones":
                self.assertEqual(str(eval_total_price), "2000")
                self.assertEqual(eval_remaining_stock.as_long(), 45)
            else:
                self.fail(f"未知的商品名称: {eval_product_name}")
        else:
            self.fail("案例1预期sat，但得到了unsat")


    def test_case2_filter_price_stock_satisfiable(self):
        """测试案例2: 可满足的查询 - 价格高于1000且库存少于30的商品
        
        对应SQL:
        SELECT 
            p.name, 
            p.price, 
            p.stock 
        FROM 
            products p 
        WHERE 
            p.price > 1000 AND p.stock < 30
        """
        print("\n运行测试: test_case2_filter_price_stock_satisfiable")
        print("-" * 50)
        z3_sql = SqlToZ3()
        products, _ = self._create_and_populate_tables(z3_sql) # orders表在此案例中不直接使用

        print("对应SQL: SELECT p.name, p.price, p.stock FROM products p WHERE p.price > 1000 AND p.stock < 30")
        
        # Column.__gt__ / __lt__ 返回 ColumnConstraint 对象
        # SqlToZ3.add() 应该能处理 ColumnConstraint
        constraint1 = products.price > 1000.0 # 使用 RealVal
        constraint2 = products.stock < 30
        
        z3_sql.add(constraint1)
        z3_sql.add(constraint2)
        
        check_res = z3_sql.check()
        self.assertEqual(check_res, sat, "案例2: 筛选查询应有解")

        if check_res == sat:
            model = z3_sql.get_model()
            
            # 我们需要一个方法来获取满足条件的行变量
            # 假设 ColumnConstraint 应用时会暴露其行变量，或者 add 方法返回它
            # 从原代码逻辑，它迭代 self.z3_sql.constraints，这是 solver 内部的
            # 更好的方法是，让 ColumnConstraint 的应用返回其 Z3 行变量
            # 或者 SqlToZ3.add(ColumnConstraint) 返回这个行变量

            # 简化：由于我们只有一个表 products，且约束直接作用于其列，
            # 求解器会找到一个满足条件的 products 行。
            # 我们需要获取这个 products 行的变量。
            # 这里的 `constraint1.row_var` 等是假设的API，需要 SqlToZ3 支持
            # 鉴于当前 SqlToZ3 的 add 方法，我们可能需要找到一个被约束的行变量
            
            # 暂时假设我们可以获取一个满足条件的 product_row_id (Z3 IntVal)
            # 这部分比较棘手，因为 add(ColumnConstraint) 不直接返回行变量
            # 我们需要迭代所有 products.rows，并检查哪个满足模型
            
            found_products = []
            product_name_func = z3_sql.column_functions["products_name"]
            product_price_func = z3_sql.column_functions["products_price"]
            product_stock_func = z3_sql.column_functions["products_stock"]

            for p_row_id_obj in products.rows: # products.rows 包含插入的行ID (Python int)
                # p_row_id_z3 = IntVal(p_row_id_obj) # 转换为Z3 IntVal
                # 检查这个特定的行ID是否满足模型中的所有约束
                # 即 model.eval(products.price(p_row_id_z3) > 1000.0) 和 model.eval(products.stock(p_row_id_z3) < 30)
                # 这种方法比较直接
                
                eval_price = model.eval(product_price_func(p_row_id_obj))
                eval_stock = model.eval(product_stock_func(p_row_id_obj))

                # 使用 Z3py 的比较，然后用 is_true
                price_cond_met = model.eval(eval_price > RealVal(1000.0))
                stock_cond_met = model.eval(eval_stock < IntVal(30))
                
                if is_true(price_cond_met) and is_true(stock_cond_met):
                    name = self._decode_z3_string(model.eval(product_name_func(p_row_id_obj)))
                    found_products.append({
                        "name": name,
                        "price": str(eval_price), # RealVal to string
                        "stock": eval_stock.as_long()
                    })
                    print(f"查询结果: 商品名称: {name}, 价格: {eval_price.as_decimal(2)}, 库存: {eval_stock}")

            self.assertGreater(len(found_products), 0, "应至少找到一个满足条件的产品")
            
            expected_sat_products = [
                {"name": "Laptop", "price": "5000", "stock": 10},
                {"name": "Phone", "price": "3000", "stock": 20}
            ]
            
            # 转换为可比较的元组列表
            actual_tuples = sorted([(p["name"], p["price"], p["stock"]) for p in found_products])
            expected_tuples = sorted([(p["name"], p["price"], p["stock"]) for p in expected_sat_products])
            
            self.assertListEqual(actual_tuples, expected_tuples, "找到的满足条件的产品列表与预期不符")
        else:
            self.fail("案例2预期sat，但得到了unsat")


    def test_case3_filter_price_stock_unsatisfiable(self):
        """测试案例3: 不可满足的查询 - 价格低于500且库存大于100的商品
        
        对应SQL:
        SELECT 
            p.name, 
            p.price, 
            p.stock 
        FROM 
            products p 
        WHERE 
            p.price < 500 AND p.stock > 100
        """
        print("\n运行测试: test_case3_filter_price_stock_unsatisfiable")
        print("-" * 50)
        z3_sql = SqlToZ3()
        products, _ = self._create_and_populate_tables(z3_sql)

        print("对应SQL: SELECT p.name, p.price, p.stock FROM products p WHERE p.price < 500 AND p.stock > 100")
        
        z3_sql.add(products.price < 500.0)
        z3_sql.add(products.stock > 100)
        
        check_res = z3_sql.check()
        self.assertEqual(check_res, unsat, "案例3: 筛选查询应无解")
        if check_res == sat:
            self.fail("案例3预期unsat，但得到了sat")
        else:
            print("查询没有结果（符合预期）")

    def test_case4_expression_in_constraints(self):
        """测试案例4: 将表达式作为约束添加到Z3求解器中 - 查找订单总价大于2500且剩余库存小于15的订单
        
        对应SQL:
        SELECT 
            p.name, 
            o.id 
        FROM 
            products p 
            JOIN orders o ON p.id = o.product_id 
        WHERE 
            p.price * o.quantity * (1 - o.discount) > 2500 
            AND p.stock - o.quantity < 15
        """
        print("\n运行测试: test_case4_expression_in_constraints")
        print("-" * 50)
        z3_sql = SqlToZ3()
        products, orders = self._create_and_populate_tables(z3_sql)
        
        print("对应SQL: SELECT p.name, o.id FROM products p JOIN orders o ON p.id = o.product_id WHERE p.price * o.quantity * (1 - o.discount) > 2500 AND p.stock - o.quantity < 15")
        
        # 创建JOIN条件
        join_condition_tuple = products.id == orders.product_id
        z3_sql.add(join_condition_tuple)

        # 获取JOIN操作产生的行变量
        p_row_var = join_condition_tuple[0]
        o_row_var = join_condition_tuple[2]

        # 定义表达式函数
        def create_total_price_expr(product_row_expr, order_row_expr):
            return z3_sql.column_functions["products_price"](product_row_expr) * \
                   z3_sql.column_functions["orders_quantity"](order_row_expr) * \
                   (RealVal(1.0) - z3_sql.column_functions["orders_discount"](order_row_expr))

        def create_remaining_stock_expr(product_row_expr, order_row_expr):
            return z3_sql.column_functions["products_stock"](product_row_expr) - \
                   z3_sql.column_functions["orders_quantity"](order_row_expr)
        
        # 关键区别：将这些表达式作为约束添加到Z3解析器中
        # 添加总价格大于2500的约束
        z3_sql.add(create_total_price_expr(p_row_var, o_row_var) > RealVal(2500.0))
        # 添加剩余库存小于15的约束
        z3_sql.add(create_remaining_stock_expr(p_row_var, o_row_var) < IntVal(15))
        
        check_res = z3_sql.check()
        self.assertEqual(check_res, sat, "案例4: 带表达式约束的查询应有解")

        if check_res == sat:
            model = z3_sql.get_model()
            
            # 评估结果
            product_name_func = z3_sql.column_functions["products_name"]
            order_id_func = z3_sql.column_functions["orders_id"]
            
            eval_product_name = self._decode_z3_string(model.eval(product_name_func(p_row_var)))
            eval_order_id = model.eval(order_id_func(o_row_var)).as_long()
            eval_total_price = model.eval(create_total_price_expr(p_row_var, o_row_var))
            eval_remaining_stock = model.eval(create_remaining_stock_expr(p_row_var, o_row_var))
            
            print(f"查询结果: 商品名称: {eval_product_name}, 订单ID: {eval_order_id}")
            print(f"订单总价: {eval_total_price}, 剩余库存: {eval_remaining_stock}")
            
            # 验证结果确实满足我们的约束条件
            self.assertGreaterEqual(float(str(eval_total_price)), 2500.0, "订单总价应大于2500")
            self.assertLess(eval_remaining_stock.as_long(), 15, "剩余库存应小于15")
            
            # 进一步验证特定结果
            # Laptop (id=1), Order (id=101, pid=1, q=2, d=0.1)
            # Price: 5000 * 2 * (1-0.1) = 10000 * 0.9 = 9000, Stock: 10 - 2 = 8
            if eval_product_name == "Laptop" and eval_order_id == 101:
                self.assertEqual(str(eval_total_price), "9000")
                self.assertEqual(eval_remaining_stock.as_long(), 8)
                
            # 检查所有可能出现的结果都满足我们的约束条件
            self.assertGreaterEqual(float(str(eval_total_price)), 2500.0)
            self.assertLess(eval_remaining_stock.as_long(), 15)

    def test_case5_expression_in_select_and_constraint(self):
        """测试案例5: 计算表达式同时出现在SELECT字段和约束条件中
        
        对应SQL:
        -- 单一SQL查询，计算表达式同时用于SELECT和WHERE
        SELECT 
            p.name,
            o.id,
            p.price * o.quantity * (1 - o.discount) AS total_price
        FROM 
            products p 
            JOIN orders o ON p.id = o.product_id
        WHERE 
            p.price * o.quantity * (1 - o.discount) > 3000;
        """
        print("\n运行测试: test_case5_expression_in_select_and_constraint")
        print("-" * 50)
        z3_sql = SqlToZ3()
        products, orders = self._create_and_populate_tables(z3_sql)
        
        print("对应SQL: SELECT p.name, o.id, p.price * o.quantity * (1 - o.discount) AS total_price FROM products p JOIN orders o ON p.id = o.product_id WHERE p.price * o.quantity * (1 - o.discount) > 3000")
        
        # 创建JOIN条件
        join_condition_tuple = products.id == orders.product_id
        z3_sql.add(join_condition_tuple)
        
        # 获取JOIN操作产生的行变量
        p_row_var = join_condition_tuple[0]
        o_row_var = join_condition_tuple[2]
        
        # 定义订单总价计算表达式
        def create_total_price_expr(product_row_expr, order_row_expr):
            return z3_sql.column_functions["products_price"](product_row_expr) * \
                   z3_sql.column_functions["orders_quantity"](order_row_expr) * \
                   (RealVal(1.0) - z3_sql.column_functions["orders_discount"](order_row_expr))
        
        # 关键区别：使用相同的表达式函数同时作为选择字段和约束条件
        # 1. 添加约束：总价大于3000
        z3_sql.add(create_total_price_expr(p_row_var, o_row_var) > RealVal(3000.0))
        
        # 检查所有约束是否可满足
        check_res = z3_sql.check()
        self.assertEqual(check_res, sat, "订单总价大于3000的约束应有解")
        
        if check_res == sat:
            model = z3_sql.get_model()
            
            # 从模型中提取结果
            product_name_func = z3_sql.column_functions["products_name"]
            order_id_func = z3_sql.column_functions["orders_id"]
            
            # 获取行值
            product_row_id = model.eval(p_row_var)
            order_row_id = model.eval(o_row_var)
            
            # 2. 使用相同的表达式函数计算SELECT字段的值
            eval_product_name = self._decode_z3_string(model.eval(product_name_func(product_row_id)))
            eval_order_id = model.eval(order_id_func(order_row_id)).as_long()
            eval_total_price = model.eval(create_total_price_expr(product_row_id, order_row_id))
            
            print(f"查询结果: 商品名称: {eval_product_name}, 订单ID: {eval_order_id}, 订单总价: {eval_total_price}")
            
            # 验证结果确实满足总价大于3000的条件
            self.assertGreater(float(str(eval_total_price)), 3000.0, "订单总价应大于3000")
            
            # 检查特定结果
            # Laptop (id=1), Order (id=101, pid=1, q=2, d=0.1)
            # Price: 5000 * 2 * (1-0.1) = 10000 * 0.9 = 9000
            if eval_product_name == "Laptop" and eval_order_id == 101:
                self.assertEqual(str(eval_total_price), "9000")
                
            # 或者 Phone (id=2), Order (id=102, pid=2, q=1, d=0.05)
            # Price: 3000 * 1 * (1-0.05) = 3000 * 0.95 = 2850 (不满足 > 3000)
            if eval_product_name == "Phone" and eval_order_id == 102:
                self.fail("Phone订单总价为2850，不满足大于3000的条件，不应被选中")
        else:
            self.fail("订单总价大于3000的约束应有解，但得到了unsat")

if __name__ == "__main__":
    unittest.main() 