"""
复杂查询示例
展示SQL到Z3约束转换的复杂查询用法
"""

import sys
import unittest # 导入unittest模块
import codecs # 导入codecs模块
from pathlib import Path
from z3 import *

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入SQL到Z3模块
from new_model.core import SqlToZ3

class TestComplexExample(unittest.TestCase): # 创建测试类

    def setUp(self):
        """每个测试方法运行前执行，用于初始化共享资源"""
        self.z3_sql = SqlToZ3()
        # print("Setup for a new test method called.") # Debugging print

    def _create_and_populate_tables(self):
        """辅助方法：创建并填充customers和orders表"""
        customers = self.z3_sql.create_table("customers", {
            "id": IntSort(),
            "name": StringSort(),
            "age": IntSort(),
            "region": StringSort()
        })
        
        orders = self.z3_sql.create_table("orders", {
            "id": IntSort(),
            "customer_id": IntSort(),
            "amount": IntSort(),
            "region": StringSort(),
            "created_at": StringSort()
        })
        
        # 插入测试数据
        customers.insert({"id": 1, "name": "张三", "age": 30, "region": "North"})
        customers.insert({"id": 2, "name": "李四", "age": 25, "region": "South"})
        customers.insert({"id": 3, "name": "王五", "age": 40, "region": "East"})
        
        orders.insert({"id": 101, "customer_id": 1, "amount": 200, "region": "North", "created_at": "2023-01-01"})
        orders.insert({"id": 102, "customer_id": 1, "amount": 300, "region": "North", "created_at": "2023-02-01"})
        orders.insert({"id": 103, "customer_id": 2, "amount": 150, "region": "South", "created_at": "2023-01-15"})
        orders.insert({"id": 104, "customer_id": 1, "amount": 250, "region": "East", "created_at": "2023-03-01"})
        return customers, orders

    def test_case1_satisfiable_multicondition_join(self):
        """测试案例1: 可满足的查询 - 客户与其同一地区订单的关联 (多条件JOIN)"""
        print("\n运行测试: test_case1_satisfiable_multicondition_join")
        print("-" * 50)
        customers, orders = self._create_and_populate_tables()
        
        results_table = self.z3_sql.create_table("results", {
            "customer_name": StringSort(),
            "order_amount": IntSort(),
            "region": StringSort()
        })
        
        print("对应SQL: SELECT c.name, o.amount, c.region FROM customers c JOIN orders o ON c.id = o.customer_id AND c.region = o.region")
        
        join_result_1 = customers.id == orders.customer_id
        customer_row_var = join_result_1[0]
        order_row_var = join_result_1[2]
        
        join_result_2 = customers.region == orders.region
        customer_row_var2 = join_result_2[0]
        order_row_var2 = join_result_2[2]
        
        self.z3_sql.add(customer_row_var == customer_row_var2)
        self.z3_sql.add(order_row_var == order_row_var2)
        
        check_res = self.z3_sql.check()
        self.assertEqual(check_res, sat, "案例1: 求解器应返回 sat")

        if check_res == sat:
            model = self.z3_sql.get_model()
            customer_row_val = model.eval(customer_row_var).as_long()
            order_row_val = model.eval(order_row_var).as_long()
            
            customer_id_func = self.z3_sql.column_functions["customers_id"]
            customer_name_func = self.z3_sql.column_functions["customers_name"]
            customer_region_func = self.z3_sql.column_functions["customers_region"]
            order_customer_id_func = self.z3_sql.column_functions["orders_customer_id"]
            order_region_func = self.z3_sql.column_functions["orders_region"]
            order_amount_func = self.z3_sql.column_functions["orders_amount"]

            eval_c_id = model.eval(customer_id_func(customer_row_val))
            eval_c_name_raw = model.eval(customer_name_func(customer_row_val)).as_string()
            eval_c_name_processed = eval_c_name_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_name = codecs.decode(eval_c_name_processed.encode('utf-8'), 'unicode_escape')
            
            eval_c_region_raw = model.eval(customer_region_func(customer_row_val)).as_string()
            eval_c_region_processed = eval_c_region_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_region = codecs.decode(eval_c_region_processed.encode('utf-8'), 'unicode_escape')
            
            eval_o_customer_id = model.eval(order_customer_id_func(order_row_val))
            eval_o_region_raw = model.eval(order_region_func(order_row_val)).as_string()
            eval_o_region_processed = eval_o_region_raw.replace('\\u{', '\\u').replace('}', '')
            eval_o_region = codecs.decode(eval_o_region_processed.encode('utf-8'), 'unicode_escape')
            eval_o_amount = model.eval(order_amount_func(order_row_val))

            print(f"DEBUG: eval_c_name type: {type(eval_c_name)}, repr: {repr(eval_c_name)}")
            print(f"DEBUG: eval_c_region type: {type(eval_c_region)}, repr: {repr(eval_c_region)}")
            print(f"DEBUG: eval_o_region type: {type(eval_o_region)}, repr: {repr(eval_o_region)}")

            print(f"查询结果: 客户名='{eval_c_name}', 客户地区='{eval_c_region}', 订单地区='{eval_o_region}', 订单金额={eval_o_amount}")
            
            # 断言JOIN条件满足
            self.assertEqual(eval_c_id, eval_o_customer_id, "JOIN条件1 (客户ID) 应满足")
            self.assertEqual(eval_c_region, eval_o_region, "JOIN条件2 (地区) 应满足")

            # 验证找到的解是否是数据中的有效组合
            # 原始数据中满足 c.id = o.customer_id AND c.region = o.region 的组合有：
            # 1. 张三 (id=1, region=North) -> 订单101 (amount=200, region=North)
            # 2. 张三 (id=1, region=North) -> 订单102 (amount=300, region=North)
            # 3. 李四 (id=2, region=South) -> 订单103 (amount=150, region=South)
            
            valid_solutions = [
                {"name": "张三", "c_region": "North", "o_region": "North", "o_amount": 200, "c_id": 1, "o_cid": 1},
                {"name": "张三", "c_region": "North", "o_region": "North", "o_amount": 300, "c_id": 1, "o_cid": 1},
                {"name": "李四", "c_region": "South", "o_region": "South", "o_amount": 150, "c_id": 2, "o_cid": 2}
            ]
            
            current_solution = {
                "name": eval_c_name,
                "c_region": eval_c_region,
                "o_region": eval_o_region,
                "o_amount": eval_o_amount.as_long(),
                "c_id": eval_c_id.as_long(),
                "o_cid": eval_o_customer_id.as_long()
            }
            
            self.assertIn(current_solution, valid_solutions, f"找到的解 {current_solution} 不是预期的有效解之一。")

    def test_case1_2_single_condition_join(self):
        """测试案例1.2: 对比单条件JOIN - 只匹配客户ID"""
        print("\n运行测试: test_case1_2_single_condition_join")
        print("-" * 50)
        customers, orders = self._create_and_populate_tables()
        print("对应SQL: SELECT c.name, o.amount, c.region, o.region FROM customers c JOIN orders o ON c.id = o.customer_id")

        join_result_3 = customers.id == orders.customer_id
        customer_row_var = join_result_3[0]
        order_row_var = join_result_3[2]

        check_res = self.z3_sql.check()
        self.assertEqual(check_res, sat, "案例1.2: 求解器应返回 sat")

        if check_res == sat:
            model = self.z3_sql.get_model()
            customer_row_val = model.eval(customer_row_var).as_long()
            order_row_val = model.eval(order_row_var).as_long()

            customer_id_func = self.z3_sql.column_functions["customers_id"]
            customer_name_func = self.z3_sql.column_functions["customers_name"]
            customer_region_func = self.z3_sql.column_functions["customers_region"]
            order_customer_id_func = self.z3_sql.column_functions["orders_customer_id"]
            order_region_func = self.z3_sql.column_functions["orders_region"]

            eval_c_id = model.eval(customer_id_func(customer_row_val))
            eval_o_customer_id = model.eval(order_customer_id_func(order_row_val))
            self.assertEqual(eval_c_id, eval_o_customer_id, "单条件JOIN (客户ID) 应满足")
            
            eval_c_name_raw = model.eval(customer_name_func(customer_row_val)).as_string()
            eval_c_name_processed = eval_c_name_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_name = codecs.decode(eval_c_name_processed.encode('utf-8'), 'unicode_escape')
            
            eval_c_region_raw = model.eval(customer_region_func(customer_row_val)).as_string()
            eval_c_region_processed = eval_c_region_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_region = codecs.decode(eval_c_region_processed.encode('utf-8'), 'unicode_escape')
            
            eval_o_region_raw = model.eval(order_region_func(order_row_val)).as_string()
            eval_o_region_processed = eval_o_region_raw.replace('\\u{', '\\u').replace('}', '')
            eval_o_region = codecs.decode(eval_o_region_processed.encode('utf-8'), 'unicode_escape')
            
            print(f"DEBUG (case1.2): eval_c_name type: {type(eval_c_name)}, repr: {repr(eval_c_name)}")
            print(f"DEBUG (case1.2): eval_c_region type: {type(eval_c_region)}, repr: {repr(eval_c_region)}")
            print(f"DEBUG (case1.2): eval_o_region type: {type(eval_o_region)}, repr: {repr(eval_o_region)}")
            
            print(f"查询结果: 客户名='{eval_c_name}', 客户地区='{eval_c_region}', 订单地区='{eval_o_region}'")
            
            # 验证是否可能地区不匹配 (例如客户1的订单104)
            if eval_c_id.as_long() == 1 and eval_c_region == "North" and eval_o_region == "East":
                print("找到一个客户ID匹配但地区不匹配的例子，符合单条件JOIN的预期。")
            elif eval_c_id.as_long() == 1 and eval_c_region == "North" and eval_o_region == "North":
                 print("找到一个客户ID和地区都匹配的例子。")
            elif eval_c_id.as_long() == 2 and eval_c_region == "South" and eval_o_region == "South":
                 print("找到一个客户ID和地区都匹配的例子。")
            # 可以添加更多验证，确保找到的解是原始数据中有效的单ID JOIN

    def test_case1_3_multicondition_join_with_helper(self):
        """测试案例1.3: 使用join_condition_and助手方法实现多条件JOIN"""
        print("\n运行测试: test_case1_3_multicondition_join_with_helper")
        print("-" * 50)
        customers, orders = self._create_and_populate_tables()
        
        print("对应SQL: SELECT c.name, o.amount, c.region FROM customers c JOIN orders o ON c.id = o.customer_id AND c.region = o.region")
        
        # 使用新的join_condition_and方法一次性添加多个JOIN条件，并获取统一的行变量
        join_vars = self.z3_sql.join_condition_and(
            customers.id == orders.customer_id,
            customers.region == orders.region
        )
        
        self.assertIsNotNone(join_vars, "join_condition_and方法应成功执行并返回行变量")
        
        # 使用join_condition_and返回的统一行变量
        customer_row_var = join_vars[0]
        order_row_var = join_vars[2]
        
        check_res = self.z3_sql.check()
        self.assertEqual(check_res, sat, "案例1.3: 求解器应返回 sat")

        if check_res == sat:
            model = self.z3_sql.get_model()
            customer_row_val = model.eval(customer_row_var).as_long()
            order_row_val = model.eval(order_row_var).as_long()
            
            customer_id_func = self.z3_sql.column_functions["customers_id"]
            customer_name_func = self.z3_sql.column_functions["customers_name"]
            customer_region_func = self.z3_sql.column_functions["customers_region"]
            order_customer_id_func = self.z3_sql.column_functions["orders_customer_id"]
            order_region_func = self.z3_sql.column_functions["orders_region"]
            order_amount_func = self.z3_sql.column_functions["orders_amount"]

            eval_c_id = model.eval(customer_id_func(customer_row_val))
            eval_c_name_raw = model.eval(customer_name_func(customer_row_val)).as_string()
            eval_c_name_processed = eval_c_name_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_name = codecs.decode(eval_c_name_processed.encode('utf-8'), 'unicode_escape')
            
            eval_c_region_raw = model.eval(customer_region_func(customer_row_val)).as_string()
            eval_c_region_processed = eval_c_region_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_region = codecs.decode(eval_c_region_processed.encode('utf-8'), 'unicode_escape')
            
            eval_o_customer_id = model.eval(order_customer_id_func(order_row_val))
            eval_o_region_raw = model.eval(order_region_func(order_row_val)).as_string()
            eval_o_region_processed = eval_o_region_raw.replace('\\u{', '\\u').replace('}', '')
            eval_o_region = codecs.decode(eval_o_region_processed.encode('utf-8'), 'unicode_escape')
            eval_o_amount = model.eval(order_amount_func(order_row_val))

            print(f"DEBUG: eval_c_name type: {type(eval_c_name)}, repr: {repr(eval_c_name)}")
            print(f"DEBUG: eval_c_region type: {type(eval_c_region)}, repr: {repr(eval_c_region)}")
            print(f"DEBUG: eval_o_region type: {type(eval_o_region)}, repr: {repr(eval_o_region)}")

            print(f"查询结果: 客户名='{eval_c_name}', 客户地区='{eval_c_region}', 订单地区='{eval_o_region}', 订单金额={eval_o_amount}")
            
            # 断言JOIN条件满足
            self.assertEqual(eval_c_id, eval_o_customer_id, "JOIN条件1 (客户ID) 应满足")
            self.assertEqual(eval_c_region, eval_o_region, "JOIN条件2 (地区) 应满足")

            # 验证找到的解是否是数据中的有效组合
            valid_solutions = [
                {"name": "张三", "c_region": "North", "o_region": "North", "o_amount": 200, "c_id": 1, "o_cid": 1},
                {"name": "张三", "c_region": "North", "o_region": "North", "o_amount": 300, "c_id": 1, "o_cid": 1},
                {"name": "李四", "c_region": "South", "o_region": "South", "o_amount": 150, "c_id": 2, "o_cid": 2}
            ]
            
            current_solution = {
                "name": eval_c_name,
                "c_region": eval_c_region,
                "o_region": eval_o_region,
                "o_amount": eval_o_amount.as_long(),
                "c_id": eval_c_id.as_long(),
                "o_cid": eval_o_customer_id.as_long()
            }
            
            self.assertIn(current_solution, valid_solutions, f"找到的解 {current_solution} 不是预期的有效解之一。")
            print("测试成功: 使用join_condition_and方法实现了多条件JOIN，结果符合预期。")

    def test_case2_unsatisfiable_query(self):
        """测试案例2: 不可满足的查询 - 获取ID为4的客户的订单"""
        print("\n运行测试: test_case2_unsatisfiable_query")
        print("-" * 50)
        customers, orders = self._create_and_populate_tables()
        print("对应SQL: SELECT c.name, o.amount FROM customers c JOIN orders o ON c.id = o.customer_id AND c.region = o.region WHERE c.id = 4")

        join_result_4 = customers.id == orders.customer_id
        customer_row_var = join_result_4[0]
        # order_row_var = join_result_4[2] # order_row_var 在这个特定查询中不直接用于最终断言
        
        customers.region == orders.region # 添加第二个JOIN条件，结果不直接使用，但会加入求解器
        
        self.z3_sql.add(self.z3_sql.column_functions["customers_id"](customer_row_var) == 4)
        
        check_res = self.z3_sql.check()
        self.assertEqual(check_res, unsat, "案例2: 求解器应返回 unsat，因为ID为4的客户不存在")
        if check_res == sat:
            self.fail("案例2预期unsat，但得到了sat")
        else:
            print("查询没有结果（符合预期）")

    def test_case3_multi_table_join_with_filter(self):
        """测试案例3: 复杂的多表JOIN - 客户、订单和产品，带筛选"""
        print("\n运行测试: test_case3_multi_table_join_with_filter")
        print("-" * 50)
        
        # self.z3_sql 实例在 setUp 中已创建，这里不需要重新创建 SqlToZ3()
        # 也不需要调用 _create_and_populate_tables()，因为表结构和数据特定于此测试

        # 1. 创建 customers 表并填充数据
        customers = self.z3_sql.create_table("customers", {
            "id": IntSort(),
            "name": StringSort(),
            "age": IntSort(),
            "region": StringSort()
        })
        customers.insert({"id": 1, "name": "张三", "age": 30, "region": "North"})
        customers.insert({"id": 2, "name": "李四", "age": 25, "region": "South"})
        customers.insert({"id": 3, "name": "王五", "age": 40, "region": "East"})

        # 2. 创建 orders 表（包含 product_id）并填充数据
        orders = self.z3_sql.create_table("orders", {
            "id": IntSort(),
            "customer_id": IntSort(),
            "product_id": IntSort(), 
            "amount": IntSort(),
            "region": StringSort(),
            "created_at": StringSort()
        })
        orders.insert({"id": 101, "customer_id": 1, "product_id": 201, "amount": 1, "region": "North", "created_at": "2023-01-01"})
        orders.insert({"id": 102, "customer_id": 1, "product_id": 202, "amount": 2, "region": "North", "created_at": "2023-02-01"})
        orders.insert({"id": 103, "customer_id": 2, "product_id": 203, "amount": 5, "region": "South", "created_at": "2023-01-15"})
        orders.insert({"id": 104, "customer_id": 1, "product_id": 203, "amount": 3, "region": "East", "created_at": "2023-03-01"})

        # 3. 创建 products 表并填充数据
        products = self.z3_sql.create_table("products", {
            "id": IntSort(),
            "name": StringSort(),
            "category": StringSort(),
            "price": IntSort()
        })
        products.insert({"id": 201, "name": "笔记本电脑", "category": "电子产品", "price": 5000})
        products.insert({"id": 202, "name": "智能手机", "category": "电子产品", "price": 3000})
        products.insert({"id": 203, "name": "书籍", "category": "文具", "price": 100})

        print("模拟SQL查询: SELECT c.name, p.name, o.amount * p.price, c.region FROM ... WHERE p.category = '电子产品'")

        join_c_o = customers.id == orders.customer_id
        cust_row_var = join_c_o[0]
        order_row_var_from_co = join_c_o[2]
        
        customers.region == orders.region # 第二个C-O JOIN条件
        
        join_o_p = orders.product_id == products.id
        order_row_var_from_op = join_o_p[0]
        prod_row_var = join_o_p[2]
        
        self.z3_sql.add(order_row_var_from_co == order_row_var_from_op) # 确保orders行变量一致
        
        product_category_func = self.z3_sql.column_functions["products_category"]
        self.z3_sql.add(product_category_func(prod_row_var) == "电子产品")

        check_res = self.z3_sql.check()
        self.assertEqual(check_res, sat, "案例3: 求解器应返回 sat")

        if check_res == sat:
            model = self.z3_sql.get_model()
            cust_row_val = model.eval(cust_row_var).as_long()
            order_row_val = model.eval(order_row_var_from_co).as_long() # 使用统一的order行变量
            prod_row_val = model.eval(prod_row_var).as_long()

            cust_name_func = self.z3_sql.column_functions["customers_name"]
            prod_name_func = self.z3_sql.column_functions["products_name"]
            prod_cat_func = self.z3_sql.column_functions["products_category"]
            order_amount_func = self.z3_sql.column_functions["orders_amount"]
            prod_price_func = self.z3_sql.column_functions["products_price"]
            cust_region_func = self.z3_sql.column_functions["customers_region"]
            order_region_func = self.z3_sql.column_functions["orders_region"]
            cust_id_func = self.z3_sql.column_functions["customers_id"]
            order_cust_id_func = self.z3_sql.column_functions["orders_customer_id"]
            order_prod_id_func = self.z3_sql.column_functions["orders_product_id"]
            prod_id_func = self.z3_sql.column_functions["products_id"]

            eval_c_name_raw = model.eval(cust_name_func(cust_row_val)).as_string()
            eval_c_name_processed = eval_c_name_raw.replace('\\u{', '\\u').replace('}', '')
            eval_c_name = codecs.decode(eval_c_name_processed.encode('utf-8'), 'unicode_escape')
            
            eval_p_name_raw = model.eval(prod_name_func(prod_row_val)).as_string()
            eval_p_name_processed = eval_p_name_raw.replace('\\u{', '\\u').replace('}', '')
            eval_p_name = codecs.decode(eval_p_name_processed.encode('utf-8'), 'unicode_escape')
            
            eval_p_cat_raw = model.eval(prod_cat_func(prod_row_val)).as_string()
            eval_p_cat_processed = eval_p_cat_raw.replace('\\u{', '\\u').replace('}', '')
            eval_p_cat = codecs.decode(eval_p_cat_processed.encode('utf-8'), 'unicode_escape')
            
            print(f"DEBUG (case3): eval_c_name type: {type(eval_c_name)}, repr: {repr(eval_c_name)}")
            print(f"DEBUG (case3): eval_p_name type: {type(eval_p_name)}, repr: {repr(eval_p_name)}")
            print(f"DEBUG (case3): eval_p_cat type: {type(eval_p_cat)}, repr: {repr(eval_p_cat)}")
            
            print(f"查询结果: 客户='{eval_c_name}', 产品='{eval_p_name}', 类别='{eval_p_cat}'")
            
            self.assertEqual(eval_p_cat, "电子产品", "产品类别应为电子产品")

            # 验证JOIN条件
            self.assertEqual(model.eval(cust_id_func(cust_row_val)), model.eval(order_cust_id_func(order_row_val)), "JOIN C.id=O.cid")
            self.assertEqual(model.eval(cust_region_func(cust_row_val)), model.eval(order_region_func(order_row_val)), "JOIN C.region=O.region")
            self.assertEqual(model.eval(order_prod_id_func(order_row_val)), model.eval(prod_id_func(prod_row_val)), "JOIN O.pid=P.id")

            # 基于数据，预期结果是 张三(id=1) 购买 笔记本电脑(id=201) 或 智能手机(id=202)
            # 因为他们的地区都是North且产品是电子产品
            if eval_c_name == "张三":
                self.assertIn(eval_p_name, ["笔记本电脑", "智能手机"], "张三应购买电子产品")
                amount = model.eval(order_amount_func(order_row_val)).as_long()
                price = model.eval(prod_price_func(prod_row_val)).as_long()
                total_value = amount * price
                if eval_p_name == "笔记本电脑":
                    self.assertEqual(amount, 1)
                    self.assertEqual(price, 5000)
                    self.assertEqual(total_value, 5000)
                elif eval_p_name == "智能手机":
                    self.assertEqual(amount, 2)
                    self.assertEqual(price, 3000)
                    self.assertEqual(total_value, 6000)
            else:
                # 此测试数据和条件下，不应有其他客户满足条件
                self.fail(f"不应找到其他客户，但找到了: {eval_c_name}")

if __name__ == "__main__":
    unittest.main() # 修改主执行块 