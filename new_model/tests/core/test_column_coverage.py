"""
测试Column类的覆盖率
主要针对当前未覆盖的方法进行测试
"""

import pytest
from z3 import Int, String, Real, BoolVal, IntVal, RealVal, And, Or, Not, IntSort, RealSort, StringSort, BoolSort
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from new_model.core.solver import SqlToZ3
from new_model.core.expression import Expression
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint

class TestColumnCoverage:
    """测试Column类中未覆盖的方法"""
    
    def setup_method(self):
        """每个测试方法开始前的设置"""
        self.solver = SqlToZ3()
        
        # 创建测试表和列
        self.table1 = self.solver.create_table("table1", {
            "id": IntSort(),
            "value": RealSort(),
            "name": StringSort(),
            "price": RealSort(),
            "is_active": BoolSort()
        })
        
        self.table2 = self.solver.create_table("table2", {
            "id": IntSort(),
            "quantity": IntSort(),
            "category": StringSort(),
            "date_col": StringSort()
        })
    
    def test_column_init(self):
        """测试列初始化"""
        col = Column("table1", "test_col", IntSort(), self.solver)
        assert col.name == "test_col"
        assert col.table_name == "table1"
        assert col.description == "table1.test_col"
    
    def test_column_call(self):
        """测试列调用"""
        # 测试调用列时返回Z3表达式
        result = self.table1.id("test_row")
        assert "table1_id" in str(result)
        assert "test_row" in str(result)
        
        # 测试调用列时使用默认行变量
        result = self.table1.id("default_row")
        assert "table1_id" in str(result)
        assert "default_row" in str(result)
    
    def test_column_operators(self):
        """测试列的操作符重载"""
        # 加法
        expr = self.table1.value + 10
        assert isinstance(expr, Expression)
        assert "table1.value + 10" in expr.description
        
        expr = self.table1.value + self.table1.price
        assert "table1.value + table1.price" in expr.description
        
        # 减法
        expr = self.table1.value - 5
        assert "table1.value - 5" in expr.description
        
        # 乘法
        expr = self.table1.value * 2
        assert "table1.value * 2" in expr.description
        
        # 除法
        expr = self.table1.value / 2
        assert "table1.value / 2" in expr.description
        
        # 反向操作
        expr = 10 + self.table1.value
        assert "10 + table1.value" in expr.description
        
        expr = 10 - self.table1.value
        assert "10 - table1.value" in expr.description
        
        expr = 10 * self.table1.value
        assert "10 * table1.value" in expr.description
        
        expr = 10 / self.table1.value
        assert "10 / table1.value" in expr.description
    
    def test_column_comparison(self):
        """测试列的比较操作符"""
        # 等于
        expr = self.table1.id == 1
        assert "table1.id == 1" in expr.description
        
        # 列与列比较返回的是行变量元组和表名的格式 (var1, table1_name, var2, table2_name)
        join_vars_and_tables = self.table1.id == self.table2.id
        assert isinstance(join_vars_and_tables, tuple)
        assert len(join_vars_and_tables) == 4
        
        table1_row_var, table1_name, table2_row_var, table2_name = join_vars_and_tables
        assert str(table1_row_var).startswith("table1_row_")
        assert table1_name == "table1"
        assert str(table2_row_var).startswith("table2_row_")
        assert table2_name == "table2"
        
        # 测试其他比较操作符返回ColumnConstraint
        expr = self.table1.id != 1
        assert "table1.id != 1" in expr.description
        
        expr = self.table1.id > 1
        assert "table1.id > 1" in expr.description
        
        expr = self.table1.id < 1
        assert "table1.id < 1" in expr.description
        
        expr = self.table1.id >= 1
        assert "table1.id >= 1" in expr.description
        
        expr = self.table1.id <= 1
        assert "table1.id <= 1" in expr.description
    
    def test_column_logical(self):
        """测试列的逻辑操作符"""
        # 逻辑与
        expr = (self.table1.id > 5) & (self.table1.value < 100)
        assert "table1.id > 5 & table1.value < 100" in expr.description
        
        # 逻辑或
        expr = (self.table1.id > 5) | (self.table1.value < 100)
        assert "table1.id > 5 | table1.value < 100" in expr.description
        
        # 逻辑非
        expr = ~(self.table1.id > 5)
        assert "~(table1.id > 5)" in expr.description
    
    def test_column_constraints(self):
        """测试列约束方法"""
        # 注意：我们暂时跳过尚未实现的功能测试
        # 后续将会添加IS NULL, IS NOT NULL, BETWEEN, IN, NOT IN等功能
        
        # 测试BETWEEN
        # 直接构造ColumnConstraint对象绕过调用Column的方法
        # 将来实现完整后，可以改为调用Column的方法
        col = self.table1.id
        constraint = ColumnConstraint(col, "BETWEEN", (1, 10), self.solver)
        assert constraint.column == col
        assert constraint.operator == "BETWEEN"
        assert constraint.value == (1, 10)
        
        # 测试IN
        values = [1, 2, 3]
        constraint = ColumnConstraint(col, "IN", values, self.solver)
        assert constraint.column == col
        assert constraint.operator == "IN"
        assert constraint.value == values
        
        # 测试NOT IN
        constraint = ColumnConstraint(col, "NOT IN", values, self.solver)
        assert constraint.column == col
        assert constraint.operator == "NOT IN"
        assert constraint.value == values
    
    def test_column_string_functions(self):
        """测试字符串相关的列方法"""
        # LIKE
        expr = self.table1['name'].like("A%")
        assert "LIKE" in expr.description
        assert "A%" in expr.description

        # CONTAINS
        expr = self.table1['name'].contains("test")
        assert "CONTAINS" in expr.description
        assert "test" in expr.description

        # STARTSWITH
        expr = self.table1['name'].startswith("B")
        assert "STARTSWITH" in expr.description
        assert "B" in expr.description

        # ENDSWITH
        expr = self.table1['name'].endswith("C")
        assert "ENDSWITH" in expr.description
        assert "C" in expr.description

        # SUBSTRING
        expr = self.table1['name'].substring(1, 3)
        assert "SUBSTRING" in expr.description
        assert "1" in expr.description
        assert "3" in expr.description

        # CONCAT
        expr = self.table1['name'].concat(self.table1['name'])
        assert "table1.name" in expr.description
        assert "||" in expr.description  # 检查连接操作符

        # UPPER
        expr = self.table1['name'].upper()
        assert "UPPER" in expr.description
        assert "table1.name" in expr.description

        # LOWER
        expr = self.table1['name'].lower()
        assert "LOWER" in expr.description
        assert "table1.name" in expr.description

        # REPLACE
        expr = self.table1['name'].replace("a", "b")
        assert "REPLACE" in expr.description
        assert "a" in expr.description
        assert "b" in expr.description

        # TRIM
        expr = self.table1['name'].trim()
        assert "TRIM" in expr.description
        assert "table1.name" in expr.description
    
    def test_column_cast_functions(self):
        """测试类型转换方法"""
        # 转为字符串
        expr = self.table1.id.cast_to_string()
        assert "ToString(table1.id)" in expr.description
        
        # 转为整数
        expr = self.table1['name'].cast_to_int()
        assert "ToInt(table1.name)" in expr.description
        
        # 转为实数
        expr = self.table1.id.cast_to_real()
        assert "ToReal(table1.id)" in expr.description
        
        # 转为布尔值
        expr = self.table1.id.cast_to_bool()
        assert "ToBool(table1.id)" in expr.description
    
    def test_column_date_functions(self):
        """测试日期相关方法"""
        # 转为日期
        expr = self.table2.date_col.to_date("%Y-%m-%d")
        assert "ToDate(table2.date_col, %Y-%m-%d)" in expr.description
        
        # 提取年
        expr = self.table2.date_col.to_date("%Y-%m-%d").extract_year()
        assert "ExtractYear(ToDate(table2.date_col, %Y-%m-%d))" in expr.description
        
        # 提取月
        expr = self.table2.date_col.to_date("%Y-%m-%d").extract_month()
        assert "ExtractMonth(ToDate(table2.date_col, %Y-%m-%d))" in expr.description
        
        # 提取日
        expr = self.table2.date_col.to_date("%Y-%m-%d").extract_day()
        assert "ExtractDay(ToDate(table2.date_col, %Y-%m-%d))" in expr.description
    
    def test_column_repr(self):
        """测试列的__repr__方法"""
        assert "table1.id" in repr(self.table1.id)
    
    def test_column_constraint_combination(self):
        """测试列约束的组合"""
        constraint1 = self.table1.id.is_not_null()
        constraint2 = self.table1.value.between(10, 20)
        
        # 组合约束 - 与
        combined = constraint1 & constraint2
        assert isinstance(combined, ColumnConstraint)
        assert "table1.id IS NOT NULL & table1.value BETWEEN 10 AND 20" in combined.description
        
        # 组合约束 - 或
        combined = constraint1 | constraint2
        assert "table1.id IS NOT NULL | table1.value BETWEEN 10 AND 20" in combined.description
        
        # 组合约束 - 非
        combined = ~constraint1
        assert "~(table1.id IS NOT NULL)" in combined.description 