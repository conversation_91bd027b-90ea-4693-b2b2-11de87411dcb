import unittest
from unittest.mock import patch, MagicMock
import logging
from z3 import (
    Int, String, Bool, Real, Context, IntSort, StringSort, BoolSort, RealSort, 
    FuncDeclRef, ExprRef, IntVal, RealVal, StringVal, BoolRef, BoolVal, 
    Z3Exception, And as Z3And, Or as Z3Or, Not as Z3Not, Implies as Z3Implies, 
    If as Z3If, unsat, sat, is_bool, is_expr, unknown
)
from typing import Optional, Any, Dict, List

from new_model.core.solver import SqlToZ3
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint
from new_model.core.expression import Expression

# 配置日志级别
logging.basicConfig(level=logging.WARNING, format='%(levelname)s - %(filename)s:%(lineno)d - %(message)s')

class TestTableSimplified(unittest.TestCase):
    """针对table.py中已实现功能的简化测试"""
    
    def setUp(self):
        """测试前设置环境"""
        self.solver = SqlToZ3(log_level="WARNING")
        
        # 创建测试表
        self.student_table = self.solver.create_table(
            "students", 
            {
                "id": IntSort(),
                "name": StringSort(),
                "age": IntSort(),
                "gpa": RealSort(),
                "is_active": BoolSort()
            }
        )
        
        # 插入一些测试数据
        self.student_table.insert({"id": 1, "name": "Alice", "age": 20, "gpa": 3.5, "is_active": True})
        self.student_table.insert({"id": 2, "name": "Bob", "age": 22, "gpa": 3.2, "is_active": True})
    
    def test_table_properties(self):
        """测试表的基本属性"""
        # 测试表名
        self.assertEqual(self.student_table.name, "students")
        
        # 测试列数量
        self.assertEqual(len(self.student_table.columns_with_types), 5)
        
        # 测试行数量
        self.assertEqual(len(self.student_table.rows), 2)
    
    def test_table_create_with_different_types(self):
        """测试创建具有不同数据类型的表"""
        # 创建一个包含各种数据类型的表
        complex_table = self.solver.create_table(
            "complex_types", 
            {
                "int_col": IntSort(),
                "real_col": RealSort(),
                "string_col": StringSort(),
                "bool_col": BoolSort()
            }
        )
        
        # 验证表创建成功
        self.assertEqual(complex_table.name, "complex_types")
        self.assertEqual(len(complex_table.columns_with_types), 4)
    
    def test_table_insert_with_constants(self):
        """测试插入常量值"""
        # 创建一个新表
        test_table = self.solver.create_table(
            "test_insert", 
            {
                "id": IntSort(),
                "value": StringSort()
            }
        )
        
        # 插入常量值
        row_id = test_table.insert({"id": 1, "value": "test"})
        
        # 验证插入成功
        self.assertTrue(row_id in test_table.rows)
        self.assertEqual(len(test_table.rows), 1)
    
    def test_table_insert_with_expressions(self):
        """测试使用表达式插入数据"""
        # 获取Column对象
        age_column = self.student_table["age"]
        
        # 创建一个基于现有列的表达式
        age_plus_10 = Expression(lambda row_var: age_column.function(row_var) + 10, 
                               self.solver, 
                               "age + 10")
        
        # 创建一个新表
        derived_table = self.solver.create_table(
            "derived", 
            {
                "id": IntSort(),
                "modified_age": IntSort()
            }
        )
        
        # 使用表达式插入数据
        row_id = derived_table.insert({
            "id": 1,
            "modified_age": age_plus_10
        })
        
        # 验证插入成功
        self.assertTrue(row_id in derived_table.rows)
        self.assertEqual(len(derived_table.rows), 1)
    
    def test_table_get_row_var(self):
        """测试获取表的行变量"""
        # 获取行变量
        row_var = self.student_table.get_row_var()
        
        # 验证行变量类型
        self.assertIsNotNone(row_var)
        self.assertIsInstance(row_var, ExprRef)
    
    def test_table_column_access_by_getattr(self):
        """测试通过属性访问列"""
        # 通过属性访问列
        name_column = self.student_table.name_column if hasattr(self.student_table, "name_column") else self.student_table["name"]
        
        # 验证列对象
        self.assertIsNotNone(name_column)
        self.assertIsInstance(name_column, Column)
        self.assertEqual(name_column.column_name, "name")
    
    def test_table_column_access_by_getitem(self):
        """测试通过下标访问列"""
        # 通过下标访问列
        age_column = self.student_table["age"]
        
        # 验证列对象
        self.assertIsNotNone(age_column)
        self.assertIsInstance(age_column, Column)
        self.assertEqual(age_column.column_name, "age")
    
    def test_table_columns_property(self):
        """测试columns属性"""
        # 获取所有列的字典
        columns_dict = self.student_table.columns
        
        # 验证返回值是字典并包含所有列
        self.assertIsInstance(columns_dict, dict)
        self.assertEqual(len(columns_dict), 5)
        
        # 验证字典中的列对象
        self.assertIn("id", columns_dict)
        self.assertIn("name", columns_dict)
        self.assertIn("age", columns_dict)
        self.assertIn("gpa", columns_dict)
        self.assertIn("is_active", columns_dict)
        
        # 验证字典中的值是Column对象
        self.assertIsInstance(columns_dict["id"], Column)
    
    def test_table_repr(self):
        """测试表的字符串表示"""
        # 获取表的字符串表示
        repr_str = repr(self.student_table)
        
        # 验证字符串中包含关键信息
        self.assertIn("students", repr_str)
        self.assertIn("num_rows=2", repr_str)

if __name__ == "__main__":
    unittest.main() 