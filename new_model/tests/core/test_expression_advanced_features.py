"""
测试Expression类的高级特性
针对未被其他测试覆盖的方法进行测试
"""

import pytest
from z3 import Int, String, Real, BoolVal, IntVal, RealVal, And, Or, Not, IntSort, RealSort, StringSort, BoolSort, is_bool
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from new_model.core.solver import SqlToZ3
from new_model.core.expression import Expression
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint

class TestExpressionAdvancedFeatures:
    """测试Expression类的高级特性"""
    
    def setup_method(self):
        """每个测试方法开始前的设置"""
        self.solver = SqlToZ3()
        
        # 创建测试表和列
        self.table1 = self.solver.create_table("table1", {
            "id": IntSort(),
            "value": RealSort(),
            "name": StringSort(),
            "price": RealSort(),
            "is_active": BoolSort(),
            "count": IntSort()
        })
        
        # 添加一些测试数据
        self.table1.insert({"id": 1, "value": 10.5, "name": "Item 1", "price": 100.0, "is_active": True, "count": 5})
        self.table1.insert({"id": 2, "value": 20.5, "name": "Item 2", "price": 200.0, "is_active": False, "count": 10})
        
        # 获取表的行变量
        self.row_var = self.table1.get_row_var()
    
    def test_expression_init_with_error(self):
        """测试Expression初始化时处理错误情况"""
        # 测试异常处理
        with pytest.raises(TypeError):
            # 传入非函数的lambda表达式
            Expression("not_a_function", self.solver, "错误表达式")
    
    def test_expression_call_with_python_bool(self):
        """测试Expression处理Python布尔值的情况"""
        # 创建一个总是返回Python布尔值的表达式
        expr = Expression(lambda row_var: True, self.solver, "总是True")
        
        # 调用表达式应该返回Z3的BoolVal
        result = expr(self.row_var)
        assert is_bool(result)  # 使用Z3的is_bool函数检查类型
        assert str(result) == "True"
        
        # 创建一个总是返回Python布尔值False的表达式
        expr = Expression(lambda row_var: False, self.solver, "总是False")
        result = expr(self.row_var)
        assert is_bool(result)  # 使用Z3的is_bool函数检查类型
        assert str(result) == "False"
    
    def test_expression_call_with_exception(self):
        """测试Expression调用时出现异常的情况"""
        # 创建一个会抛出异常的表达式
        def raise_exception(row_var):
            raise ValueError("测试异常")
        
        expr = Expression(raise_exception, self.solver, "会抛出异常的表达式")
        
        # 调用应该抛出异常
        with pytest.raises(ValueError):
            expr(self.row_var)
    
    def test_expression_arithmetic_operations(self):
        """测试表达式的算术运算"""
        # 创建基础表达式
        expr1 = Expression(lambda row_var: self.table1.value(row_var) * 2, self.solver, "value * 2")
        expr2 = Expression(lambda row_var: self.table1.count(row_var) + 5, self.solver, "count + 5")
        
        # 加法
        add_expr = expr1 + expr2
        assert isinstance(add_expr, Expression)
        assert "value * 2 + count + 5" in add_expr.description
        
        # 减法
        sub_expr = expr1 - expr2
        assert "value * 2 - (count + 5)" in sub_expr.description
        
        # 乘法
        mul_expr = expr1 * expr2
        assert "(value * 2 * count + 5)" in mul_expr.description
        
        # 除法
        div_expr = expr1 / expr2
        assert "(value * 2 / count + 5)" in div_expr.description
    
    def test_expression_reverse_operations(self):
        """测试表达式的反向运算"""
        # 创建基础表达式
        expr = Expression(lambda row_var: self.table1.value(row_var) * 2, self.solver, "value * 2")
        
        # 反向加法
        r_add_expr = 10 + expr
        assert "10 + value * 2" in r_add_expr.description
        
        # 反向减法
        r_sub_expr = 10 - expr
        assert "10 - (value * 2)" in r_sub_expr.description
        
        # 反向乘法
        r_mul_expr = 10 * expr
        assert "(10 * value * 2)" in r_mul_expr.description
        
        # 反向除法
        r_div_expr = 10 / expr
        assert "(10 / value * 2)" in r_div_expr.description
    
    def test_expression_comparison_operations(self):
        """测试表达式的比较运算"""
        # 创建基础表达式
        expr1 = Expression(lambda row_var: self.table1.value(row_var) * 2, self.solver, "value * 2")
        expr2 = Expression(lambda row_var: self.table1.count(row_var) + 5, self.solver, "count + 5")
        
        # 等于
        eq_expr = expr1 == expr2
        assert "value * 2 == count + 5" in eq_expr.description
        
        # 不等于
        ne_expr = expr1 != expr2
        assert "value * 2 != count + 5" in ne_expr.description
        
        # 大于
        gt_expr = expr1 > expr2
        assert "value * 2 > count + 5" in gt_expr.description
        
        # 小于
        lt_expr = expr1 < expr2
        assert "value * 2 < count + 5" in lt_expr.description
        
        # 大于等于
        ge_expr = expr1 >= expr2
        assert "value * 2 >= count + 5" in ge_expr.description
        
        # 小于等于
        le_expr = expr1 <= expr2
        assert "value * 2 <= count + 5" in le_expr.description
        
        # 与Python数值比较
        eq_expr = expr1 == 10
        assert "value * 2 == 10" in eq_expr.description
    
    def test_expression_logical_operations(self):
        """测试表达式的逻辑运算"""
        # 创建布尔表达式
        expr1 = Expression(lambda row_var: self.table1.value(row_var) > 15, self.solver, "value > 15")
        expr2 = Expression(lambda row_var: self.table1.count(row_var) < 8, self.solver, "count < 8")
        
        # 逻辑与
        and_expr = expr1 & expr2
        assert "value > 15 & count < 8" in and_expr.description
        
        # 逻辑或
        or_expr = expr1 | expr2
        assert "value > 15 | count < 8" in or_expr.description
        
        # 逻辑非
        not_expr = ~expr1
        assert "~(value > 15)" in not_expr.description
        
        # 与Python布尔值操作
        and_expr = expr1 & True
        assert "value > 15 & True" in and_expr.description
        
        or_expr = expr1 | False
        assert "value > 15 | False" in or_expr.description
    
    def test_expression_contains_operation(self):
        """测试表达式的包含操作"""
        # 创建字符串表达式
        expr = Expression(lambda row_var: self.table1.name(row_var), self.solver, "name")
        
        # 包含测试
        contains_expr = expr.contains("Item")
        assert "name CONTAINS Item" in contains_expr.description
    
    def test_expression_repr(self):
        """测试表达式的字符串表示"""
        # 创建表达式
        expr = Expression(lambda row_var: self.table1.value(row_var) * 2, self.solver, "value * 2")
        
        # 检查 __repr__
        assert "Expression(value * 2)" in repr(expr)
    
    def test_nested_expressions(self):
        """测试嵌套表达式处理"""
        # 创建多层嵌套表达式
        expr1 = self.table1.value + 10
        expr2 = self.table1.count * 2
        expr3 = expr1 + expr2  # (value + 10) + (count * 2)
        expr4 = expr3 * 3      # ((value + 10) + (count * 2)) * 3
        expr5 = expr4 - 5      # (((value + 10) + (count * 2)) * 3) - 5
        
        # 检查描述是否正确保留了表达式结构
        assert "value" in expr5.description
        assert "count" in expr5.description
        assert "+" in expr5.description
        assert "*" in expr5.description
        assert "-" in expr5.description
        
        # 检查可以正确调用
        result = expr5(self.row_var)
        assert "table1_value" in str(result)
        assert "table1_count" in str(result)
    
    def test_expression_type_handling(self):
        """测试表达式处理不同类型的能力"""
        # 整数表达式
        int_expr = Expression(lambda row_var: self.table1.id(row_var) + 1, self.solver, "id + 1")
        result = int_expr(self.row_var)
        assert "table1_id" in str(result)
        assert "+" in str(result)
        
        # 浮点数表达式
        real_expr = Expression(lambda row_var: self.table1.value(row_var) * 1.5, self.solver, "value * 1.5")
        result = real_expr(self.row_var)
        assert "table1_value" in str(result)
        assert "*" in str(result)
        
        # 字符串表达式 - 修复: self.table1.name 可能是一个字符串属性而不是函数，修改为使用table对象的 __getitem__ 方法
        str_expr = Expression(lambda row_var: self.table1["name"](row_var), self.solver, "name")
        result = str_expr(self.row_var)
        assert "table1_name" in str(result)
        
        # 布尔表达式
        bool_expr = Expression(lambda row_var: self.table1["is_active"](row_var), self.solver, "is_active")
        result = bool_expr(self.row_var)
        assert "table1_is_active" in str(result)
    
    def test_expression_as_z3_expr_without_row_var(self):
        """测试无需行变量的表达式转换为Z3表达式"""
        # 创建一个不依赖行变量的表达式
        expr = Expression(lambda row_var: BoolVal(True), self.solver, "常量True")
        
        # 调用 as_z3_expr 不传入行变量也应能正常工作
        result = expr.as_z3_expr()
        assert str(result) == "True" 