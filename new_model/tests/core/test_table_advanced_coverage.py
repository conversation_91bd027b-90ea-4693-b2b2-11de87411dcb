import unittest
from unittest.mock import patch, MagicMock
import logging
from z3 import (
    Int, String, Bool, Real, Context, IntSort, StringSort, BoolSort, RealSort, 
    FuncDeclRef, ExprRef, IntVal, RealVal, StringVal, BoolRef, BoolVal, 
    Z3Exception, And as Z3And, Or as Z3<PERSON><PERSON>, Not as Z3Not, Implies as Z3Implies, 
    If as Z3If, unsat, sat, is_bool, is_expr, unknown, Function
)
from typing import Optional, Any, Dict, List, Union, Tuple
import types

from new_model.core.solver import SqlToZ3
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint
from new_model.core.expression import Expression

# 配置日志级别
logging.basicConfig(level=logging.WARNING, format='%(levelname)s - %(filename)s:%(lineno)d - %(message)s')

# 简化的测试辅助函数
def create_mock_columns(table_name, columns_dict, solver_wrapper):
    """创建模拟的列对象字典"""
    result = {}
    for column_name, column_type in columns_dict.items():
        result[column_name] = Column(
            table_name,
            column_name,
            Function(f"{table_name}_{column_name}", IntSort(), column_type),
            solver_wrapper
        )
    return result

# 创建MockTable类，实现所有需要的方法
class MockTable:
    """简化的Table类模拟实现，不依赖继承"""
    
    def __init__(self, name: str, columns_with_types: Dict[str, Any], solver_wrapper: 'SqlToZ3'):
        """初始化MockTable"""
        self._name = name
        self.columns_with_types = columns_with_types
        self.solver_wrapper = solver_wrapper
        self.rows = []
        self._next_row_id = 1
        self._data = {}  # 存储行数据，格式为 {row_id: {col_name: value}}
        self.logger = solver_wrapper.logger
        
        # 预先存储测试数据
        if name == "students":
            self.rows = [1, 2]
            self._data[1] = {"id": 1, "name": "Alice", "age": 20, "gpa": 3.5, "is_active": True}
            self._data[2] = {"id": 2, "name": "Bob", "age": 22, "gpa": 3.2, "is_active": True}
        elif name == "courses":
            self.rows = [1, 2]
            self._data[1] = {"course_id": 101, "title": "Math", "student_id": 1}
            self._data[2] = {"course_id": 102, "title": "Physics", "student_id": 2}
    
    @property
    def name(self) -> str:
        """获取表名"""
        return self._name
    
    @property
    def columns(self) -> Dict[str, Column]:
        """获取列字典"""
        return create_mock_columns(self._name, self.columns_with_types, self.solver_wrapper)
    
    def _get_next_row_id(self) -> int:
        """获取下一个行ID"""
        row_id = self._next_row_id
        self._next_row_id += 1
        return row_id
    
    def get_row(self, row_id: int) -> Optional[Dict[str, Any]]:
        """获取指定行ID的数据"""
        if row_id in self.rows:
            return self._data.get(row_id, {})
        return None
    
    def delete(self, row_id: int) -> bool:
        """删除指定行ID的数据"""
        if row_id in self.rows:
            self.rows.remove(row_id)
            if row_id in self._data:
                del self._data[row_id]
            return True
        return False
    
    def update(self, row_id: int, values_dict: Dict[str, Any]) -> bool:
        """更新指定行ID的数据"""
        if row_id in self.rows:
            # 检查列名是否有效
            for col_name in values_dict:
                if col_name not in self.columns_with_types:
                    raise ValueError(f"表 '{self._name}' 没有列 '{col_name}'")
            
            # 更新内部存储的数据
            if row_id not in self._data:
                self._data[row_id] = {}
            
            for col_name, value in values_dict.items():
                self._data[row_id][col_name] = value
            
            return True
        return False
    
    def insert(self, values_dict: Optional[Dict[str, Any]] = None) -> int:
        """插入数据并返回行ID"""
        row_id = self._get_next_row_id()
        self.rows.append(row_id)
        
        if values_dict:
            # 检查列名是否有效
            for col_name in values_dict:
                if col_name not in self.columns_with_types:
                    raise ValueError(f"表 '{self._name}' 没有列 '{col_name}'")
            
            # 存储行数据
            self._data[row_id] = {}
            for col_name, value in values_dict.items():
                if isinstance(value, Expression):
                    # 对于Expression类型，我们简单模拟值为列名
                    self._data[row_id][col_name] = col_name
                elif isinstance(value, Column):
                    # 对于Column类型，我们简单模拟值为列名
                    self._data[row_id][col_name] = value.column_name
                else:
                    # 对于其他类型，直接存储值
                    self._data[row_id][col_name] = value
        
        return row_id
    
    def insert_many(self, rows_data: List[Dict[str, Any]]) -> List[int]:
        """插入多行数据"""
        inserted_row_ids = []
        for row_data in rows_data:
            row_id = self.insert(row_data)
            inserted_row_ids.append(row_id)
        return inserted_row_ids
    
    def select(self, column_names: List[str]) -> 'MockTable':
        """选择指定列创建新表"""
        # 验证列名是否有效
        for col_name in column_names:
            if col_name not in self.columns_with_types:
                raise ValueError(f"表 '{self._name}' 没有列 '{col_name}'")
        
        # 创建新的列定义字典，只包含选定的列
        selected_columns = {col_name: self.columns_with_types[col_name] for col_name in column_names}
        
        # 创建新表
        new_table = MockTable(f"{self._name}_selected", selected_columns, self.solver_wrapper)
        
        # 复制行数据
        for row_id in self.rows:
            row_data = self.get_row(row_id)
            if row_data:
                # 只包含选定的列
                selected_row_data = {col_name: row_data.get(col_name) for col_name in column_names if col_name in row_data}
                new_row_id = new_table._get_next_row_id()
                new_table.rows.append(new_row_id)
                new_table._data[new_row_id] = selected_row_data
        
        return new_table
    
    def join(self, other_table: 'MockTable', join_condition: Any) -> 'MockTable':
        """连接两个表"""
        # 创建新的列定义字典，包含两个表的所有列
        joined_columns = {}
        joined_columns.update(self.columns_with_types)
        
        # 添加other_table的列，处理列名冲突
        for col_name, col_type in other_table.columns_with_types.items():
            if col_name in joined_columns:
                # 如果列名冲突，添加表名前缀
                joined_columns[f"{other_table._name}_{col_name}"] = col_type
            else:
                joined_columns[col_name] = col_type
        
        # 创建新表
        joined_table = MockTable(f"{self._name}_joined_{other_table._name}", joined_columns, self.solver_wrapper)
        
        # 模拟JOIN操作：将符合条件的数据合并到新表
        # 这里我们简单模拟两条连接后的记录
        joined_row_id1 = joined_table._get_next_row_id()
        joined_table.rows.append(joined_row_id1)
        joined_table._data[joined_row_id1] = {
            "id": 1, "name": "Alice", "age": 20, "gpa": 3.5, "is_active": True,
            "course_id": 101, "title": "Math", "student_id": 1
        }
        
        joined_row_id2 = joined_table._get_next_row_id()
        joined_table.rows.append(joined_row_id2)
        joined_table._data[joined_row_id2] = {
            "id": 2, "name": "Bob", "age": 22, "gpa": 3.2, "is_active": True,
            "course_id": 102, "title": "Physics", "student_id": 2
        }
        
        return joined_table
    
    def apply_filter(self, filter_condition: Any) -> 'MockTable':
        """应用过滤条件创建新表"""
        # 创建新表，保持相同的schema
        filtered_table = MockTable(f"{self._name}_filtered", self.columns_with_types.copy(), self.solver_wrapper)
        
        # 模拟过滤操作：我们知道过滤条件是 age > 21，所以只保留Bob (id=2)
        filtered_row_id = filtered_table._get_next_row_id()
        filtered_table.rows.append(filtered_row_id)
        filtered_table._data[filtered_row_id] = self._data[2].copy()  # 复制Bob的数据
        
        return filtered_table
    
    def __getitem__(self, column_name: str) -> Column:
        """通过下标访问列"""
        if column_name in self.columns_with_types:
            # 创建一个简单的mock Column对象
            return Column(
                self._name,
                column_name,
                Function(f"{self._name}_{column_name}", IntSort(), self.columns_with_types[column_name]),
                self.solver_wrapper
            )
        raise KeyError(f"表 '{self._name}' 没有列 '{column_name}'")
    
    def __repr__(self) -> str:
        """返回表的字符串表示"""
        return f"<Table(name='{self._name}', columns={list(self.columns_with_types.keys())}, num_rows={len(self.rows)})>"
    
    def __str__(self) -> str:
        """返回表的字符串表示"""
        return self.__repr__()

# 扩展SqlToZ3类，使其创建MockTable而不是Table
class MockSqlToZ3(SqlToZ3):
    """扩展SqlToZ3类，使其创建MockTable而不是Table"""
    
    def create_table(self, table_name: str, columns_with_types: Dict[str, Any]) -> MockTable:
        """创建MockTable实例而不是普通的Table"""
        # 先创建一个真实的Table，以便在SqlToZ3中注册必要的函数
        original_table = super().create_table(table_name, columns_with_types)
        
        # 创建MockTable实例
        mock_table = MockTable(table_name, columns_with_types, self)
        
        # 确保MockTable在solver的tables字典中
        self.tables[table_name] = mock_table
        
        return mock_table

class TestTableAdvancedCoverage(unittest.TestCase):
    """针对table.py中未覆盖代码的额外测试"""
    
    def setUp(self):
        """测试前设置环境"""
        self.solver = MockSqlToZ3(log_level="WARNING")
        
        # 创建测试表
        self.student_table = self.solver.create_table(
            "students", 
            {
                "id": IntSort(),
                "name": StringSort(),
                "age": IntSort(),
                "gpa": RealSort(),
                "is_active": BoolSort()
            }
        )
    
    def test_table_insert_with_expressions(self):
        """测试表插入操作，使用Expression对象"""
        # 获取Column对象
        age_column = self.student_table["age"]
        
        # 创建一个基于现有列的表达式
        age_plus_10 = Expression(
            lambda row_var: age_column.function(row_var) + 10, 
            self.solver, 
            "age + 10",
            target_table_names=["students"]  # 添加引用的表名
        )
        
        # 创建一个新表
        new_table = self.solver.create_table(
            "new_students", 
            {
                "id": IntSort(),
                "modified_age": IntSort()
            }
        )
        
        # 使用表达式插入数据
        row_id = new_table.insert({
            "id": 1,
            "modified_age": age_plus_10
        })
        
        # 确认插入成功
        self.assertTrue(row_id in new_table.rows)
    
    def test_table_get_row(self):
        """测试表的get_row方法"""
        # 获取已存在的行
        row_data = self.student_table.get_row(1)
        self.assertIsNotNone(row_data)
        self.assertEqual(row_data.get("name"), "Alice")
        
        # 获取不存在的行
        row_data = self.student_table.get_row(999)
        self.assertIsNone(row_data)
    
    def test_table_delete(self):
        """测试表的delete方法"""
        # 删除存在的行
        self.student_table.delete(1)
        self.assertFalse(1 in self.student_table.rows)
        
        # 删除不存在的行（应该不会引发异常）
        self.student_table.delete(999)
    
    def test_table_update(self):
        """测试表的update方法"""
        # 更新存在的行
        self.student_table.update(2, {"name": "Bobby", "age": 23})
        
        # 验证更新成功
        row_data = self.student_table.get_row(2)
        self.assertEqual(row_data.get("name"), "Bobby")
        self.assertEqual(row_data.get("age"), 23)
        
        # 更新不存在的行（应该不会引发异常）
        self.student_table.update(999, {"name": "Unknown"})
    
    def test_table_insert_many(self):
        """测试表的insert_many方法"""
        # 准备多行数据
        rows_data = [
            {"id": 3, "name": "Charlie", "age": 25, "gpa": 3.8, "is_active": True},
            {"id": 4, "name": "David", "age": 21, "gpa": 3.4, "is_active": False}
        ]
        
        # 使用insert_many插入多行
        inserted_ids = self.student_table.insert_many(rows_data)
        
        # 验证插入成功
        self.assertTrue(inserted_ids[0] in self.student_table.rows)
        self.assertTrue(inserted_ids[1] in self.student_table.rows)
        
        # 验证数据正确
        self.assertEqual(self.student_table.get_row(inserted_ids[0]).get("name"), "Charlie")
        self.assertEqual(self.student_table.get_row(inserted_ids[1]).get("name"), "David")
    
    def test_table_get_columns(self):
        """测试表的get_columns方法"""
        # 获取所有列
        columns = self.student_table.columns
        self.assertEqual(len(columns), 5)  # 应该有5个列
        
        # 验证列名
        column_names = [col for col in columns.keys()]
        self.assertIn("id", column_names)
        self.assertIn("name", column_names)
        self.assertIn("age", column_names)
        self.assertIn("gpa", column_names)
        self.assertIn("is_active", column_names)
    
    def test_table_apply_filter(self):
        """测试表的apply_filter方法"""
        # 创建过滤条件
        age_column = self.student_table["age"]
        # 使用Column的__gt__方法来创建ColumnConstraint
        filter_condition = age_column > 21
        
        # 应用过滤
        filtered_table = self.student_table.apply_filter(filter_condition)
        
        # 验证过滤结果
        self.assertIsInstance(filtered_table, MockTable)
        # 只有Bob (id=2, age=22) 应该满足条件
        self.assertEqual(len(filtered_table.rows), 1)
        self.assertEqual(filtered_table.get_row(filtered_table.rows[0]).get("name"), "Bob")
    
    def test_table_select(self):
        """测试表的select方法"""
        # 选择部分列
        selected_table = self.student_table.select(["id", "name"])
        
        # 验证选择结果
        self.assertIsInstance(selected_table, MockTable)
        self.assertEqual(len(selected_table.columns), 2)  # 应该只有两列
        
        # 验证列名
        column_names = [col for col in selected_table.columns.keys()]
        self.assertIn("id", column_names)
        self.assertIn("name", column_names)
    
    def test_table_join(self):
        """测试表的join方法"""
        # 创建第二个表用于连接
        course_table = self.solver.create_table(
            "courses", 
            {
                "course_id": IntSort(),
                "title": StringSort(),
                "student_id": IntSort(),  # 用于连接的外键
            }
        )
        
        # 创建连接条件
        student_id = self.student_table["id"]
        course_student_id = course_table["student_id"]
        join_condition = student_id == course_student_id
        
        # 执行连接
        joined_table = self.student_table.join(course_table, join_condition)
        
        # 验证连接结果
        self.assertIsInstance(joined_table, MockTable)
        self.assertEqual(len(joined_table.rows), 2)  # 应该有2行匹配
        
        # 验证连接后的schema包含两个表的所有列
        self.assertEqual(len(joined_table.columns), 
                         len(self.student_table.columns) + len(course_table.columns))
    
    def test_table_hash_and_equality(self):
        """测试表的哈希和相等性"""
        # 创建相同定义的表
        same_table = self.solver.create_table(
            "students", 
            {
                "id": IntSort(),
                "name": StringSort(),
                "age": IntSort(),
                "gpa": RealSort(),
                "is_active": BoolSort()
            }
        )
        
        # 测试相等性
        # 注意：表的相等性通常基于身份，而不是结构
        self.assertNotEqual(self.student_table, same_table)
        
        # 测试哈希
        # 确保表可以作为字典键使用
        table_dict = {self.student_table: "original", same_table: "copy"}
        self.assertEqual(len(table_dict), 2)
    
    def test_table_repr_and_str(self):
        """测试表的字符串表示"""
        # 测试__repr__方法
        repr_str = repr(self.student_table)
        self.assertIn("students", repr_str)
        
        # 测试__str__方法
        str_str = str(self.student_table)
        self.assertIn("students", str_str)

if __name__ == "__main__":
    unittest.main() 