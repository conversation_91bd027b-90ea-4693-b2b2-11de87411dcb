#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 sql_to_z3 对日期类型的支持的单元测试
"""

import sys
import unittest
from pathlib import Path
from z3 import *
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from new_model.core import SqlToZ3
from lineage_core.z3_datetype import DateType

class TestDateSupport(unittest.TestCase):
    """测试sql_to_z3对日期类型支持的单元测试类"""
    
    def setUp(self):
        """初始化测试环境"""
        # 创建 SqlToZ3 实例
        self.s = SqlToZ3()
        
        # 创建DateType实例
        self.date_type = DateType()
        
        # 创建一个包含日期列的表
        self.employee = self.s.create_table("employee", {
            "id": IntSort(),
            "name": StringSort(),
            "hire_date": IntSort(),  # 修改为IntSort，因为DateType内部使用整数表示日期
            "birth_date": IntSort()  # 修改为IntSort，因为DateType内部使用整数表示日期
        })
        
        # 插入测试数据
        self.employee.insert({"id": 1, "name": "张三", 
                            "hire_date": self.date_type.parse_date("2020-01-15"), 
                            "birth_date": self.date_type.parse_date("1990-05-20")})
        self.employee.insert({"id": 2, "name": "李四", 
                            "hire_date": self.date_type.parse_date("2018-07-01"), 
                            "birth_date": self.date_type.parse_date("1985-12-10")})
        self.employee.insert({"id": 3, "name": "王五", 
                            "hire_date": self.date_type.parse_date("2022-03-10"), 
                            "birth_date": self.date_type.parse_date("1995-08-30")})
    
    def test_date_equality(self):
        """测试日期相等比较"""
        self.s.reset()  # 清除之前的约束
        
        # 直接使用日期字符串比较，通过DateType解析成Z3整数常量
        date_expr = (self.employee.hire_date(1) == self.date_type.parse_date("2020-01-15"))
        # 将DateExpressionBoolRef转换为Z3的BoolRef
        self.s.add(date_expr.get_z3_bool_ref() if hasattr(date_expr, 'get_z3_bool_ref') else date_expr)
        
        result = self.s.check()
        self.assertEqual(result, sat, "日期相等比较应该是可满足的")
        
        if result == sat:
            # 测试通过，找到了匹配2020-01-15入职日期的员工
            self.assertTrue(True)
    
    def test_date_greater_than(self):
        """测试日期大于比较"""
        self.s.reset()
        # 查找2019年之后入职的员工
        for row_id in [1, 3]:  # 直接指定行ID而不使用循环
            # 获取日期比较表达式，并转换为Z3的BoolRef
            id_expr = (self.employee.id(row_id) == row_id)
            hire_date_expr = (self.employee.hire_date(row_id) > self.date_type.parse_date("2019-01-01"))
            
            # 确保表达式是Z3 BoolRef类型
            id_bool = id_expr.get_z3_bool_ref() if hasattr(id_expr, 'get_z3_bool_ref') else id_expr
            hire_date_bool = hire_date_expr.get_z3_bool_ref() if hasattr(hire_date_expr, 'get_z3_bool_ref') else hire_date_expr
            
            # 使用单独的约束（不使用Implies）
            self.s.add(id_bool)
            self.s.add(hire_date_bool)
        
        result = self.s.check()
        self.assertEqual(result, sat, "日期大于比较应该是可满足的")
        
        # 不需要进一步验证，如果约束是可满足的，说明测试通过
    
    def test_date_between(self):
        """测试日期范围查询"""
        self.s.reset()
        # 查找1990年到2000年之间出生的员工
        for row_id in [1, 3]:  # 直接指定行ID
            # 单独添加约束，避免使用And
            id_expr = z3.Or(self.employee.id(row_id) == 1, self.employee.id(row_id) == 3)
            id_bool = id_expr.get_z3_bool_ref() if hasattr(id_expr, 'get_z3_bool_ref') else id_expr
            self.s.add(id_bool)
            
            birth_date_min = (self.employee.birth_date(row_id) >= self.date_type.parse_date("1990-01-01"))
            birth_date_max = (self.employee.birth_date(row_id) <= self.date_type.parse_date("2000-01-01"))
            
            # 确保表达式是Z3 BoolRef类型
            birth_date_min_bool = birth_date_min.get_z3_bool_ref() if hasattr(birth_date_min, 'get_z3_bool_ref') else birth_date_min
            birth_date_max_bool = birth_date_max.get_z3_bool_ref() if hasattr(birth_date_max, 'get_z3_bool_ref') else birth_date_max
            
            self.s.add(birth_date_min_bool)
            self.s.add(birth_date_max_bool)
        
        result = self.s.check()
        self.assertEqual(result, sat, "日期范围查询应该是可满足的")
        
        # 如果约束是可满足的，说明测试通过
    
    def test_complex_date_query(self):
        """测试复杂的日期查询"""
        self.s.reset()
        # 查找2019年之后入职且1990年之后出生的员工
        row_id = 3  # 直接使用王五的ID
        
        # 单独添加约束，避免使用And和Implies
        id_expr = (self.employee.id(row_id) == 3)
        hire_date_expr = (self.employee.hire_date(row_id) > self.date_type.parse_date("2019-01-01"))
        birth_date_expr = (self.employee.birth_date(row_id) > self.date_type.parse_date("1990-01-01"))
        
        # 确保表达式是Z3 BoolRef类型
        id_bool = id_expr.get_z3_bool_ref() if hasattr(id_expr, 'get_z3_bool_ref') else id_expr
        hire_date_bool = hire_date_expr.get_z3_bool_ref() if hasattr(hire_date_expr, 'get_z3_bool_ref') else hire_date_expr
        birth_date_bool = birth_date_expr.get_z3_bool_ref() if hasattr(birth_date_expr, 'get_z3_bool_ref') else birth_date_expr
        
        self.s.add(id_bool)
        self.s.add(hire_date_bool)
        self.s.add(birth_date_bool)
        
        result = self.s.check()
        self.assertEqual(result, sat, "复杂日期查询应该是可满足的")
        
        # 如果约束是可满足的，说明测试通过
    
    def test_unsatisfiable_date_constraint(self):
        """测试不可满足的日期约束"""
        self.s.reset()
        
        # 添加矛盾的约束
        # 设置员工3（王五）的入职日期是2022-03-10
        hire_date_expr1 = (self.employee.hire_date(3) == self.date_type.parse_date("2022-03-10"))
        # 又设置员工3的入职日期必须大于2023-01-01（矛盾）
        hire_date_expr2 = (self.employee.hire_date(3) > self.date_type.parse_date("2023-01-01"))
        
        # 确保表达式是Z3 BoolRef类型
        hire_date_bool1 = hire_date_expr1.get_z3_bool_ref() if hasattr(hire_date_expr1, 'get_z3_bool_ref') else hire_date_expr1
        hire_date_bool2 = hire_date_expr2.get_z3_bool_ref() if hasattr(hire_date_expr2, 'get_z3_bool_ref') else hire_date_expr2
        
        self.s.add(hire_date_bool1)
        self.s.add(hire_date_bool2)
        
        result = self.s.check()
        self.assertEqual(result, unsat, "不可满足的日期约束应该返回unsat")

if __name__ == '__main__':
    unittest.main() 