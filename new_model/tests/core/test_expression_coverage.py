"""
测试Expression类的覆盖率
主要针对当前未覆盖的方法进行测试
"""

import pytest
from z3 import Int, String, Real, BoolVal, IntVal, RealVal, And, Or, Not, IntSort, RealSort, StringSort, BoolSort
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from new_model.core.solver import SqlToZ3
from new_model.core.expression import Expression
from new_model.core.table import Table
from new_model.core.column import Column

class TestExpressionCoverage:
    """测试Expression类中未覆盖的方法"""
    
    def setup_method(self):
        """每个测试方法开始前的设置"""
        self.solver = SqlToZ3()
        
        # 创建测试表和列
        self.table1 = self.solver.create_table("table1", {
            "id": IntSort(),
            "value": RealSort(),
            "name": StringSort(),
            "price": RealSort(),
            "is_active": BoolSort()
        })
        
        self.table2 = self.solver.create_table("table2", {
            "id": IntSort(),
            "quantity": IntSort(),
            "category": StringSort()
        })
    
    def test_expression_init_with_description(self):
        """测试表达式初始化时提供描述"""
        expr = Expression(lambda row: self.table1.id(row) > 5, self.solver, "id_greater_than_5")
        assert expr.description == "id_greater_than_5"
    
    def test_expression_init_error(self):
        """测试初始化时传入非可调用对象"""
        with pytest.raises(TypeError):
            Expression("not_callable", self.solver, "error_test")
    
    def test_expression_call_without_row_var(self):
        """测试不带行变量调用表达式"""
        # 创建不需要行变量的表达式
        expr = Expression(lambda: BoolVal(True), self.solver, "constant_true")
        result = expr()
        assert str(result) == "True"
    
    def test_expression_call_with_python_bool_result(self):
        """测试表达式返回Python布尔值"""
        expr = Expression(lambda _: True, self.solver, "python_true")
        result = expr("dummy_row")
        assert str(result) == "True"
        
        expr = Expression(lambda _: False, self.solver, "python_false")
        result = expr("dummy_row")
        assert str(result) == "False"
    
    def test_expression_call_exception(self):
        """测试调用表达式时发生异常"""
        def buggy_function(row):
            raise ValueError("测试异常")
        
        expr = Expression(buggy_function, self.solver, "buggy_expr")
        with pytest.raises(ValueError):
            expr("dummy_row")
    
    def test_expression_add_operations(self):
        """测试加法操作符"""
        # 表达式 + 常量
        expr1 = Expression(lambda row: self.table1.value(row), self.solver, "value")
        expr_add_const = expr1 + 10
        assert "value + 10" in expr_add_const.description
        
        # 表达式 + 列
        expr_add_col = expr1 + self.table1.price
        assert "value + price" in expr_add_col.description
        
        # 表达式 + 表达式
        expr2 = Expression(lambda row: self.table1.price(row), self.solver, "price")
        expr_add_expr = expr1 + expr2
        assert "value + price" in expr_add_expr.description
        
        # 测试字符串拼接
        expr_str = Expression(lambda row: self.table1.name(row), self.solver, "name")
        expr_str_concat = expr_str + "suffix"  # 移除前导空格
        assert "name + suffix" in expr_str_concat.description
    
    def test_expression_sub_operations(self):
        """测试减法操作符"""
        expr1 = Expression(lambda row: self.table1.value(row), self.solver, "value")
        # 表达式 - 常量
        expr_sub_const = expr1 - 5
        assert "value - 5" in expr_sub_const.description
        
        # 表达式 - 列
        expr_sub_col = expr1 - self.table1.price
        assert "value - price" in expr_sub_col.description
        
        # 表达式 - 表达式
        expr2 = Expression(lambda row: self.table1.price(row), self.solver, "price")
        expr_sub_expr = expr1 - expr2
        assert "value - (price)" in expr_sub_expr.description
    
    def test_expression_mul_operations(self):
        """测试乘法操作符"""
        expr1 = Expression(lambda row: self.table1.value(row), self.solver, "value")
        # 表达式 * 常量
        expr_mul_const = expr1 * 2
        assert "value * 2" in expr_mul_const.description
        
        # 表达式 * 列
        expr_mul_col = expr1 * self.table1.price
        assert "value * price" in expr_mul_col.description
        
        # 表达式 * 表达式
        expr2 = Expression(lambda row: self.table1.price(row), self.solver, "price")
        expr_mul_expr = expr1 * expr2
        assert "value * price" in expr_mul_expr.description
    
    def test_expression_div_operations(self):
        """测试除法操作符"""
        expr1 = Expression(lambda row: self.table1.value(row), self.solver, "value")
        # 表达式 / 常量
        expr_div_const = expr1 / 2
        assert "value / 2" in expr_div_const.description
        
        # 表达式 / 列
        expr_div_col = expr1 / self.table1.price
        assert "value / price" in expr_div_col.description
        
        # 表达式 / 表达式
        expr2 = Expression(lambda row: self.table1.price(row), self.solver, "price")
        expr_div_expr = expr1 / expr2
        assert "value / price" in expr_div_expr.description
    
    def test_expression_reverse_operations(self):
        """测试反向操作符 (常量在左侧)"""
        expr = Expression(lambda row: self.table1.value(row), self.solver, "value")
        
        # 反向加法: 常量 + 表达式
        expr_radd = 10 + expr
        assert "10 + value" in expr_radd.description  # 可能是 (10 + value)
        
        # 反向减法: 常量 - 表达式
        expr_rsub = 10 - expr
        assert "10 - (value)" in expr_rsub.description
        
        # 反向乘法: 常量 * 表达式
        expr_rmul = 10 * expr
        assert "10 * value" in expr_rmul.description  # 可能是 (10 * value)
        
        # 反向除法: 常量 / 表达式
        expr_rdiv = 10 / expr
        assert "10 / value" in expr_rdiv.description  # 可能是 (10 / value)
    
    def test_expression_comparison_operations(self):
        """测试比较操作符"""
        expr = Expression(lambda row: self.table1.value(row), self.solver, "value")
        
        # 等于
        expr_eq_const = expr == 10
        assert "value == 10" in expr_eq_const.description
        
        # 不等于
        expr_ne_const = expr != 10
        assert "value != 10" in expr_ne_const.description
        
        # 大于
        expr_gt_const = expr > 10
        assert "value > 10" in expr_gt_const.description
        
        # 小于
        expr_lt_const = expr < 10
        assert "value < 10" in expr_lt_const.description
        
        # 大于等于
        expr_ge_const = expr >= 10
        assert "value >= 10" in expr_ge_const.description
        
        # 小于等于
        expr_le_const = expr <= 10
        assert "value <= 10" in expr_le_const.description
    
    def test_expression_logical_operations(self):
        """测试逻辑操作符"""
        expr1 = Expression(lambda row: self.table1.value(row) > 10, self.solver, "value_gt_10")
        expr2 = Expression(lambda row: self.table1.price(row) < 100, self.solver, "price_lt_100")
        
        # 逻辑与
        expr_and = expr1 & expr2
        assert "value_gt_10 & price_lt_100" in expr_and.description
        
        # 逻辑或
        expr_or = expr1 | expr2
        assert "value_gt_10 | price_lt_100" in expr_or.description
        
        # 逻辑非
        expr_not = ~expr1
        assert "~(value_gt_10)" in expr_not.description
        
        # 与Python布尔值的逻辑操作
        expr_and_bool = expr1 & True
        expr_or_bool = expr1 | False
        
        # 简单Z3 BoolRef的逻辑操作
        expr_and_z3bool = expr1 & BoolVal(True)
        expr_or_z3bool = expr1 | BoolVal(False)
    
    def test_expression_contains_operation(self):
        """测试contains操作符"""
        # 字符串中包含子串
        expr = Expression(lambda row: self.table1.name(row), self.solver, "name")
        name_contains_a = expr.contains("a")
        assert "name CONTAINS a" in name_contains_a.description
    
    def test_expression_repr(self):
        """测试表达式的__repr__方法"""
        expr = Expression(lambda row: self.table1.value(row) > 10, self.solver, "value_gt_10")
        assert "value_gt_10" in repr(expr) 