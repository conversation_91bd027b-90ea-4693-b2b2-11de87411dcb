"""
基本示例
展示SQL到Z3约束转换的基本用法
"""

import sys
import unittest # 导入unittest模块
from pathlib import Path
from z3 import *
import functools

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入SQL到Z3模块
from new_model.core import SqlToZ3

class TestBasicExample(unittest.TestCase): # 创建测试类
    def test_run_basic_logic(self): # 将原函数改为测试方法
        """
        测试基本示例的逻辑：
        创建表、插入数据和查询，并验证结果。
        """
        print("运行SQL到Z3约束转换基本示例 (单元测试):")
        print("-" * 50)
        
        # 创建包装器
        z3_sql = SqlToZ3()
        
        # 创建表结构
        order_mapping = z3_sql.create_table("order_mapping", {
            "current_order_id": StringSort(),
            "historical_order_id": StringSort()
        })
        
        # 使用改进的插入语法
        om_row1 = order_mapping.insert({ # 假设 insert 返回行ID，或者我们知道行ID从1开始
            "current_order_id": "1001", 
            "historical_order_id": "9001"
        })
        
        om_row2 = order_mapping.insert({
            "current_order_id": "9001", 
            "historical_order_id": "9002"
        })
        
        current_orders = z3_sql.create_table("current_orders", {
            "current_order_id": StringSort()
        })
        
        # 使用for_all_rows=True，从order_mapping的所有行中获取current_order_id
        current_orders.insert({
            "current_order_id": ("order_mapping", "current_order_id")
        }, for_all_rows=True)
        
        print("数据添加成功")
        
        # 示例：使用自然语法添加JOIN和WHERE条件
        # 类似SQL: SELECT * FROM order_mapping join current_orders on historical_order_id = current_orders.current_order_id
        join_result = order_mapping.historical_order_id == current_orders.current_order_id
        mapping_id_var = join_result[0] # order_mapping 表的行变量
        current_id_var = join_result[2] # current_orders 表的行变量
        
        # 检查结果
        check_result = z3_sql.check()
        self.assertEqual(check_result, sat, "求解器应返回 sat，表示存在匹配的订单") # 断言1: 检查是否可满足

        if check_result == sat:
            print("\n结果: 存在历史订单号也在当前订单号列表中的情况")
            model = z3_sql.get_model()
            self.assertIsNotNone(model, "模型不应为None") # 断言2: 模型已成功获取

            mapping_id_val = model.eval(mapping_id_var).as_long() # 获取具体行ID值
            current_id_val = model.eval(current_id_var).as_long()
            
            # 获取相关列函数，用于结果展示和断言
            # 键名应为 "表名_列名"
            order_mapping_current_id_func = z3_sql.column_functions["order_mapping_current_order_id"]
            order_mapping_historical_id_func = z3_sql.column_functions["order_mapping_historical_order_id"]
            current_orders_id_func = z3_sql.column_functions["current_orders_current_order_id"]
            
            print(f"示例解: mapping_id_val = {mapping_id_val}, current_id_val = {current_id_val}")
            
            eval_om_current = model.eval(order_mapping_current_id_func(mapping_id_val)).as_string() # 使用 as_string()
            eval_om_historical = model.eval(order_mapping_historical_id_func(mapping_id_val)).as_string() # 使用 as_string()
            eval_co_current = model.eval(current_orders_id_func(current_id_val)).as_string() # 使用 as_string()

            print(f"order_mapping.current_order_id = {eval_om_current}")
            print(f"order_mapping.historical_order_id = {eval_om_historical}")
            print(f"current_orders.current_order_id = {eval_co_current}")

            # 断言3: historical_order_id 应该等于 current_orders.current_order_id
            self.assertEqual(eval_om_historical, eval_co_current, "匹配的historical_order_id应等于current_orders.current_order_id")
            
            # 断言4: 根据数据，匹配的值应该是 "9001"
            self.assertEqual(eval_om_historical, "9001", "匹配的historical_order_id应为 '9001'")

            # 断言5: 验证行号和对应的值 (这部分断言依赖于行ID的分配和找到的特定解)
            # 如果 mapping_id_val 指向 order_mapping 的第一行 (ID=1, co_id="1001", ho_id="9001")
            # 且 current_id_val 指向 current_orders 的第二行 (ID=2, co_id="9001", 该值来自 order_mapping 的第二行的 co_id)
            # 那么历史ID "9001" (来自om第一行) == 当前ID "9001" (来自co第二行)
            if mapping_id_val == om_row1: # 假设 om_row1 是第一行的ID (通常为1)
                 self.assertEqual(eval_om_current, "1001", "如果解是order_mapping第一行，其current_order_id应为'1001'")
                 # 并且它匹配的 current_orders.current_order_id 应该是 "9001"
                 self.assertEqual(eval_co_current, "9001", "匹配的current_orders.current_order_id应为'9001'")
                 # 进一步确认这个 "9001" 来自 order_mapping 的第二行
                 # 这个断言比较复杂，需要知道 current_orders 中第二行的来源
            
            # 一个更通用的断言是，找到的解必须是数据中存在的有效JOIN
            # 解 (eval_om_current, eval_om_historical) 必须是 order_mapping 表中的一行
            # 解 eval_co_current 必须是 current_orders 表中的一行
            # 并且 eval_om_historical == eval_co_current
            
            # 验证找到的 order_mapping 行是否在原始插入数据中
            found_om_row_in_data = False
            if (model.eval(order_mapping_current_id_func(mapping_id_val)).as_string() == "1001" and \
                model.eval(order_mapping_historical_id_func(mapping_id_val)).as_string() == "9001"):
                found_om_row_in_data = True
            elif (model.eval(order_mapping_current_id_func(mapping_id_val)).as_string() == "9001" and \
                  model.eval(order_mapping_historical_id_func(mapping_id_val)).as_string() == "9002"):
                found_om_row_in_data = True
            self.assertTrue(found_om_row_in_data, "找到的 order_mapping 行必须是预设数据之一")

            # 验证找到的 current_orders 行是否在原始插入数据 (派生数据) 中
            found_co_row_in_data = False
            # current_orders 的数据是 ("order_mapping", "current_order_id")
            # 即 "1001" 和 "9001"
            co_val = model.eval(current_orders_id_func(current_id_val)).as_string()
            if co_val == "1001" or co_val == "9001":
                found_co_row_in_data = True
            self.assertTrue(found_co_row_in_data, "找到的 current_orders 行必须是派生数据之一")

        else:
            # 如果结果是 unsat，但我们期望 sat，则测试失败
            self.fail("求解器返回 unsat，但期望 sat")
            
    def test_using_row_functions(self):
        """
        测试使用行函数(row function)来表达列之间的相等关系
        这种方法提供了更灵活的方式来处理复杂的计算表达式
        """
        print("\n运行使用行函数表达列关系的示例:")
        print("-" * 50)
        
        # 创建包装器
        z3_sql = SqlToZ3()
        
        # 创建源表结构
        source_table = z3_sql.create_table("source_table", {
            "id": StringSort(),
            "value1": IntSort(),
            "value2": IntSort()
        })
        
        # 插入源数据
        source_table.insert({
            "id": "A001", 
            "value1": 100,
            "value2": 50
        })
        
        source_table.insert({
            "id": "A002", 
            "value1": 200,
            "value2": 75
        })
        
        # 创建目标表
        target_table = z3_sql.create_table("target_table", {
            "source_id": StringSort(),
            "sum_value": IntSort(),
            "diff_value": IntSort()
        })
        
        # 定义计算函数，这些函数将应用于每一个源行
        def sum_calc(source_row, z3_sql_instance, value1_col, value2_col):
            return z3_sql_instance.column_functions[value1_col](source_row) + \
                   z3_sql_instance.column_functions[value2_col](source_row)
        
        def diff_calc(source_row, z3_sql_instance, value1_col, value2_col):
            return z3_sql_instance.column_functions[value1_col](source_row) - \
                   z3_sql_instance.column_functions[value2_col](source_row)
        
        # 为了简化调用，可以使用functools.partial预设参数
        
        # 预设参数，只留下source_row需要在调用时提供
        sum_calc_bound = functools.partial(sum_calc, 
                                          z3_sql_instance=z3_sql,
                                          value1_col="source_table_value1", 
                                          value2_col="source_table_value2")
        
        diff_calc_bound = functools.partial(diff_calc,
                                           z3_sql_instance=z3_sql,
                                           value1_col="source_table_value1",
                                           value2_col="source_table_value2")
        
        # 使用扩展的insert功能，将计算表达式应用于源表的每一行
        target_table.insert({
            "source_id": ("source_table", "id"),  # 简单的列引用
            "sum_value": ("source_table", sum_calc_bound),   # 使用绑定参数后的函数
            "diff_value": ("source_table", diff_calc_bound)  # 使用绑定参数后的函数
        }, for_all_rows=True)
        
        # 验证结果
        check_result = z3_sql.check()
        self.assertEqual(check_result, sat, "求解器应返回sat")
        
        if check_result == sat:
            print("结果: 成功使用行函数表达列关系")
            model = z3_sql.get_model()
            
            # 获取源表和目标表的列函数
            source_id_func = z3_sql.column_functions["source_table_id"]
            source_value1_func = z3_sql.column_functions["source_table_value1"]
            source_value2_func = z3_sql.column_functions["source_table_value2"]
            target_id_func = z3_sql.column_functions["target_table_source_id"]
            target_sum_func = z3_sql.column_functions["target_table_sum_value"]
            target_diff_func = z3_sql.column_functions["target_table_diff_value"]
            
            # 验证每行数据
            for source_row in range(1, 3):
                source_id = model.eval(source_id_func(source_row)).as_string()
                source_val1 = model.eval(source_value1_func(source_row)).as_long()
                source_val2 = model.eval(source_value2_func(source_row)).as_long()
                
                print(f"\n源数据行 {source_row}:")
                print(f"  id: {source_id}, value1: {source_val1}, value2: {source_val2}")
                
                # 由于for_all_rows创建的映射，目标表行应与源表行对应
                target_row = source_row
                target_source_id = model.eval(target_id_func(target_row)).as_string()
                target_sum = model.eval(target_sum_func(target_row)).as_long()
                target_diff = model.eval(target_diff_func(target_row)).as_long()
                
                print(f"对应的目标数据行 {target_row}:")
                print(f"  source_id: {target_source_id}, sum_value: {target_sum}, diff_value: {target_diff}")
                
                # 验证计算是否正确
                self.assertEqual(target_source_id, source_id,
                                f"行 {target_row} 的source_id应等于源表的id")
                self.assertEqual(target_sum, source_val1 + source_val2, 
                                f"行 {target_row} 的sum_value应等于source的value1+value2")
                self.assertEqual(target_diff, source_val1 - source_val2, 
                                f"行 {target_row} 的diff_value应等于source的value1-value2")

    def test_constraint_with_calculated_fields(self):
        """
        测试在计算字段基础上添加比较约束的场景
        这个示例展示如何处理更复杂的业务逻辑约束
        """
        print("\n运行计算字段+比较约束的示例:")
        print("-" * 50)
        
        # 创建包装器
        z3_sql = SqlToZ3()
        
        # 创建商品表，包含价格、数量和折扣信息
        products = z3_sql.create_table("products", {
            "id": StringSort(),
            "name": StringSort(),
            "price": IntSort(),      # 单价
            "quantity": IntSort(),   # 数量
            "discount": IntSort()    # 折扣金额
        })
        
        # 插入商品数据
        products.insert({
            "id": "P001",
            "name": "手机",
            "price": 3000,
            "quantity": 1,
            "discount": 200
        })
        
        products.insert({
            "id": "P002",
            "name": "耳机",
            "price": 500,
            "quantity": 2,
            "discount": 50
        })
        
        products.insert({
            "id": "P003",
            "name": "充电器",
            "price": 100,
            "quantity": 3,
            "discount": 30
        })
        
        # 创建订单表，包含计算后的总价
        orders = z3_sql.create_table("orders", {
            "product_id": StringSort(),
            "total_price": IntSort()  # 总价 = 单价*数量-折扣
        })
        
        # 定义计算总价的函数
        def calculate_total(product_row):
            price = z3_sql.column_functions["products_price"](product_row)
            quantity = z3_sql.column_functions["products_quantity"](product_row)
            discount = z3_sql.column_functions["products_discount"](product_row)
            return price * quantity - discount
        
        def product_id(product_row):
            return z3_sql.column_functions["products_id"](product_row)

        # 使用计算函数插入订单数据
        orders.insert({
            "product_id": product_id,
            "total_price": calculate_total
        }, for_all_rows=True)
        
        # 添加比较约束：只关注总价大于1000的订单
        min_price_threshold = 1000
        
        # 创建一个变量表示满足条件的订单行ID，并自动添加约束确保该变量是orders表中的一行
        order_row_above_threshold = orders.get_row_var()
        
        # 添加约束：该行的总价必须大于阈值
        z3_sql.add(z3_sql.column_functions["orders_total_price"](order_row_above_threshold) > min_price_threshold)
        
        # 验证结果
        check_result = z3_sql.check()
        self.assertEqual(check_result, sat, "求解器应返回sat，表示存在总价大于阈值的订单")
        
        if check_result == sat:
            print(f"结果: 成功找到总价大于 {min_price_threshold} 的订单")
            model = z3_sql.get_model()
            
            # 获取满足条件的订单行ID
            qualified_order_row = model.eval(order_row_above_threshold).as_long()
            
            # 获取相关的列函数
            product_id_func = z3_sql.column_functions["orders_product_id"]
            total_price_func = z3_sql.column_functions["orders_total_price"]
            
            # 获取订单信息
            product_id = model.eval(product_id_func(qualified_order_row)).as_string()
            total_price = model.eval(total_price_func(qualified_order_row)).as_long()
            
            print(f"满足条件的订单: 行ID = {qualified_order_row}")
            print(f"  商品ID: {product_id}, 总价: {total_price}")
            
            # 验证这确实是一个总价大于阈值的订单
            self.assertTrue(total_price > min_price_threshold, 
                           f"订单总价 {total_price} 应该大于阈值 {min_price_threshold}")
            
            # 获取原始商品信息以验证计算正确性
            products_row = None
            for row_id in range(1, 4):  # 我们插入了3件商品
                if model.eval(z3_sql.column_functions["products_id"](row_id)).as_string() == product_id:
                    products_row = row_id
                    break
                    
            if products_row is not None:
                price = model.eval(z3_sql.column_functions["products_price"](products_row)).as_long()
                quantity = model.eval(z3_sql.column_functions["products_quantity"](products_row)).as_long()
                discount = model.eval(z3_sql.column_functions["products_discount"](products_row)).as_long()
                
                expected_total = price * quantity - discount
                print(f"  原始商品数据: 单价 = {price}, 数量 = {quantity}, 折扣 = {discount}")
                print(f"  验证计算: {price} * {quantity} - {discount} = {expected_total}")
                
                # 验证总价计算正确
                self.assertEqual(total_price, expected_total, 
                                f"订单总价应等于 单价*数量-折扣 ({price}*{quantity}-{discount}={expected_total})")
            
                # 验证该商品确实满足阈值条件
                self.assertTrue(expected_total > min_price_threshold, 
                               f"商品 {product_id} 的总价 {expected_total} 应该大于阈值 {min_price_threshold}")
        else:
            self.fail("求解器返回 unsat，但期望 sat，表示找不到总价大于阈值的订单")

if __name__ == "__main__":
    unittest.main() # 修改主执行块 