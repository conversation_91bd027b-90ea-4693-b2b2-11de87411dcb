import unittest
from unittest.mock import patch, MagicMock
import logging
from z3 import (
    Int, String, Bool, Real, Context, IntSort, StringSort, BoolSort, RealSort, 
    FuncDeclRef, ExprRef, IntVal, RealVal, StringVal, BoolRef, BoolVal, 
    Z3Exception, And as Z3And, Or as Z3Or, Not as Z3Not, Implies as Z3Implies, 
    If as Z3If, unsat, sat, is_bool, is_expr, unknown
)
from typing import Optional, Any, Dict

from new_model.core.solver import SqlToZ3
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint
from new_model.core.expression import Expression

# 配置日志级别
logging.basicConfig(level=logging.WARNING, format='%(levelname)s - %(filename)s:%(lineno)d - %(message)s')

class TestExtendedCoverage(unittest.TestCase):
    """测试专门针对提高覆盖率的测试用例"""
    
    def setUp(self):
        """测试前设置环境"""
        self.solver = SqlToZ3(log_level="WARNING")
        
        # 创建测试表 - 使用IntSort()代替日期类型
        self.student_table = self.solver.create_table(
            "students", 
            {
                "id": IntSort(),
                "name": StringSort(),
                "age": IntSort(),
                "gpa": RealSort(),
                "is_active": BoolSort(),
                "enrollment_date": IntSort()  # 使用整数类型表示日期
            }
        )
        
        # 创建另一个测试表用于JOIN测试
        self.course_table = self.solver.create_table(
            "courses", 
            {
                "id": IntSort(),
                "title": StringSort(),
                "credits": IntSort(),
                "start_date": IntSort()  # 使用整数类型表示日期
            }
        )
        
        # 插入一些测试数据
        self.student_table.insert({"id": 1, "name": "Alice", "age": 20, "gpa": 3.5, "is_active": True})
        self.student_table.insert({"id": 2, "name": "Bob", "age": 22, "gpa": 3.2, "is_active": True})
        self.student_table.insert({"id": 3, "name": "Charlie", "age": 21, "gpa": 3.8, "is_active": False})
        
        self.course_table.insert({"id": 101, "title": "Math", "credits": 3})
        self.course_table.insert({"id": 102, "title": "Physics", "credits": 4})
    
    def test_solver_complex_logical_operations(self):
        """测试SqlToZ3中的复杂逻辑操作方法"""
        
        # 测试And操作
        age_gt_20 = self.student_table.age > 20
        gpa_gt_3 = self.student_table.gpa > 3.0
        
        and_expr = self.solver.And(age_gt_20, gpa_gt_3)
        self.assertIsInstance(and_expr, Expression)
        
        # 测试Or操作
        name_is_alice = self.student_table.name == "Alice"
        or_expr = self.solver.Or(age_gt_20, name_is_alice)
        self.assertIsInstance(or_expr, Expression)
        
        # 测试Not操作
        not_expr = self.solver.Not(age_gt_20)
        self.assertIsInstance(not_expr, Expression)
        
        # 测试Implies操作
        implies_expr = self.solver.Implies(age_gt_20, gpa_gt_3)
        self.assertIsInstance(implies_expr, Expression)
        
        # 测试Xor操作
        xor_expr = self.solver.Xor(age_gt_20, name_is_alice)
        self.assertIsInstance(xor_expr, Expression)
        
        # 测试If操作
        if_expr = self.solver.If(age_gt_20, self.student_table.gpa, RealVal(0.0))
        self.assertIsInstance(if_expr, Expression)
    
    def test_table_insert_with_validation(self):
        """测试Table.insert方法的不同情况，包括验证失败的情况"""
        
        # 正常插入
        row_id = self.student_table.insert({"id": 4, "name": "Dave", "age": 23, "gpa": 3.9, "is_active": True})
        self.assertIsInstance(row_id, int)
        
        # 测试插入缺少列的情况(不应该抛出异常，因为Z3允许未指定的列值)
        row_id = self.student_table.insert({"id": 5, "name": "Eve"})
        self.assertIsInstance(row_id, int)
        
        # 测试插入额外列的情况(应该抛出异常)
        with self.assertRaises(ValueError):
            self.student_table.insert({"id": 6, "name": "Frank", "invalid_column": "value"})
            
        # 测试for_all_rows参数，这里简单模拟
        # 正常情况下这应该基于源表，但简化测试可以不需要源表
        row_ids = self.student_table.insert({"id": 7, "name": "Test"}, for_all_rows=True)
        self.assertIsInstance(row_ids, int)  # 如果没有源表引用，仍然返回单个行ID

if __name__ == "__main__":
    unittest.main() 