import unittest
from unittest.mock import patch, MagicMock
import logging
from z3 import (
    Int, String, Bool, Real, Context, IntSort, StringSort, BoolSort, RealSort, 
    FuncDeclRef, ExprRef, IntVal, RealVal, StringVal, BoolRef, BoolVal, 
    Z3Exception, And as Z3And, Or as Z3Or, Not as Z3Not, Implies as Z3Implies, 
    If as Z3If, unsat, sat, is_bool, is_expr, unknown
)
from typing import Optional, Any, Dict, List

from new_model.core.solver import SqlToZ3
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint
from new_model.core.expression import Expression

# 配置日志级别
logging.basicConfig(level=logging.WARNING, format='%(levelname)s - %(filename)s:%(lineno)d - %(message)s')

class TestExtendedCoverageAdditional(unittest.TestCase):
    """更多测试用例，针对未覆盖的代码路径"""
    
    def setUp(self):
        """测试前设置环境"""
        self.solver = SqlToZ3(log_level="WARNING")
        
        # 创建测试表
        self.student_table = self.solver.create_table(
            "students", 
            {
                "id": IntSort(),
                "name": StringSort(),
                "age": IntSort(),
                "gpa": RealSort(),
                "is_active": BoolSort()
            }
        )
        
        # 创建另一个测试表用于JOIN测试
        self.course_table = self.solver.create_table(
            "courses", 
            {
                "id": IntSort(),
                "title": StringSort(),
                "credits": IntSort()
            }
        )
        
        # 创建第三个表用于多表JOIN测试
        self.enrollment_table = self.solver.create_table(
            "enrollments",
            {
                "student_id": IntSort(),
                "course_id": IntSort(),
                "grade": RealSort()
            }
        )
        
        # 插入一些测试数据
        self.student_table.insert({"id": 1, "name": "Alice", "age": 20, "gpa": 3.5, "is_active": True})
        self.student_table.insert({"id": 2, "name": "Bob", "age": 22, "gpa": 3.2, "is_active": True})
        self.course_table.insert({"id": 101, "title": "Math", "credits": 3})
        self.enrollment_table.insert({"student_id": 1, "course_id": 101, "grade": 4.0})
    
    def test_column_call_with_different_inputs(self):
        """测试Column.__call__方法的不同输入情况"""
        # 获取实际的Column对象
        name_column = self.student_table["name"]  # 使用__getitem__来获取Column对象
        
        # 使用整数作为row_var (非标准用法，但代码支持)
        result = name_column(1)
        self.assertIsNotNone(result)
        
        # 使用字符串作为row_var (非标准用法，但代码支持)
        result = name_column("row_1")
        self.assertIsNotNone(result)
        
        # 测试数值参数
        age_column = self.student_table["age"]
        result = age_column(1)
        self.assertIsNotNone(result)
    
    def test_column_math_operations(self):
        """测试Column类的数学运算"""
        # 获取实际的Column对象
        age_column = self.student_table["age"]
        
        # 测试反向运算 (RHS操作数在左侧)
        expr1 = 5 + age_column
        self.assertIsInstance(expr1, Expression)
        
        expr2 = 10 - age_column
        self.assertIsInstance(expr2, Expression)
        
        expr3 = 2 * age_column
        self.assertIsInstance(expr3, Expression)
        
        expr4 = 100 / age_column
        self.assertIsInstance(expr4, Expression)
    
    def test_column_operators_with_expressions(self):
        """测试Column与Expression之间的操作"""
        # 获取实际的Column对象
        age_column = self.student_table["age"]
        gpa_column = self.student_table["gpa"]
        
        # 创建一个简单的表达式
        expr = Expression(lambda row_var: age_column.function(row_var) + 5, 
                         self.solver, 
                         "age + 5")
        
        # 测试Column与Expression的比较操作
        result1 = gpa_column == expr
        self.assertIsInstance(result1, Expression)
        
        result2 = gpa_column != expr
        self.assertIsInstance(result2, Expression)
        
        result3 = gpa_column < expr
        self.assertIsInstance(result3, Expression)
        
        result4 = gpa_column <= expr
        self.assertIsInstance(result4, Expression)
        
        result5 = gpa_column > expr
        self.assertIsInstance(result5, Expression)
        
        result6 = gpa_column >= expr
        self.assertIsInstance(result6, Expression)
    
    def test_column_special_methods(self):
        """测试Column类的特殊方法"""
        # 获取实际的Column对象
        name_column = self.student_table["name"]
        age_column = self.student_table["age"]
        
        # 测试for_row方法
        expr = name_column.for_row(1)
        self.assertIsNotNone(expr)
        
        # 测试get_function方法 - 不带参数
        func = age_column.get_function()
        self.assertIsNotNone(func)
    
    def test_column_constraint_advanced(self):
        """测试ColumnConstraint的高级特性"""
        # 获取实际的Column对象
        age_column = self.student_table["age"]
        gpa_column = self.student_table["gpa"]
        name_column = self.student_table["name"]
        
        # 创建基本约束
        constraint = age_column > 20
        
        # 测试复杂操作
        # 测试与另一个约束的AND操作
        and_constraint = constraint & (gpa_column > 3.0)
        self.assertIsNotNone(and_constraint)
        
        # 测试与另一个约束的OR操作
        or_constraint = constraint | (name_column == "Alice")
        self.assertIsNotNone(or_constraint)
        
        # 测试约束求反操作
        not_constraint = ~constraint
        self.assertIsNotNone(not_constraint)
        
        # 测试as_z3_expr方法，传入row_var
        row_var = Int("test_row")
        z3_expr = constraint.as_z3_expr(row_var)
        self.assertIsNotNone(z3_expr)
    
    def test_add_column_constraint_to_solver(self):
        """测试将ColumnConstraint添加到求解器"""
        age_column = self.student_table["age"]
        constraint = age_column > 20
        self.solver.add(constraint)
        
        # 尝试求解
        result = self.solver.check()
        self.assertIsNotNone(result)
        
        if result == sat:
            model = self.solver.get_model()
            self.assertIsNotNone(model)
    
    def test_solver_add_multiple_expressions(self):
        """测试向求解器添加多个表达式"""
        age_column = self.student_table["age"]
        gpa_column = self.student_table["gpa"]
        is_active_column = self.student_table["is_active"]
        
        expr1 = age_column > 18
        expr2 = gpa_column >= 3.0
        expr3 = is_active_column == True
        
        # 添加多个表达式
        self.solver.add(expr1)
        self.solver.add(expr2)
        self.solver.add(expr3)
        
        # 尝试求解
        result = self.solver.check()
        self.assertIsNotNone(result)
    
    def test_complex_join_conditions(self):
        """测试复杂的JOIN条件"""
        # 创建多表JOIN条件
        id_column = self.student_table["id"]
        student_id_column = self.enrollment_table["student_id"]
        course_id_column = self.enrollment_table["course_id"]
        course_id_column2 = self.course_table["id"]
        
        join1 = id_column == student_id_column
        join2 = course_id_column == course_id_column2
        
        # 添加JOIN条件到求解器
        self.solver.add(join1)
        self.solver.add(join2)
        
        # 尝试求解
        result = self.solver.check()
        self.assertIsNotNone(result)
    
    def test_solver_eval_expressions(self):
        """测试求解器评估表达式的能力"""
        id_column = self.student_table["id"]
        gpa_column = self.student_table["gpa"]
        
        # 添加一些约束
        self.solver.add(id_column == 1)
        
        # 检查求解器状态
        result = self.solver.check()
        if result == sat:
            model = self.solver.get_model()
            
            # 尝试评估不同类型的表达式
            id_value = self.solver.eval(id_column.function(1))
            self.assertIsNotNone(id_value)
            
            # 尝试使用model_completion参数
            expr = gpa_column.function(1)
            value = self.solver.eval(expr, model_completion=True)
            self.assertIsNotNone(value)
    
    def test_table_insert_with_column_references(self):
        """测试表插入操作，使用列引用"""
        # 创建一个新表，引用现有表的数据
        self.derived_table = self.solver.create_table(
            "derived_students", 
            {
                "id": IntSort(),
                "original_name": StringSort(),
                "age_plus_5": IntSort()
            }
        )
        
        # 获取实际的Column对象
        name_column = self.student_table["name"]
        age_column = self.student_table["age"]
        
        # 插入数据，引用现有表的列
        self.derived_table.insert({
            "id": 1,
            "original_name": name_column,  # 引用另一个表的列
            "age_plus_5": Expression(lambda row_var: age_column.function(row_var) + 5, 
                                   self.solver, 
                                   "age + 5")
        })
        
        # 确认插入成功
        self.assertTrue(1 in self.derived_table.rows)
    
    def test_solver_join_condition_methods(self):
        """测试求解器的JOIN条件处理 - 使用相同的表"""
        # 获取实际的Column对象
        id_column = self.student_table["id"]
        student_id_column = self.enrollment_table["student_id"]
        name_column = self.student_table["name"]
        
        # 创建两个有效的JOIN条件
        join1 = id_column == student_id_column  # 标准JOIN条件，表示跨两个表的JOIN
        
        # 使用join_condition_or方法 - 应该使用同类型的表达式
        name_alice = name_column == "Alice"  # 表内过滤条件
        name_bob = name_column == "Bob"  # 另一个表内过滤条件
        
        # 测试join_condition_or与相同类型的表达式
        or_result = self.solver.join_condition_or(name_alice, name_bob)
        self.assertIsNotNone(or_result)
        
        # 测试add添加这些条件
        self.solver.add(join1)
        self.solver.add(or_result)
        
        # 尝试求解
        result = self.solver.check()
        self.assertIsNotNone(result)
    
    def test_simple_expressions_in_z3(self):
        """测试在Z3环境中使用简单表达式"""
        # 获取实际的Column对象
        age_column = self.student_table["age"]
        is_active_column = self.student_table["is_active"]
        
        # 创建简单表达式
        expr1 = age_column + 5
        
        # 创建条件表达式
        condition = is_active_column == True
        row_var = Int("test_row")
        
        # 添加带有行变量的表达式到求解器
        self.solver.add(expr1(row_var) > 25)
        
        # 尝试求解
        result = self.solver.check()
        self.assertIsNotNone(result)
    
    def test_type_conversions(self):
        """测试类型转换"""
        # 获取实际的Column对象
        age_column = self.student_table["age"]
        gpa_column = self.student_table["gpa"]
        name_column = self.student_table["name"]
        is_active_column = self.student_table["is_active"]
        
        # int到string
        int_to_str = age_column.cast_to_string()
        self.assertIsInstance(int_to_str, Expression)
        
        # real到int
        real_to_int = gpa_column.cast_to_int()
        self.assertIsInstance(real_to_int, Expression)
        
        # string到int (如果可以)
        try:
            str_to_int = name_column.cast_to_int()
            self.assertIsInstance(str_to_int, Expression)
        except Exception:
            pass  # 可能会失败，这是预期的
        
        # bool to int
        bool_to_int = is_active_column.cast_to_int()
        self.assertIsInstance(bool_to_int, Expression)

if __name__ == "__main__":
    unittest.main() 