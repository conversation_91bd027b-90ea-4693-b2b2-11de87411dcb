"""
数据血缘分析示例
展示SQL到Z3约束转换在数据血缘分析中的应用
"""

import sys
import unittest
import codecs
from pathlib import Path
from z3 import *

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入SQL到Z3模块
from new_model.core import SqlToZ3

class TestLineageExample(unittest.TestCase):

    def _decode_z3_string(self, z3_str_val):
        if hasattr(z3_str_val, 'as_string'):
            raw_str = z3_str_val.as_string()
            if '\\u{' in raw_str: # 注意双反斜杠匹配文字 '\u{'
                processed_str = raw_str.replace('\\u{', '\\u').replace('}', '')
                try:
                    return codecs.decode(processed_str.encode('utf-8'), 'unicode_escape')
                except UnicodeDecodeError:
                    return raw_str 
            return raw_str
        return str(z3_str_val)

    def setUp(self):
        """每个测试方法运行前执行，用于初始化Z3求解器和表结构"""
        self.z3_sql = SqlToZ3()
        self.source_data = self.z3_sql.create_table("source_data", {
            "id": IntSort(),
            "value": IntSort(),
            "category": StringSort()
        })
        
        self.transformed_data = self.z3_sql.create_table("transformed_data", {
            "id": IntSort(),
            "result": RealSort(), # 原始示例中是RealSort，但在计算时用了1.5，所以保持RealSort
            "source_id": IntSort()
        })
        self.is_data_populated = False # 标记数据是否已填充

    def _populate_all_data(self):
        """辅助方法：填充所有源数据和转换后的数据"""
        if self.is_data_populated:
            return

        # 插入源数据
        self.source_data.insert({"id": 1, "value": 100, "category": "A"})
        self.source_data.insert({"id": 2, "value": 200, "category": "A"})
        self.source_data.insert({"id": 3, "value": 150, "category": "B"})
        self.source_data.insert({"id": 4, "value": 300, "category": "B"})
        
        # 分类A的处理
        self.transformed_data.insert({"id": 101, "result": 200.0, "source_id": 1})
        self.transformed_data.insert({"id": 102, "result": 400.0, "source_id": 2})
        
        # 分类B的处理
        self.transformed_data.insert({"id": 203, "result": 225.0, "source_id": 3}) # 150 * 1.5
        self.transformed_data.insert({"id": 204, "result": 450.0, "source_id": 4}) # 300 * 1.5
        
        # 添加一条特殊记录，模拟错误的转换规则
        # 源ID 3 (Cat B) 应该 id=203, result=225.0
        # 但这里错误地用了: id=103 (源ID+100), result=300.0 (源value*2)
        self.transformed_data.insert({"id": 103, "result": 300.0, "source_id": 3}) 
        self.is_data_populated = True
        print("源数据和转换后数据已填充。")

    def _perform_lineage_query(self, target_transformed_id):
        """辅助方法：执行血缘分析查询，返回模型和相关行变量"""
        transformed_row_var = Int("transformed_row_var")
        self.z3_sql.add(Or([transformed_row_var == i for i in self.transformed_data.rows]))
        
        transformed_id_func = self.z3_sql.column_functions["transformed_data_id"]
        self.z3_sql.add(transformed_id_func(transformed_row_var) == target_transformed_id)
        
        transformed_source_id_func = self.z3_sql.column_functions["transformed_data_source_id"]
        
        source_row_var = Int("source_row_var")
        self.z3_sql.add(Or([source_row_var == i for i in self.source_data.rows]))
        source_id_func_s = self.z3_sql.column_functions["source_data_id"]
        
        self.z3_sql.add(source_id_func_s(source_row_var) == transformed_source_id_func(transformed_row_var))
        
        if self.z3_sql.check() == sat:
            return self.z3_sql.get_model(), source_row_var, transformed_row_var
        return None, None, None

    def test_lineage_for_erroneous_record_103(self):
        """测试追踪ID=103的错误转换记录的血缘"""
        self._populate_all_data()
        print("\n运行测试: test_lineage_for_erroneous_record_103")
        
        model, source_row_v, transformed_row_v = self._perform_lineage_query(103)
        self.assertIsNotNone(model, "应找到ID=103的记录的血缘")
        
        source_row = model[source_row_v].as_long()
        transformed_row = model[transformed_row_v].as_long()

        # 获取字段函数
        sid_s_func = self.z3_sql.column_functions["source_data_id"]
        sval_s_func = self.z3_sql.column_functions["source_data_value"]
        scat_s_func = self.z3_sql.column_functions["source_data_category"]
        tid_t_func = self.z3_sql.column_functions["transformed_data_id"]
        tres_t_func = self.z3_sql.column_functions["transformed_data_result"]

        # 验证转换后数据
        self.assertEqual(model.eval(tid_t_func(transformed_row)).as_long(), 103)
        self.assertEqual(float(model.eval(tres_t_func(transformed_row)).as_decimal(2)), 300.0)

        # 验证源数据
        source_id_val = model.eval(sid_s_func(source_row)).as_long()
        source_value_val = model.eval(sval_s_func(source_row)).as_long()
        source_category_val = self._decode_z3_string(model.eval(scat_s_func(source_row)))
        
        self.assertEqual(source_id_val, 3)
        self.assertEqual(source_value_val, 150)
        self.assertEqual(source_category_val, "B")
        
        print(f"血缘分析结果 for t.id=103: 源 s.id={source_id_val}, s.value={source_value_val}, s.category='{source_category_val}'")
        
        # 验证转换规则是否被错误应用
        expected_id_cat_b = source_id_val + 200
        expected_result_cat_b = source_value_val * 1.5
        
        # 实际应用的（错误的）是类别A的规则
        applied_id_cat_a = source_id_val + 100 
        applied_result_cat_a = source_value_val * 2.0

        actual_transformed_id = model.eval(tid_t_func(transformed_row)).as_long()
        actual_transformed_result = float(model.eval(tres_t_func(transformed_row)).as_decimal(2))

        self.assertEqual(actual_transformed_id, applied_id_cat_a, "ID应按类别A的规则错误计算")
        self.assertEqual(actual_transformed_result, applied_result_cat_a, "结果应按类别A的规则错误计算")
        self.assertNotEqual(actual_transformed_id, expected_id_cat_b, "ID不应按类别B的正确规则计算")
        self.assertNotEqual(actual_transformed_result, expected_result_cat_b, "结果不应按类别B的正确规则计算")
        print("成功验证ID=103的记录是源ID=3（类别B）但错误应用了类别A的转换规则。")

    def test_lineage_for_correct_record_cat_A(self):
        """测试追踪ID=101的正确转换记录的血缘 (类别A)"""
        self._populate_all_data()
        print("\n运行测试: test_lineage_for_correct_record_cat_A")

        model, source_row_v, transformed_row_v = self._perform_lineage_query(101)
        self.assertIsNotNone(model, "应找到ID=101的记录的血缘")

        source_row = model[source_row_v].as_long()
        transformed_row = model[transformed_row_v].as_long()

        sid_s_func = self.z3_sql.column_functions["source_data_id"]
        sval_s_func = self.z3_sql.column_functions["source_data_value"]
        scat_s_func = self.z3_sql.column_functions["source_data_category"]
        tid_t_func = self.z3_sql.column_functions["transformed_data_id"]
        tres_t_func = self.z3_sql.column_functions["transformed_data_result"]

        self.assertEqual(model.eval(tid_t_func(transformed_row)).as_long(), 101)
        self.assertEqual(float(model.eval(tres_t_func(transformed_row)).as_decimal(2)), 200.0)

        source_id_val = model.eval(sid_s_func(source_row)).as_long()
        source_value_val = model.eval(sval_s_func(source_row)).as_long()
        source_category_val = self._decode_z3_string(model.eval(scat_s_func(source_row)))

        self.assertEqual(source_id_val, 1)
        self.assertEqual(source_value_val, 100)
        self.assertEqual(source_category_val, "A")
        print(f"血缘分析结果 for t.id=101: 源 s.id={source_id_val}, s.value={source_value_val}, s.category='{source_category_val}'")

        expected_id = source_id_val + 100
        expected_result = source_value_val * 2.0
        actual_transformed_id = model.eval(tid_t_func(transformed_row)).as_long()
        actual_transformed_result = float(model.eval(tres_t_func(transformed_row)).as_decimal(2))

        self.assertEqual(actual_transformed_id, expected_id)
        self.assertEqual(actual_transformed_result, expected_result)
        print("成功验证ID=101的记录正确应用了类别A的转换规则。")

    def test_lineage_for_correct_record_cat_B(self):
        """测试追踪ID=203的正确转换记录的血缘 (类别B)"""
        self._populate_all_data()
        print("\n运行测试: test_lineage_for_correct_record_cat_B")

        model, source_row_v, transformed_row_v = self._perform_lineage_query(203)
        self.assertIsNotNone(model, "应找到ID=203的记录的血缘")

        source_row = model[source_row_v].as_long()
        transformed_row = model[transformed_row_v].as_long()

        sid_s_func = self.z3_sql.column_functions["source_data_id"]
        sval_s_func = self.z3_sql.column_functions["source_data_value"]
        scat_s_func = self.z3_sql.column_functions["source_data_category"]
        tid_t_func = self.z3_sql.column_functions["transformed_data_id"]
        tres_t_func = self.z3_sql.column_functions["transformed_data_result"]

        self.assertEqual(model.eval(tid_t_func(transformed_row)).as_long(), 203)
        self.assertEqual(float(model.eval(tres_t_func(transformed_row)).as_decimal(2)), 225.0)

        source_id_val = model.eval(sid_s_func(source_row)).as_long()
        source_value_val = model.eval(sval_s_func(source_row)).as_long()
        source_category_val = self._decode_z3_string(model.eval(scat_s_func(source_row)))

        self.assertEqual(source_id_val, 3)
        self.assertEqual(source_value_val, 150)
        self.assertEqual(source_category_val, "B")
        print(f"血缘分析结果 for t.id=203: 源 s.id={source_id_val}, s.value={source_value_val}, s.category='{source_category_val}'")
        
        expected_id = source_id_val + 200
        expected_result = source_value_val * 1.5
        actual_transformed_id = model.eval(tid_t_func(transformed_row)).as_long()
        actual_transformed_result = float(model.eval(tres_t_func(transformed_row)).as_decimal(2))

        self.assertEqual(actual_transformed_id, expected_id)
        self.assertEqual(actual_transformed_result, expected_result)
        print("成功验证ID=203的记录正确应用了类别B的转换规则。")

if __name__ == "__main__":
    unittest.main() 