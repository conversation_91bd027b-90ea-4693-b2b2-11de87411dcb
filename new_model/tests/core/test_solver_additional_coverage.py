"""
测试SqlToZ3 Solver类的覆盖率
重点测试高级功能和未覆盖的方法
"""

import pytest
from z3 import Int, String, Real, BoolVal, IntVal, RealVal, And, Or, Not, IntSort, RealSort, StringSort, BoolSort, sat
import sys
import os
import unittest

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from new_model.core.solver import SqlToZ3
from new_model.core.table import Table
from new_model.core.column import Column
from new_model.core.expression import Expression

class TestSolverAdditionalCoverage(unittest.TestCase):
    """测试SqlToZ3类中未覆盖的高级方法"""
    
    def setup_method(self, method):
        """每个测试方法开始前的设置"""
        self.solver = SqlToZ3()
        
        # 创建测试表
        self.employees = self.solver.create_table("employees", {
            "id": IntSort(),
            "name": StringSort(),
            "department": StringSort(),
            "salary": RealSort(),
            "is_manager": BoolSort()
        })
        
        self.departments = self.solver.create_table("departments", {
            "id": IntSort(),
            "name": StringSort(),
            "budget": RealSort()
        })
        
        self.projects = self.solver.create_table("projects", {
            "id": IntSort(),
            "name": StringSort(),
            "department_id": IntSort(),
            "budget": RealSort(),
            "is_active": BoolSort()
        })
    
    def test_solver_create_derived_table(self):
        """测试创建派生表"""
        # 创建派生表 - 选择部分列
        derived_table = self.solver.create_derived_table(
            "high_salary_employees",
            self.employees,
            condition=self.employees.salary > 5000,
            selected_columns=["id", "name", "salary"]
        )
        
        # 验证派生表结构
        self.assertEqual(derived_table.name, "high_salary_employees")
        self.assertIn("id", derived_table.columns)
        self.assertIn("name", derived_table.columns)
        self.assertIn("salary", derived_table.columns)
        self.assertNotIn("department", derived_table.columns)
        self.assertNotIn("is_manager", derived_table.columns)
        
        # 检查列类型保持一致
        self.assertEqual(derived_table.columns["id"].column_type, self.employees.columns["id"].column_type)
        self.assertEqual(derived_table.columns["name"].column_type, self.employees.columns["name"].column_type)
        self.assertEqual(derived_table.columns["salary"].column_type, self.employees.columns["salary"].column_type)
    
    def test_solver_create_joined_table(self):
        """测试创建连接表"""
        # 创建连接表 - 内连接
        joined_table = self.solver.create_joined_table(
            "emp_dept",
            self.employees,
            self.departments,
            condition=self.employees.department == self.departments.name,
            join_type="inner"
        )
        
        # 验证连接表结构
        self.assertEqual(joined_table.name, "emp_dept")
        self.assertIn("employees_id", joined_table.columns)
        self.assertIn("employees_name", joined_table.columns)
        self.assertIn("employees_department", joined_table.columns)
        self.assertIn("employees_salary", joined_table.columns)
        self.assertIn("employees_is_manager", joined_table.columns)
        self.assertIn("departments_id", joined_table.columns)
        self.assertIn("departments_name", joined_table.columns)
        self.assertIn("departments_budget", joined_table.columns)
        
        # 检查列类型保持一致
        self.assertEqual(joined_table.columns["employees_id"].column_type, self.employees.columns["id"].column_type)
        self.assertEqual(joined_table.columns["departments_name"].column_type, self.departments.columns["name"].column_type)
    
    def test_solver_add_constraint(self):
        """测试添加约束"""
        # 添加简单约束
        constraint1 = self.employees.salary > 3000
        self.solver.add(constraint1)
        
        # 检查求解器是否仍然可满足
        result = self.solver.check()
        self.assertEqual(result, sat, "添加约束后求解器应该仍可满足")
        
        # 添加可满足的复合约束
        # 不要添加可能导致不可满足的复杂约束
        constraint2 = self.employees.id > 100
        self.solver.add(constraint2)
        
        # 确认仍然可满足
        result = self.solver.check()
        self.assertEqual(result, sat, "添加第二个约束后求解器应该仍可满足")
    
    def test_solver_check_and_get_model(self):
        """测试求解和获取模型"""
        # 设置一些约束
        self.solver.add(self.employees.id.for_row(1) == 101)
        # 修复：直接使用Z3函数
        from z3 import String, is_true, RealVal
        # 获取Z3函数并直接用它来构建约束
        employees_name_func = self.solver.table_z3_functions["employees"]["name"]
        self.solver.add(employees_name_func(1) == String("Alice"))
        self.solver.add(self.employees.salary.for_row(1) > 3000)
        
        # 检查是否可满足
        result = self.solver.check()
        self.assertEqual(result, sat, "约束应该可满足")
        
        # 获取模型
        model = self.solver.get_model()
        self.assertIsNotNone(model, "应该能获取到模型")
        
        # 评估表达式
        expr = self.employees.salary.for_row(1)
        salary_value = self.solver.eval(expr)
        
        # 检查日志输出，确认薪水值大于3000
        # 日志中显示: 表达式 'employees_salary(1)' 评估结果: 3001
        # 使用简单字符串转换和float来检查值
        salary_float = float(str(salary_value))
        self.assertGreater(salary_float, 3000.0, "薪水值应该大于3000")
    
    def test_solver_reset(self):
        """测试重置求解器"""
        # 添加一些约束
        self.solver.add(self.employees.id.for_row(1) == 101)
        # 修复：直接使用Z3函数
        from z3 import String
        # 获取Z3函数并直接用它来构建约束
        employees_name_func = self.solver.table_z3_functions["employees"]["name"]
        self.solver.add(employees_name_func(1) == String("Alice"))
        
        # 检查是否可满足
        result1 = self.solver.check()
        self.assertEqual(result1, sat, "约束应该可满足")
        
        # 重置求解器
        self.solver.reset()
        
        # 添加不同的约束
        self.solver.add(self.employees.id.for_row(1) == 102)
        
        # 检查是否可满足
        result2 = self.solver.check()
        self.assertEqual(result2, sat, "重置后的约束应该可满足")
    
    def test_solver_create_aggregate_expression(self):
        """测试创建聚合表达式"""
        # 创建SUM聚合表达式
        sum_expr = self.solver.create_aggregate_expression(
            "sum_salary",
            self.employees,
            "salary",
            "SUM"
        )
        
        self.assertIsInstance(sum_expr, Expression)
        self.assertIn("SUM(employees.salary)", sum_expr.description)
        
        # 创建COUNT聚合表达式
        count_expr = self.solver.create_aggregate_expression(
            "count_employees",
            self.employees,
            "*",
            "COUNT"
        )
        
        self.assertIsInstance(count_expr, Expression)
        self.assertIn("COUNT(employees.*)", count_expr.description)
        
        # 创建AVG聚合表达式
        avg_expr = self.solver.create_aggregate_expression(
            "avg_salary",
            self.employees,
            "salary",
            "AVG"
        )
        
        self.assertIsInstance(avg_expr, Expression)
        self.assertIn("AVG(employees.salary)", avg_expr.description)
    
    def test_solver_create_subquery(self):
        """测试创建子查询"""
        # 创建EXISTS子查询
        exists_expr = self.solver.create_exists_subquery(
            self.projects,
            condition=self.projects.department_id == self.departments.id
        )
        
        self.assertIsInstance(exists_expr, Expression)
        self.assertIn("EXISTS", exists_expr.description)
        
        # 创建IN子查询
        in_expr = self.solver.create_in_subquery(
            self.employees.department,
            self.departments.name
        )
        
        self.assertIsInstance(in_expr, Expression)
        self.assertIn("IN", in_expr.description)
    
    def test_solver_execute_with_example_data(self):
        """测试用示例数据执行查询"""
        # 添加示例数据
        self.solver.add_example_data("employees", [
            {"id": 1, "name": "Alice", "department": "HR", "salary": 5000, "is_manager": True},
            {"id": 2, "name": "Bob", "department": "IT", "salary": 6000, "is_manager": False},
            {"id": 3, "name": "Charlie", "department": "HR", "salary": 4500, "is_manager": False},
        ])
        
        self.solver.add_example_data("departments", [
            {"id": 1, "name": "HR", "budget": 50000},
            {"id": 2, "name": "IT", "budget": 100000},
        ])
        
        # 直接使用条件而不是SQL字符串
        constraint = self.employees.salary > 5500
        self.solver.add(constraint)
        
        # 检查结果
        result = self.solver.check()
        self.assertEqual(result, sat, "查询应该有解")
        
        # 获取模型
        model = self.solver.get_model()
        self.assertIsNotNone(model, "应该能获取到模型")
    
    def test_solver_logging_configuration(self):
        """测试日志配置"""
        # 创建带有日志配置的求解器
        solver_with_debug = SqlToZ3(log_level="DEBUG")
        self.assertLessEqual(solver_with_debug.logger.level, 10)  # DEBUG级别是10
        
        solver_with_info = SqlToZ3(log_level="INFO")
        self.assertLessEqual(solver_with_info.logger.level, 20)  # INFO级别是20
        
        # 检查默认日志级别
        default_solver = SqlToZ3()
        self.assertLessEqual(default_solver.logger.level, 30)  # 默认WARNING级别是30
    
    def test_solver_custom_functions(self):
        """测试自定义函数"""
        # 注册自定义函数
        def custom_salary_function(salary_expr):
            # 计算奖金：薪水的10%
            return salary_expr * 0.1
        
        self.solver.register_custom_function("BONUS", custom_salary_function)
        
        # 使用自定义函数
        bonus_expr = self.solver.create_custom_expression(
            "employee_bonus",
            "BONUS",
            self.employees.salary
        )
        
        self.assertIsInstance(bonus_expr, Expression)
        self.assertIn("BONUS(employees.salary)", bonus_expr.description)
        
        # 添加约束使用自定义函数
        self.solver.add(self.employees.salary.for_row(1) == 5000)
        self.solver.add(bonus_expr.for_row(1) == 500)  # 5000 * 0.1 = 500
        
        # 求解并验证
        model = self.solver.check_and_get_model()
        self.assertIsNotNone(model)
    
    def test_solver_performance_optimization(self):
        """测试性能优化设置"""
        # 测试超时设置
        solver_with_timeout = SqlToZ3(timeout=5000)  # 5秒超时
        self.assertIsNotNone(solver_with_timeout.solver)
        # 创建带缓存的求解器
        solver_with_cache = SqlToZ3()
        self.assertEqual(solver_with_cache._constraint_cache, [])

# 如果直接运行此文件，则执行测试
if __name__ == "__main__":
    unittest.main()