"""
字段级血缘分析示例
展示SQL到Z3约束转换在字段级血缘分析中的应用
"""

import sys
import unittest
import codecs
from pathlib import Path
from z3 import *

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入SQL到Z3模块
from new_model.core import SqlToZ3

class TestFieldLineageExample(unittest.TestCase):

    def _decode_z3_string(self, z3_str_val):
        if hasattr(z3_str_val, 'as_string'):
            raw_str = z3_str_val.as_string()
            if '\\\\u{' in raw_str:
                processed_str = raw_str.replace('\\\\u{', '\\\\u').replace('}', '')
                try:
                    return codecs.decode(processed_str.encode('utf-8'), 'unicode_escape')
                except UnicodeDecodeError:
                    return raw_str # Fallback if decode fails
            return raw_str
        return str(z3_str_val) # Fallback for non-string Z3 objects

    def setUp(self):
        self.z3_sql = SqlToZ3()
        self.users_system_a = self.z3_sql.create_table("users_system_a", {
            "id": IntSort(), "name": StringSort(), "age": IntSort(), "score": IntSort(), "source": StringSort()
        })
        self.users_system_b = self.z3_sql.create_table("users_system_b", {
            "id": IntSort(), "name": StringSort(), "age": IntSort(), "score": IntSort(), "source": StringSort()
        })
        self.users_combined = self.z3_sql.create_table("users_combined", {
            "id": IntSort(), "name": StringSort(), "age": IntSort(), "score": IntSort(), "source": StringSort()
        })
        self.users_processed = self.z3_sql.create_table("users_processed", {
            "id": IntSort(), "name": StringSort(), "age_group": StringSort(), 
            "score_normalized": RealSort(), "source": StringSort()
        })
        # analysis_results 表的测试超出了当前对行级约束的验证范围，此处仅定义结构
        self.analysis_results = self.z3_sql.create_table("analysis_results", {
            "age_group": StringSort(), "avg_score": RealSort(), "user_count": IntSort()
        })

        # 初始数据
        self.data_a = [
            {"id": 1, "name": "Alice", "age": 25, "score": 85, "source": "A"},
            {"id": 2, "name": "Bob", "age": 32, "score": 92, "source": "A"},
            {"id": 3, "name": "Charlie", "age": 19, "score": 78, "source": "A"}
        ]
        self.data_b = [
            {"id": 101, "name": "David", "age": 45, "score": 65, "source": "B"},
            {"id": 102, "name": "Eve", "age": 28, "score": 88, "source": "B"},
            {"id": 103, "name": "Frank", "age": 36, "score": 72, "source": "B"}
        ]
        self.inserted_a_rows = []
        self.inserted_b_rows = []
        for record in self.data_a:
            self.inserted_a_rows.append(self.users_system_a.insert(record))
        for record in self.data_b:
            self.inserted_b_rows.append(self.users_system_b.insert(record))
        
        # 检查初始数据插入
        self.assertEqual(self.z3_sql.check(), sat)
        model = self.z3_sql.get_model()
        # 验证 users_system_a 的第一条记录
        name_a1 = self._decode_z3_string(model.eval(self.z3_sql.column_functions["users_system_a_name"](self.inserted_a_rows[0])))
        self.assertEqual(name_a1, "Alice")

    def test_union_all_to_combined(self):
        print("\n运行测试: test_union_all_to_combined")
        print("-" * 50)
        
        # 从系统A复制到combined
        combined_rows_from_a = []
        for row_a_id in self.inserted_a_rows:
            combined_row_id = self.users_combined.insert({})
            combined_rows_from_a.append(combined_row_id)
            for col in ["id", "name", "age", "score", "source"]:
                func_a = self.z3_sql.column_functions[f"users_system_a_{col}"]
                func_c = self.z3_sql.column_functions[f"users_combined_{col}"]
                self.z3_sql.add(func_c(combined_row_id) == func_a(row_a_id))

        # 从系统B复制到combined
        combined_rows_from_b = []
        for row_b_id in self.inserted_b_rows:
            combined_row_id = self.users_combined.insert({})
            combined_rows_from_b.append(combined_row_id)
            for col in ["id", "name", "age", "score", "source"]:
                func_b = self.z3_sql.column_functions[f"users_system_b_{col}"]
                func_c = self.z3_sql.column_functions[f"users_combined_{col}"]
                self.z3_sql.add(func_c(combined_row_id) == func_b(row_b_id))

        self.assertEqual(self.z3_sql.check(), sat)
        model = self.z3_sql.get_model()

        # 验证来自A的数据
        name_c_a1 = self._decode_z3_string(model.eval(self.z3_sql.column_functions["users_combined_name"](combined_rows_from_a[0])))
        age_c_a1 = model.eval(self.z3_sql.column_functions["users_combined_age"](combined_rows_from_a[0])).as_long()
        source_c_a1 = self._decode_z3_string(model.eval(self.z3_sql.column_functions["users_combined_source"](combined_rows_from_a[0])))
        self.assertEqual(name_c_a1, "Alice")
        self.assertEqual(age_c_a1, 25)
        self.assertEqual(source_c_a1, "A")

        # 验证来自B的数据 (取David)
        name_c_b1 = self._decode_z3_string(model.eval(self.z3_sql.column_functions["users_combined_name"](combined_rows_from_b[0])))
        age_c_b1 = model.eval(self.z3_sql.column_functions["users_combined_age"](combined_rows_from_b[0])).as_long()
        source_c_b1 = self._decode_z3_string(model.eval(self.z3_sql.column_functions["users_combined_source"](combined_rows_from_b[0])))
        self.assertEqual(name_c_b1, "David")
        self.assertEqual(age_c_b1, 45)
        self.assertEqual(source_c_b1, "B")
        
        # 保存 combined_rows for a后续的测试
        self.all_combined_rows = combined_rows_from_a + combined_rows_from_b
        self.original_data_combined = self.data_a + self.data_b

    def test_data_processing_to_processed(self):
        print("\n运行测试: test_data_processing_to_processed")
        print("-" * 50)
        # 先执行UNION ALL步骤以填充users_combined
        self.test_union_all_to_combined() 
        # 注意: self.all_combined_rows 和 self.original_data_combined 已被填充

        processed_rows_map = {} # original_combined_row_id -> processed_row_id

        for idx, combined_row_id in enumerate(self.all_combined_rows):
            original_record = self.original_data_combined[idx]
            processed_row_id = self.users_processed.insert({})
            processed_rows_map[combined_row_id] = processed_row_id

            id_c_func = self.z3_sql.column_functions["users_combined_id"]
            name_c_func = self.z3_sql.column_functions["users_combined_name"]
            age_c_func = self.z3_sql.column_functions["users_combined_age"]
            score_c_func = self.z3_sql.column_functions["users_combined_score"]
            source_c_func = self.z3_sql.column_functions["users_combined_source"]

            id_d_func = self.z3_sql.column_functions["users_processed_id"]
            name_d_func = self.z3_sql.column_functions["users_processed_name"]
            age_group_d_func = self.z3_sql.column_functions["users_processed_age_group"]
            score_norm_d_func = self.z3_sql.column_functions["users_processed_score_normalized"]
            source_d_func = self.z3_sql.column_functions["users_processed_source"]

            # 直接复制 id, name, source
            self.z3_sql.add(id_d_func(processed_row_id) == id_c_func(combined_row_id))
            self.z3_sql.add(name_d_func(processed_row_id) == name_c_func(combined_row_id))
            self.z3_sql.add(source_d_func(processed_row_id) == source_c_func(combined_row_id))

            # score_normalized
            self.z3_sql.add(score_norm_d_func(processed_row_id) == ToReal(score_c_func(combined_row_id)) / RealVal(100.0))

            # age_group
            age_val = age_c_func(combined_row_id)
            age_group_z3_val = If(age_val < 20, StringVal("teen"),
                                If(And(age_val >= 20, age_val <= 30), StringVal("young_adult"),
                                   If(And(age_val >= 31, age_val <= 40), StringVal("adult"),
                                      StringVal("senior"))))
            self.z3_sql.add(age_group_d_func(processed_row_id) == age_group_z3_val)

        self.assertEqual(self.z3_sql.check(), sat)
        model = self.z3_sql.get_model()

        # 验证加工后的数据 - 随机抽样几个进行验证
        # 验证Alice (id=1, age=25, score=85, source=A)
        # -> age_group='young_adult', score_normalized=0.85
        alice_orig_combined_row_id = self.all_combined_rows[0] # Alice是第一个
        alice_processed_row_id = processed_rows_map[alice_orig_combined_row_id]
        
        alice_p_id = model.eval(id_d_func(alice_processed_row_id)).as_long()
        alice_p_name = self._decode_z3_string(model.eval(name_d_func(alice_processed_row_id)))
        alice_p_age_group = self._decode_z3_string(model.eval(age_group_d_func(alice_processed_row_id)))
        alice_p_score_norm = model.eval(score_norm_d_func(alice_processed_row_id))
        alice_p_source = self._decode_z3_string(model.eval(source_d_func(alice_processed_row_id)))

        self.assertEqual(alice_p_id, 1)
        self.assertEqual(alice_p_name, "Alice")
        self.assertEqual(alice_p_age_group, "young_adult")
        self.assertEqual(alice_p_score_norm.as_decimal(2), "0.85") # as_decimal(2) should give "0.85"
        self.assertEqual(alice_p_source, "A")

        # 验证Charlie (id=3, age=19, score=78, source=A)
        # -> age_group='teen', score_normalized=0.78
        charlie_orig_combined_row_id = self.all_combined_rows[2] # Charlie是第三个
        charlie_processed_row_id = processed_rows_map[charlie_orig_combined_row_id]

        charlie_p_age_group = self._decode_z3_string(model.eval(age_group_d_func(charlie_processed_row_id)))
        charlie_p_score_norm = model.eval(score_norm_d_func(charlie_processed_row_id))
        self.assertEqual(charlie_p_age_group, "teen")
        self.assertEqual(charlie_p_score_norm.as_decimal(2), "0.78")

        # 验证David (id=101, age=45, score=65, source=B)
        # -> age_group='senior', score_normalized=0.65
        david_orig_combined_row_id = self.all_combined_rows[3] # David是第四个 (data_a有3个)
        david_processed_row_id = processed_rows_map[david_orig_combined_row_id]

        david_p_age_group = self._decode_z3_string(model.eval(age_group_d_func(david_processed_row_id)))
        david_p_score_norm = model.eval(score_norm_d_func(david_processed_row_id))
        self.assertEqual(david_p_age_group, "senior")
        self.assertEqual(david_p_score_norm.as_decimal(2), "0.65")
        
        self.processed_rows_map = processed_rows_map # Save for potential next step

    def test_final_analysis_filter_source_a(self):
        print("\n运行测试: test_final_analysis_filter_source_a")
        print("-" * 50)
        # 先执行数据加工步骤以填充users_processed
        self.test_data_processing_to_processed()
        # 注意: self.processed_rows_map 已被填充

        # 添加约束：只考虑 source = 'A' 的数据
        # 遍历所有 processed rows，添加约束如果其 source != 'A'，则该行无效 (或者只对 source == 'A' 的行进行后续操作)
        # 这里更简单的做法是为最终的 analysis_results 创建一个代表性行，并约束其输入必须来自 source 'A'
        # 由于我们不直接填充 analysis_results，我们只验证添加 source='A' 约束后是否仍有解
        
        # 创建一个虚拟的行变量，代表一个来自 processed 表且 source 为 'A' 的行
        processed_row_for_analysis = Int("processed_row_for_analysis_A")
        
        # 约束这个行变量必须是 users_processed 表中的有效行ID之一
        valid_processed_ids_cond = Or([processed_row_for_analysis == r_id for r_id in self.processed_rows_map.values()])
        self.z3_sql.add(valid_processed_ids_cond)

        # 约束这个行的 source 必须是 'A'
        source_d_func = self.z3_sql.column_functions["users_processed_source"]
        self.z3_sql.add(source_d_func(processed_row_for_analysis) == StringVal("A"))

        check_res = self.z3_sql.check()
        self.assertEqual(check_res, sat, "应该能找到source='A'的处理后数据")

        if check_res == sat:
            model = self.z3_sql.get_model()
            # 我们可以获取一个这样的行，并验证其 source
            example_processed_row_A_id = model.eval(processed_row_for_analysis)
            actual_source = self._decode_z3_string(model.eval(source_d_func(example_processed_row_A_id)))
            self.assertEqual(actual_source, "A")
            print(f"找到一个source='A'的processed记录，行ID: {example_processed_row_A_id}, source: {actual_source}")

            # 进一步验证这条记录是否真的来自原始数据A中的某条记录
            # 这需要更复杂的反向血缘追踪，超出了此单元测试的简单验证范围
            # 但至少我们验证了存在 source='A' 的处理后数据

        print("最终分析表的填充和聚合结果验证超出了当前单元测试范围。")
        print("已验证：存在 source = 'A' 的处理后数据可用于分析。")

if __name__ == '__main__':
    unittest.main()


"""
原文件内容:

def run_field_lineage_example():
    ...
    (省略原始函数体)
""" 