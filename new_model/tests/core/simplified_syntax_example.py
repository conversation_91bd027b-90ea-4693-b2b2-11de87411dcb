"""
简化语法示例
展示SQL到Z3约束转换中的简化语法用法
"""

import sys
import unittest
import codecs
from pathlib import Path
from z3 import *

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# 导入SQL到Z3模块
from new_model.core import SqlToZ3

class TestSimplifiedSyntaxExample(unittest.TestCase):

    def _decode_z3_string(self, z3_str_val):
        if hasattr(z3_str_val, 'as_string'):
            raw_str = z3_str_val.as_string()
            if '\\\\u{' in raw_str:
                processed_str = raw_str.replace('\\\\u{', '\\\\u').replace('}', '')
                try:
                    return codecs.decode(processed_str.encode('utf-8'), 'unicode_escape')
                except UnicodeDecodeError:
                    return raw_str
            return raw_str
        return str(z3_str_val)

    def _create_products_table(self, z3_sql_instance):
        products = z3_sql_instance.create_table("products", {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort(),
            "stock": IntSort(),
            "category": StringSort()
        })
        products.insert({"id": 1, "name": "Laptop", "price": 5000.0, "stock": 10, "category": "Electronics"})
        products.insert({"id": 2, "name": "Phone", "price": 3000.0, "stock": 20, "category": "Electronics"})
        products.insert({"id": 3, "name": "Headphones", "price": 500.0, "stock": 50, "category": "Accessories"})
        products.insert({"id": 4, "name": "Mouse", "price": 200.0, "stock": 100, "category": "Accessories"})
        return products

    def _create_join_tables(self, z3_sql_instance):
        products = z3_sql_instance.create_table("products", {
            "id": IntSort(), "name": StringSort(), "price": RealSort(), 
            "stock": IntSort(), "category": StringSort()
        })
        customers = z3_sql_instance.create_table("customers", {"id": IntSort(), "name": StringSort()})
        orders = z3_sql_instance.create_table("orders", {
            "id": IntSort(), "customer_id": IntSort(), "product_id": IntSort(), "quantity": IntSort()
        })

        products.insert({"id": 1, "name": "Laptop", "price": 5000.0, "stock": 10, "category": "Electronics"})
        products.insert({"id": 2, "name": "Phone", "price": 3000.0, "stock": 20, "category": "Electronics"})
        customers.insert({"id": 101, "name": "John"})
        customers.insert({"id": 102, "name": "Mike"})
        orders.insert({"id": 1001, "customer_id": 101, "product_id": 1, "quantity": 1})
        orders.insert({"id": 1002, "customer_id": 102, "product_id": 2, "quantity": 2})
        return customers, orders, products

    def test_single_condition_price(self):
        print("\n运行测试: test_single_condition_price")
        print("-" * 50)
        z3_sql = SqlToZ3()
        products = self._create_products_table(z3_sql)
        
        z3_sql.add(products.price > 2000.0)
        
        check_res = z3_sql.check()
        self.assertEqual(check_res, sat)

        if check_res == sat:
            model = z3_sql.get_model()
            found_count = 0
            expected_products = [("Laptop", "5000"), ("Phone", "3000")] # (name, price_str)
            
            product_name_func = z3_sql.column_functions["products_name"]
            product_price_func = z3_sql.column_functions["products_price"]

            for p_row_id in products.rows:
                price_val = model.eval(product_price_func(p_row_id))
                if is_true(model.eval(price_val > RealVal(2000.0))):
                    name_val = self._decode_z3_string(model.eval(product_name_func(p_row_id)))
                    print(f"满足条件的产品: {name_val}, 价格: {price_val.as_decimal(2)}")
                    self.assertIn((name_val, price_val.as_decimal(2)), expected_products)
                    found_count += 1
            self.assertEqual(found_count, 2, "应找到2个价格大于2000的产品")

    def test_join_and_filter_customer_product_price(self):
        print("\n运行测试: test_join_and_filter_customer_product_price")
        print("-" * 50)
        z3_sql = SqlToZ3()
        customers, orders, products = self._create_join_tables(z3_sql)

        print("对应SQL: SELECT c.name FROM customers c JOIN orders o ON c.id = o.customer_id JOIN products p ON o.product_id = p.id WHERE p.price > 4000")
        
        customers_orders_join_vars = customers.id == orders.customer_id
        orders_products_join_vars = orders.product_id == products.id
        z3_sql.add(customers_orders_join_vars) # Implicitly creates constraints via SqlToZ3.add for tuple
        z3_sql.add(orders_products_join_vars)
        
        cust_row_var = customers_orders_join_vars[0]
        # order_row_var_from_co = customers_orders_join_vars[2]
        # order_row_var_from_op = orders_products_join_vars[0]
        prod_row_var = orders_products_join_vars[2]
        # z3_sql.add(order_row_var_from_co == order_row_var_from_op) # Ensured by how JOINs are handled if add takes tuples

        z3_sql.add(products.price > 4000.0)
        
        check_res = z3_sql.check()
        self.assertEqual(check_res, sat)

        if check_res == sat:
            model = z3_sql.get_model()
            cust_row_id = model.eval(cust_row_var) # Z3 IntVal representing the row ID
            prod_row_id = model.eval(prod_row_var)
            
            customer_name_func = z3_sql.column_functions["customers_name"]
            product_name_func = z3_sql.column_functions["products_name"]
            product_price_func = z3_sql.column_functions["products_price"]
            
            cust_name = self._decode_z3_string(model.eval(customer_name_func(cust_row_id)))
            prod_name = self._decode_z3_string(model.eval(product_name_func(prod_row_id)))
            prod_price = model.eval(product_price_func(prod_row_id))
            
            print(f"查询结果: 客户名称: {cust_name}, 购买产品: {prod_name}, 产品价格: {prod_price.as_decimal(2)}")
            self.assertEqual(cust_name, "John")
            self.assertEqual(prod_name, "Laptop")
            self.assertEqual(prod_price.as_decimal(2), "5000")

    def test_unsatisfiable_customer_id(self):
        print("\n运行测试: test_unsatisfiable_customer_id")
        print("-" * 50)
        z3_sql = SqlToZ3()
        customers = z3_sql.create_table("customers", {"id": IntSort(), "name": StringSort()})
        customers.insert({"id": 101, "name": "John"})
        customers.insert({"id": 102, "name": "Mike"})
        
        z3_sql.add(customers.id == 103)
        
        check_res = z3_sql.check()
        self.assertEqual(check_res, unsat)
        if check_res == sat:
            self.fail("预期unsat，但得到了sat")
        else:
            print("查询没有结果（符合预期）")

if __name__ == '__main__':
    unittest.main() 