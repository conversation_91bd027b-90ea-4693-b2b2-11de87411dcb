# SQL到Z3约束转换器

本目录展示了SQL语义到Z3约束的映射，用于数据血缘分析与验证。通过将SQL的数据操作转换为Z3约束，可以利用Z3求解器分析数据流转、验证数据约束，以及执行假设性分析。

其关键能力是引入了行变量的概念，使得原本静态的表内数据关系变为动态关系。比如同一个表如果join自己，关联条件 tbl.col1==tbl.col2, 如果只考虑字段相等的约束，则要求表中这两个字段必须存在一条记录相等，现在引入行变量的概念，这个条件实际指的是，可以是两条不同的记录，使得 col1==col2，来源于不同的记录。

## 1. 核心功能

- SQL表结构到Z3约束的转换
- 行级和字段级数据血缘分析
- 复杂查询条件（WHERE, JOIN）的约束建模
- 多表连接关系的表达与分析
- 支持假设情景分析（what-if analysis）

## 2. 使用示例

### 2.1 基本用法

```python
from new_model.sql_to_z3 import SqlToZ3
from z3 import *

# 创建转换器
z3_sql = SqlToZ3()

# 创建表结构
customers = z3_sql.create_table("customers", {
    "id": IntSort(),
    "name": StringSort(),
    "age": IntSort()
})

# 插入数据
customers.insert({"id": 1, "name": "张三", "age": 30})
customers.insert({"id": 2, "name": "李四", "age": 25})

# 添加约束条件
z3_sql.add(customers.age > 20)

# 检查约束是否可满足
if z3_sql.check() == sat:
    model = z3_sql.get_model()
    # 分析结果...
```

### 2.2 高级示例

- 字段级血缘分析: [field_lineage_example.py](field_lineage_example.py)
- 复杂查询处理: [complex_example.py](complex_example.py)
- 空值处理: [null_datatype_example.py](null_datatype_example.py)

## 3. 最近改进

### 3.1 多条件JOIN实现

- 支持使用AND连接的多条件JOIN
- 实现客户ID和地区的双条件连接示例
- 添加了对不可满足查询的处理和诊断
- 展示单条件与多条件JOIN的区别，验证过滤效果

### 3.2 复杂数据分析场景

- 实现三表JOIN查询，关联客户、订单和产品
- 添加派生计算字段，如订单总金额
- 结合WHERE条件和JOIN条件的复杂查询
- 验证字段级数据血缘，确认数据依赖关系

### 3.4 简化的表达式语法

- 提供更简洁的JOIN语法：`table1.col1 == table2.col2`
- 支持自动创建和关联行变量，无需手动管理
- 实现多条件JOIN的自然表达：可以直接写多个条件，系统自动关联
- 简化复杂查询表达，减少代码量，提高可读性

## 4. 使用说明

### 4.1 创建表结构

```python
# 创建带有多种数据类型的表
table = z3_sql.create_table("table_name", {
    "int_column": IntSort(),
    "string_column": StringSort(),
    "real_column": RealSort(),
    "bool_column": BoolSort()
})
```

### 4.2 数据操作

```python
# 插入一行数据
row_id = table.insert({"column1": value1, "column2": value2})

# 多表数据关联
result_table.insert({
    "result_column": source_table.source_column
})
```

### 4.3 添加约束

```python
# 简单条件
z3_sql.add(table.column1 > 10)

# 简化的JOIN表达式
customer_row, order_row = customers.id == orders.customer_id
customers.region == orders.region  # 自动使用相同的行变量

# 复杂JOIN条件（传统方式）
z3_sql.add(And(
    table1.id == table2.table1_id,
    table1.region == table2.region
))
```

### 4.4 验证和分析

```python
# 检查约束是否满足
if z3_sql.check() == sat:
    model = z3_sql.get_model()
    
    # 获取模型中的值
    value = model.eval(table.column(row_id))
    
    # 验证假设条件
    z3_sql.add(table.column == new_value)
    if z3_sql.check() == sat:
        # 假设成立...
```

## 5. 依赖

- Python 3.6+
- Z3 Solver