"""
这个例子展示了如何使用Z3求解器来模拟SQL JOIN和筛选操作。

# SQL查询等价表示:
'''sql
-- 创建表
CREATE TABLE users (user_id INT, name VARCHAR(255));
CREATE TABLE products (product_id INT, name VARCHAR(255), price INT);
CREATE TABLE orders (order_id INT, user_id INT, product_id INT, amount INT);

-- 插入数据
INSERT INTO users VALUES (1, 'Alice'), (2, 'Bob');
INSERT INTO products VALUES (101, 'Laptop', 1200), (102, 'Mouse', 25);
INSERT INTO orders VALUES (1001, 1, 101, 1), (1002, 2, 102, 2), (1003, 1, 102, 1);

-- 查询：找到用户名为Alice且购买了价格大于20的产品的订单
SELECT u.name, p.name, p.price, o.order_id, o.amount
FROM users u
JOIN orders o ON u.user_id = o.user_id
JOIN products p ON o.product_id = p.product_id
WHERE u.name = 'Alice' AND p.price > 20;
'''

# 数据表结构:
# users (user_id, name)
# | user_id | name  |
# |---------|-------|
# | 1       | Alice |
# | 2       | Bob   |
#
# products (product_id, name, price)
# | product_id | name   | price |
# |------------|--------|-------|
# | 101        | Laptop | 1200  |
# | 102        | Mouse  | 25    |
#
# orders (order_id, user_id, product_id, amount)
# | order_id | user_id | product_id | amount |
# |----------|---------|------------|--------|
# | 1001     | 1       | 101        | 1      |
# | 1002     | 2       | 102        | 2      |
# | 1003     | 1       | 102        | 1      |
"""

import sys
import unittest # 导入unittest模块
import codecs # 导入codecs模块
from pathlib import Path
# 从z3导入所有可能需要的类型，或者使用 z3.<Type> 形式
import z3

sys.path.insert(0, str(Path(__file__).parent.parent.parent))
from new_model.core.solver import SqlToZ3
# from new_model.sql_to_z3.table import Table # Table 通常通过 SqlToZ3 实例创建
# from new_model.sql_to_z3.column import Column # Column 通常通过 Table 实例访问
# from new_model.sql_to_z3.expression import Expression # Expression 可能会在复杂查询中使用

class TestMultiTableJoinExample(unittest.TestCase):

    def setUp(self):
        """每个测试方法运行前执行，用于初始化共享资源"""
        self.s = SqlToZ3()
        print("--- [测试] 创建表 ---")
        self.users = self.s.create_table("users", {"user_id": z3.IntSort(), "name": z3.StringSort()})
        self.orders = self.s.create_table("orders", {"order_id": z3.IntSort(), "user_id": z3.IntSort(), "product_id": z3.IntSort(), "amount": z3.IntSort()})
        self.products = self.s.create_table("products", {"product_id": z3.IntSort(), "name": z3.StringSort(), "price": z3.IntSort()})

        print("\n--- [测试] 插入数据 ---")
        self.users.insert({"user_id": 1, "name": "Alice"})
        self.users.insert({"user_id": 2, "name": "Bob"})

        self.products.insert({"product_id": 101, "name": "Laptop", "price": 1200})
        self.products.insert({"product_id": 102, "name": "Mouse", "price": 25})
        
        self.orders.insert({"order_id": 1001, "user_id": 1, "product_id": 101, "amount": 1}) # Alice, Laptop
        self.orders.insert({"order_id": 1002, "user_id": 2, "product_id": 102, "amount": 2}) # Bob, Mouse
        self.orders.insert({"order_id": 1003, "user_id": 1, "product_id": 102, "amount": 1}) # Alice, Mouse


    def _decode_z3_string(self, z3_str_val):
        """辅助方法：解码Z3字符串值（可能包含 \\\\u{...} 转义）"""
        if hasattr(z3_str_val, 'as_string'): # 检查是否有 as_string 方法
            raw_str = z3_str_val.as_string()
            # 检查是否包含Z3特有的Unicode转义 \\u{...}
            if '\\\\u{' in raw_str:
                processed_str = raw_str.replace('\\\\u{', '\\\\u').replace('}', '')
                try:
                    return codecs.decode(processed_str.encode('utf-8'), 'unicode_escape')
                except UnicodeDecodeError:
                    # 如果替换后仍然解码失败，可能原始字符串就是普通的带反斜杠的字符串
                    # 例如 "\\\\u" （两个反斜杠和一个u）
                    return raw_str # 或者采取其他回退策略
            return raw_str # 如果不包含特殊转义，直接返回
        return str(z3_str_val) # 对于其他类型，转换为字符串

    def test_find_alice_products_gt_20(self):
        """
        测试查询：找到用户名为Alice且购买了价格大于20的产品的订单
        SQL:
        SELECT u.name, p.name, p.price, o.order_id, o.amount
        FROM users u
        JOIN orders o ON u.user_id = o.user_id
        JOIN products p ON o.product_id = p.product_id
        WHERE u.name = 'Alice' AND p.price > 20;
        预期结果:
        Alice, Laptop, 1200, 1001, 1
        Alice, Mouse, 25, 1003, 1
        """
        print("\n--- [测试] 添加 JOIN 条件 ---")
        self.s.add(self.users.user_id == self.orders.user_id)
        self.s.add(self.orders.product_id == self.products.product_id)

        print("\n--- [测试] 添加筛选条件 ---")
        self.s.add(self.users["name"] == "Alice")
        self.s.add(self.products.price > 20)

        print("\n--- [测试] 检查求解器 ---")
        result = self.s.check()
        print(f"求解结果: {result}")
        self.assertEqual(result, z3.sat, "查询应该有解")

        if result == z3.sat:
            print("\n--- [测试] 获取模型 ---")
            model = self.s.get_model()

            # 打印所有已知行的变量分配 (可选的调试信息)
            # print("表行变量:")
            # for d in model.decls():
            #     if str(d.name()).endswith("_row") and d.arity() == 0:
            #         row_var_name = d.name()
            #         row_value = model[d]
            #         print(f"  {row_var_name}: {row_value}")

            print("\n--- [测试] 找出满足所有条件的行并进行断言 ---")
            found_results = []
            
            # 获取列函数以从模型中提取值
            users_user_id_func = self.s.column_functions["users_user_id"]
            users_name_func = self.s.column_functions["users_name"]
            orders_order_id_func = self.s.column_functions["orders_order_id"]
            orders_user_id_func = self.s.column_functions["orders_user_id"]
            orders_product_id_func = self.s.column_functions["orders_product_id"]
            orders_amount_func = self.s.column_functions["orders_amount"]
            products_product_id_func = self.s.column_functions["products_product_id"]
            products_name_func = self.s.column_functions["products_name"]
            products_price_func = self.s.column_functions["products_price"]

            # 注意：在 setUp 中，我们是显式插入 orders 数据的，包括 user_id 和 product_id
            # 所以这里的迭代和检查逻辑是为了验证 Z3 是否能找到满足所有约束的 *原始插入数据行*
            # 而不是让 Z3 自己去 "组合" 出新的 user_id/product_id 值。
            
            # 我们需要迭代 solver 内部管理的行变量，这些变量由 JOIN 条件引入并关联
            # 或者，更简单的方法是，既然我们知道原始数据，并且断言 sat，
            # 我们可以检查求解器是否将特定的原始行标记为满足条件。
            # 但这里我们还是采用原示例的迭代方式来验证结果。

            # 迭代所有可能的原始数据行组合，并检查它们是否满足模型中的约束
            # (这里的 users.rows, orders.rows, products.rows 指的是我们插入的数据行)
            
            # 记录满足条件的组合的详细信息，用于后续断言
            actual_join_results = []

            for u_row_id_obj in self.users.rows: # u_row_id_obj 是行号，如 IntVal(1)
                for o_row_id_obj in self.orders.rows:
                    for p_row_id_obj in self.products.rows:
                        
                        # 从模型中获取具体值
                        # u_row_id_val, o_row_id_val, p_row_id_val 已经是 Z3 IntNumRef，可以直接用
                        
                        eval_u_id = model.eval(users_user_id_func(u_row_id_obj))
                        eval_u_name_raw = model.eval(users_name_func(u_row_id_obj))
                        eval_u_name = self._decode_z3_string(eval_u_name_raw)
                        
                        eval_o_id = model.eval(orders_order_id_func(o_row_id_obj))
                        eval_o_user_id = model.eval(orders_user_id_func(o_row_id_obj))
                        eval_o_product_id = model.eval(orders_product_id_func(o_row_id_obj))
                        eval_o_amount = model.eval(orders_amount_func(o_row_id_obj))
                        
                        eval_p_id = model.eval(products_product_id_func(p_row_id_obj))
                        eval_p_name_raw = model.eval(products_name_func(p_row_id_obj))
                        eval_p_name = self._decode_z3_string(eval_p_name_raw)
                        eval_p_price = model.eval(products_price_func(p_row_id_obj))

                        # 检查模型是否满足JOIN条件 (u.user_id == o.user_id AND o.product_id == p.product_id)
                        # 并且满足筛选条件 (u.name == 'Alice' AND p.price > 20)
                        # 注意：这些条件已经被求解器 s 强制执行了。
                        # 我们在这里迭代是为了从模型中 *提取* 出这些满足条件的行。
                        
                        # 直接从模型判断这些行是否是通过求解器确定的解的一部分。
                        # 对于插入的行，它们的值已经是固定的。
                        # 求解器会确定这些固定值的组合是否满足所有约束。
                        
                        # 我们需要检查的是，对于当前迭代的 u_row_id, o_row_id, p_row_id，
                        # 它们是否满足了所有添加的 JOIN 和 WHERE 约束。
                        # 最简单的方法是再次查询模型，看这些值是否符合。
                        
                        join1_holds = model.eval(eval_u_id == eval_o_user_id)
                        join2_holds = model.eval(eval_o_product_id == eval_p_id)
                        
                        filter_name_holds = model.eval(users_name_func(u_row_id_obj) == z3.StringVal("Alice"))
                        filter_price_holds = model.eval(products_price_func(p_row_id_obj) > 20)

                        if z3.is_true(join1_holds) and z3.is_true(join2_holds) and \
                           z3.is_true(filter_name_holds) and z3.is_true(filter_price_holds):
                            
                            print(f"  找到组合: User(id={eval_u_id}, name='{eval_u_name}') - "
                                  f"Order(id={eval_o_id}, user_id={eval_o_user_id}, prod_id={eval_o_product_id}, amt={eval_o_amount}) - "
                                  f"Product(id={eval_p_id}, name='{eval_p_name}', price={eval_p_price})")
                            
                            actual_join_results.append({
                                "user_name": eval_u_name,
                                "user_id": eval_u_id.as_long(),
                                "order_id": eval_o_id.as_long(),
                                "order_amount": eval_o_amount.as_long(),
                                "product_name": eval_p_name,
                                "product_id": eval_p_id.as_long(),
                                "product_price": eval_p_price.as_long()
                            })
            
            self.assertEqual(len(actual_join_results), 2, "应该找到两个满足条件的订单组合")

            expected_results = [
                {"user_name": "Alice", "product_name": "Laptop", "product_price": 1200, "order_id": 1001, "order_amount": 1},
                {"user_name": "Alice", "product_name": "Mouse", "product_price": 25, "order_id": 1003, "order_amount": 1},
            ]

            # 为了比较，我们将实际结果和预期结果转换为可比较的形式（例如，元组的集合）
            # 只比较关心的字段，并确保顺序一致
            def result_to_tuple(r):
                return (r["user_name"], r["product_name"], r["product_price"], r["order_id"], r["order_amount"])

            actual_tuples = sorted([result_to_tuple(r) for r in actual_join_results])
            expected_tuples = sorted([result_to_tuple(r) for r in expected_results])
            
            self.assertListEqual(actual_tuples, expected_tuples, "找到的订单组合与预期不符")

        elif result == z3.unsat:
            print("\n[测试] 无解，表示不存在满足所有条件的行组合。")
            self.fail("查询不应该无解") # 根据问题描述，应该有解
        else:
            print("\n[测试] 求解器结果未知。")
            self.fail(f"求解器结果未知: {result}")

    def test_select_all_alice_orders_and_products(self):
        """
        测试查询：找到用户名为Alice的所有订单以及对应的产品信息
        SQL:
        SELECT u.name, o.order_id, p.name as product_name, p.price
        FROM users u
        JOIN orders o ON u.user_id = o.user_id
        JOIN products p ON o.product_id = p.product_id
        WHERE u.name = 'Alice';

        预期结果:
        Alice, 1001, Laptop, 1200
        Alice, 1003, Mouse, 25
        """
        print("\n--- [测试] 多表JOIN找到Alice的所有产品 ---")
        # 添加JOIN条件
        self.s.add(self.users.user_id == self.orders.user_id)
        self.s.add(self.orders.product_id == self.products.product_id)

        # 添加筛选条件
        self.s.add(self.users["name"] == "Alice") # 使用 __getitem__

        result = self.s.check()
        print(f"求解结果: {result}")
        self.assertEqual(result, z3.sat, "查询Alice的所有订单应该有解")

        if result == z3.sat:
            model = self.s.get_model()
            print("模型找到了，正在尝试提取Alice的订单...")
            
            # 提取并验证结果
            # 这部分比较复杂，需要迭代所有可能的行组合并检查模型
            # 我们需要一种方法来查询模型中所有满足约束的 (users_row, orders_row, products_row) 组合
            
            # 简化版：检查是否存在预期的组合
            # 为了做到这一点，我们需要知道哪些行变量对应哪些表，以及它们之间的关系。
            # 这通常通过分析 self.s.join_conditions 和 self.s.constraints 来完成。
            
            # 示例性的提取逻辑 (需要更完善的实现):
            # found_results = self.s.get_all_solutions_for_query(
            #    select_columns=[self.users['name'], self.orders['order_id'], self.products['name'], self.products['price']],
            #    model=model
            # )
            # print(f"提取到的结果: {found_results}")
            
            # expected = [
            #    ("Alice", 1001, "Laptop", 1200),
            #    ("Alice", 1003, "Mouse", 25),
            # ]
            # self.assertCountEqual(found_results, expected, "查询结果与预期不符")
            pass # 暂时跳过详细的模型验证

    def test_no_bob_products_gt_1000(self):
        """
        测试查询：找到用户名为Bob且购买了价格大于1000的产品的订单
        SQL:
        SELECT u.name, p.name, p.price
        FROM users u
        JOIN orders o ON u.user_id = o.user_id
        JOIN products p ON o.product_id = p.product_id
        WHERE u.name = 'Bob' AND p.price > 1000;
        预期结果: 无 (Bob只买了Mouse，价格25)
        """
        print("\n--- [测试] Bob没有价格大于1000的产品 ---")
        self.s.add(self.users.user_id == self.orders.user_id)
        self.s.add(self.orders.product_id == self.products.product_id)
        
        self.s.add(self.users["name"] == "Bob") # 使用 __getitem__
        self.s.add(self.products.price > 1000)
        
        result = self.s.check()
        print(f"求解结果: {result}")
        self.assertEqual(result, z3.unsat, "Bob没有价格大于1000的产品，查询应该无解")

    def test_complex_join_with_multiple_conditions(self):
        """
        测试更复杂的JOIN条件和筛选
        例如：找到Alice购买的，且订单金额大于0，产品价格小于1500的 Laptop 订单
        SQL:
        SELECT u.name, p.name, o.amount, p.price
        FROM users u
        JOIN orders o ON u.user_id = o.user_id
        JOIN products p ON o.product_id = p.product_id
        WHERE u.name = 'Alice' AND o.amount > 0 AND p.price < 1500 AND p.name = 'Laptop';
        预期结果: Alice, Laptop, 1, 1200
        """
        print("\n--- [测试] 复杂JOIN与多条件筛选 ---")
        self.s.add(self.users.user_id == self.orders.user_id)
        self.s.add(self.orders.product_id == self.products.product_id)

        self.s.add(self.users["name"] == "Alice")
        self.s.add(self.orders.amount > 0)
        self.s.add(self.products.price < 1500)
        self.s.add(self.products["name"] == "Laptop") # 使用 __getitem__

        result = self.s.check()
        print(f"求解结果: {result}")
        self.assertEqual(result, z3.sat, "复杂查询应该有解")

        # 可选：模型验证
        if result == z3.sat:
            # ... 验证模型中的具体值 ...
            pass
            
    def test_join_on_non_id_columns_if_needed(self):
        """
        如果未来支持在非ID列上JOIN (例如 users.name == address.user_name)
        这里可以添加相应的测试。目前我们的JOIN主要基于 user_id, product_id。
        """
        pass

    def test_self_join_scenario(self):
        """
        如果需要测试自连接 (例如, 员工和经理在同一张表)
        这需要表能支持别名，或者通过其他方式处理。
        目前我们的简单模型可能不直接支持，或者需要特定建表方式。
        """
        pass

    def tearDown(self):
        print(f"--- [测试结束] {self.id()} ---")
        # 可选：在这里可以清理或重置求解器状态，如果每个测试需要独立环境
        # 但通常每个测试方法会创建新的SqlToZ3实例 (通过 self.s = SqlToZ3())
        # 所以这里的 tearDown 可能不是必须的，除非有共享的类级别资源。
        # self.s.reset() # 如果有 reset 方法并且需要
        pass

if __name__ == '__main__':
    unittest.main()

# 原来的打印模型部分 (现在由测试方法中的断言和打印替代)
# ... (省略原文件末尾的打印逻辑) ... 