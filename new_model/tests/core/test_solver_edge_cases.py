"""
测试SqlToZ3类的边缘情况和错误处理
针对未被其他测试覆盖的方法进行测试
"""

import pytest
from z3 import Int, String, Real, BoolVal, IntVal, RealVal, StringVal, And, Or, Not, Implies, Xor, If, IntSort, RealSort, StringSort, BoolSort, Function, sat, unsat, unknown, BoolRef
import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from new_model.core.solver import SqlToZ3
from new_model.core.expression import Expression
from new_model.core.table import Table
from new_model.core.column import Column, ColumnConstraint

class TestSolverEdgeCases:
    """测试SqlToZ3类的边缘情况和错误处理"""
    
    def setup_method(self):
        """每个测试方法开始前的设置"""
        self.solver = SqlToZ3()
        
        # 创建测试表和列
        self.users = self.solver.create_table("users", {
            "id": IntSort(),
            "name": StringSort(),
            "age": IntSort(),
            "salary": RealSort(),
            "is_active": BoolSort()
        })
        
        self.orders = self.solver.create_table("orders", {
            "id": IntSort(),
            "user_id": IntSort(),
            "amount": RealSort(),
            "created_at": IntSort()
        })
        
        # 添加一些测试数据
        self.users.insert({"id": 1, "name": "Alice", "age": 30, "salary": 50000.0, "is_active": True})
        self.users.insert({"id": 2, "name": "Bob", "age": 25, "salary": 40000.0, "is_active": False})
        
        self.orders.insert({"id": 101, "user_id": 1, "amount": 1000.0, "created_at": 20230101})
        self.orders.insert({"id": 102, "user_id": 2, "amount": 500.0, "created_at": 20230102})
    
    def test_solver_reset(self):
        """测试solver的reset方法"""
        # 添加约束
        self.solver.add(self.users.age > 20)
        assert len(self.solver.constraints) > 0
        
        # 重置求解器
        self.solver.reset()
        
        # 检查约束是否已清除
        assert len(self.solver.constraints) == 0
        assert len(self.solver._constraint_cache) == 0
        
        # 重置后应该仍可使用
        self.solver.add(self.users.age > 30)
        assert self.solver.check() == sat
    
    def test_solver_logging_configuration(self):
        """测试solver的日志配置"""
        # 创建一个新的求解器实例，配置日志级别
        solver = SqlToZ3(log_level="DEBUG")
        
        # 验证日志级别已设置
        assert solver.logger.level == logging.DEBUG
        
        # 再创建一个使用不同日志级别的求解器
        solver2 = SqlToZ3(log_level="ERROR")
        assert solver2.logger.level == logging.ERROR
    
    def test_logical_operations(self):
        """测试逻辑操作函数"""
        # 使用SqlToZ3中实际存在的逻辑操作方法
        
        # 测试 Not 方法
        result = self.solver.Not(BoolVal(True))
        assert str(result) == "Not(True)"
        
        # 测试 And 方法
        result = self.solver.And(BoolVal(True), BoolVal(False))
        assert str(result) == "And(True, False)"
        
        # 测试 Or 方法
        result = self.solver.Or(BoolVal(False), BoolVal(True))
        assert str(result) == "Or(False, True)"
        
        # 测试 Implies 方法
        result = self.solver.Implies(BoolVal(True), BoolVal(False))
        assert str(result) == "Implies(True, False)"
        
        # 测试 Xor 方法
        result = self.solver.Xor(BoolVal(True), BoolVal(True))
        assert str(result) == "Xor(True, True)"
        
        # 测试 If 方法
        result = self.solver.If(BoolVal(True), IntVal(1), IntVal(2))
        assert str(result) == "If(True, 1, 2)"
    
    def test_add_expression_edge_cases(self):
        """测试添加表达式的边缘情况"""
        # 测试添加常量True/False
        self.solver.add(True)
        self.solver.add(False)
        
        # 测试添加Z3 BoolVal
        self.solver.add(BoolVal(True))
        
        # 测试添加None - 实际上SqlToZ3可能会处理None，不一定抛出异常
        # 根据实际情况测试，我们不期望特定的异常
        try:
            self.solver.add(None)
        except Exception:
            pass  # 如果抛出异常，我们接受任何类型的异常
        
        # 测试添加无效类型 - 可能抛出TypeError
        # 注意：SqlToZ3实现可能会处理一些基本类型，所以实际结果取决于实现
        try:
            self.solver.add(123)  # 整数不一定是有效的约束
        except (TypeError, ValueError):
            pass  # 异常是预期的
    
    def test_convert_constraint(self):
        """测试_convert_constraint方法"""
        # 测试转换基本类型
        int_val = self.solver._convert_constraint(10)
        assert isinstance(int_val, IntVal(0).__class__)  # 使用IntVal函数创建的对象的类型
        assert str(int_val) == "10"
        
        # 测试转换浮点数
        real_val = self.solver._convert_constraint(10.5)
        assert isinstance(real_val, RealVal(0).__class__)  # 使用RealVal函数创建的对象的类型
        # Z3使用分数表示法表示浮点数，10.5 = 21/2
        assert str(real_val) == "21/2" or str(real_val) == "10.5"  # 允许任一种表示
        
        # 测试转换字符串
        str_val = self.solver._convert_constraint("test")
        assert isinstance(str_val, StringVal("").__class__)  # 使用StringVal函数创建的对象的类型
        # Z3在显示字符串时会加上引号，如 "test"
        assert str(str_val) == '"test"'
        
        # 测试转换布尔值
        bool_val = self.solver._convert_constraint(True)
        assert isinstance(bool_val, BoolRef)  # 使用直接导入的BoolRef类型
        assert str(bool_val) == "True"
        
        # 测试转换None (应该返回None或抛出异常)
        try:
            self.solver._convert_constraint(None)
        except ValueError:
            pass  # 异常是预期的
    
    def test_check_and_get_model_with_unknown_result(self):
        """测试check未知结果的情况"""
        # 创建一个可能导致unknown结果的复杂约束
        # 注意：模拟unknown结果很困难，这里我们尽量创建复杂约束
        
        # 添加一些复杂约束
        solver = SqlToZ3()
        x = Int('x')
        y = Int('y')
        
        # 添加非线性约束
        solver.add(x * y > 1000000)
        solver.add(x > 0)
        solver.add(y > 0)
        solver.add(x < 10000)
        solver.add(y < 10000)
        
        # 假设检查结果为unknown (这个测试可能不稳定)
        # 如果结果为unknown，应该返回None
        if solver.check() == unknown:
            assert solver.get_model() is None
    
    def test_create_joined_table(self):
        """测试create_joined_table方法"""
        # 创建JOIN条件
        join_condition = self.users.id == self.orders.user_id
        
        # 将JOIN结果添加到solver
        self.solver.add(join_condition)
        
        # 创建联合表 - 修正函数签名参数顺序
        joined_table = self.solver.create_joined_table("user_orders", self.users, self.orders, join_condition)
        
        # 验证联合表 - 适应新的列名格式（带表前缀）
        assert joined_table.name == "user_orders"
        assert "users_id" in joined_table.columns
        assert "users_name" in joined_table.columns
        assert "orders_user_id" in joined_table.columns
        assert "orders_amount" in joined_table.columns
    
    def test_create_derived_table(self):
        """测试create_derived_table方法"""
        # 创建派生表条件
        condition = self.users.age > 25
        
        # 将条件添加到solver
        self.solver.add(condition)
        
        # 创建派生表
        senior_users = self.solver.create_derived_table("senior_users", self.users, condition)
        
        # 验证派生表
        assert senior_users.name == "senior_users"
        assert "id" in senior_users.columns
        assert "name" in senior_users.columns
        assert "age" in senior_users.columns
    
    def test_create_aggregate_expression(self):
        """测试create_aggregate_expression方法"""
        # 使用正确的函数签名
        # 创建求和表达式
        sum_expr = self.solver.create_aggregate_expression("SUM", self.orders, "amount", "SUM")
        assert "SUM" in sum_expr.description
        
        # 创建平均值表达式
        avg_expr = self.solver.create_aggregate_expression("AVG", self.users, "age", "AVG")
        assert "AVG" in avg_expr.description
        
        # 创建计数表达式
        count_expr = self.solver.create_aggregate_expression("COUNT", self.users, "id", "COUNT")
        assert "COUNT" in count_expr.description
    
    def test_eval_with_model(self):
        """测试eval函数与模型一起使用"""
        # 添加一些约束
        self.solver.add(self.users.age > 25)
        
        # 检查求解器并获取模型
        assert self.solver.check() == sat
        model = self.solver.get_model()
        assert model is not None
        
        # 获取行变量
        row_var = self.users.get_row_var()
        
        # 使用模型评估表达式
        age_expr = self.users.age(row_var)
        age_value = self.solver.eval(age_expr, model)
        
        # 检查年龄值是否大于25
        age_int = int(str(age_value))
        assert age_int > 25 