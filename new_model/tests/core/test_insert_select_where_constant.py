import unittest
from z3 import Solver, StringSort, sat, StringVal, IntSort, IntVal
from new_model.core.solver import SqlToZ3


class TestInsertSelectWhereConstant(unittest.TestCase):

    def test_insert_with_where_clause_constant(self):
        """
        测试场景: INSERT INTO tbl (col1, col2) SELECT col1, col2 FROM source_table WHERE source_table.col1 = '001'
        验证目标表 tbl 中的 col1 确实被约束为 '001'。
        同时也验证了，即使源表 (source_table) 的列在 WHERE 条件中被常量约束，
        我们仍然需要为 source_table 定义结构并创建其 Z3 变量，因为其他列 (如 col2)
        的值需要从 source_table 传递到目标表。
        """
        solver_wrapper = SqlToZ3()

        # 1. 定义表结构模式
        source_table_schema = {
            "col1": StringSort(), # 被 WHERE 条件约束的列
            "col2": StringSort(), # 其他需要传递的列
            "id": IntSort()       # 一个主键或唯一标识符，用于区分行
        }
        target_table_schema = {
            "col1": StringSort(), # 对应 source_table.col1
            "col2": StringSort(), # 对应 source_table.col2
            "id": IntSort()       # 主键或唯一标识符
        }

        # 使用 solver_wrapper.create_table 来创建表，确保列函数被注册
        source_table = solver_wrapper.create_table("source_table", source_table_schema)
        target_table = solver_wrapper.create_table("tbl", target_table_schema)

        # 2. 为源表获取代表"一行"的Z3符号变量 (s_row_id_z3)
        #    为目标表插入一个新行并获取其具体ID (t_row_id_concrete)
        s_row_id_z3 = source_table.get_row_var()  # 返回一个 Z3 Int 变量
        t_row_id_concrete = target_table.insert() # 返回一个 Python int 作为行ID

        # 为了方便，获取列函数 (可选步骤，也可以直接在下面使用)
        s_col1_func = solver_wrapper.column_functions["source_table_col1"]
        s_col2_func = solver_wrapper.column_functions["source_table_col2"]
        s_id_func = solver_wrapper.column_functions["source_table_id"]

        t_col1_func = solver_wrapper.column_functions["tbl_col1"]
        t_col2_func = solver_wrapper.column_functions["tbl_col2"]
        t_id_func = solver_wrapper.column_functions["tbl_id"]
        
        # 3. 添加约束
        # 约束1: 来自 WHERE source_table.col1 = '001'
        # 这个约束应用于源表的符号行 s_row_id_z3
        solver_wrapper.add(s_col1_func(s_row_id_z3) == StringVal("001"))

        # 约束2: SELECT col1, col2 FROM source_table ... INSERT INTO tbl (col1, col2) ...
        # 这表示目标表的列值来源于源表的列值。
        solver_wrapper.add(t_col1_func(t_row_id_concrete) == s_col1_func(s_row_id_z3))
        solver_wrapper.add(t_col2_func(t_row_id_concrete) == s_col2_func(s_row_id_z3))

        # (可选) 为了使模型有唯一解（对于我们关心的变量），可以给 s_row_id_z3 的其他列赋具体值。
        solver_wrapper.add(s_col2_func(s_row_id_z3) == StringVal("some string"))
        solver_wrapper.add(s_id_func(s_row_id_z3) == IntVal(101))
        
        # (可选) 添加 ID 约束，例如目标行继承源行ID
        solver_wrapper.add(t_id_func(t_row_id_concrete) == s_id_func(s_row_id_z3))

        # 4. 求解和断言
        check_result = solver_wrapper.check()
        self.assertEqual(check_result, sat, f"求解器应该找到一个满足条件的模型 (sat)，但得到了 {check_result}")

        if check_result == sat:
            model = solver_wrapper.get_model()

            # 验证目标表 tbl.col1 的值
            target_col1_value_z3 = model.eval(t_col1_func(t_row_id_concrete), model_completion=True)
            self.assertIsNotNone(target_col1_value_z3, "目标表 tbl.col1 的值不应为 None")
            self.assertEqual(target_col1_value_z3.as_string(), "001",
                             "目标表 tbl.col1 的值应该被约束为 '001'")

            # 验证目标表 tbl.col2 的值是否从源表正确传递
            target_col2_value_z3 = model.eval(t_col2_func(t_row_id_concrete), model_completion=True)
            source_col2_value_in_model_z3 = model.eval(s_col2_func(s_row_id_z3), model_completion=True)

            self.assertIsNotNone(target_col2_value_z3, "目标表 tbl.col2 的值不应为 None")
            self.assertIsNotNone(source_col2_value_in_model_z3, "源表 s_row.col2 的值在模型中不应为 None")

            self.assertEqual(target_col2_value_z3.as_string(), source_col2_value_in_model_z3.as_string(),
                             "目标表 tbl.col2 的值应该等于源表 source_table.col2 的值")
            self.assertEqual(target_col2_value_z3.as_string(), "some string",
                             "目标表 tbl.col2 的值也应该等于我们设定的 'some string'")

            # 打印模型信息以便调试或确认
            print("\n--- 测试 'test_insert_with_where_clause_constant' ---")
            print("求解结果: sat")
            print("模型值:")
            
            s_id_val_model = model.eval(s_id_func(s_row_id_z3), model_completion=True)
            s_col1_val_model = model.eval(s_col1_func(s_row_id_z3), model_completion=True)
            s_col2_val_model = model.eval(s_col2_func(s_row_id_z3), model_completion=True)
            
            t_id_val_model = model.eval(t_id_func(t_row_id_concrete), model_completion=True)
            t_col1_val_model = model.eval(t_col1_func(t_row_id_concrete), model_completion=True)
            t_col2_val_model = model.eval(t_col2_func(t_row_id_concrete), model_completion=True)

            print(f"  源表行 (s_row_id_z3: {s_row_id_z3}) 在模型中的值:")
            print(f"    id: {s_id_val_model}")
            print(f"    col1: \"{s_col1_val_model.as_string()}\"")
            print(f"    col2: \"{s_col2_val_model.as_string()}\"")
            print(f"  目标表行 (t_row_id_concrete: {t_row_id_concrete}) 在模型中的值:")
            print(f"    id: {t_id_val_model}")
            print(f"    col1: \"{t_col1_val_model.as_string()}\"  <-- 应为 \"001\"")
            print(f"    col2: \"{t_col2_val_model.as_string()}\"  <-- 应为 \"some string\"")
            print("----------------------------------------------------")

if __name__ == '__main__':
    unittest.main() 