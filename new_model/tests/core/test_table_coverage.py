"""
测试Table类的覆盖率
主要测试未覆盖的方法
"""

import pytest
from z3 import Int, String, Real, BoolVal, IntVal, RealVal, And, Or, Not, IntSort, RealSort, StringSort, BoolSort
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

from new_model.core.solver import SqlToZ3
from new_model.core.table import Table
from new_model.core.column import Column

class TestTableCoverage:
    """测试Table类中未覆盖的方法"""
    
    def setup_method(self):
        """每个测试方法开始前的设置"""
        self.solver = SqlToZ3()
    
    def test_table_init(self):
        """测试表初始化"""
        # 使用字典初始化
        schema = {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort(),
            "active": BoolSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        assert table.name == "test_table"
        assert list(table.columns_with_types.keys()) == ["id", "name", "price", "active"]
        assert "id" in table.columns_with_types
        assert "name" in table.columns_with_types
        assert "price" in table.columns_with_types
        assert "active" in table.columns_with_types
    
    def test_table_getattr(self):
        """测试通过属性访问列"""
        schema = {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 通过属性访问列 - 这会在底层调用Table.__getattr__方法
        id_col = self.solver.tables["test_table"].id
        assert isinstance(id_col, Column)
        assert id_col.column_name == "id"
        assert id_col.table_name == "test_table"
        
        # 访问不存在的列
        with pytest.raises(AttributeError):
            self.solver.tables["test_table"].nonexistent_column
    
    def test_table_getitem(self):
        """测试通过索引访问列"""
        schema = {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 通过索引访问列
        id_col = self.solver.tables["test_table"]["id"]
        assert isinstance(id_col, Column)
        assert id_col.column_name == "id"
        assert id_col.table_name == "test_table"
        
        # 访问不存在的列
        with pytest.raises(KeyError):
            self.solver.tables["test_table"]["nonexistent_column"]
    
    def test_table_get_row_var(self):
        """测试获取行变量"""
        schema = {
            "id": IntSort(),
            "name": StringSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 获取行变量
        var = table.get_row_var()
        assert var is not None
        assert "test_table_row_var_" in str(var)
        
        # 再次获取行变量 (应该是同一个)
        var2 = table.get_row_var()
        assert var is var2
    
    def test_table_columns_property(self):
        """测试获取列对象字典"""
        schema = {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 获取列字典
        columns = table.columns
        assert isinstance(columns, dict)
        assert len(columns) == 3
        assert "id" in columns
        assert "name" in columns
        assert "price" in columns
        assert isinstance(columns["id"], Column)
    
    def test_table_name_property(self):
        """测试表名属性"""
        schema = {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 获取表名
        assert table.name == "test_table"
    
    def test_table_insert_single_row(self):
        """测试向表中插入单行数据"""
        schema = {
            "id": IntSort(),
            "name": StringSort(),
            "price": RealSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 插入单行
        row_id = table.insert({"id": 1, "name": "test", "price": 10.5})
        assert isinstance(row_id, int)
        assert row_id in table.rows
        assert len(table.rows) == 1
    
    def test_table_contains_column(self):
        """测试表是否包含列"""
        schema = {
            "id": IntSort(),
            "name": StringSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 检查列是否存在
        assert "id" in table.columns_with_types
        assert "name" in table.columns_with_types
        assert "price" not in table.columns_with_types
    
    def test_table_repr(self):
        """测试表的__repr__方法"""
        schema = {
            "id": IntSort(),
            "name": StringSort()
        }
        table = self.solver.create_table("test_table", schema)
        
        # 检查表的字符串表示
        repr_str = repr(table)
        assert "test_table" in repr_str
        assert "id" in repr_str
        assert "name" in repr_str 