#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据血缘分析模块的示例脚本
"""

import os
import sys
import json
import pytest
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入血缘分析模块
from new_model.sql_convert.data_lineage_analyzer import analyze_sql_lineage


# 定义测试用的SQL字符串
ADVANCED_SQL = """
INSERT INTO intermediate_table1 (id, name, doubled_value, upper_category, create_time)
SELECT 
    1,
    name,
    value * 2.0 AS doubled_value,
    UPPER(category) AS upper_category,
    create_time
FROM source_table1
WHERE value > 10;

INSERT INTO intermediate_table2 (id, description, adjusted_amount, status_code, update_time)
SELECT 
    1, 
    description,
    amount * 1.1 AS adjusted_amount,
    CASE WHEN status = 'ACTIVE' THEN 'A' ELSE 'I' END AS status_code,
    update_time
FROM source_table2
WHERE amount > 1000;

INSERT INTO target_table (id, name, doubled_value, upper_category, adjusted_amount, status_code, fixed_value, current_date)
SELECT 
    a.id,
    a.name,
    a.doubled_value,
    a.upper_category,
    b.adjusted_amount,
    b.status_code,
    'CONSTANT' AS fixed_value,  -- 常量列，不依赖任何源表
    CURRENT_DATE() AS current_date  -- 函数列，不依赖任何源表
FROM intermediate_table1 a
JOIN intermediate_table2 b ON a.id = b.id
WHERE a.doubled_value > 1 AND b.adjusted_amount > 1 
"""

# 定义不满足约束的SQL (缺少中间表依赖关系)
INVALID_SQL = """
INSERT INTO intermediate_table1 (id, name, doubled_value, upper_category, create_time)
SELECT 
    1,
    name,
    value * 2.0 AS doubled_value,
    UPPER(category) AS upper_category,
    create_time
FROM source_table1
WHERE value > 10;

INSERT INTO intermediate_table2 (id, description, adjusted_amount, status_code, update_time)
SELECT 
    1, 
    description,
    amount * 1.1 AS adjusted_amount,
    CASE WHEN status = 'ACTIVE' THEN 'A' ELSE 'I' END AS status_code,
    update_time
FROM source_table2
WHERE amount > 1000;

INSERT INTO target_table (id, name, doubled_value, upper_category, adjusted_amount, status_code, fixed_value, current_date)
SELECT 
    a.id,
    a.name,
    a.doubled_value,
    a.upper_category,
    b.adjusted_amount,
    b.status_code,
    'CONSTANT' AS fixed_value,  -- 常量列，不依赖任何源表
    CURRENT_DATE() AS current_date  -- 函数列，不依赖任何源表
FROM intermediate_table1 a
JOIN intermediate_table2 b ON a.id = b.id
WHERE a.doubled_value > 1 AND b.adjusted_amount < 1 
"""


@pytest.fixture
def initial_schemas():
    """提供所有表模式信息的fixture"""
    return {
        "source_table1": {
            "id": "Int",
            "name": "String",
            "value": "Float",
            "category": "String",
            "create_time": "Date"
        },
        "source_table2": {
            "id": "Int",
            "description": "String",
            "amount": "Float",
            "status": "String",
            "update_time": "Date"
        },
        "intermediate_table1": {
            "id": "Int",
            "name": "String",
            "doubled_value": "Float", 
            "upper_category": "String",
            "create_time": "Date"
        },
        "intermediate_table2": {
            "id": "Int",
            "description": "String",
            "adjusted_amount": "Float",
            "status_code": "String",
            "update_time": "Date"
        },
        "target_table": {
            "id": "Int",
            "name": "String",
            "doubled_value": "Float",
            "upper_category": "String",
            "adjusted_amount": "Float",
            "status_code": "String",
            "fixed_value": "String",
            "current_date": "Date"
        }
    }


def test_valid_lineage(initial_schemas):
    """测试有效的数据血缘分析 - 这个测试预期会失败，因为SQL中的条件约束不可满足"""
    # 设置最终目标表名
    final_target_table_name = "target_table"
    
    # 调用血缘分析函数
    has_lineage = analyze_sql_lineage(
        sql=ADVANCED_SQL,
        initial_schemas=initial_schemas,
        final_target_table_name=final_target_table_name
    )
    
    # 断言：验证血缘关系结果
    # 注意：这个测试预期会失败，因为SQL中的条件约束不可满足
    # 条件 a.doubled_value > 1 AND b.adjusted_amount > 1 在给定的SQL中是同时满足的
    assert has_lineage is True, "预期存在有效的血缘关系，因为SQL中的条件约束可满足"


def test_invalid_lineage(initial_schemas):
    """测试不满足约束的数据血缘分析 - 这个测试预期会通过，因为SQL中的条件是可满足的"""
    # 设置最终目标表名
    final_target_table_name = "target_table"
    
    # 调用血缘分析函数
    has_lineage = analyze_sql_lineage(
        sql=INVALID_SQL,
        initial_schemas=initial_schemas,
        final_target_table_name=final_target_table_name
    )
    
    # 断言：验证血缘关系结果
    # 这个SQL中的条件是可满足的，所以应该存在血缘关系
    assert has_lineage is False, "预期不存在有效的血缘关系，因为SQL中的条件约束不可满足"
