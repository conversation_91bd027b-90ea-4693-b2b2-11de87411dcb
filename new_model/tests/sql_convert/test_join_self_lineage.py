#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试 join_self.sql 的数据血缘分析
"""

import os
import sys
import json
import pytest
from typing import Dict

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入血缘分析模块
from new_model.sql_convert.data_lineage_analyzer import analyze_sql_lineage

# 定义测试用的SQL字符串 - 满足约束的情况
JOIN_SELF_SQL = """
INSERT INTO t1 (id, old_id, username)
SELECT 
    1 AS id,
    2 AS old_id,
    username
FROM s1;

INSERT INTO t1 (id, old_id, username)
SELECT 
    2 AS id,
    4 AS old_id,
    username
FROM s2;

INSERT INTO t2 (id, old_id, username)
SELECT 
    id AS id,
    old_id AS old_id,
    username AS username
FROM t1;

INSERT INTO t3 (id, old_id, username)
SELECT 
    a.id,
    a.old_id,
    a.username
FROM t2 a left join t2 b on a.id = b.old_id;
"""

# 定义不满足约束的SQL - 缺少中间表t2
INVALID_JOIN_SQL = """
INSERT INTO t1 (id, old_id, username)
SELECT 
    1 AS id,
    2 AS old_id,
    username
FROM s1;

INSERT INTO t1 (id, old_id, username)
SELECT 
    3 AS id,
    4 AS old_id,
    username
FROM s2;

-- 直接从t1到t3，跳过t2
INSERT INTO t3 (id, old_id, username)
SELECT 
    a.id,
    a.old_id,
    a.username
FROM t1 a left join t1 b on a.id = b.old_id;
"""


@pytest.fixture
def initial_schemas():
    """提供所有表模式信息的fixture"""
    return {
        "s1": {
            "username": "String"
        },
        "s2": {
            "username": "String"
        },
        "t1": {
            "id": "Int",
            "old_id": "Int",
            "username": "String"
        },
        "t2": {
            "id": "Int",
            "old_id": "Int",
            "username": "String"
        },
        "t3": {
            "id": "Int",
            "old_id": "Int",
            "username": "String"
        }
    }


def test_valid_join_self_lineage(initial_schemas):
    """测试有效的自连接数据血缘分析"""
    # 设置最终目标表名
    final_target_table_name = "t3"
    
    # 调用血缘分析函数
    has_lineage = analyze_sql_lineage(
        sql=JOIN_SELF_SQL,
        initial_schemas=initial_schemas,
        final_target_table_name=final_target_table_name
    )
    
    # 断言：验证血缘关系结果
    # 这个SQL中的条件是可满足的，所以应该存在血缘关系
    assert has_lineage is True, "预期存在有效的血缘关系，因为SQL中的条件约束可满足"


def test_invalid_join_lineage(initial_schemas):
    """测试不满足约束的自连接数据血缘分析"""
    # 设置最终目标表名
    final_target_table_name = "t3"
    
    # 调用血缘分析函数
    has_lineage = analyze_sql_lineage(
        sql=INVALID_JOIN_SQL,
        initial_schemas=initial_schemas,
        final_target_table_name=final_target_table_name
    )
    
    # 断言：验证血缘关系结果
    # 这个SQL中的条件是可满足的，所以应该存在血缘关系
    assert has_lineage is False, "预期不存在有效的血缘关系，因为SQL中的条件约束不可满足" 