"""
列类模块
用于表示SQL中的列，并提供操作符重载
"""

from z3 import *
from typing import Any, Optional, Tuple, Union, TYPE_CHECKING
from .expression import Expression

# 避免循环导入，仅用于类型注解
if TYPE_CHECKING:
    from .solver import SqlToZ3 # 导入 SqlToZ3 以进行类型提示

class Column:
    """表示表的列"""
    def __init__(self, table_name: str, column_name: str, function: Function, solver_wrapper: 'SqlToZ3'):
        """
        初始化列对象
        
        Args:
            table_name: 表名
            column_name: 列名
            function: Z3函数
            solver_wrapper: SqlToZ3实例的引用
        """
        self.table_name = table_name
        self.column_name = column_name
        self.function = function  # Z3函数
        self.solver_wrapper = solver_wrapper  # SqlToZ3实例的引用
        self._description = f"{table_name}.{column_name}"  # 描述
    @property
    def name(self):
        """列名"""
        return self.column_name

    @property
    def z3_type(self):
        """此列的Z3类型 (SortRef 或 DatatypeRef)"""
        return self.function.range() # 函数返回值类型即为列类型
        
    @property
    def description(self):
        """列的描述字符串，格式为 table_name.column_name"""
        return self._description

    @property
    def column_type(self):
        return self.z3_type

    def is_date_column(self) -> bool:
        """
        判断当前列是否是日期类型
        
        Returns:
            bool: 当前列是否是日期类型
        """
        # 检查表对象是否存在date_columns属性，且当前列名在其中
        table = self.solver_wrapper.tables.get(self.table_name)
        if table and hasattr(table, 'date_columns') and self.column_name in table.date_columns:
            return True
        return False
    
    def __repr__(self) -> str:
        """返回列的字符串表示形式"""
        return f"{self.table_name}.{self.column_name}"
    
    def __call__(self, row_var: ExprRef) -> ExprRef:
        """使Column可调用，返回Z3表达式"""
        # 检查 row_var 是否为 ExprRef 类型（Z3变量）
        if not isinstance(row_var, ExprRef):
            self.solver_wrapper.logger.warning(f"Column.__call__ 接收到非 ExprRef 类型的 row_var: {type(row_var)}，这可能导致错误")
            # 如果是整数，尝试直接使用
            if isinstance(row_var, int):
                self.solver_wrapper.logger.info(f"将整数 {row_var} 直接用作行ID")
                return self.function(row_var)
            elif isinstance(row_var, str):
                # 如果是字符串，直接使用该字符串作为变量名创建一个新的整数变量
                self.solver_wrapper.logger.info(f"使用字符串 '{row_var}' 作为变量名创建行变量")
                row_var = Int(row_var, self.solver_wrapper.solver.ctx)
                return self.function(row_var)
            else:
                # 对于其他类型，尝试创建一个新的整数变量
                var_suffix = self.solver_wrapper._get_unique_var_suffix()
                var_name = f"row_var_{var_suffix}"
                self.solver_wrapper.logger.warning(f"创建新的整数变量 '{var_name}' 作为行变量")
                row_var = Int(var_name, self.solver_wrapper.solver.ctx)
                return self.function(row_var)
        
        # 如果是 ExprRef，直接使用
        return self.function(row_var)
    
    def _create_join_vars_and_row_constraints(self, other_table_name: str) -> Tuple[ExprRef, ExprRef]:
        """
        辅助方法：为涉及两表的比较操作（如JOIN）创建行变量并添加行ID有效性约束。

        Args:
            other_table_name (str): 参与比较的另一个表的名称。

        Returns:
            Tuple[z3.ExprRef, z3.ExprRef]: 一个包含两个Z3整数行变量的元组 (var_self, var_other)。
        """
        # 为当前列的表创建一个唯一的Z3行变量
        var_self = Int(f"{self.table_name}_row_{self.solver_wrapper._get_unique_var_suffix()}")
        # 为另一个表创建一个唯一的Z3行变量
        var_other = Int(f"{other_table_name}_row_{self.solver_wrapper._get_unique_var_suffix()}")
        
        # 添加约束：确保 var_self 是 self.table_name 中的一个有效行ID
        # self.solver_wrapper.tables 应该是表名到Table对象的映射
        # Table对象应该有一个rows属性，包含有效行ID的列表或范围
        if self.table_name in self.solver_wrapper.tables and self.solver_wrapper.tables[self.table_name].rows:
            self.solver_wrapper.add(Or([var_self == i for i in self.solver_wrapper.tables[self.table_name].rows]))
        else:
            # 如果表信息或行信息缺失，记录警告。这可能表示表未正确初始化或数据未加载。
            # 在某些情况下，如果行是动态确定的，这里可能不需要严格约束。
            self.solver_wrapper.logger.warning(f"表 '{self.table_name}' 或其行信息未找到，未添加行变量 '{var_self}' 的有效性约束。")

        # 添加约束：确保 var_other 是 other_table_name 中的一个有效行ID
        if other_table_name in self.solver_wrapper.tables and self.solver_wrapper.tables[other_table_name].rows:
            self.solver_wrapper.add(Or([var_other == i for i in self.solver_wrapper.tables[other_table_name].rows]))
        else:
            self.solver_wrapper.logger.warning(f"表 '{other_table_name}' 或其行信息未找到，未添加行变量 '{var_other}' 的有效性约束。")
            
        return var_self, var_other

    def __eq__(self, other: Any) -> Union[Tuple[ExprRef, ExprRef], Expression, 'ColumnConstraint']:
        self.solver_wrapper.logger.info(f"DEBUG_LOG: Column.__eq__ called. self: ({type(self)}, id: {id(self)}, desc: {self._description}), other: ({type(other)}, value: '{str(other)[:100]}')")
        if isinstance(other, Column):
            # Column对象 vs Column对象 (通常表示表之间的JOIN条件)
            # 核心目标：
            # 1. 为这两个表（self 和 other）创建一对相应的Z3行变量（var1, var2）。
            # 2. 添加行ID有效性约束，确保这两个变量的值在各自表的有效行范围内。
            # 3. 添加核心的JOIN约束：self.function(var1) == other.function(var2)。
            # 4. 返回这对行变量 (var1, var2) 以及它们各自的表名，以便 SqlToZ3.add 方法可以记录这个JOIN关系。

            table1_name = self.table_name
            table2_name = other.table_name
            
            self.solver_wrapper.logger.info(f"列与列比较 (JOIN): {self._description} ({table1_name}) == {other._description} ({table2_name})")

            # _create_join_vars_and_row_constraints 会创建两个新的自由Z3行变量，并添加行有效性约束
            # 它返回 (var_for_table1, var_for_table2)
            var1, var2 = self._create_join_vars_and_row_constraints(table2_name)
            
            # 添加核心的JOIN等式约束到求解器
            join_expr = self.function(var1) == other.function(var2)
            self.solver_wrapper.add(join_expr) # 直接将Z3表达式添加到求解器
            
            self.solver_wrapper.logger.debug(f"创建的JOIN变量对: {var1} (for {table1_name}), {var2} (for {table2_name}). JOIN表达式: {join_expr}")
            
            # 返回 Z3 行变量和它们各自的表名
            return (var1, table1_name, var2, table2_name)
            
        elif isinstance(other, Expression):
            # 情况2: 列 == 表达式
            self.solver_wrapper.logger.debug(f"列与表达式比较 (==): {self._description} == {other.description}")
            return Expression(
                lambda row_var: self.function(row_var) == other(row_var), # 表达式的求值函数
                self.solver_wrapper, # 传递solver_wrapper
                f"({self._description} == {other.description})" # 新表达式的描述
            )
        else:
            # 情况3: 列 == 常量
            self.solver_wrapper.logger.info(f"DEBUG_LOG: Column.__eq__ entering 'else' branch for constant comparison. self: {self._description}, other: '{other}'")
            constraint = ColumnConstraint(self, "==", other, self.solver_wrapper)
            self.solver_wrapper.logger.info(f"DEBUG_LOG: Column.__eq__ created ColumnConstraint: {type(constraint)}, id: {id(constraint)}, desc: {constraint.description}")
            return constraint

    def __ne__(self, other: Any) -> Union[Tuple[ExprRef, str, ExprRef, str], Expression, 'ColumnConstraint']:
        """重载不等于操作符"""
        self.solver_wrapper.logger.info(f"DEBUG_LOG: Column.__ne__ called. self: ({type(self)}, id: {id(self)}, desc: {self._description}), other: ({type(other)}, value: '{str(other)[:100]}')")
        
        if isinstance(other, Column):
            # 情况1: 列 != 列 (处理JOIN)
            self.solver_wrapper.logger.debug(f"列与列比较 (!=): {self._description} != {other._description}")
            
            table1_name = self.table_name
            table2_name = other.table_name
            
            var1, var2 = self._create_join_vars_and_row_constraints(other.table_name)
            join_constraint = self.function(var1) != other.function(var2)
            self.solver_wrapper.add(join_constraint)
            self.solver_wrapper.logger.info(f"添加JOIN约束到求解器: {self.function(var1)} != {other.function(var2)}")
            return (var1, table1_name, var2, table2_name)
        elif isinstance(other, Expression):
            # 情况2: 列 != 表达式
            self.solver_wrapper.logger.debug(f"列与表达式比较 (!=): {self._description} != {other.description}")
            return Expression(
                lambda row_var: self.function(row_var) != other(row_var),
                self.solver_wrapper,
                f"({self._description} != {other.description})"
            )
        else:
            # 情况3: 列 != 常量
            self.solver_wrapper.logger.debug(f"列与常量比较 (!=): {self._description} != {other}")
            constraint = ColumnConstraint(self, "!=", other, self.solver_wrapper)
            self.solver_wrapper.logger.info(f"DEBUG_LOG: Column.__ne__ created ColumnConstraint: {type(constraint)}, id: {id(constraint)}, desc: {constraint.description}")
            return constraint
    
    # 添加对其他比较运算符的类似优化
    def __gt__(self, other: Any) -> Union[Tuple[ExprRef, str, ExprRef, str], Expression, 'ColumnConstraint']:
        """重载大于运算符"""
        if isinstance(other, Column):
            # 情况1: 列 > 列 (处理JOIN)
            self.solver_wrapper.logger.debug(f"列与列比较 (>): {self._description} > {other._description}")
            
            table1_name = self.table_name
            table2_name = other.table_name
            
            var1, var2 = self._create_join_vars_and_row_constraints(other.table_name)
            join_constraint = self.function(var1) > other.function(var2)
            self.solver_wrapper.add(join_constraint)
            self.solver_wrapper.logger.info(f"添加JOIN约束到求解器: {self.function(var1)} > {other.function(var2)}")
            return (var1, table1_name, var2, table2_name)
        elif isinstance(other, (int, float, str)): # 支持与多种类型的常量比较
            self.solver_wrapper.logger.debug(f"列与常量比较 (>): {self._description} > {other}")
            return ColumnConstraint(self, ">", other, self.solver_wrapper)
        elif isinstance(other, Expression):
            self.solver_wrapper.logger.debug(f"列与表达式比较 (>): {self._description} > {other.description if hasattr(other, 'description') else other}")
            return Expression(
                lambda row_var: self.function(row_var) > other(row_var),
                self.solver_wrapper,
                f"({self._description} > {getattr(other, 'description', str(other))})"
            )
        # 如果 other 不是 Column, Expression, 或支持的常量类型，则此操作未定义
        self.solver_wrapper.logger.warning(f"不支持的操作: Column > {type(other)}")
        return NotImplemented
    
    def __lt__(self, other: Any) -> Union[Tuple[ExprRef, str, ExprRef, str], Expression, 'ColumnConstraint']:
        """重载小于运算符"""
        if isinstance(other, Column):
            # 情况1: 列 < 列 (处理JOIN)
            self.solver_wrapper.logger.debug(f"列与列比较 (<): {self._description} < {other._description}")
            
            table1_name = self.table_name
            table2_name = other.table_name
            
            var1, var2 = self._create_join_vars_and_row_constraints(other.table_name)
            join_constraint = self.function(var1) < other.function(var2)
            self.solver_wrapper.add(join_constraint)
            self.solver_wrapper.logger.info(f"添加JOIN约束到求解器: {self.function(var1)} < {other.function(var2)}")
            return (var1, table1_name, var2, table2_name)
        elif isinstance(other, (int, float, str)):
            self.solver_wrapper.logger.debug(f"列与常量比较 (<): {self._description} < {other}")
            return ColumnConstraint(self, "<", other, self.solver_wrapper)
        elif isinstance(other, Expression):
            self.solver_wrapper.logger.debug(f"列与表达式比较 (<): {self._description} < {other.description if hasattr(other, 'description') else other}")
            return Expression(
                lambda row_var: self.function(row_var) < other(row_var),
                self.solver_wrapper,
                f"({self._description} < {getattr(other, 'description', str(other))})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column < {type(other)}")
        return NotImplemented
    
    def __ge__(self, other: Any) -> Union[Tuple[ExprRef, str, ExprRef, str], Expression, 'ColumnConstraint']:
        """重载大于等于运算符"""
        if isinstance(other, Column):
            # 情况1: 列 >= 列 (处理JOIN)
            self.solver_wrapper.logger.debug(f"列与列比较 (>=): {self._description} >= {other._description}")
            
            table1_name = self.table_name
            table2_name = other.table_name
            
            var1, var2 = self._create_join_vars_and_row_constraints(other.table_name)
            join_constraint = self.function(var1) >= other.function(var2)
            self.solver_wrapper.add(join_constraint)
            self.solver_wrapper.logger.info(f"添加JOIN约束到求解器: {self.function(var1)} >= {other.function(var2)}")
            return (var1, table1_name, var2, table2_name)
        elif isinstance(other, (int, float, str)):
            self.solver_wrapper.logger.debug(f"列与常量比较 (>=): {self._description} >= {other}")
            return ColumnConstraint(self, ">=", other, self.solver_wrapper)
        elif isinstance(other, Expression):
            self.solver_wrapper.logger.debug(f"列与表达式比较 (>=): {self._description} >= {other.description if hasattr(other, 'description') else other}")
            return Expression(
                lambda row_var: self.function(row_var) >= other(row_var),
                self.solver_wrapper,
                f"({self._description} >= {getattr(other, 'description', str(other))})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column >= {type(other)}")
        return NotImplemented
    
    def __le__(self, other: Any) -> Union[Tuple[ExprRef, str, ExprRef, str], Expression, 'ColumnConstraint']:
        """重载小于等于运算符"""
        if isinstance(other, Column):
            # 情况1: 列 <= 列 (处理JOIN)
            self.solver_wrapper.logger.debug(f"列与列比较 (<=): {self._description} <= {other._description}")
            
            table1_name = self.table_name
            table2_name = other.table_name
            
            var1, var2 = self._create_join_vars_and_row_constraints(other.table_name)
            join_constraint = self.function(var1) <= other.function(var2)
            self.solver_wrapper.add(join_constraint)
            self.solver_wrapper.logger.info(f"添加JOIN约束到求解器: {self.function(var1)} <= {other.function(var2)}")
            return (var1, table1_name, var2, table2_name)
        elif isinstance(other, (int, float, str)):
            self.solver_wrapper.logger.debug(f"列与常量比较 (<=): {self._description} <= {other}")
            return ColumnConstraint(self, "<=", other, self.solver_wrapper)
        elif isinstance(other, Expression):
            self.solver_wrapper.logger.debug(f"列与表达式比较 (<=): {self._description} <= {other.description if hasattr(other, 'description') else other}")
            return Expression(
                lambda row_var: self.function(row_var) <= other(row_var),
                self.solver_wrapper,
                f"({self._description} <= {getattr(other, 'description', str(other))})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column <= {type(other)}")
        return NotImplemented
    
    # --- 算术运算符 ---    
    def __add__(self, other: Any) -> Expression:
        """
        重载加法 (+) 操作符。
        无论 `other` 是常量、另一个 `Column` 还是 `Expression`，结果都是一个新的 `Expression` 对象。

        Args:
            other (Any): 与此列相加的对象。可以是常量 (int, float, str -- 字符串会进行拼接)，
                         另一个 `Column` 对象，或一个 `Expression` 对象。

        Returns:
            Expression: 一个新的 `Expression` 对象，代表加法操作。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_name = "+"
        self.solver_wrapper.logger.debug(f"列运算 ({op_name}): {self._description} {op_name} {getattr(other, 'description', other)}")
        if isinstance(other, (int, float, str)):
            # 列 + 常量
            return Expression(
                lambda row_var: self.function(row_var) + other, # Z3会自动处理类型（例如 IntExpr + Python int）
                self.solver_wrapper,
                f"({self._description} {op_name} {other})"
            )
        elif isinstance(other, Column):
            # 列 + 列
            return Expression(
                lambda row_var: self.function(row_var) + other.function(row_var), # 两列在同一行上的值相加
                self.solver_wrapper,
                f"({self._description} {op_name} {other._description})"
            )
        elif isinstance(other, Expression):
            # 列 + 表达式
            return Expression(
                lambda row_var: self.function(row_var) + other(row_var), # 列的值与表达式在同一行上的结果相加
                self.solver_wrapper,
                f"({self._description} {op_name} {other._description})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column {op_name} {type(other)}")
        return NotImplemented
    
    def __sub__(self, other: Any) -> Expression:
        """
        重载减法 (-) 操作符。
        类似于 `__add__`，结果总是 `Expression` 对象。
        不支持字符串的减法。

        Args:
            other (Any): 从此列中减去的对象 (int, float, Column, Expression)。

        Returns:
            Expression: 代表减法操作的新 `Expression`。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_name = "-"
        self.solver_wrapper.logger.debug(f"列运算 ({op_name}): {self._description} {op_name} {getattr(other, 'description', other)}")
        if isinstance(other, (int, float)): # 字符串减法无意义
            return Expression(
                lambda row_var: self.function(row_var) - other,
                self.solver_wrapper,
                f"({self._description} {op_name} {other})"
            )
        elif isinstance(other, Column):
            return Expression(
                lambda row_var: self.function(row_var) - other.function(row_var),
                self.solver_wrapper,
                f"({self._description} {op_name} {other._description})"
            )
        elif isinstance(other, Expression):
            return Expression(
                lambda row_var: self.function(row_var) - other(row_var),
                self.solver_wrapper,
                f"({self._description} {op_name} {other._description})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column {op_name} {type(other)}")
        return NotImplemented
    
    def __mul__(self, other: Any) -> Expression:
        """
        重载乘法 (*) 操作符。
        类似于 `__add__`，结果总是 `Expression` 对象。
        不支持字符串与其他类型的乘法 (除了字符串与整数的Z3序列重复)。

        Args:
            other (Any): 与此列相乘的对象 (int, float, Column, Expression)。

        Returns:
            Expression: 代表乘法操作的新 `Expression`。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_name = "*"
        self.solver_wrapper.logger.debug(f"列运算 ({op_name}): {self._description} {op_name} {getattr(other, 'description', other)}")
        if isinstance(other, (int, float)): # Z3中，Seq * Int 表示重复，Arith * Arith 正常乘法
            return Expression(
                lambda row_var: self.function(row_var) * other,
                self.solver_wrapper,
                f"({self._description} {op_name} {other})"
            )
        elif isinstance(other, Column):
            return Expression(
                lambda row_var: self.function(row_var) * other.function(row_var),
                self.solver_wrapper,
                f"({self._description} {op_name} {other._description})"
            )
        elif isinstance(other, Expression):
            return Expression(
                lambda row_var: self.function(row_var) * other(row_var),
                self.solver_wrapper,
                f"({self.description} {op_name} {other.description})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column {op_name} {type(other)}")
        return NotImplemented
    
    def __truediv__(self, other: Any) -> Expression:
        """
        重载真除法 (/) 操作符。
        类似于 `__add__`，结果总是 `Expression` 对象。
        要求操作数为数值类型。

        Args:
            other (Any): 除数 (int, float, Column, Expression)。

        Returns:
            Expression: 代表除法操作的新 `Expression`。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_name = "/"
        self.solver_wrapper.logger.debug(f"列运算 ({op_name}): {self.description} {op_name} {getattr(other, 'description', other)}")
        if isinstance(other, (int, float)):
            return Expression(
                # Z3要求除法操作数是实数或整数，如果列是字符串，这里会出错
                lambda row_var: self.function(row_var) / other,
                self.solver_wrapper,
                f"({self.description} {op_name} {other})"
            )
        elif isinstance(other, Column):
            return Expression(
                lambda row_var: self.function(row_var) / other.function(row_var),
                self.solver_wrapper,
                f"({self.description} {op_name} {other.description})"
            )
        elif isinstance(other, Expression):
            return Expression(
                lambda row_var: self.function(row_var) / other(row_var),
                self.solver_wrapper,
                f"({self.description} {op_name} {other.description})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: Column {op_name} {type(other)}")
        return NotImplemented

    # --- 反向算术运算符 --- 
    def __radd__(self, other: Any) -> Expression:
        """
        重载右加法 (+) 操作符 (例如 `constant + column`)。
        与 `__add__` 类似，但运算顺序相反，确保描述字符串正确反映操作顺序。
        """
        self.solver_wrapper.logger.debug(f"右列运算 (+): {getattr(other, 'description', other)} + {self.description}")
        op_name = "+"
        if isinstance(other, (int, float, str)):
            return Expression(
                lambda row_var: other + self.function(row_var),
                self.solver_wrapper,
                f"{other} {op_name} {self.description}"
            )
        elif isinstance(other, Expression):
            return Expression(
                lambda row_var: other(row_var) + self.function(row_var),
                self.solver_wrapper,
                f"{other.description} {op_name} {self.description}"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: {type(other)} {op_name} Column")
        return NotImplemented
    
    def __rsub__(self, other: Any) -> Expression:
        """
        重载右减法 (-) 操作符 (例如 `constant - column`)。

        Args:
            other (Any): 被减数 (通常是常量 int, float)。

        Returns:
            Expression: 代表减法操作的新 `Expression`。

        Raises:
            NotImplementedError: 如果 `other` 的类型不支持。
        """
        op_name = "-"
        self.solver_wrapper.logger.debug(f"右列运算 ({op_name}): {getattr(other, 'description', other)} {op_name} {self.description}")
        if isinstance(other, (int, float)): # 常量 - 列
            return Expression(
                lambda row_var: other - self.function(row_var),
                self.solver_wrapper,
                f"({other} {op_name} {self.description})"
            )
        # 不支持 Column - Column 或 Expression - Column 通过 __rsub__，那些应由 other 的 __sub__ 处理
        self.solver_wrapper.logger.warning(f"不支持的操作: {type(other)} {op_name} Column")
        return NotImplemented
    
    def __rmul__(self, other: Any) -> Expression:
        """
        重载右乘法 (*) 操作符 (例如 `constant * column`)。
        与 `__mul__` 类似，但运算顺序相反，确保描述字符串正确反映操作顺序。
        """
        self.solver_wrapper.logger.debug(f"右列运算 (*): {getattr(other, 'description', other)} * {self.description}")
        op_name = "*"
        if isinstance(other, (int, float, str)):
            return Expression(
                lambda row_var: other * self.function(row_var),
                self.solver_wrapper,
                f"{other} {op_name} {self.description}"
            )
        elif isinstance(other, Expression):
            return Expression(
                lambda row_var: other(row_var) * self.function(row_var),
                self.solver_wrapper,
                f"{other.description} {op_name} {self.description}"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: {type(other)} {op_name} Column")
        return NotImplemented
    
    def __rtruediv__(self, other: Any) -> Expression:
        """
        重载右真除法 (/) 运算符 (例如 `constant / column`)。

        Args:
            other (Any): 被除数 (通常是常量 int, float)。

        Returns:
            Expression: 代表除法操作的新 `Expression`。

        Raises:
            NotImplementedError: 如果 `other` 的类型不支持。
        """
        op_name = "/"
        self.solver_wrapper.logger.debug(f"右列运算 ({op_name}): {getattr(other, 'description', other)} {op_name} {self.description}")
        if isinstance(other, (int, float)): # 常量 / 列
            return Expression(
                lambda row_var: other / self.function(row_var),
                self.solver_wrapper,
                f"({other} {op_name} {self.description})"
            )
        self.solver_wrapper.logger.warning(f"不支持的操作: {type(other)} {op_name} Column")
        return NotImplemented

    def length(self) -> Expression:
        """
        获取字符串长度
        
        Returns:
            Expression: 代表字符串长度的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串length操作: LENGTH({self.description})")
        
        from z3 import Length
        return Expression(
            lambda row_var: Length(self.function(row_var)),
            self.solver_wrapper,
            f"LENGTH({self.description})"
        )
        
    # --- 类型转换方法 ---
    def cast_to_string(self) -> Expression:
        """
        将列值转换为字符串
        
        Returns:
            Expression: 代表类型转换的表达式
        """
        self.solver_wrapper.logger.debug(f"类型转换: ToString({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._cast_to_string(self.function(row_var)),
            self.solver_wrapper,
            f"ToString({self.description})"
        )
    
    def cast_to_int(self) -> Expression:
        """
        将列值转换为整数
        
        Returns:
            Expression: 代表类型转换的表达式
        """
        self.solver_wrapper.logger.debug(f"类型转换: ToInt({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._cast_to_int(self.function(row_var)),
            self.solver_wrapper,
            f"ToInt({self.description})"
        )
    
    def cast_to_real(self) -> Expression:
        """
        将列值转换为浮点数
        
        Returns:
            Expression: 代表类型转换的表达式
        """
        self.solver_wrapper.logger.debug(f"类型转换: ToReal({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._cast_to_real(self.function(row_var)),
            self.solver_wrapper,
            f"ToReal({self.description})"
        )
    
    def cast_to_bool(self) -> Expression:
        """
        将列值转换为布尔值

        Returns:
            Expression: 表示类型转换结果的表达式对象
        """
        self.solver_wrapper.logger.debug(f"类型转换: ToBool({self.description})")
        
        return Expression(
            lambda row_var: BoolVal(True) if self.function(row_var) != 0 else BoolVal(False),
            self.solver_wrapper,
            f"ToBool({self.description})"
        )
    
    # --- 日期操作方法 ---
    def to_date(self, format_str: str) -> Expression:
        """
        将字符串转换为日期
        
        Args:
            format_str: 日期格式，如'%Y-%m-%d'
            
        Returns:
            Expression: 代表日期转换的表达式
        """
        self.solver_wrapper.logger.debug(f"日期转换: ToDate({self.description}, {format_str})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._convert_to_date(self.function(row_var), format_str),
            self.solver_wrapper,
            f"ToDate({self.description}, {format_str})"
        )
    
    def extract_year(self) -> Expression:
        """
        从日期中提取年份
        
        Returns:
            Expression: 代表年份提取的表达式
        """
        self.solver_wrapper.logger.debug(f"日期操作: ExtractYear({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._extract_year(self.function(row_var)),
            self.solver_wrapper,
            f"ExtractYear({self.description})"
        )
    
    def extract_month(self) -> Expression:
        """
        从日期中提取月份
        
        Returns:
            Expression: 代表月份提取的表达式
        """
        self.solver_wrapper.logger.debug(f"日期操作: ExtractMonth({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._extract_month(self.function(row_var)),
            self.solver_wrapper,
            f"ExtractMonth({self.description})"
        )
    
    def extract_day(self) -> Expression:
        """
        从日期中提取天
        
        Returns:
            Expression: 代表提取天的表达式
        """
        self.solver_wrapper.logger.debug(f"日期提取天: ExtractDay({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._create_extract_day_expression(self.function(row_var)),
            self.solver_wrapper,
            f"ExtractDay({self.description})"
        )

    # --- 字符串相关方法 ---
    def like(self, pattern: str) -> Expression:
        """
        SQL LIKE操作符，用于模式匹配
        
        Args:
            pattern: 匹配模式，如'A%','_B%'等
            
        Returns:
            Expression: 代表LIKE匹配的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串LIKE操作: {self.description} LIKE {pattern}")
        
        # 这里简化处理，实际情况下需要将SQL的LIKE模式转换为正则表达式
        return Expression(
            lambda row_var: self.solver_wrapper._create_like_expression(self.function(row_var), pattern),
            self.solver_wrapper,
            f"{self.description} LIKE {pattern}"
        )
    
    def contains(self, substring: str) -> Expression:
        """
        检查字符串是否包含子串
        
        Args:
            substring: 要检查的子串
            
        Returns:
            Expression: 代表包含检查的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串contains操作: {self.description}.contains({substring})")
        
        from z3 import Contains
        return Expression(
            lambda row_var: Contains(self.function(row_var), substring),
            self.solver_wrapper,
            f"{self.description}.contains({substring})"
        )
    
    def startswith(self, prefix: str) -> Expression:
        """
        检查字符串是否以指定前缀开始
        
        Args:
            prefix: 前缀字符串
            
        Returns:
            Expression: 代表前缀检查的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串startswith操作: {self.description}.startswith({prefix})")
        
        from z3 import PrefixOf
        return Expression(
            lambda row_var: PrefixOf(prefix, self.function(row_var)),
            self.solver_wrapper,
            f"{self.description}.startswith({prefix})"
        )
    
    def endswith(self, suffix: str) -> 'Expression':
        """
        检查字符串列是否以指定后缀结尾。
        
        Args:
            suffix: 要检查的后缀字符串
            
        Returns:
            Expression: 表示 "column ENDSWITH suffix" 的表达式对象
        """
        from .expression import Expression
        
        def endswith_func(row_var):
            col_val = self.function(row_var)
            suffix_val = StringVal(str(suffix), self.solver_wrapper.solver.ctx)
            return self.solver_wrapper._create_endswith_constraint(col_val, suffix_val)
        
        return Expression(endswith_func, self.solver_wrapper, f"{self.description} ENDSWITH '{suffix}'")
    
    def substring(self, start: int, length: int) -> 'Expression':
        """
        从字符串列中提取子字符串。
        
        Args:
            start: 起始位置（从1开始）
            length: 要提取的字符数
            
        Returns:
            Expression: 表示 "SUBSTRING(column, start, length)" 的表达式对象
        """
        from .expression import Expression
        
        def substring_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_substring(col_val, start, length)
        
        return Expression(substring_func, self.solver_wrapper, f"SUBSTRING({self.description}, {start}, {length})")
        
    def concat(self, other: Any) -> 'Expression':
        """
        创建字符串连接表达式，将当前列与另一个列或常量字符串连接起来。

        Args:
            other: 要连接的另一个列或常量字符串

        Returns:
            Expression: 表示连接结果的表达式对象
        """
        from .expression import Expression
        
        def concat_func(row_var):
            col_val = self.function(row_var)
            
            if isinstance(other, Column):
                other_val = other.function(row_var)
                return Concat(col_val, other_val)
            elif isinstance(other, Expression):
                other_val = other(row_var)
                return Concat(col_val, other_val)
            else:
                # 如果 other 是常量值，则直接连接
                other_str_val = StringVal(str(other), self.solver_wrapper.solver.ctx)
                return Concat(col_val, other_str_val)
            
        other_desc = other.description if hasattr(other, 'description') else str(other)
        description = f"CONCAT({self.description}, {other_desc})"
        return Expression(concat_func, self.solver_wrapper, description)
    
    def upper(self) -> 'Expression':
        """
        将字符串列转换为大写。
        
        Returns:
            Expression: 表示 "UPPER(column)" 的表达式对象
        """
        from .expression import Expression
        
        def upper_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_upper(col_val)
        
        return Expression(upper_func, self.solver_wrapper, f"UPPER({self.description})")
    
    def lower(self) -> 'Expression':
        """
        将字符串列转换为小写。
        
        Returns:
            Expression: 表示 "LOWER(column)" 的表达式对象
        """
        from .expression import Expression
        
        def lower_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_lower(col_val)
        
        return Expression(lower_func, self.solver_wrapper, f"LOWER({self.description})")
    
    def replace(self, old_str: str, new_str: str) -> 'Expression':
        """
        替换字符串列中的子字符串。
        
        Args:
            old_str: 要替换的子字符串
            new_str: 替换成的新子字符串
            
        Returns:
            Expression: 表示 "REPLACE(column, old_str, new_str)" 的表达式对象
        """
        from .expression import Expression
        
        def replace_func(row_var):
            col_val = self.function(row_var)
            old_val = StringVal(str(old_str), self.solver_wrapper.solver.ctx)
            new_val = StringVal(str(new_str), self.solver_wrapper.solver.ctx)
            return self.solver_wrapper._create_replace(col_val, old_val, new_val)
        
        return Expression(replace_func, self.solver_wrapper, f"REPLACE({self.description}, {old_str}, {new_str})")
    
    def trim(self) -> 'Expression':
        """
        去除字符串列两端的空白字符。
        
        Returns:
            Expression: 表示 "TRIM(column)" 的表达式对象
        """
        from .expression import Expression
        
        def trim_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_trim(col_val)
        
        return Expression(trim_func, self.solver_wrapper, f"TRIM({self.description})")
    
    # --- NULL检查和其他条件方法 ---
    def is_null(self) -> 'ColumnConstraint':
        """
        创建一个检查列值是否为NULL的约束。

        Returns:
            ColumnConstraint: 表示 "column IS NULL" 的约束对象。
        """
        return ColumnConstraint(self, "IS NULL", None, self.solver_wrapper)

    def is_not_null(self) -> 'ColumnConstraint':
        """
        创建一个检查列值是否不为NULL的约束。

        Returns:
            ColumnConstraint: 表示 "column IS NOT NULL" 的约束对象。
        """
        return ColumnConstraint(self, "IS NOT NULL", None, self.solver_wrapper)
        
    def between(self, lower: Any, upper: Any) -> 'ColumnConstraint':
        """
        创建一个检查列值是否在指定范围内的约束。

        Args:
            lower: 范围的下限值。
            upper: 范围的上限值。

        Returns:
            ColumnConstraint: 表示 "column BETWEEN lower AND upper" 的约束对象。
        """
        return ColumnConstraint(self, "BETWEEN", (lower, upper), self.solver_wrapper)
        
    def in_list(self, values: list) -> 'ColumnConstraint':
        """
        创建一个检查列值是否在指定列表中的约束。

        Args:
            values: 要检查的值列表。

        Returns:
            ColumnConstraint: 表示 "column IN (values)" 的约束对象。
        """
        return ColumnConstraint(self, "IN", values, self.solver_wrapper)
        
    def not_in_list(self, values: list) -> 'ColumnConstraint':
        """
        创建一个检查列值是否不在指定列表中的约束。

        Args:
            values: 要检查的值列表。

        Returns:
            ColumnConstraint: 表示 "column NOT IN (values)" 的约束对象。
        """
        return ColumnConstraint(self, "NOT IN", values, self.solver_wrapper)

    def like(self, pattern: str) -> 'Expression':
        """
        创建一个LIKE表达式，检查列值是否匹配指定的模式。

        Args:
            pattern: SQL LIKE模式，可以包含通配符 % 和 _。

        Returns:
            Expression: 表示 "column LIKE pattern" 的表达式对象。
        """
        from .expression import Expression
        
        def like_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_like_expression(col_val, pattern)
        
        return Expression(like_func, self.solver_wrapper, f"{self.description} LIKE '{pattern}'")

    def contains(self, substring: str) -> 'Expression':
        """
        创建一个检查列值是否包含指定子串的表达式。

        Args:
            substring: 要检查的子串。

        Returns:
            Expression: 表示 "column CONTAINS substring" 的表达式对象。
        """
        from .expression import Expression
        
        def contains_func(row_var):
            col_val = self.function(row_var)
            # 使用Z3的字符串包含函数
            return Contains(col_val, StringVal(substring))
        
        return Expression(contains_func, self.solver_wrapper, f"{self.description} CONTAINS '{substring}'")

    def startswith(self, prefix: str) -> 'Expression':
        """
        创建一个检查列值是否以指定前缀开头的表达式。

        Args:
            prefix: 要检查的前缀。

        Returns:
            Expression: 表示 "column STARTSWITH prefix" 的表达式对象。
        """
        from .expression import Expression
        
        def startswith_func(row_var):
            col_val = self.function(row_var)
            # 使用Z3的字符串前缀函数
            return PrefixOf(StringVal(prefix), col_val)
        
        return Expression(startswith_func, self.solver_wrapper, f"{self.description} STARTSWITH '{prefix}'")

    def endswith(self, suffix: str) -> 'Expression':
        """
        创建一个检查列值是否以指定后缀结尾的表达式。

        Args:
            suffix: 要检查的后缀。

        Returns:
            Expression: 表示 "column ENDSWITH suffix" 的表达式对象
        """
        from .expression import Expression
        
        def endswith_func(row_var):
            col_val = self.function(row_var)
            suffix_val = StringVal(str(suffix), self.solver_wrapper.solver.ctx)
            return self.solver_wrapper._create_endswith_constraint(col_val, suffix_val)
        
        return Expression(endswith_func, self.solver_wrapper, f"{self.description} ENDSWITH '{suffix}'")

    def substring(self, start: int, length: int) -> 'Expression':
        """
        创建一个提取列值子串的表达式。

        Args:
            start: 子串的起始位置（从0开始）。
            length: 子串的长度。

        Returns:
            Expression: 表示 "SUBSTRING(column, start, length)" 的表达式对象
        """
        from .expression import Expression
        
        def substring_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_substring(col_val, start, length)
        
        return Expression(substring_func, self.solver_wrapper, f"SUBSTRING({self.description}, {start}, {length})")

    def concat(self, other: Any) -> 'Expression':
        """
        创建一个字符串连接表达式。

        Args:
            other: 要连接的值，可以是另一个列、表达式或常量。

        Returns:
            Expression: 表示 "column || other" 的表达式对象。
        """
        from .expression import Expression
        
        def concat_func(row_var):
            col_val = self.function(row_var)
            
            # 处理不同类型的other
            if isinstance(other, Column):
                other_val = other.function(row_var)
            elif isinstance(other, Expression):
                other_val = other(row_var)
            elif isinstance(other, str):
                other_val = StringVal(other)
            else:
                # 对于其他类型，尝试转换为字符串
                other_val = self.solver_wrapper._cast_to_string(other)
            
            # 使用Z3的字符串连接函数
            return Concat(col_val, other_val)
        
        other_desc = getattr(other, 'description', str(other))
        return Expression(concat_func, self.solver_wrapper, f"({self.description} || {other_desc})")

    def upper(self) -> 'Expression':
        """
        创建一个将列值转换为大写的表达式。

        Returns:
            Expression: 表示 "UPPER(column)" 的表达式对象
        """
        from .expression import Expression
        
        def upper_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_upper(col_val)
        
        return Expression(upper_func, self.solver_wrapper, f"UPPER({self.description})")

    def lower(self) -> 'Expression':
        """
        创建一个将列值转换为小写的表达式。

        Returns:
            Expression: 表示 "LOWER(column)" 的表达式对象
        """
        from .expression import Expression
        
        def lower_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_lower(col_val)
        
        return Expression(lower_func, self.solver_wrapper, f"LOWER({self.description})")

    def replace(self, old_str: str, new_str: str) -> 'Expression':
        """
        创建一个替换列值中子串的表达式。

        Args:
            old_str: 要替换的子串。
            new_str: 替换成的新子串。

        Returns:
            Expression: 表示 "REPLACE(column, old_str, new_str)" 的表达式对象
        """
        from .expression import Expression
        
        def replace_func(row_var):
            col_val = self.function(row_var)
            old_val = StringVal(str(old_str), self.solver_wrapper.solver.ctx)
            new_val = StringVal(str(new_str), self.solver_wrapper.solver.ctx)
            return self.solver_wrapper._create_replace(col_val, old_val, new_val)
        
        return Expression(replace_func, self.solver_wrapper, f"REPLACE({self.description}, {old_str}, {new_str})")

    def trim(self) -> 'Expression':
        """
        创建一个去除列值两端空白的表达式。

        Returns:
            Expression: 表示 "TRIM(column)" 的表达式对象
        """
        from .expression import Expression
        
        def trim_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_trim(col_val)
        
        return Expression(trim_func, self.solver_wrapper, f"TRIM({self.description})")

    def extract_year(self) -> 'Expression':
        """
        从日期列中提取年份。

        Returns:
            Expression: 表示 "EXTRACT(YEAR FROM column)" 的表达式对象
        """
        from .expression import Expression
        
        def extract_year_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._extract_year(col_val)
        
        return Expression(extract_year_func, self.solver_wrapper, f"ExtractYear({self.description})")

    def extract_month(self) -> 'Expression':
        """
        从日期列中提取月份。

        Returns:
            Expression: 表示 "EXTRACT(MONTH FROM column)" 的表达式对象
        """
        from .expression import Expression
        
        def extract_month_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._extract_month(col_val)
        
        return Expression(extract_month_func, self.solver_wrapper, f"ExtractMonth({self.description})")

    def extract_day(self) -> 'Expression':
        """
        从日期列中提取日期。

        Returns:
            Expression: 表示 "EXTRACT(DAY FROM column)" 的表达式对象
        """
        from .expression import Expression
        
        def extract_day_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._extract_day(col_val)
        
        return Expression(extract_day_func, self.solver_wrapper, f"ExtractDay({self.description})")

    def for_row(self, row_id: int) -> ExprRef:
        """
        为指定行ID返回Z3表达式。
        这允许直接引用表中特定行的列值，例如 table.column.for_row(1)
        
        Args:
            row_id: 行的ID（整数）
            
        Returns:
            Z3表达式，代表该列在指定行的值
        """
        # 使用函数应用构造表达式，例如employees_name(1)表示employees表的第1行的name值
        return self.function(row_id)
        
    def get_function(self, column_name=None):
        """
        获取此列的Z3函数或指定列名的Z3函数
        
        Args:
            column_name: 可选，列名。如果提供，则返回该列的Z3函数
            
        Returns:
            Z3函数，可用于构建约束
        """
        if column_name is None:
            # 返回此列自身的Z3函数
            return self.function
        
        # 如果提供了列名，从solver_wrapper获取对应的Z3函数
        table_name = self.table_name
        if table_name in self.solver_wrapper.table_z3_functions and column_name in self.solver_wrapper.table_z3_functions[table_name]:
            return self.solver_wrapper.table_z3_functions[table_name][column_name]
        elif f"{table_name}_{column_name}" in self.solver_wrapper.column_functions:
            return self.solver_wrapper.column_functions[f"{table_name}_{column_name}"]
        else:
            raise AttributeError(f"在 Table.__getattr__ 中: 表 '{table_name}' 的列 '{column_name}' 的Z3函数未在solver_wrapper中找到")

class ColumnConstraint:
    """代表列与常量（或简单值）比较的待应用约束。
    
    当一个 `Column` 对象与一个常量（如整数、字符串、浮点数）进行比较时，
    例如 `my_column == 10` 或 `my_column > 'abc'`，会创建此类的一个实例。
    这个实例本身并不立即将约束添加到Z3求解器，而是封装了比较的意图。
    它允许用户决定何时以及如何在更广泛的上下文中使用这个约束，
    例如将其作为更复杂表达式的一部分，或在特定条件下才应用它。

    可以通过调用 `apply()` 方法或将其传递给 `SqlToZ3.add()` 来将此约束应用于求解器。
    也可以通过 `as_z3_expr()` 获取其对应的Z3表达式（如果尚未应用，会先应用）。
    它还重载了逻辑运算符 `__and__` 和 `__or__`，以便与其他 `ColumnConstraint` 或 `Expression` 对象组合。
    """
    def __init__(self, column_or_constraint: Union['Column', 'ColumnConstraint'], 
                operator: str, value: Any, solver_wrapper: 'SqlToZ3'):
        """
        初始化列约束。

        Args:
            column_or_constraint: 列对象，或在复合约束情况下（AND/OR）是左侧的ColumnConstraint对象。
            operator: 操作符，例如 '==', '!=', '<', '>', '<=', '>=', 'AND', 'OR'
            value: 与列比较的值，或在复合约束情况下是右侧的ColumnConstraint对象。
            solver_wrapper: 对SqlToZ3实例的引用，用于访问求解器、表和其他功能。
        """
        self.column = column_or_constraint
        self.operator = operator
        self.value = value
        self.solver_wrapper = solver_wrapper
        
        # 用于复合约束（AND/OR）的左侧操作数信息
        self.original_operator_for_composite = None 
        self.original_value_for_composite = None
        
        # 如果当前约束是通过AND/OR复合构造的
        if operator in ['AND', 'OR'] and isinstance(column_or_constraint, ColumnConstraint):
            # 保存原始操作符和值，用于递归构建复合约束的Z3表达式
            self.original_operator_for_composite = column_or_constraint.operator
            self.original_value_for_composite = column_or_constraint.value
        
        self.row_var = None  # 将在apply()时设置
        self.z3_constraint = None  # 存储生成的Z3约束，将在apply()时设置
        self.applied_to_solver = False  # 标记是否已将此约束应用到求解器
        
        # 构建描述字符串，用于日志和调试
        if operator in ['AND', 'OR']:
            if isinstance(column_or_constraint, ColumnConstraint) and isinstance(value, ColumnConstraint):
                self._description = f"({column_or_constraint.description}) {self.get_operator_symbol()} ({value.description})"
            else:
                self._description = f"未知的复合约束（{operator}）"
        else:
            # 处理简单约束
            if isinstance(column_or_constraint, Column):
                # 如果是Column对象，使用其description属性
                col_desc = column_or_constraint._description if hasattr(column_or_constraint, '_description') else str(column_or_constraint)
                self._description = f"{col_desc} {self.get_operator_symbol()} {value}"
            elif isinstance(column_or_constraint, ColumnConstraint):
                # 如果是ColumnConstraint对象，使用其description属性
                self._description = f"({column_or_constraint.description}) {self.get_operator_symbol()} {value}"
            else:
                # 其他情况
                self._description = f"{column_or_constraint} {self.get_operator_symbol()} {value}"
                
        self.solver_wrapper.logger.info(f"DEBUG_LOG: ColumnConstraint.__init__ completed. id: {id(self)}, Column: '{getattr(column_or_constraint, '_description', column_or_constraint)}', Operator: '{operator}', Value: '{str(value)[:100]}', SolverWrapper id: {id(solver_wrapper)}")

    @property
    def description(self) -> str:
        """返回描述字符串"""
        return self._description
    
    @description.setter
    def description(self, value: str):
        """设置描述字符串"""
        self._description = value
        
    def get_operator_symbol(self) -> str:
        """
        将操作符转换为符号表示
        
        Returns:
            str: 操作符的符号表示
        """
        operator_symbols = {
            "==": "==",
            "!=": "!=",
            "<": "<",
            ">": ">",
            "<=": "<=",
            ">=": ">=",
            "AND": "&&",
            "OR": "||",
            "IN": "IN",
            "NOT IN": "NOT IN",
            "LIKE": "LIKE",
            "IS NULL": "IS NULL",
            "IS NOT NULL": "IS NOT NULL"
        }
        return operator_symbols.get(self.operator, self.operator)

    def apply(self, join_var: Optional[ExprRef] = None, force_reapply: bool = False) -> ExprRef:
        self.solver_wrapper.logger.info(f"DEBUG_LOG: ColumnConstraint.apply called. id: {id(self)}, Constraint: '{self.description}', join_var: {join_var}, force_reapply: {force_reapply}, applied_to_solver: {self.applied_to_solver}")
        # 如果已经应用过且非强制重应用，直接返回之前使用的行变量
        if self.applied_to_solver and self.row_var is not None and not force_reapply:
            self.solver_wrapper.logger.debug(f"约束 '{self.description}' 已应用，跳过重复应用。使用的行变量: {self.row_var}")
            return self.row_var

        self.solver_wrapper.logger.debug(f"开始应用列约束: '{self.description}'. 传入的JOIN变量: {join_var}, Force reapply: {force_reapply}")

        # 1. 决定使用哪个行变量
        if join_var is not None:
            var_to_use = join_var
            self.solver_wrapper.logger.debug(f"  使用提供的JOIN变量: {var_to_use} for '{self.description}'")
        elif self.row_var is not None and not force_reapply: # 如果已有行变量且非强制，则复用
            var_to_use = self.row_var
            self.solver_wrapper.logger.debug(f"  复用已存在的行变量: {var_to_use} for '{self.description}'")
        else:
            # 为列的表创建一个唯一的Z3行变量
            unique_suffix = self.solver_wrapper._get_unique_var_suffix() if hasattr(self.solver_wrapper, '_get_unique_var_suffix') else "uid_apply"
            var_to_use = Int(f"{self.column.table_name}_row_{unique_suffix}")
            self.solver_wrapper.logger.debug(f"  为 '{self.description}' 创建新的行变量: {var_to_use} for table '{self.column.table_name}'")
            
            # 2. 添加行ID有效性约束 (仅当创建新变量时，且行信息可用)
            table_obj = self.solver_wrapper.tables.get(self.column.table_name)
            if table_obj and table_obj.rows:
                valid_rows_constraint = Or([var_to_use == i for i in table_obj.rows])
                self.solver_wrapper.add(valid_rows_constraint) 
                self.solver_wrapper.logger.debug(f"    为行变量 {var_to_use} 添加了有效行ID约束 (表: {self.column.table_name})。")
            else:
                self.solver_wrapper.logger.warning(
                    f"表 '{self.column.table_name}' 或其行信息未在solver_wrapper中找到/为空，未对新行变量 '{var_to_use}' 添加有效性约束。"
                )
        
        self.row_var = var_to_use # 记录或更新使用的行变量

        # 3. 使用_build_z3_expr_with_var方法构建Z3约束表达式
        self.z3_constraint = self._build_z3_expr_with_var(self.row_var)
        
        self.solver_wrapper.logger.info(f"DEBUG_LOG: ColumnConstraint.apply generated z3_constraint: {self.z3_constraint} (type: {type(self.z3_constraint)}) for '{self.description}' using row_var: {self.row_var}")

        # 4. 将Z3约束添加到求解器 (仅当它是新的或被强制重应用时)
        if self.z3_constraint is not None and (not self.applied_to_solver or force_reapply):
            self.solver_wrapper.add(self.z3_constraint) # SqlToZ3.add 处理实际添加
            self.applied_to_solver = True 
            self.solver_wrapper.logger.info(f"列约束 '{self.description}' 已成功应用并添加到求解器 (行变量: {self.row_var})。")
        elif self.z3_constraint is None:
            self.solver_wrapper.logger.error(f"列约束 '{self.description}' 未能生成有效的Z3表达式。")

        # 5. 返回行变量
        if self.row_var is None:
            # 理论上，此时row_var必有值
            self.solver_wrapper.logger.critical(f"在apply方法结束时，row_var意外为None for '{self.description}'")
            # 创建一个临时的，但这暗示了逻辑错误
            fallback_var_name = f"{self.column.table_name}_row_fallback_{self.solver_wrapper._get_unique_var_suffix()}"
            self.row_var = Int(fallback_var_name)
            self.solver_wrapper.logger.warning(f"  创建了备用row_var: {self.row_var}")

        return self.row_var
    
    def __bool__(self) -> bool:
        """
        允许在布尔上下文中使用 `ColumnConstraint` 对象 (例如 `if col_constraint:` )。
        这主要用于允许 `solver_wrapper.add(col_constraint)` 这样的语法，
        其中 `add` 方法内部可能会检查其真值（尽管通常是检查类型）。
        
        返回 `True` 意味着对象本身是有效的，但不代表其封装的约束是逻辑上的True。
        一个 `ColumnConstraint` 对象总是被认为是存在的，直到它被评估或应用。
        """
        # self.solver_wrapper.logger.debug(f"ColumnConstraint '{self.description}' 在布尔上下文中求值 -> True (表示对象存在)")
        return True # 表示对象存在且可被处理
    
    def as_z3_expr(self, row_var_override: Optional[ExprRef] = None) -> BoolRef:
        """
        返回此约束对应的Z3布尔表达式。
        
        如果约束尚未通过 `apply()` 应用到求解器，此方法会先调用 `apply()` 来生成并缓存Z3表达式，
        同时将约束添加到求解器。
        如果已经应用，则直接返回缓存的Z3表达式。

        当提供了 `row_var_override` 时，行为有所不同：
        它将使用这个覆盖的行变量 *临时构建* 一个Z3表达式版本，而 *不修改* 实例的 `self.row_var`
        或 `self.z3_constraint` 状态，也 *不会* 将此临时表达式重复添加到求解器。
        这主要用于当 `ColumnConstraint` 在一个 `Expression` 的lambda函数内部被求值时，
        需要确保它使用该 `Expression` 上下文中的行变量。

        Args:
            row_var_override (Optional[z3.ExprRef]): 如果提供，则在本次调用中强制使用此行变量
                                                    来构建返回的Z3表达式。这不会改变约束
                                                    实例内部的状态或其与求解器的主要关联。

        Returns:
            z3.BoolRef: 代表此列约束的Z3布尔表达式。

        Raises:
            RuntimeError: 如果在应用约束后 `z3_constraint` 仍为 `None` (理论上不应发生)。
        """
        self.solver_wrapper.logger.debug(f"请求 '{self.description}' 的Z3表达式. Applied: {self.applied_to_solver}, Override var: {row_var_override}")
        
        if row_var_override is not None:
            # 如果提供了行变量覆盖，则使用它构建一个临时的Z3表达式
            # 这不会修改实例的状态 (self.row_var, self.z3_constraint, self.applied_to_solver)
            # 也不会将此临时表达式添加到求解器。
            self.solver_wrapper.logger.debug(f"  为 '{self.description}' 使用覆盖的行变量 '{row_var_override}' 临时构建Z3表达式。")
            temp_expr = self._build_z3_expr_with_var(row_var_override)
            self.solver_wrapper.logger.debug(f"    临时构建的表达式: {temp_expr}")
            return temp_expr

        # 如果没有行变量覆盖，则走标准路径：应用约束（如果需要）并返回其主要的Z3表达式
        if not self.applied_to_solver or self.z3_constraint is None:
            self.solver_wrapper.logger.debug(f"  约束 '{self.description}' 尚未应用或无Z3表达式，将调用 apply()。")
            self.apply() # apply会处理行变量（可能新建），并设置 self.z3_constraint 和 self.applied_to_solver
        
        if self.z3_constraint is None:
            err_msg = f"约束 '{self.description}' 在调用 as_z3_expr() 后，其 z3_constraint 属性仍为 None。这不应发生。"
            self.solver_wrapper.logger.error(err_msg)
            raise RuntimeError(err_msg)
        
        self.solver_wrapper.logger.debug(f"  返回已应用/生成的Z3表达式: {self.z3_constraint} (使用行变量: {self.row_var})")
        return self.z3_constraint

    def _build_z3_expr_with_var(self, row_var_to_use: ExprRef) -> BoolRef:
        """
        (私有)核心逻辑：使用指定的行变量构建此约束的Z3表达式。
        此方法是实际生成Z3表达式的地方。
        """
        self.solver_wrapper.logger.debug(f"    [_build_z3_expr_with_var] 构建 '{self.description}' 使用行变量: {row_var_to_use}")
        
        col_val_at_row = self.column.function(row_var_to_use) # 例如, users_name(row_var)
        op = self.operator

        # 处理AND/OR复合约束的左侧 (self.column) 和右侧 (self.value，它也是一个ColumnConstraint)
        if op in ["AND", "OR"] and isinstance(self.value, ColumnConstraint):
            self.solver_wrapper.logger.debug(f"处理{op}复合约束: {self.description}")
            # 左侧表达式 (self.column 代表的约束)
            # 注意：这里的 self.column 实际上是复合约束的左侧 ColumnConstraint 的 self.column
            # 我们需要递归地构建左侧 ColumnConstraint 的 Z3 表达式
            # 但 self 本身就是左侧 ColumnConstraint，所以我们需要构建 self（不包含 self.value 的部分）的表达式
            left_expr_builder = ColumnConstraint(self.column, self.original_operator_for_composite, self.original_value_for_composite, self.solver_wrapper)
            left_expr = left_expr_builder._build_z3_expr_with_var(row_var_to_use)
            
            # 右侧表达式 (self.value 代表的约束)
            right_expr = self.value._build_z3_expr_with_var(row_var_to_use)
            
            if op == "AND":
                final_expr = And(left_expr, right_expr, self.solver_wrapper.solver.ctx)
            else: # OR
                final_expr = Or(left_expr, right_expr, self.solver_wrapper.solver.ctx)
            self.solver_wrapper.logger.debug(f"    [_build_z3_expr_with_var] 构建的复合 ({op}) 表达式 for '{self.description}': {final_expr}")
            return final_expr
        
        # 处理 IS NULL 和 IS NOT NULL
        if op == "IS NULL":
            # _create_is_null_expr 应该存在于 solver_wrapper 中，并处理特定于类型的NULL表示
            final_expr = self.solver_wrapper._create_is_null_expr(col_val_at_row, self.column.z3_type)
            self.solver_wrapper.logger.debug(f"    [_build_z3_expr_with_var] 构建的 IS NULL 表达式 for '{self.description}': {final_expr}")
            return final_expr
        if op == "IS NOT NULL":
            null_expr = self.solver_wrapper._create_is_null_expr(col_val_at_row, self.column.z3_type)
            final_expr = Not(null_expr, self.solver_wrapper.solver.ctx) # Not(is_null(...))
            self.solver_wrapper.logger.debug(f"    [_build_z3_expr_with_var] 构建的 IS NOT NULL 表达式 for '{self.description}': {final_expr}")
            return final_expr

        # 对于其他操作符，准备右侧的比较值
        # 使用 solver_wrapper 的转换方法，并传递目标 Z3 类型
        val_to_compare = self.solver_wrapper._convert_value_to_z3_compatible_type(
            self.value, 
            target_z3_type=self.column.z3_type # 使用列的Z3类型作为转换目标类型
        )

        self.solver_wrapper.logger.debug(f"      常量值 '{self.value}' (在_build_z3_expr_with_var中) 已转换为Z3兼容类型: '{val_to_compare}' (类型: {type(val_to_compare)})")

        if val_to_compare is None:
            # 如果转换失败，记录错误并可能抛出异常或返回一个表示错误的表达式
            # 例如，可以尝试生成一个始终为 False 的表达式，或根据策略处理
            self.solver_wrapper.logger.error(f"值 '{self.value}' 无法转换为与列 '{self.column.description}' (类型 {self.column.z3_type})兼容的Z3类型。约束构建失败。")
            # 返回一个始终为False的表达式，或者可以考虑抛出异常
            # raise TypeError(f"无法为约束 {self.description} 构建Z3表达式，因值转换失败。")
            return BoolVal(False, self.solver_wrapper.solver.ctx) 

        # 根据操作符构建表达式
        expr = None
        try:
            if op == "==": expr = (col_val_at_row == val_to_compare)
            elif op == "!=": expr = (col_val_at_row != val_to_compare)
            elif op == ">": expr = (col_val_at_row > val_to_compare)
            elif op == "<": expr = (col_val_at_row < val_to_compare)
            elif op == ">=": expr = (col_val_at_row >= val_to_compare)
            elif op == "<=": expr = (col_val_at_row <= val_to_compare)
            elif op == "LIKE":
                # 假设 _create_like_expression 处理 StringVal 和 Z3正则/模式匹配
                if is_string_sort(col_val_at_row.sort()) and isinstance(self.value, str):
                    expr = self.solver_wrapper._create_like_expression(col_val_at_row, self.value)
                else:
                    self.solver_wrapper.logger.error(f"LIKE 操作符仅支持字符串列和字符串模式。列类型: {col_val_at_row.sort()}, 模式类型: {type(self.value)}")
                    return BoolVal(False, self.solver_wrapper.solver.ctx) # 操作无效
            # 可以添加对其他 SQL 特有操作符的支持，例如 BETWEEN, IN 等
            # elif op == "BETWEEN":
            #     lower_bound = self.solver_wrapper._convert_value_to_z3_compatible_type(self.value[0], self.column.z3_type)
            #     upper_bound = self.solver_wrapper._convert_value_to_z3_compatible_type(self.value[1], self.column.z3_type)
            #     if lower_bound is not None and upper_bound is not None:
            #         expr = Z3And(col_val_at_row >= lower_bound, col_val_at_row <= upper_bound, self.solver_wrapper.solver.ctx)
            #     else:
            #         self.solver_wrapper.logger.error(f"BETWEEN 操作的界限值转换失败。约束: {self.description}")
            #         return BoolVal(False, self.solver_wrapper.solver.ctx)
            # elif op == "IN":
            #     if isinstance(self.value, (list, tuple)):
            #         or_clauses = []
            #         for v_in in self.value:
            #             z3_v_in = self.solver_wrapper._convert_value_to_z3_compatible_type(v_in, self.column.z3_type)
            #             if z3_v_in is not None:
            #                 or_clauses.append(col_val_at_row == z3_v_in)
            #             else:
            #                 self.solver_wrapper.logger.warning(f"IN列表中的值 '{v_in}' 转换失败，已忽略。约束: {self.description}")
            #         if or_clauses:
            #             expr = Z3Or(*or_clauses, self.solver_wrapper.solver.ctx)
            #         else:
            #             self.solver_wrapper.logger.error(f"IN列表中的所有值都无法转换。约束: {self.description}")
            #             return BoolVal(False, self.solver_wrapper.solver.ctx) # 如果列表为空或所有转换都失败
            #     else:
            #         self.solver_wrapper.logger.error(f"IN 操作的值必须是列表或元组。收到: {type(self.value)}. 约束: {self.description}")
            #         return BoolVal(False, self.solver_wrapper.solver.ctx)
            else:
                self.solver_wrapper.logger.error(f"不支持的操作符 '{op}' 在约束 '{self.description}' 中")
                # 对于不支持的操作符，返回一个始终为False的表达式，或者抛出异常
                return BoolVal(False, self.solver_wrapper.solver.ctx)
        except Z3Exception as e:
            # 捕获Z3本身可能抛出的类型不匹配等异常
            self.solver_wrapper.logger.error(f"构建Z3表达式 '{self.description}' 时发生Z3异常: {e}. ColVal: {col_val_at_row} (Sort: {col_val_at_row.sort()}), Op: '{op}', CompVal: {val_to_compare} (Sort: {val_to_compare.sort() if hasattr(val_to_compare, 'sort') else type(val_to_compare)}) ")
            return BoolVal(False, self.solver_wrapper.solver.ctx) # 返回False表示约束无法满足或构建失败

        self.solver_wrapper.logger.debug(f"    [_build_z3_expr_with_var] 构建的表达式 for '{self.description}': {expr}")
        return expr
    
    def _build_z3_expr_without_value(self, row_var_to_use: ExprRef) -> BoolRef:
        """
        辅助方法：构建不使用self.value的Z3表达式，用于构建复合约束的左侧。
        基本上是_build_z3_expr_with_var的简化版，忽略value并仅保留原始约束操作符。
        
        Args:
            row_var_to_use (ExprRef): 用于构建表达式的Z3行变量。
            
        Returns:
            BoolRef: 构建的Z3布尔表达式，不包括复合部分。
        """
        # 保存原始值和操作符
        original_value = self.value
        original_operator = self.operator
        
        # 提取原始约束属性 (假设以"(X op Y) & Z"格式的描述的左半部分"X op Y")
        if " & " in self.description or " | " in self.description:
            parts = self.description.split(" & ")[0] if " & " in self.description else self.description.split(" | ")[0]
            # 格式可能是(X op Y)，去掉括号
            if parts.startswith("(") and parts.endswith(")"):
                parts = parts[1:-1]
            
            # 现在parts应该是"X op Y"格式，尝试提取操作符
            # 简单处理：假设格式是"表名.列名 操作符 值"
            try:
                column_part = self.column.description  # 表名.列名
                rest = parts.replace(column_part, "", 1).strip()
                
                # 尝试提取操作符和值
                for possible_op in ["==", "!=", ">=", "<=", ">", "<", "IN", "NOT IN", "BETWEEN", "IS NULL", "IS NOT NULL"]:
                    if rest.startswith(possible_op):
                        self.operator = possible_op
                        self.value = rest[len(possible_op):].strip()
                        break
            except Exception as e:
                self.solver_wrapper.logger.error(f"尝试提取复合约束左侧时出错: {e}")
        
        # 使用原始的_build_z3_expr_with_var构建表达式
        try:
            return self._build_z3_expr_with_var(row_var_to_use)
        finally:
            # 恢复原始值和操作符
            self.value = original_value
            self.operator = original_operator

    def __and__(self, other: Any) -> Union['ColumnConstraint', Expression]:
        """
        重载逻辑与 (AND) 运算符 (`&`)。
        允许 `ColumnConstraint` 与另一个 `ColumnConstraint` 或 `Expression` 对象进行AND组合。
        如果与同表的ColumnConstraint组合，则返回一个新的ColumnConstraint对象。
        否则返回一个Expression对象。

        Args:
            other (Any): 与此约束进行AND操作的对象。
                         期望是 `ColumnConstraint` 或 `Expression`。
                         也支持Python的 `bool` 类型。

        Returns:
            Union[ColumnConstraint, Expression]: 表示AND组合的约束对象。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        self.solver_wrapper.logger.debug(f"列约束AND操作准备: ({self.description}) AND ({getattr(other, 'description', str(other))})")
        
        if isinstance(other, ColumnConstraint):
            if self.column.table_name == other.column.table_name:
                # 相同表的约束，创建一个新的ColumnConstraint对象
                self.solver_wrapper.logger.debug(f"  ANDing two ColumnConstraints for the same table '{self.column.table_name}'")
                
                # 格式化描述
                self_desc = self.description
                if self.operator == "IS NOT NULL" and "None" in self_desc:
                    self_desc = self_desc.replace(" None", "")
                if self.operator == "BETWEEN" and isinstance(self.value, tuple) and str(self.value) in self_desc:
                    lower, upper = self.value
                    self_desc = self_desc.replace(str(self.value), f"{lower} AND {upper}")
                
                other_desc = other.description
                if other.operator == "IS NOT NULL" and "None" in other_desc:
                    other_desc = other_desc.replace(" None", "")
                if other.operator == "BETWEEN" and isinstance(other.value, tuple) and str(other.value) in other_desc:
                    lower, upper = other.value
                    other_desc = other_desc.replace(str(other.value), f"{lower} AND {upper}")
                
                # 创建一个特殊的ColumnConstraint，表示AND组合
                combined_description = f"{self_desc} & {other_desc}"
                # 不使用deepcopy，直接创建新对象
                combined_constraint = ColumnConstraint(
                    self.column, 
                    "AND", 
                    other, 
                    self.solver_wrapper
                )
                combined_constraint.description = combined_description  # 更新描述
                
                return combined_constraint  # 返回复合约束对象
            else:
                # 不同表的约束
                self.solver_wrapper.logger.warning(
                    f"ANDing ColumnConstraints for different tables ('{self.column.table_name}' and '{other.column.table_name}'). "
                    f"Caller must ensure row_var_lambda context is appropriate for both or use explicit JOINs. "
                    f"Resulting Expression will evaluate: ({self.description}) AND ({other.description}) using a common row_var."
                )
                return Expression(
                    lambda row_var_lambda: And(self._build_z3_expr_with_var(row_var_lambda), 
                                              other._build_z3_expr_with_var(row_var_lambda)),
                    self.solver_wrapper,
                    f"{self.description} & {other.description} [Cross-Table Warning]"
                )
        elif isinstance(other, Expression):
            self.solver_wrapper.logger.debug(f"  ANDing ColumnConstraint with Expression: '{other.description}'")
            return Expression(
                lambda row_var_lambda: And(self._build_z3_expr_with_var(row_var_lambda), 
                                         other(row_var_lambda)),
                self.solver_wrapper,
                f"{self.description} & {other.description}"
            )
        elif isinstance(other, bool):
            self.solver_wrapper.logger.debug(f"  ANDing ColumnConstraint with Python bool: {other}")
            if other: # AND True -> 返回代表自身的约束
                return self
            else: # AND False -> 返回代表False的Expression
                return Expression(
                    lambda row_var_lambda: BoolVal(False), 
                    self.solver_wrapper, 
                    "False"
                )
        
        self.solver_wrapper.logger.warning(f"不支持的AND操作: ColumnConstraint AND {type(other)}")
        return NotImplemented

    def __or__(self, other: Any) -> Union['ColumnConstraint', Expression]:
        """
        重载逻辑或 (OR) 运算符 (`|`)。
        类似于 `__and__`，允许 `ColumnConstraint` 与 `ColumnConstraint` 或 `Expression` 组合。
        如果与同表的ColumnConstraint组合，则返回一个新的ColumnConstraint对象。
        否则返回一个Expression对象。

        Args:
            other (Any): 与此约束进行OR操作的对象 (ColumnConstraint, Expression, bool)。

        Returns:
            Union[ColumnConstraint, Expression]: 表示OR组合的约束对象。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        self.solver_wrapper.logger.debug(f"列约束OR操作准备: ({self.description}) OR ({getattr(other, 'description', str(other))})")

        if isinstance(other, ColumnConstraint):
            if self.column.table_name == other.column.table_name:
                # 相同表的约束，创建一个新的ColumnConstraint对象
                self.solver_wrapper.logger.debug(f"  ORing two ColumnConstraints for the same table '{self.column.table_name}'")
                
                # 格式化描述
                self_desc = self.description
                if self.operator == "IS NOT NULL" and "None" in self_desc:
                    self_desc = self_desc.replace(" None", "")
                if self.operator == "BETWEEN" and isinstance(self.value, tuple) and str(self.value) in self_desc:
                    lower, upper = self.value
                    self_desc = self_desc.replace(str(self.value), f"{lower} AND {upper}")
                
                other_desc = other.description
                if other.operator == "IS NOT NULL" and "None" in other_desc:
                    other_desc = other_desc.replace(" None", "")
                if other.operator == "BETWEEN" and isinstance(other.value, tuple) and str(other.value) in other_desc:
                    lower, upper = other.value
                    other_desc = other_desc.replace(str(other.value), f"{lower} AND {upper}")
                
                # 创建一个特殊的ColumnConstraint，表示OR组合
                combined_description = f"{self_desc} | {other_desc}"
                # 不使用deepcopy，直接创建新对象
                combined_constraint = ColumnConstraint(
                    self.column, 
                    "OR", 
                    other, 
                    self.solver_wrapper
                )
                combined_constraint.description = combined_description  # 更新描述
                
                return combined_constraint  # 返回复合约束对象
            else:
                # 不同表的约束
                self.solver_wrapper.logger.warning(
                    f"ORing ColumnConstraints for different tables ('{self.column.table_name}' and '{other.column.table_name}'). "
                    f"Caller must ensure row_var_lambda context is appropriate for both or use explicit JOINs. "
                    f"Resulting Expression will evaluate: ({self.description}) OR ({other.description}) using a common row_var."
                )
                return Expression(
                    lambda row_var_lambda: Or(self._build_z3_expr_with_var(row_var_lambda), 
                                            other._build_z3_expr_with_var(row_var_lambda)),
                    self.solver_wrapper,
                    f"{self.description} | {other.description} [Cross-Table Warning]"
                )
        elif isinstance(other, Expression):
            self.solver_wrapper.logger.debug(f"  ORing ColumnConstraint with Expression: '{other.description}'")
            return Expression(
                lambda row_var_lambda: Or(self._build_z3_expr_with_var(row_var_lambda), 
                                       other(row_var_lambda)),
                self.solver_wrapper,
                f"{self.description} | {other.description}"
            )
        elif isinstance(other, bool):
            self.solver_wrapper.logger.debug(f"  ORing ColumnConstraint with Python bool: {other}")
            if other: # OR True -> 返回代表True的Expression
                return Expression(
                    lambda row_var_lambda: BoolVal(True), 
                    self.solver_wrapper, 
                    "True"
                )
            else: # OR False -> 返回代表自身的约束
                return self
        
        self.solver_wrapper.logger.warning(f"不支持的OR操作: ColumnConstraint OR {type(other)}")
        return NotImplemented

    def __repr__(self) -> str:
        """
        返回约束的字符串表示
        
        Returns:
            str: 约束的字符串表示
        """
        return f"<ColumnConstraint('{self.description}')>"
        
    def __invert__(self) -> Expression:
        """
        重载逻辑非 (~) 运算符。
        创建一个新的 `Expression` 对象，其 `expr_func` 会在求值时
        对当前约束的结果执行逻辑NOT操作。
        
        Returns:
            Expression: 代表逻辑NOT操作的新 `Expression` 对象。
        """
        self.solver_wrapper.logger.debug(f"列约束非操作: ~({self.description})")
        
        # 格式化描述
        desc = self.description
        if self.operator == "IS NOT NULL" and "None" in desc:
            desc = desc.replace(" None", "")
        if self.operator == "BETWEEN" and isinstance(self.value, tuple) and str(self.value) in desc:
            lower, upper = self.value
            desc = desc.replace(str(self.value), f"{lower} AND {upper}")
        
        return Expression(
            lambda row_var_lambda: Not(self._build_z3_expr_with_var(row_var_lambda)),
            self.solver_wrapper,
            f"~({desc})"
        )

    def is_null(self) -> 'ColumnConstraint':
        """
        创建一个检查列值是否为NULL的约束。

        Returns:
            ColumnConstraint: 表示 "column IS NULL" 的约束对象。
        """
        return ColumnConstraint(self, "IS NULL", None, self.solver_wrapper)

    def is_not_null(self) -> 'ColumnConstraint':
        """
        创建一个检查列值是否不为NULL的约束。

        Returns:
            ColumnConstraint: 表示 "column IS NOT NULL" 的约束对象。
        """
        return ColumnConstraint(self, "IS NOT NULL", None, self.solver_wrapper)
        
    def between(self, lower: Any, upper: Any) -> 'ColumnConstraint':
        """
        创建一个检查列值是否在指定范围内的约束。

        Args:
            lower: 范围的下限值。
            upper: 范围的上限值。

        Returns:
            ColumnConstraint: 表示 "column BETWEEN lower AND upper" 的约束对象。
        """
        return ColumnConstraint(self, "BETWEEN", (lower, upper), self.solver_wrapper)
        
    def in_list(self, values: list) -> 'ColumnConstraint':
        """
        创建一个检查列值是否在指定列表中的约束。

        Args:
            values: 要检查的值列表。

        Returns:
            ColumnConstraint: 表示 "column IN (values)" 的约束对象。
        """
        return ColumnConstraint(self, "IN", values, self.solver_wrapper)
        
    def not_in_list(self, values: list) -> 'ColumnConstraint':
        """
        创建一个检查约束列的值是否不在指定列表中的约束。

        Args:
            values: 要检查的值列表。

        Returns:
            ColumnConstraint: 表示 "column NOT IN (values)" 的约束对象。
        """
        # 创建新的ColumnConstraint，使用原始列作为列参数
        return self.column.not_in_list(values)

    # 删除以下字符串操作方法，它们与列类的方法重复
    # 下面的 concat 方法由于冲突，已经被删除

    def upper(self) -> Expression:
        """将约束列的值转换为大写。"""
        return self.column.upper()

    def lower(self) -> Expression:
        """将约束列的值转换为小写。"""
        return self.column.lower()
    
    def like(self, pattern: str) -> Expression:
        """
        SQL LIKE操作符，用于模式匹配
        
        Args:
            pattern: 匹配模式，如'A%','_B%'等
            
        Returns:
            Expression: 代表LIKE匹配的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串LIKE操作: {self.description} LIKE {pattern}")
        
        # 这里简化处理，实际情况下需要将SQL的LIKE模式转换为正则表达式
        return Expression(
            lambda row_var: self.solver_wrapper._create_like_expression(self.function(row_var), pattern),
            self.solver_wrapper,
            f"{self.description} LIKE {pattern}"
        )
    
    def contains(self, substring: str) -> Expression:
        """
        检查字符串是否包含子串
        
        Args:
            substring: 要检查的子串
            
        Returns:
            Expression: 代表包含检查的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串contains操作: {self.description}.contains({substring})")
        
        from z3 import Contains
        return Expression(
            lambda row_var: Contains(self.function(row_var), substring),
            self.solver_wrapper,
            f"{self.description}.contains({substring})"
        )
    
    def startswith(self, prefix: str) -> Expression:
        """
        检查字符串是否以指定前缀开始
        
        Args:
            prefix: 前缀字符串
            
        Returns:
            Expression: 代表前缀检查的表达式
        """
        self.solver_wrapper.logger.debug(f"字符串startswith操作: {self.description}.startswith({prefix})")
        
        from z3 import PrefixOf
        return Expression(
            lambda row_var: PrefixOf(prefix, self.function(row_var)),
            self.solver_wrapper,
            f"{self.description}.startswith({prefix})"
        )
    
    def endswith(self, suffix: str) -> 'Expression':
        """
        检查字符串列是否以指定后缀结尾。
        
        Args:
            suffix: 要检查的后缀字符串
            
        Returns:
            Expression: 表示 "column ENDSWITH suffix" 的表达式对象
        """
        from .expression import Expression
        
        def endswith_func(row_var):
            col_val = self.function(row_var)
            suffix_val = StringVal(str(suffix), self.solver_wrapper.solver.ctx)
            return self.solver_wrapper._create_endswith_constraint(col_val, suffix_val)
        
        return Expression(endswith_func, self.solver_wrapper, f"{self.description} ENDSWITH '{suffix}'")
    
    def substring(self, start: int, length: int) -> 'Expression':
        """
        从字符串列中提取子字符串。
        
        Args:
            start: 起始位置（从1开始）
            length: 要提取的字符数
            
        Returns:
            Expression: 表示 "SUBSTRING(column, start, length)" 的表达式对象
        """
        from .expression import Expression
        
        def substring_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_substring(col_val, start, length)
        
        return Expression(substring_func, self.solver_wrapper, f"SUBSTRING({self.description}, {start}, {length})")
        
    # concat 方法已经在 Column 类中定义，不再重复
    
    def trim(self) -> Expression:
        """
        去除字符串列两端的空白字符。
        
        Returns:
            Expression: 表示 "TRIM(column)" 的表达式对象
        """
        from .expression import Expression
        
        def trim_func(row_var):
            col_val = self.function(row_var)
            return self.solver_wrapper._create_trim(col_val)
        
        return Expression(trim_func, self.solver_wrapper, f"TRIM({self.description})") 