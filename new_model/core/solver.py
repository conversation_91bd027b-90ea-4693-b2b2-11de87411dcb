"""
SQL到Z3求解器模块
提供SQL到Z3约束的转换核心功能
"""

import sys
from pathlib import Path
from z3 import *
from typing import Dict, List, Any, Optional, Union, Tuple
import logging

# 添加项目根目录到路径，以便导入lineage_core
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver
from lineage_core.logger import logger # 导入logger
from lineage_core.z3_datetype import DateType, DateExpression, DateExpressionBoolRef # 导入DateType、DateExpression和DateExpressionBoolRef

# 导入其他模块
from .table import Table

# 导入需要的类
from .column import ColumnConstraint
from .expression import Expression


class SqlToZ3:
    """SQL到Z3约束的转换包装器"""

    def __init__(self, log_level: Optional[str] = None, timeout: Optional[int] = None):
        """
        初始化SQL到Z3转换器。
        该转换器负责管理表、列函数、约束以及与Z3求解器的交互。

        Args:
            log_level (Optional[str]): 日志级别 (例如 "DEBUG", "INFO", "WARNING")。
            timeout (Optional[int]): Z3求解器的超时时间 (毫秒)。
        """
        self.solver: TypeAwareEnhancedSolver = TypeAwareEnhancedSolver()  # Z3求解器实例，增强了类型意识
        self.tables: Dict[str, Table] = {}  # 字典，用于存储所有已定义的表对象，键为表名，值为Table实例
        # 同时保留两种存储方式，保持兼容性
        self.column_functions: Dict[str, Function] = {}  # 旧的存储方式: {表名_列名: z3_function}
        self.table_z3_functions: Dict[str, Dict[str, Function]] = {}  # 新的存储方式: {表名: {列名: z3_function}}
        self.constraints: List[Any] = []  # 列表，用于存储用户添加的原始约束，方便调试和追踪（可以是Z3表达式、ColumnConstraint对象等）
        self.join_conditions: List[Dict[str, Any]] = []  # 列表，用于存储JOIN操作产生的行变量对，例如 {'v1': z3_var1, 't1': table1_name, 'v2': z3_var2, 't2': table2_name}
        self._constraint_cache: List[ExprRef] = []  # 列表，用于缓存已转换为Z3表达式的约束，目前主要由TypeAwareEnhancedSolver内部管理
        self._var_suffix_counter: int = 0 # 用于生成唯一变量名后缀的计数器
        self.logger = logger # 为该类的实例分配一个logger，方便各方法记录日志
        self.date_type = DateType()  # 创建DateType实例，用于处理日期类型

        if log_level:
            try:
                level = getattr(logging, log_level.upper(), logging.INFO) # 默认为INFO
                self.logger.setLevel(level)
                self.logger.info(f"SqlToZ3 日志级别设置为: {log_level.upper()}")
            except AttributeError:
                self.logger.warning(f"无效的日志级别: {log_level}. 将使用默认级别.")
        
        if timeout is not None:
            self.solver.set(":timeout", timeout) # Z3的超时单位是毫秒
            self.logger.info(f"SqlToZ3 求解器超时设置为: {timeout} ms")

    def _get_unique_var_suffix(self) -> str:
        """
        生成一个唯一的后缀字符串，用于创建Z3变量名，以避免命名冲突。
        每次调用时，后缀计数器会递增。

        Returns:
            str: 一个唯一的后缀字符串 (例如, "0", "1", ...)。
        """
        suffix = str(self._var_suffix_counter)
        self._var_suffix_counter += 1
        self.logger.debug(f"生成唯一变量后缀: {suffix}")
        return suffix

    def add(self, constraint: Any):
        logger = self.logger
        logger.info(f"DEBUG_LOG: SqlToZ3.add received constraint. Type: {type(constraint)}, Value: '{str(constraint)[:200]}', id: {id(constraint)}")

        # 调试约束类型，以便跟踪问题
        # logger.debug(f"add 方法收到的约束类型: {type(constraint)}, 值: {constraint}") # 原有日志，信息类似

        # 首先检查是否传入了 False，这通常是不期望的
        if constraint is False: # 特别关注这个分支
            logger.warning(f"DEBUG_LOG: SqlToZ3.add received a literal Python False. This will lead to unsat.")
            # 这可能是某些列比较操作（如 Column.__eq__）直接返回 False 的结果
            # 在 Column 类方法可能返回布尔值而不是 ColumnConstraint 对象的情况下发出警告
            converted_expr = self._convert_constraint(constraint)  # 转换为 Z3 BoolVal(False)
            self.solver.add(converted_expr)
            self.constraints.append(converted_expr)
            return

        # 对于 ColumnConstraint 对象的处理
        if isinstance(constraint, ColumnConstraint):
            logger.info(f"DEBUG_LOG: SqlToZ3.add processing ColumnConstraint. id: {id(constraint)}, Description: '{constraint.description}'")
            passed_join_var: Optional[ExprRef] = None
            constraint_table_name = constraint.column.table_name

            # 遍历已记录的JOIN条件，尝试找到与当前列约束相关的已存在的行变量
            for join_info in self.join_conditions:
                # join_info 现在是 {'v1': z3_var1, 't1': table1_name, 'v2': z3_var2, 't2': table2_name}
                if constraint_table_name == join_info['t1']:
                    passed_join_var = join_info['v1']
                    logger.debug(f"列约束 '{constraint.description}' 的表 '{constraint_table_name}' 与JOIN条件中的行变量 '{passed_join_var}' (来自表 '{join_info['t1']}') 关联")
                    break 
                elif constraint_table_name == join_info['t2']:
                    passed_join_var = join_info['v2']
                    logger.debug(f"列约束 '{constraint.description}' 的表 '{constraint_table_name}' 与JOIN条件中的行变量 '{passed_join_var}' (来自表 '{join_info['t2']}') 关联")
                    break
            
            # 应用列约束。如果找到了匹配的passed_join_var，ColumnConstraint.apply会使用它；
            # 否则，apply方法通常会创建新的自由行变量。
            # ColumnConstraint.apply 负责将其自身的Z3表达式添加到求解器。
            constraint.apply(passed_join_var)
            # self.constraints.append(constraint) # 仍然记录原始ColumnConstraint对象，供调试或可能的其他用途
            logger.info(f"列约束 '{constraint.description}' 已处理并应用到求解器 (可能使用了JOIN变量 '{str(passed_join_var) if passed_join_var is not None else 'N/A'}')")

        elif isinstance(constraint, Expression):
            # 处理 Expression 对象
            logger.debug(f"准备添加表达式约束: {constraint.description}")
            # 对于Expression，通常需要一个新的自由行变量来定义其上下文，除非它在更复杂的结构中被调用。
            # 这里假设Expression直接添加到solver时，需要创建一个代表某表的某行的变量。
            # 这个逻辑比较复杂，因为Expression本身不直接绑定到单一表。
            # 当前实现中，Expression.as_z3_expr() 如果没有row_var会创建临时变量并报警。
            # SqlToZ3.add() 在这里需要确定是哪个表的上下文。
            # 这是一个可以进一步细化设计的地方。目前，我们依赖Expression内部的逻辑或更高层调用来提供row_var。
            # 如果Expression需要被"普遍化"到所有行，那其创建方式会不同。
            
            # 简化处理：假设表达式直接求值为一个Z3 BoolRef，或者它能在无特定row_var时有意义地转换
            # (这通常不安全，Expression.as_z3_expr() 无参数调用会报警)
            # 更安全的做法是要求 Expression 在添加到 solver 时，必须能转换为一个独立的布尔命题，
            # 或者通过 s.ForAll(lambda row_var: expr(row_var)) 等方式应用。
            # 暂时：直接尝试转换为Z3表达式并添加，依赖 Expression 自身逻辑。
            z3_expr = self._convert_constraint(constraint) # _convert_constraint 会处理 Expression
            if z3_expr is not None and isinstance(z3_expr, BoolRef):
                if self._add_to_solver_if_new(z3_expr):
                    self.constraints.append(z3_expr) # 记录实际添加到solver的Z3表达式
                    logger.info(f"已添加表达式生成的约束到求解器: {z3_expr}")
                else:
                    logger.debug(f"表达式约束 '{z3_expr}' 已存在，不再重复添加")
            elif z3_expr is not None:
                logger.warning(f"表达式 '{constraint.description}' 转换为非布尔Z3表达式 '{z3_expr}'，无法直接作为约束添加。请检查表达式逻辑。")
            else:
                logger.error(f"表达式 '{constraint.description}' 无法转换为Z3表达式。")

        elif isinstance(constraint, tuple) and len(constraint) == 4:
            # 处理JOIN条件返回的元组 (var1, table1_name, var2, table2_name)
            z3_var1, tbl1_name, z3_var2, tbl2_name = constraint
            if isinstance(z3_var1, ExprRef) and isinstance(z3_var2, ExprRef) and isinstance(tbl1_name, str) and isinstance(tbl2_name, str):
                join_info = {'v1': z3_var1, 't1': tbl1_name, 'v2': z3_var2, 't2': tbl2_name}
                self.join_conditions.append(join_info)
                
                # 尝试合并相关的JOIN条件
                self._merge_join_conditions(join_info)
                
                logger.info(f"记录的JOIN信息: 表'{tbl1_name}'的行变量'{z3_var1}' 与 表'{tbl2_name}'的行变量'{z3_var2}'")
                # 核心的JOIN约束 (col1_func(var1) == col2_func(var2)) 已经在Column.__eq__中直接添加到solver
                # 这里 SqlToZ3 仅记录这对变量及其表名，用于后续 ColumnConstraint 的上下文判断
                # 和 _merge_join_conditions 的传递性处理。
            else:
                logger.error(f"接收到格式不正确的JOIN元组: {constraint}")

        elif isinstance(constraint, (BoolRef, bool, int, float, str)):
            # 处理直接的Z3布尔表达式或Python基本类型
            logger.debug(f"准备添加通用约束: {constraint}")
            # 将Python布尔值和其他基本类型转换为Z3表达式
            converted_expr = self._convert_constraint(constraint)
            
            # TypeAwareEnhancedSolver 会在其 check 方法中处理约束缓存和实际添加
            # 此处直接调用 solver.add() 即可，它内部会处理
            if isinstance(converted_expr, BoolRef):
                # 修复：检查是否添加了False作为约束，这通常是不期望的
                if is_false(converted_expr):
                    logger.warning(f"警告: 添加了 False 约束 (从 {constraint} 转换而来)，这将导致求解器结果为 unsat。")
                
                self.solver.add(converted_expr)
                self.constraints.append(converted_expr) # 记录转换后的约束
                logger.info(f"已添加通用Z3约束到求解器: {converted_expr}")
            else:
                # 如果是非布尔值，比如IntVal, RealVal, StringVal等，不能直接添加为约束
                # 可以考虑将其转换为布尔值约束，如 IntVal(10) -> IntVal(10) != 0
                # 或者直接忽略这些非布尔值
                logger.warning(f"约束 '{constraint}' 转换为非布尔值 '{converted_expr}'，无法直接作为约束添加，已忽略")

        else:
            # 处理其他类型的约束
            logger.debug(f"处理其他类型约束: {constraint} (类型: {type(constraint)})")
            try:
                # 尝试将其转换为Z3表达式并添加
                z3_expr = self._convert_constraint(constraint)
                if z3_expr is not None and isinstance(z3_expr, BoolRef):
                    self.solver.add(z3_expr)
                    self.constraints.append(z3_expr)
                    logger.info(f"已添加其他类型约束转换的Z3表达式到求解器: {z3_expr}")
                else:
                    logger.warning(f"约束 '{constraint}' 转换结果无效或非布尔表达式: {z3_expr}，已忽略。")
            except Exception as e:
                logger.exception(f"处理约束时出错: {e}. 约束: {constraint}")

    def _merge_join_conditions(self, new_join: Dict[str, Any]):
        """
        合并关联的JOIN条件，以处理多表JOIN中的传递性约束。

        当一个新的JOIN条件 `new_join = {'v1': varA, 't1': TableA, 'v2': varB, 't2': TableB}`
        （代表 TableA.row_var 和 TableB.row_var）被添加时，此方法会检查 `self.join_conditions` 
        中已有的其他JOIN条件。如果发现一个已有的JOIN条件，例如 
        `existing_join = {'v1': varB_prime, 't1': TableB, 'v2': varC, 't2': TableC}`，
        并且 `varB` 和 `varB_prime` 都代表同一个表 (TableB) 的行变量（即使它们是不同的Z3变量实例），
        那么就需要添加一个传递约束 `varB == varB_prime`，以确保它们在模型中取相同的值。
        这对于建立跨多个表的正确连接至关重要。

        Args:
            new_join: 新添加的JOIN条件，是一个包含键 'v1', 't1', 'v2', 't2' 的字典，
                     其中v1和v2是Z3行变量，t1和t2是表名。
        """
        logger.debug(f"开始合并JOIN条件，新JOIN涉及: {new_join['t1']} 和 {new_join['t2']}")
        var1_new, var2_new = new_join['v1'], new_join['v2'] # 新加入的JOIN条件的两个行变量
        tbl1_new, tbl2_new = new_join['t1'], new_join['t2'] # 新加入的JOIN条件的两个表名

        # 遍历所有已记录的JOIN条件 (除了刚添加的这个)
        for existing_join in self.join_conditions:
            # 跳过与新加入的JOIN条件完全相同的那个
            if existing_join is new_join:
                continue

            var1_existing, var2_existing = existing_join['v1'], existing_join['v2']
            tbl1_existing, tbl2_existing = existing_join['t1'], existing_join['t2']

            # 检查表名是否匹配以及是否需要添加传递性约束
            # 场景1: 新JOIN的表1与现有JOIN的表1相同
            if tbl1_new == tbl1_existing and var1_new is not var1_existing:
                self.solver.add(var1_new == var1_existing)
                logger.info(f"添加JOIN变量传递约束: {tbl1_new} 表的 {var1_new} == {var1_existing}")
            
            # 场景2: 新JOIN的表1与现有JOIN的表2相同
            elif tbl1_new == tbl2_existing and var1_new is not var2_existing:
                self.solver.add(var1_new == var2_existing)
                logger.info(f"添加JOIN变量传递约束: {tbl1_new} 表的 {var1_new} == {var2_existing}")

            # 场景3: 新JOIN的表2与现有JOIN的表1相同
            if tbl2_new == tbl1_existing and var2_new is not var1_existing:
                self.solver.add(var2_new == var1_existing)
                logger.info(f"添加JOIN变量传递约束: {tbl2_new} 表的 {var2_new} == {var1_existing}")
            
            # 场景4: 新JOIN的表2与现有JOIN的表2相同
            elif tbl2_new == tbl2_existing and var2_new is not var2_existing:
                self.solver.add(var2_new == var2_existing)
                logger.info(f"添加JOIN变量传递约束: {tbl2_new} 表的 {var2_new} == {var2_existing}")

    def create_table(self, name: str, columns: Dict[str, Any]) -> Table:
        """
        根据提供的表名和列定义创建一个新的表。
        为表中的每一列创建对应的Z3函数，该函数将行ID映射到列值。

        Args:
            name: 表的名称 (字符串)。
            columns: 一个字典，定义了表的列。键是列名 (字符串)，值是该列的Z3数据类型 (例如, IntSort(), StringSort(), BoolSort())
                    或者DateType实例，表示日期类型。

        Returns:
            一个 `Table` 对象，代表新创建的表。
        """
        logger.debug(f"开始创建表: {name}，列定义: {columns}")
        
        # 预处理列定义，处理特殊类型字符串
        processed_columns = {}
        date_columns = set()  # 创建一个集合来存储日期类型的列名
        
        for col_name, col_type in columns.items():
            # 处理日期类型
            if isinstance(col_type, str) and col_type.lower() == "date":
                processed_columns[col_name] = self.date_type
                date_columns.add(col_name)
                logger.debug(f"列 '{col_name}' 被识别为日期类型")
            else:
                processed_columns[col_name] = col_type
        
        # 创建表对象
        table = Table(name, processed_columns, self)
        
        # 为表中每一列生成Z3函数，该函数将行ID映射到列值
        # 例如, employees_name: Int -> String  表示函数输入行ID (Int)，输出该行的name列值 (String)
        
        # 确保表名在table_z3_functions中有一个条目
        if name not in self.table_z3_functions:
            self.table_z3_functions[name] = {}
            
        for col_name, z3_col_type in processed_columns.items():
            # 构造唯一的函数名: "表名_列名"
            func_name = f"{name}_{col_name}"
            
            # 检查是否已存在同名函数，避免重复创建 (理论上不应发生，除非表名和列名组合重复)
            if func_name not in self.column_functions:
                z3_func = Function(func_name, IntSort(), z3_col_type)
                # 同时存储在旧的column_functions和新的table_z3_functions结构中
                self.column_functions[func_name] = z3_func
                self.table_z3_functions[name][col_name] = z3_func
                logger.debug(f"为表 '{name}' 创建并注册Z3函数: {func_name}(IntSort) -> {z3_col_type}")
            else:
                # 如果已存在，确保也存在于table_z3_functions中
                z3_func = self.column_functions[func_name]
                self.table_z3_functions[name][col_name] = z3_func
                logger.warning(f"函数名 '{func_name}' 已存在，跳过创建。")
        
        # 将创建的表对象添加到表字典中
        self.tables[name] = table
        
        # 如果有日期类型的列，为表对象添加一个日期列集合属性
        if date_columns:
            table.date_columns = date_columns
            logger.debug(f"为表 '{name}' 设置date_columns: {date_columns}")
        
        logger.info(f"表 '{name}' 创建成功。")
        return table

    def get_table(self, table_name: str) -> Table:
        """
        根据表名检索已创建的表对象。

        Args:
            table_name: 要检索的表的名称 (字符串)。

        Returns:
            对应的 `Table` 对象。

        Raises:
            KeyError: 如果具有指定名称的表不存在。
        """
        logger.debug(f"尝试获取表: {table_name}")
        if table_name not in self.tables:
            logger.error(f"获取表失败: 未找到名为 '{table_name}' 的表。")
            raise KeyError(f"未找到名为 '{table_name}' 的表。可用表: {list(self.tables.keys())}")
        return self.tables[table_name]

    def check(self) -> CheckSatResult:
        """
        调用Z3求解器检查当前所有约束是否可满足。

        Returns:
            Z3的检查结果，一个 `CheckSatResult` 枚举值 (例如, `z3.sat`, `z3.unsat`, `z3.unknown`)。
        """
        logger.debug("开始检查约束的可满足性...")
        # TypeAwareEnhancedSolver的check方法会处理_constraint_cache中的约束
        result = self.solver.check()
        logger.info(f"约束可满足性检查结果: {result}")
        return result

    def get_model(self) -> ModelRef:
        """
        如果约束可满足 (即 `check()` 返回 `sat`)，则获取Z3模型。
        模型包含了满足所有约束的变量赋值。

        Returns:
            一个Z3 `ModelRef` 对象。

        Raises:
            Z3Exception: 如果在 `check()` 未返回 `sat` 时调用此方法 (例如求解器状态为 `unsat` 或 `unknown`，或者 `check()` 未被调用)。
        """
        logger.debug("尝试获取Z3模型...")
        try:
            model = self.solver.model()
            logger.info("Z3模型获取成功。")
            return model
        except Z3Exception as e:
            logger.error(f"获取Z3模型失败: {e}. 请确保在调用get_model()前已成功执行check()并得到sat结果。")
            raise

    def eval(self, expr: Union[ExprRef, BoolRef, ArithRef, ArrayRef, DatatypeRef], model_completion: bool = False) -> Any:
        """
        在当前Z3模型下评估给定Z3表达式的值。

        Args:
            expr: 要评估的Z3表达式 (ExprRef 或其子类)。
            model_completion: Z3 `eval` 方法的 `model_completion` 参数。如果为 `True`，
                              求解器将尝试为模型中未指定值的变量找到一个赋值。

        Returns:
            表达式在模型下的具体值。类型取决于表达式本身 (例如, Z3 IntNumRef, BoolRef, StringVal 等)。

        Raises:
            Z3Exception: 如果无法评估表达式 (例如, `check()` 未返回 `sat`, 或模型不完整且 `model_completion=False`)。
        """
        logger.debug(f"准备在模型中评估表达式: {expr}")
        try:
            model = self.solver.model() # 首先获取模型
            evaluated_value = model.eval(expr, model_completion=model_completion)
            logger.debug(f"表达式 '{expr}' 评估结果: {evaluated_value}")
            return evaluated_value
        except Z3Exception as e:
            logger.error(f"评估表达式 '{expr}' 失败: {e}. 确保模型可用且表达式有效。")
            raise

    def _convert_constraint(self, constraint: Any) -> Optional[ExprRef]:
        """
        将各种类型的约束转换为Z3表达式。
        
        Args:
            constraint: 要转换的约束，可以是多种类型
        
        Returns:
            转换后的Z3表达式，如果无法转换则返回None
        """
        # 处理None
        if constraint is None:
            self.logger.warning("尝试转换None约束，返回None")
            return None
        
        # 处理布尔值
        if isinstance(constraint, bool):
            return BoolVal(constraint)
        
        # 处理整数
        if isinstance(constraint, int):
            return IntVal(constraint)
        
        # 处理浮点数
        if isinstance(constraint, float):
            return RealVal(constraint)
        
        # 处理字符串
        if isinstance(constraint, str):
            return StringVal(constraint)
        
        # 处理Z3表达式
        if isinstance(constraint, ExprRef):
            return constraint
        
        # 处理DateExpression
        if isinstance(constraint, DateExpression):
            return constraint.date_expr  # 返回DateExpression内部的Z3表达式
        
        # 处理ColumnConstraint
        if isinstance(constraint, ColumnConstraint):
            # 如果约束已经有缓存的Z3表达式，直接返回
            if hasattr(constraint, 'z3_constraint') and constraint.z3_constraint is not None:
                return constraint.z3_constraint
            
            # 否则，创建一个新的行变量并应用约束
            row_var_suffix = self._get_unique_var_suffix()
            row_var = Int(f"{constraint.column.table_name}_row_{row_var_suffix}")
            
            # 确保行变量是有效的（在表的行集合中）
            table = self.tables.get(constraint.column.table_name)
            if table and table.rows:
                self.add(Or([row_var == i for i in table.rows]))
            
            # 应用约束并返回
            return constraint.as_z3_expr(row_var)
        
        # 处理Expression
        if isinstance(constraint, Expression):
            return constraint.as_z3_expr()
        
        # 处理列表类型 - 抛出TypeError异常
        if isinstance(constraint, list):
            constraint_str = str(constraint)[:100] + ('...' if len(str(constraint)) > 100 else '')
            self.logger.error(f"不支持的约束类型: {type(constraint)}, 值: '{constraint_str}'")
            raise TypeError(f"不支持的约束类型: {type(constraint)}, 值: '{constraint_str}'")
        
        # 处理DateExpressionBoolRef
        if isinstance(constraint, DateExpressionBoolRef):
            return constraint.get_z3_bool_ref()  # 使用get_z3_bool_ref()方法获取内部的Z3 BoolRef
        
        # 无法识别的约束类型
        constraint_str = str(constraint)[:100] + ('...' if len(str(constraint)) > 100 else '')
        self.logger.error(f"未支持的约束类型: {type(constraint)}, 值: '{constraint_str}'")
        return None

    def _add_to_solver_if_new(self, z3_expr: BoolRef) -> bool:
        """
        添加Z3约束到求解器，但只有当它是一个新的约束时。
        
        Args:
            z3_expr: 要添加的Z3布尔表达式约束。
            
        Returns:
            bool: 如果约束是新的并被添加，则返回True，如果约束已存在，则返回False。
        """
        # 简单实现：直接添加到求解器，并假设约束是新的
        # 实际上，TypeAwareEnhancedSolver 内部可能会有自己的重复检测机制
        # 但为了测试，我们简单实现一个永远返回 True 的版本
        self.solver.add(z3_expr)
        return True 

    def _propagate_types(self):
        """
        执行类型传播。
        此方法应在每次添加约束后调用，以更新和检查求解器中表达式的类型信息。
        具体的实现依赖于 `TypeAwareEnhancedSolver`。
        """
        logger.debug("开始执行类型传播...")
        try:
            self.solver.propagate_types()
            logger.debug("类型传播执行完毕。")
        except Exception as e:
            logger.error(f"类型传播过程中发生错误: {e}", exc_info=True)
            # 根据需要，可以选择是否重新抛出异常或如何处理错误

    # 添加一个处理Not操作的便捷方法
    # 这些便捷方法应该直接操作内部solver的对应方法
    def Not(self, expr: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool]) -> Union[BoolRef, Expression]:
        """
        对给定的表达式或约束执行逻辑非 (NOT) 操作。

        Args:
            expr: 可以是Z3布尔表达式 (`BoolRef`), 自定义 `Expression` 对象,
                  `ColumnConstraint` 对象, 或 `DateExpressionBoolRef` 对象，
                  或 Python 布尔值。

        Returns:
            - 如果输入是 `Expression` 或 `ColumnConstraint`，返回一个新的 `Expression` 代表NOT操作。
            - 如果输入是 `BoolRef` 或 `DateExpressionBoolRef`，返回其对应的Z3 NOT表达式 (`BoolRef`)。
            - 如果输入是 Python 布尔值，返回其对应的Z3 NOT表达式 (`BoolRef`)。

        Raises:
            TypeError: 如果输入表达式的类型不受支持。
        """
        logger.debug(f"对表达式/约束执行NOT操作: {getattr(expr, 'description', expr)}")
        if isinstance(expr, Expression):
            return Expression(lambda row_var: Not(expr(row_var)), self, f"NOT({expr.description})")
        elif isinstance(expr, ColumnConstraint):
            # 对于列约束，我们需要先将其转换为Z3表达式
            # 注意：这会直接将列约束应用并添加到solver，Not操作会作用于其结果
            # 如果希望延迟应用，需要更复杂的处理
            # z3_expr = expr.as_z3_expr() # 这会应用约束
            # return Not(z3_expr)
            # 或者，创建一个新的Expression来表示这个延迟的NOT
            return Expression(lambda row_var: Not(expr.apply(row_var)), self, f"NOT({expr.description})")
        elif isinstance(expr, DateExpressionBoolRef):
            return Not(expr.z3_expr) # 直接对内部的Z3表达式取反
        elif isinstance(expr, bool): # 支持 Python 布尔值
            return Not(BoolVal(expr))
        elif isinstance(expr, BoolRef): # 确保这是Z3的BoolRef
            return Not(expr)
        else:
            raise TypeError(f"不支持对类型 {type(expr)} 执行Not操作")
    
    def And(self, *args: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool]) -> Union[BoolRef, Expression]:
        """
        对给定的多个表达式或约束执行逻辑与 (AND) 操作。

        Args:
            *args: 一个或多个参数，每个参数可以是Python布尔值 (`bool`), Z3布尔表达式 (`BoolRef`),
                   自定义 `Expression` 对象, `ColumnConstraint` 对象, 或 `DateExpressionBoolRef` 对象。

        Returns:
            - 如果所有参数都是简单类型 (最终可以转换为 `BoolRef`)，返回一个Z3 `BoolRef` 代表AND操作的结果。
            - 如果至少有一个参数是 `Expression` 或 `ColumnConstraint`，返回一个新的 `Expression` 对象，
              该对象在被求值时会执行AND操作。

        Raises:
            TypeError: 如果参数列表中包含不支持的类型。
            ValueError: 如果未提供任何参数。
        """
        if not args:
            logger.warning("And 操作未收到任何参数，将返回 True (Z3的And()行为)")
            return BoolVal(True) # 或者根据需求抛出错误: raise ValueError("And 操作需要至少一个参数")

        logger.debug(f"对参数列表执行AND操作 (共 {len(args)} 个参数)")

        # 检查是否需要返回一个 Expression 对象
        if any(isinstance(arg, (Expression, ColumnConstraint)) for arg in args):
            # 如果有Expression或ColumnConstraint，结果是一个新的Expression
            
            def combined_expr_func(row_var):
                z3_args = []
                for arg in args:
                    if isinstance(arg, Expression):
                        z3_args.append(arg(row_var))
                    elif isinstance(arg, ColumnConstraint):
                        # ColumnConstraint的apply会直接将其加入solver，并返回一个变量。
                        # 我们需要的是它代表的布尔约束本身。
                        # as_z3_expr() 会处理这个问题。
                        z3_args.append(arg.as_z3_expr(row_var)) # 假设as_z3_expr可以接受row_var
                    elif isinstance(arg, DateExpressionBoolRef):
                        z3_args.append(arg.z3_expr)
                    elif isinstance(arg, bool):
                        z3_args.append(BoolVal(arg))
                    elif isinstance(arg, BoolRef): # Z3 BoolRef
                        z3_args.append(arg)
                    else:
                        raise TypeError(f"And操作中不支持的参数类型: {type(arg)}")
                return And(*z3_args)

            descriptions = [getattr(arg, 'description', str(arg)) for arg in args]
            return Expression(combined_expr_func, self, f"({' AND '.join(descriptions)})")
        else:
            # 所有参数都可以直接转换为Z3 BoolRef
            z3_args = []
            for arg in args:
                if isinstance(arg, DateExpressionBoolRef):
                    z3_args.append(arg.z3_expr)
                elif isinstance(arg, bool):
                    z3_args.append(BoolVal(arg))
                elif isinstance(arg, BoolRef): # Z3 BoolRef
                    z3_args.append(arg)
                else:
                     # 这个分支理论上不应该被走到，因为前面的any检查会捕获Expression和ColumnConstraint
                    raise TypeError(f"And操作中不支持的参数类型 (此分支不应执行): {type(arg)}")
            return And(*z3_args)

    def Or(self, *args: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool]) -> Union[BoolRef, Expression]:
        """
        对给定的多个表达式或约束执行逻辑或 (OR) 操作。

        Args:
            *args: 一个或多个参数，每个参数可以是Python布尔值 (`bool`), Z3布尔表达式 (`BoolRef`),
                   自定义 `Expression` 对象, `ColumnConstraint` 对象, 或 `DateExpressionBoolRef` 对象。

        Returns:
            - 如果所有参数都是简单类型 (最终可以转换为 `BoolRef`)，返回一个Z3 `BoolRef` 代表OR操作的结果。
            - 如果至少有一个参数是 `Expression` 或 `ColumnConstraint`，返回一个新的 `Expression` 对象，
              该对象在被求值时会执行OR操作。

        Raises:
            TypeError: 如果参数列表中包含不支持的类型。
            ValueError: 如果未提供任何参数。
        """
        if not args:
            logger.warning("Or 操作未收到任何参数，将返回 False (Z3的Or()行为)")
            return BoolVal(False) # 或者根据需求抛出错误: raise ValueError("Or 操作需要至少一个参数")
        
        logger.debug(f"对参数列表执行OR操作 (共 {len(args)} 个参数)")

        if any(isinstance(arg, (Expression, ColumnConstraint)) for arg in args):
            def combined_expr_func(row_var):
                z3_args = []
                for arg in args:
                    if isinstance(arg, Expression):
                        z3_args.append(arg(row_var))
                    elif isinstance(arg, ColumnConstraint):
                        z3_args.append(arg.as_z3_expr(row_var)) # 假设as_z3_expr可以接受row_var
                    elif isinstance(arg, DateExpressionBoolRef):
                        z3_args.append(arg.z3_expr)
                    elif isinstance(arg, bool):
                        z3_args.append(BoolVal(arg))
                    elif isinstance(arg, BoolRef):
                        z3_args.append(arg)
                    else:
                        raise TypeError(f"Or操作中不支持的参数类型: {type(arg)}")
                return Or(*z3_args)
            
            descriptions = [getattr(arg, 'description', str(arg)) for arg in args]
            return Expression(combined_expr_func, self, f"({' OR '.join(descriptions)})")
        else:
            z3_args = []
            for arg in args:
                if isinstance(arg, DateExpressionBoolRef):
                    z3_args.append(arg.z3_expr)
                elif isinstance(arg, bool):
                    z3_args.append(BoolVal(arg))
                elif isinstance(arg, BoolRef):
                    z3_args.append(arg)
                else:
                    raise TypeError(f"Or操作中不支持的参数类型 (此分支不应执行): {type(arg)}")
            return Or(*z3_args)

    def Implies(self, \
                  antecedent: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool], \
                  consequent: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool]) -> Union[BoolRef, Expression]:
        """
        创建蕴含关系 (antecedent => consequent)。

        Args:
            antecedent: 前件。可以是 Python bool, Z3 BoolRef, Expression, ColumnConstraint, 或 DateExpressionBoolRef。
            consequent: 后件。可以是 Python bool, Z3 BoolRef, Expression, ColumnConstraint, 或 DateExpressionBoolRef。

        Returns:
            - Z3 BoolRef 如果前后件最终都为 Z3 BoolRef。
            - Expression 对象如果任一为 Expression 或 ColumnConstraint。
        """
        logger.debug(f"创建Implies操作: {getattr(antecedent, 'description', antecedent)} => {getattr(consequent, 'description', consequent)}")
        
        # 统一处理类型，尝试将Python bool, DateExpressionBoolRef 转换为Z3 BoolRef
        def to_z3_bool_val_if_simple(item, row_var_for_expr=None):
            if isinstance(item, bool): return BoolVal(item)
            if isinstance(item, DateExpressionBoolRef): return item.z3_expr
            if isinstance(item, BoolRef): return item
            if isinstance(item, Expression) and row_var_for_expr is not None: return item(row_var_for_expr)
            if isinstance(item, ColumnConstraint) and row_var_for_expr is not None: return item.as_z3_expr(row_var_for_expr) # 假设 as_z3_expr 接受 row_var
            return None # 表示不是简单类型或无法立即转换

        # 检查是否需要返回 Expression
        if isinstance(antecedent, (Expression, ColumnConstraint)) or isinstance(consequent, (Expression, ColumnConstraint)):
            def implies_func(row_var):
                a = to_z3_bool_val_if_simple(antecedent, row_var) if not isinstance(antecedent, (Expression, ColumnConstraint)) else antecedent(row_var)
                if isinstance(antecedent, ColumnConstraint): a = antecedent.as_z3_expr(row_var)

                b = to_z3_bool_val_if_simple(consequent, row_var) if not isinstance(consequent, (Expression, ColumnConstraint)) else consequent(row_var)
                if isinstance(consequent, ColumnConstraint): b = consequent.as_z3_expr(row_var)

                if a is None or b is None: # Should not happen if logic is correct
                    raise TypeError("Implies操作中参数未能正确转换为Z3表达式")
                return Implies(a, b)

            desc_a = getattr(antecedent, 'description', str(antecedent))
            desc_b = getattr(consequent, 'description', str(consequent))
            return Expression(implies_func, self, f"({desc_a} => {desc_b})")
        else:
            # 两者都可以直接转为 Z3 BoolRef
            a_z3 = to_z3_bool_val_if_simple(antecedent)
            b_z3 = to_z3_bool_val_if_simple(consequent)
            if a_z3 is None or b_z3 is None:
                 raise TypeError(f"Implies操作中参数无法转换为Z3 BoolRef: ant({type(antecedent)}), con({type(consequent)})")
            return Implies(a_z3, b_z3)

    def Xor(self, \
              left: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool], \
              right: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool]) -> Union[BoolRef, Expression]:
        """
        创建异或关系 (left XOR right)。

        Args:
            left: 左操作数。
            right: 右操作数。
            (类型同 Implies)

        Returns:
            - Z3 BoolRef 或 Expression 对象。
        """
        logger.debug(f"创建Xor操作: {getattr(left, 'description', left)} XOR {getattr(right, 'description', right)}")

        # (与Implies类似的类型处理逻辑)
        def to_z3_bool_val_if_simple(item, row_var_for_expr=None):
            if isinstance(item, bool): return BoolVal(item)
            if isinstance(item, DateExpressionBoolRef): return item.z3_expr
            if isinstance(item, BoolRef): return item
            if isinstance(item, Expression) and row_var_for_expr is not None: return item(row_var_for_expr)
            if isinstance(item, ColumnConstraint) and row_var_for_expr is not None: return item.as_z3_expr(row_var_for_expr)
            return None

        if isinstance(left, (Expression, ColumnConstraint)) or isinstance(right, (Expression, ColumnConstraint)):
            def xor_func(row_var):
                l = to_z3_bool_val_if_simple(left, row_var) if not isinstance(left, (Expression, ColumnConstraint)) else left(row_var)
                if isinstance(left, ColumnConstraint): l = left.as_z3_expr(row_var)

                r = to_z3_bool_val_if_simple(right, row_var) if not isinstance(right, (Expression, ColumnConstraint)) else right(row_var)
                if isinstance(right, ColumnConstraint): r = right.as_z3_expr(row_var)
                
                if l is None or r is None:
                    raise TypeError("Xor操作中参数未能正确转换为Z3表达式")
                return Xor(l, r)

            desc_l = getattr(left, 'description', str(left))
            desc_r = getattr(right, 'description', str(right))
            return Expression(xor_func, self, f"({desc_l} XOR {desc_r})")
        else:
            l_z3 = to_z3_bool_val_if_simple(left)
            r_z3 = to_z3_bool_val_if_simple(right)
            if l_z3 is None or r_z3 is None:
                raise TypeError(f"Xor操作中参数无法转换为Z3 BoolRef: left({type(left)}), right({type(right)})")
            return Xor(l_z3, r_z3)

    def If(self, \
             condition: Union[BoolRef, Expression, ColumnConstraint, DateExpressionBoolRef, bool], \
             then_expr: Union[ExprRef, Expression, ColumnConstraint, Any], \
             else_expr: Union[ExprRef, Expression, ColumnConstraint, Any]) -> Union[ExprRef, Expression]:
        """
        创建条件表达式 (IF condition THEN then_expr ELSE else_expr)。

        Args:
            condition: 条件。可以是 Python bool, Z3 BoolRef, Expression, ColumnConstraint, 或 DateExpressionBoolRef。
            then_expr: 条件为真时的表达式。可以是 Z3 ExprRef (任何类型), Expression, ColumnConstraint, 或 Python 常量。
            else_expr: 条件为假时的表达式。类型同 then_expr。

        Returns:
            - Z3 ExprRef (具体类型取决于 then_expr 和 else_expr) 或 Expression 对象。
              如果 condition, then_expr, else_expr 均可直接转换为 Z3 类型，则返回 Z3 ExprRef。
              否则，返回 Expression 对象。
        """
        logger.debug(f"创建If操作: IF {getattr(condition, 'description', condition)} THEN {getattr(then_expr, 'description', then_expr)} ELSE {getattr(else_expr, 'description', else_expr)}")

        def to_z3_val(item, row_var_for_expr=None):
            # 对于 then_expr 和 else_expr，它们可以是任意类型，不一定是布尔值
            if isinstance(item, Expression) and row_var_for_expr is not None: return item(row_var_for_expr)
            if isinstance(item, ColumnConstraint) and row_var_for_expr is not None: return item.as_z3_expr(row_var_for_expr) # 假设其返回適切なZ3表达式
            if isinstance(item, DateExpressionBoolRef): return item.z3_expr # 条件部分
            if isinstance(item, DateExpression): return item.z3_expr # If then/else can be Date types
            if isinstance(item, (bool, int, float, str)): return self._convert_constraint(item) # 利用转换确保是Z3字面量
            if isinstance(item, ExprRef): return item #已经是Z3表达式
            return None # 表示不是简单类型或无法立即转换

        # 检查是否需要返回 Expression
        is_cond_complex = isinstance(condition, (Expression, ColumnConstraint))
        is_then_complex = isinstance(then_expr, (Expression, ColumnConstraint))
        is_else_complex = isinstance(else_expr, (Expression, ColumnConstraint))

        if is_cond_complex or is_then_complex or is_else_complex:
            def if_func(row_var):
                c = to_z3_val(condition, row_var) # 条件必须是布尔
                t = to_z3_val(then_expr, row_var)
                e = to_z3_val(else_expr, row_var)

                if not is_bool(c): # 确保条件是布尔类型
                     # 尝试从ColumnConstraint或Expression中获取布尔表达式
                    if isinstance(condition, ColumnConstraint): c = condition.as_z3_expr(row_var)
                    elif isinstance(condition, Expression): # 假设Expression返回布尔
                         # 如果Expression本身不返回布尔，这里会出错，需要用户确保
                         pass # c已经是condition(row_var)的结果
                    else: #如果不是前述类型且不是bool,则报错
                        raise TypeError(f"If操作的条件部分必须是布尔类型, 得到 {type(c)}")

                if c is None or t is None or e is None:
                    raise TypeError("If操作中参数未能正确转换为Z3表达式")
                return If(c, t, e)

            desc_c = getattr(condition, 'description', str(condition))
            desc_t = getattr(then_expr, 'description', str(then_expr))
            desc_e = getattr(else_expr, 'description', str(else_expr))
            return Expression(if_func, self, f"IF({desc_c}, {desc_t}, {desc_e})")
        else:
            # 所有部分都可以直接转为 Z3 类型
            c_z3 = to_z3_val(condition)
            t_z3 = to_z3_val(then_expr)
            e_z3 = to_z3_val(else_expr)

            if not is_bool(c_z3):
                 raise TypeError(f"If操作的条件部分必须是布尔类型, 得到 {type(c_z3)}")
            if c_z3 is None or t_z3 is None or e_z3 is None:
                raise TypeError(f"If操作中参数无法转换为Z3表达式: c({type(condition)}), t({type(then_expr)}), e({type(else_expr)})")
            return If(c_z3, t_z3, e_z3)

    def model(self) -> z3.ModelRef:
        """
        获取Z3模型（如果有解）。
        这是 get_model() 的一个别名，为了与z3.Solver的API更相似。

        Returns:
            Z3模型对象 (ModelRef)
        """
        logger.debug("调用 model() (get_model的别名)")
        return self.get_model()

    def __enter__(self):
        """进入上下文管理器。"""
        logger.debug("进入SqlToZ3上下文")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器。可以添加资源清理逻辑。"""
        logger.debug("退出SqlToZ3上下文")
        # 在此可以添加例如求解器重置等清理逻辑 (如果需要)
        # self.solver.reset()
        if exc_type:
            logger.error(f"SqlToZ3上下文中发生异常: {exc_type} {exc_val}", exc_info=(exc_type, exc_val, exc_tb))
        return False # 不抑制异常，让其正常传播 

    def reset(self):
        """
        重置求解器状态。
        清除所有约束，但保留已定义的表和列函数。
        """
        logger.info("重置求解器状态...")
        # 重置Z3求解器
        self.solver = TypeAwareEnhancedSolver()
        # 清除约束和JOIN条件
        self.constraints = []
        self.join_conditions = []
        # 重置约束缓存
        self._constraint_cache = []
        logger.info("求解器已重置，所有约束已清除。")

    def create_derived_table(self, name: str, source_table: Table, condition=None, selected_columns=None) -> Table:
        """
        创建派生表，可以选择性地包含源表的部分列，并可以添加筛选条件。
        
        Args:
            name: 派生表的名称。
            source_table: 源表对象。
            condition: 可选的筛选条件，用于限制源表的行被包含在派生表中。
            selected_columns: 可选的列名列表，指定要包含在派生表中的列。默认为None，表示包含所有列。
            
        Returns:
            Table: 新创建的派生表对象。
        """
        self.logger.debug(f"创建派生表 '{name}' 从源表 '{source_table._name}'")
        
        # 确定要包含的列
        if selected_columns is None:
            selected_columns = list(source_table.columns_with_types.keys())
        else:
            # 验证所有选择的列都存在于源表中
            for col in selected_columns:
                if col not in source_table.columns_with_types:
                    raise ValueError(f"列 '{col}' 不存在于源表 '{source_table._name}' 中")
        
        # 从源表中提取选定列的类型
        columns_with_types = {}
        for col in selected_columns:
            columns_with_types[col] = source_table.columns_with_types[col]
        
        # 创建派生表
        derived_table = self.create_table(name, columns_with_types)
        
        # 添加一行到派生表，表示这是从源表派生的
        derived_table_row_id = derived_table.insert()
        
        # 创建源表的行变量
        row_id = Int(f"{source_table._name}_row_{self._get_unique_var_suffix()}")
        
        # 如果指定了条件，添加到求解器
        if condition is not None:
            self.logger.debug(f"为派生表 '{name}' 添加条件")
            self.add(condition)
        
        # 为每一列创建约束，将派生表的值与源表的值关联起来
        for col in selected_columns:
            try:
                # 尝试从 table_z3_functions 获取函数
                source_col_func = self.table_z3_functions[source_table._name][col]
                derived_col_func = self.table_z3_functions[derived_table._name][col]
            except KeyError:
                # 如果找不到，回退到 column_functions
                source_col_func = self.column_functions[f"{source_table._name}_{col}"]
                derived_col_func = self.column_functions[f"{derived_table._name}_{col}"]
            self.add(derived_col_func(derived_table_row_id) == source_col_func(row_id))
        
        self.logger.info(f"派生表 '{name}' 创建成功，包含列: {selected_columns}")
        
        return derived_table
        
    # 辅助方法，用于创建Z3表达式
    def _create_is_null_expr(self, col_val: ExprRef) -> BoolRef:
        """
        创建IS NULL约束的Z3表达式。
        
        在Z3中没有直接表示NULL的方式，这里使用特殊的逻辑来模拟NULL。
        对不同类型的列需要不同的处理方式。
        
        Args:
            col_val: 列值的Z3表达式
            
        Returns:
            BoolRef: 表示"值为NULL"的Z3布尔表达式
        """
        # 这是一个简化实现，实际实现应该根据列的类型进行差异化处理
        # 例如，对于整数类型可以使用一个特殊值如INT_MIN表示NULL
        # 对于字符串类型可以使用空字符串或特殊字符串表示NULL
        # 对于布尔类型可能需要使用额外的布尔变量跟踪NULL状态
        
        sort_kind = col_val.sort().kind()
        
        if sort_kind == Z3_INT_SORT:
            # 对于整数，使用一个特殊的值如-999999表示NULL
            return col_val == IntVal(-999999)
        elif sort_kind == Z3_REAL_SORT:
            # 对于实数，可以使用一个特殊值如-999999.0表示NULL
            return col_val == RealVal(-999999)
        elif sort_kind == Z3_STRING_SORT:
            # 对于字符串，使用空字符串或特殊字符串表示NULL
            return col_val == StringVal("")
        elif sort_kind == Z3_BOOL_SORT:
            # 对于布尔值，需要更复杂的处理，这里使用Z3的未解释函数
            # 创建一个特殊的布尔值标记NULL状态
            is_null_flag = Function(f"is_null_{col_val}", IntSort(), BoolSort())
            return is_null_flag(IntVal(0))
        else:
            # 对于其他类型，返回False，因为我们无法确定如何表示NULL
            self.logger.warning(f"无法为类型 {col_val.sort()} 创建IS NULL表达式")
            return BoolVal(False)
    
    def _create_like_expression(self, col_val: ExprRef, pattern: str) -> BoolRef:
        """
        创建LIKE模式匹配的Z3表达式。
        
        SQL中的LIKE操作符用于模式匹配，例如"LIKE 'A%'"表示以A开头的任何字符串。
        Z3没有直接的LIKE操作符，但我们可以使用正则表达式模拟。
        
        Args:
            col_val: 列值的Z3表达式
            pattern: LIKE模式，例如'A%'、'_B%'等
            
        Returns:
            BoolRef: 表示LIKE匹配的Z3布尔表达式
        """
        # 将SQL的LIKE模式转换为Z3支持的正则表达式
        # 这是一个简化实现，只处理最基本的%和_通配符
        
        from z3 import Re, InRe
        
        # 将SQL LIKE模式转换为正则表达式
        # 替换 % 为 .*
        # 替换 _ 为 .
        # 处理转义字符
        regex_pattern = ""
        i = 0
        while i < len(pattern):
            if pattern[i] == '%':
                regex_pattern += ".*"
            elif pattern[i] == '_':
                regex_pattern += "."
            elif pattern[i] == '\\' and i + 1 < len(pattern):
                # 处理转义字符
                regex_pattern += pattern[i+1]
                i += 1  # 跳过下一个字符
            else:
                regex_pattern += pattern[i]
            i += 1
        
        self.logger.debug(f"SQL LIKE模式 '{pattern}' 已转换为正则表达式 '{regex_pattern}'")
        
        # 创建Z3正则表达式对象并检查字符串是否匹配
        regex = Re(regex_pattern)
        return InRe(col_val, regex)
    
    def _cast_to_string(self, val: ExprRef) -> ExprRef:
        """将值转换为字符串类型"""
        # Z3没有直接的类型转换函数，需要模拟
        sort_kind = val.sort().kind()
        
        if sort_kind == Z3_STRING_SORT:
            return val  # 已经是字符串
        
        # 创建一个将其他类型转换为字符串的未解释函数
        cast_func_name = f"to_string_{sort_kind}"
        if not hasattr(self, cast_func_name):
            setattr(self, cast_func_name, Function(cast_func_name, val.sort(), StringSort()))
        
        cast_func = getattr(self, cast_func_name)
        return cast_func(val)
    
    def _cast_to_int(self, val: ExprRef) -> ExprRef:
        """将值转换为整数类型"""
        sort_kind = val.sort().kind()
        
        if sort_kind == Z3_INT_SORT:
            return val  # 已经是整数
        
        # 创建一个将其他类型转换为整数的未解释函数
        cast_func_name = f"to_int_{sort_kind}"
        if not hasattr(self, cast_func_name):
            setattr(self, cast_func_name, Function(cast_func_name, val.sort(), IntSort()))
        
        cast_func = getattr(self, cast_func_name)
        return cast_func(val)
    
    def _cast_to_real(self, val: ExprRef) -> ExprRef:
        """将值转换为实数类型"""
        sort_kind = val.sort().kind()
        
        if sort_kind == Z3_REAL_SORT:
            return val  # 已经是实数
        elif sort_kind == Z3_INT_SORT:
            return ToReal(val)  # Z3提供了整数到实数的直接转换
        
        # 创建一个将其他类型转换为实数的未解释函数
        cast_func_name = f"to_real_{sort_kind}"
        if not hasattr(self, cast_func_name):
            setattr(self, cast_func_name, Function(cast_func_name, val.sort(), RealSort()))
        
        cast_func = getattr(self, cast_func_name)
        return cast_func(val)
    
    def _create_upper_expression(self, val: ExprRef) -> ExprRef:
        """创建转大写的表达式"""
        if val.sort().kind() != Z3_STRING_SORT:
            self.logger.warning(f"尝试对非字符串类型 {val.sort()} 使用UPPER函数")
            return val
        
        # 创建一个未解释函数来模拟UPPER
        if not hasattr(self, "_upper_func"):
            self._upper_func = Function("UPPER", StringSort(), StringSort())
        
        return self._upper_func(val)
    
    def _create_lower_expression(self, val: ExprRef) -> ExprRef:
        """创建转小写的表达式"""
        if val.sort().kind() != Z3_STRING_SORT:
            self.logger.warning(f"尝试对非字符串类型 {val.sort()} 使用LOWER函数")
            return val
        
        # 创建一个未解释函数来模拟LOWER
        if not hasattr(self, "_lower_func"):
            self._lower_func = Function("LOWER", StringSort(), StringSort())
        
        return self._lower_func(val)
    
    def _create_replace_expression(self, val: ExprRef, old_str: str, new_str: str) -> ExprRef:
        """创建替换子串的表达式"""
        if val.sort().kind() != Z3_STRING_SORT:
            self.logger.warning(f"尝试对非字符串类型 {val.sort()} 使用REPLACE函数")
            return val
        
        # 创建一个未解释函数来模拟REPLACE
        func_name = f"REPLACE_{old_str}_{new_str}"
        if not hasattr(self, func_name):
            setattr(self, func_name, Function(func_name, StringSort(), StringSort()))
        
        replace_func = getattr(self, func_name)
        return replace_func(val)
    
    def _create_trim_expression(self, val: ExprRef) -> ExprRef:
        """创建去除两端空白的表达式"""
        if val.sort().kind() != Z3_STRING_SORT:
            self.logger.warning(f"尝试对非字符串类型 {val.sort()} 使用TRIM函数")
            return val
        
        # 创建一个未解释函数来模拟TRIM
        if not hasattr(self, "_trim_func"):
            self._trim_func = Function("TRIM", StringSort(), StringSort())
        
        return self._trim_func(val)
    
    def _convert_to_date(self, val: ExprRef, format_str: str) -> ExprRef:
        """将字符串转换为日期类型"""
        if val.sort().kind() != Z3_STRING_SORT:
            self.logger.warning(f"尝试对非字符串类型 {val.sort()} 使用TO_DATE函数")
            return val
        
        # 创建一个未解释函数来模拟日期转换
        func_name = f"TO_DATE_{format_str}"
        if not hasattr(self, func_name):
            setattr(self, func_name, Function(func_name, StringSort(), IntSort()))
        
        to_date_func = getattr(self, func_name)
        return to_date_func(val)
    
    def _extract_year(self, val: ExprRef) -> ExprRef:
        """从日期中提取年份"""
        # 这里假设日期表示为整数
        if val.sort().kind() != Z3_INT_SORT:
            self.logger.warning(f"尝试对非整数类型 {val.sort()} 使用EXTRACT YEAR函数")
            return val
        
        # 创建一个未解释函数来模拟年份提取
        if not hasattr(self, "_extract_year_func"):
            self._extract_year_func = Function("EXTRACT_YEAR", IntSort(), IntSort())
        
        return self._extract_year_func(val)
    
    def _extract_month(self, val: ExprRef) -> ExprRef:
        """从日期中提取月份"""
        # 这里假设日期表示为整数
        if val.sort().kind() != Z3_INT_SORT:
            self.logger.warning(f"尝试对非整数类型 {val.sort()} 使用EXTRACT MONTH函数")
            return val
        
        # 创建一个未解释函数来模拟月份提取
        if not hasattr(self, "_extract_month_func"):
            self._extract_month_func = Function("EXTRACT_MONTH", IntSort(), IntSort())
        
        return self._extract_month_func(val)
    
    def _extract_day(self, val: ExprRef) -> ExprRef:
        """从日期中提取天"""
        # 这里假设日期表示为整数
        if val.sort().kind() != Z3_INT_SORT:
            self.logger.warning(f"尝试对非整数类型 {val.sort()} 使用EXTRACT DAY函数")
            return val
        
        # 创建一个未解释函数来模拟日提取
        if not hasattr(self, "_extract_day_func"):
            self._extract_day_func = Function("EXTRACT_DAY", IntSort(), IntSort())
        
        return self._extract_day_func(val)

    def join_condition_and(self, *conditions):
        """
        创建多个JOIN条件的AND组合。
        
        该方法允许直接将多个表间比较组合为一个AND逻辑关系，
        避免需要手动提取行变量并添加相等约束的繁琐过程。
        
        注意: Column.__eq__方法已经将JOIN约束添加到求解器，
        本方法只需确保行变量之间的一致性。
        
        Args:
            *conditions: 可变数量的表间比较表达式。每个表达式应该是由Column.__eq__等方法返回的
                        (var1, table1_name, var2, table2_name)类型的元组。
        
        Returns:
            tuple: 如成功，返回第一个JOIN条件的行变量元组，以便后续使用；如失败则返回None。
        
        Example:
            ```
            # 等价于:
            # join1 = customers.id == orders.customer_id
            # join2 = customers.region == orders.region
            # self.z3_sql.add(join1[0] == join2[0])
            # self.z3_sql.add(join1[2] == join2[2])
            join_vars = self.z3_sql.join_condition_and(
                customers.id == orders.customer_id,
                customers.region == orders.region
            )
            if join_vars:
                customer_row_var, order_row_var = join_vars[0], join_vars[2]
                # ... 使用这些统一的行变量进行后续处理
            ```
        """
        if not conditions:
            self.logger.warning("join_condition_and调用时未提供条件")
            return None
            
        # 检查条件是否都是有效的JOIN表达式
        for i, condition in enumerate(conditions):
            if not isinstance(condition, tuple) or len(condition) != 4:
                self.logger.error(f"join_condition_and的第{i+1}个参数不是有效的JOIN表达式: {condition}")
                return None
        
        # 第一个JOIN条件的行变量作为参考点
        ref_row_var1 = conditions[0][0]  # 第一个表的行变量
        ref_row_var2 = conditions[0][2]  # 第二个表的行变量
        ref_table1 = conditions[0][1]  # 第一个表名
        ref_table2 = conditions[0][3]  # 第二个表名
        
        # 确保所有条件都涉及相同的表对
        for i, (row_var1, table1, row_var2, table2) in enumerate(conditions[1:], start=1):
            if table1 != ref_table1 or table2 != ref_table2:
                self.logger.error(f"join_condition_and的表不匹配: 第1个条件使用表({ref_table1},{ref_table2})，第{i+1}个条件使用表({table1},{table2})")
                return None
            
            # 添加行变量相等的约束 - 这是关键步骤
            self.add(row_var1 == ref_row_var1)
            self.add(row_var2 == ref_row_var2)
            
        self.logger.info(f"成功添加了{len(conditions)}个JOIN条件的AND组合")
        return conditions[0]  # 返回第一个JOIN条件的元组，包含统一后的行变量
        
    def join_condition_or(self, *conditions):
        """
        创建多个JOIN条件的OR组合。
        
        与join_condition_and不同，OR组合需要为每个条件创建独立的行变量对，
        因为每个条件可能匹配不同的行组合。
        
        Args:
            *conditions: 可变数量的表间比较表达式。每个表达式应该是由Column.__eq__等方法返回的
                        (var1, table1_name, var2, table2_name)类型的元组。
        
        Returns:
            bool: 返回True表示OR条件已成功添加到求解器。
        
        Example:
            ```
            # 匹配任一条件的客户与订单
            self.z3_sql.join_condition_or(
                customers.id == orders.customer_id,
                customers.name == orders.customer_name
            )
            ```
        """
        if not conditions:
            self.logger.warning("join_condition_or调用时未提供条件")
            return False
            
        # 检查条件是否都是有效的JOIN表达式
        for i, condition in enumerate(conditions):
            if not isinstance(condition, tuple) or len(condition) != 4:
                self.logger.error(f"join_condition_or的第{i+1}个参数不是有效的JOIN表达式: {condition}")
                return False
        
        # 确保所有条件都涉及相同的表对
        ref_table1 = conditions[0][1]  # 第一个表名
        ref_table2 = conditions[0][3]  # 第二个表名
        
        for i, (_, table1, _, table2) in enumerate(conditions[1:], start=1):
            if table1 != ref_table1 or table2 != ref_table2:
                self.logger.error(f"join_condition_or的表不匹配: 第1个条件使用表({ref_table1},{ref_table2})，第{i+1}个条件使用表({table1},{table2})")
                return False
        
        # 创建一对"主"行变量
        master_var1_name = f"{ref_table1}_row_master_{self._get_unique_var_suffix()}"
        master_var2_name = f"{ref_table2}_row_master_{self._get_unique_var_suffix()}"
        master_var1 = Int(master_var1_name)
        master_var2 = Int(master_var2_name)
        
        # 添加主行变量的有效性约束
        if ref_table1 in self.tables and self.tables[ref_table1].rows:
            self.add(Or([master_var1 == i for i in self.tables[ref_table1].rows]))
        if ref_table2 in self.tables and self.tables[ref_table2].rows:
            self.add(Or([master_var2 == i for i in self.tables[ref_table2].rows]))
        
        # 构建每个条件的约束，合并为一个大的OR表达式
        or_constraints = []
        for row_var1, _, row_var2, _ in conditions:
            # 约束主行变量等于当前条件的行变量
            cond_constraint = And(master_var1 == row_var1, master_var2 == row_var2)
            or_constraints.append(cond_constraint)
            
        # 添加OR约束
        if or_constraints:
            self.add(Or(or_constraints))
            self.logger.info(f"成功添加了{len(conditions)}个JOIN条件的OR组合")
            
            # 记录JOIN条件
            join_info = {'v1': master_var1, 't1': ref_table1, 'v2': master_var2, 't2': ref_table2}
            self.join_conditions.append(join_info)
            return True
            
        return False

    def _convert_value_to_z3_compatible_type(self, value: Any, target_z3_type: Optional[SortRef] = None) -> Optional[ExprRef]:
        """
        将Python值转换为与目标Z3类型兼容的Z3表达式。
        如果提供了target_z3_type，则转换会尝试匹配该类型。
        """
        from z3 import is_bool, is_int, is_real, is_string, BoolVal, IntVal, RealVal, StringVal, BoolSort, IntSort, RealSort, StringSort
        
        ctx = self.solver.ctx # 获取上下文
        
        # 优先处理目标类型已明确的情况
        if target_z3_type is not None:
            if is_bool(target_z3_type) or target_z3_type == BoolSort():
                if isinstance(value, bool):
                    return BoolVal(value, ctx=ctx)
                elif isinstance(value, str):
                    if value.lower() == "true":
                        return BoolVal(True, ctx=ctx)
                    elif value.lower() == "false":
                        return BoolVal(False, ctx=ctx)
            elif is_int(target_z3_type) or target_z3_type == IntSort():
                if isinstance(value, (int, float)) or (isinstance(value, str) and value.isdigit()):
                    return IntVal(int(value), ctx=ctx)
            elif is_real(target_z3_type) or target_z3_type == RealSort():
                if isinstance(value, (int, float)) or (isinstance(value, str) and (value.replace('.', '', 1).isdigit() or value.lstrip('+-').replace('.', '', 1).isdigit())):
                    return RealVal(value, ctx=ctx)
            elif is_string(target_z3_type) or target_z3_type == StringSort():
                return StringVal(str(value), ctx=ctx)
        
        # 如果没有明确的目标类型，或无法转换为目标类型，尝试根据Python类型进行自动转换
        if isinstance(value, bool):
            return BoolVal(value, ctx=ctx)
        elif isinstance(value, int):
            return IntVal(value, ctx=ctx)
        elif isinstance(value, float):
            return RealVal(value, ctx=ctx)
        elif isinstance(value, str):
            # 尝试判断字符串是否表示特定类型
            if value.lower() in ["true", "false"]:
                return BoolVal(value.lower() == "true", ctx=ctx)
            elif value.isdigit():
                return IntVal(int(value), ctx=ctx)
            elif value.replace('.', '', 1).isdigit() or value.lstrip('+-').replace('.', '', 1).isdigit():
                return RealVal(value, ctx=ctx)
            else:
                return StringVal(value, ctx=ctx)
        
        # 如果无法转换，返回None，调用者可以据此决定如何处理
        self.logger.warning(f"无法将Python值 {value} (类型: {type(value)}) 转换为Z3兼容类型")
        return None

    def create_joined_table(self, name: str, table1: Table, table2: Table, condition: Any, join_type: str = "inner") -> Table:
        """
        创建两个表的连接表。
        
        Args:
            name: 新表的名称
            table1: 第一个表
            table2: 第二个表
            condition: 连接条件
            join_type: 连接类型，目前只支持"inner"
            
        Returns:
            Table: 连接后的新表
        """
        self.logger.info(f"创建连接表 '{name}' 从表 '{table1.name}' 和 '{table2.name}'")
        
        # 合并两个表的列定义
        columns = {}
        for col_name, col_type in table1.columns_with_types.items():
            columns[f"{table1.name}_{col_name}"] = col_type
        
        for col_name, col_type in table2.columns_with_types.items():
            columns[f"{table2.name}_{col_name}"] = col_type
        
        # 创建新表
        joined_table = self.create_table(name, columns)
        
        # 添加连接条件
        if condition is not None:
            self.add(condition)
        
        return joined_table

    def create_aggregate_expression(self, name: str, table: Table, column_name: str, aggregate_function: str) -> Expression:
        """
        创建聚合表达式，如SUM, AVG, COUNT等。
        
        Args:
            name: 表达式名称
            table: 源表
            column_name: 要聚合的列名
            aggregate_function: 聚合函数名称，如"SUM", "AVG", "COUNT"等
            
        Returns:
            Expression: 代表聚合操作的表达式
        """
        self.logger.info(f"创建聚合表达式 '{name}' 对表 '{table.name}' 的列 '{column_name}' 使用函数 '{aggregate_function}'")
        
        # 获取列对象
        # 特殊处理 '*' 列（用于COUNT(*)）
        if column_name == '*' and aggregate_function.upper() == 'COUNT':
            # 对于COUNT(*)，我们不需要实际的列
            from new_model.core.expression import Expression
            
            def count_all_expr_func(row_var):
                # 简化实现：始终返回1作为计数
                from z3 import IntVal
                return IntVal(1, self.solver.ctx)
            
            return Expression(
                count_all_expr_func,
                self,
                f"{aggregate_function}({table.name}.*)"
            )
        else:
            column = getattr(table, column_name)
            
            # 创建一个简单的表达式，表示聚合操作
            # 注意：这是一个简化实现，实际上需要更复杂的逻辑来处理不同的聚合函数
            from new_model.core.expression import Expression
            
            def aggregate_expr_func(row_var):
                # 这里应该根据aggregate_function类型返回不同的表达式
                # 但目前只是简单地返回列在给定行的值
                return column.function(row_var)
            
            return Expression(
                aggregate_expr_func,
                self,
                f"{aggregate_function}({table.name}.{column_name})"
            )

    def create_exists_subquery(self, table: Table, condition: Any) -> Expression:
        """
        创建EXISTS子查询表达式。
        
        Args:
            table: 子查询的表
            condition: 子查询的条件
            
        Returns:
            Expression: 代表EXISTS子查询的表达式
        """
        self.logger.info(f"创建EXISTS子查询对表 '{table.name}'")
        
        # 添加子查询条件
        if condition is not None:
            self.add(condition)
        
        # 创建一个简单的表达式，表示EXISTS子查询
        # 注意：这是一个简化实现，实际上需要更复杂的逻辑来处理EXISTS子查询
        from new_model.core.expression import Expression
        
        def exists_expr_func(row_var):
            # 简化实现：始终返回True
            from z3 import BoolVal
            return BoolVal(True, self.solver.ctx)
        
        return Expression(
            exists_expr_func,
            self,
            f"EXISTS (SELECT * FROM {table.name} WHERE ...)"
        )

    def add_example_data(self, table_name: str, data: List[Dict[str, Any]]):
        """
        向表中添加示例数据。
        
        Args:
            table_name: 表名
            data: 数据列表，每个元素是一个字典，键是列名，值是列值
        """
        self.logger.info(f"向表 '{table_name}' 添加 {len(data)} 行示例数据")
        
        # 获取表对象
        table = self.tables.get(table_name)
        if table is None:
            raise ValueError(f"表 '{table_name}' 不存在")
        
        # 为每行数据添加约束
        for i, row_data in enumerate(data):
            row_id = i + 1  # 行ID从1开始
            
            # 为每个列添加约束
            for col_name, col_value in row_data.items():
                # 获取列的Z3函数
                z3_func = self.table_z3_functions[table_name][col_name]
                
                # 为不同类型的值进行转换
                from z3 import String, Int, Real, Bool
                
                # 根据列类型进行转换
                z3_type = z3_func.range()  # 函数返回类型即为列类型
                
                # 转换值为Z3兼容类型
                if isinstance(col_value, str):
                    z3_value = String(col_value)
                elif isinstance(col_value, int):
                    z3_value = Int(col_value)
                elif isinstance(col_value, float):
                    z3_value = Real(col_value)
                elif isinstance(col_value, bool):
                    z3_value = Bool(col_value)
                else:
                    z3_value = col_value
                
                # 添加约束：z3_func(row_id) == z3_value
                self.add(z3_func(row_id) == z3_value)

    def register_custom_function(self, name: str, func_impl: Any):
        """
        注册自定义函数。
        
        Args:
            name: 函数名称
            func_impl: 函数实现，应该接受一个参数（表达式）并返回一个新的表达式
        """
        self.logger.info(f"注册自定义函数 '{name}'")
        
        # 存储自定义函数
        if not hasattr(self, "custom_functions"):
            self.custom_functions = {}
        
        self.custom_functions[name] = func_impl

    def create_custom_expression(self, name: str, function_name: str, *args):
        """
        使用自定义函数创建表达式。
        
        Args:
            name: 表达式名称
            function_name: 自定义函数名称
            args: 传递给自定义函数的参数
            
        Returns:
            Expression: 使用自定义函数创建的表达式
        """
        self.logger.info(f"创建自定义表达式 '{name}' 使用函数 '{function_name}'")
        
        # 检查自定义函数是否已注册
        if not hasattr(self, "custom_functions") or function_name not in self.custom_functions:
            raise ValueError(f"自定义函数 '{function_name}' 尚未注册")
        
        # 获取自定义函数
        custom_func = self.custom_functions[function_name]
        
        # 创建一个表达式，使用自定义函数
        from new_model.core.expression import Expression
        
        def custom_expr_func(row_var):
            # 获取参数值
            arg_values = [arg(row_var) if hasattr(arg, '__call__') else arg for arg in args]
            
            # 调用自定义函数
            return custom_func(*arg_values)
        
        # 构建描述
        arg_descs = [getattr(arg, 'description', str(arg)) for arg in args]
        description = f"{function_name}({', '.join(arg_descs)})"
        
        return Expression(
            custom_expr_func,
            self,
            description
        )
        
    def create_in_subquery(self, column: 'Column', subquery_column: 'Column') -> 'Expression':
        """
        创建IN子查询表达式，例如 WHERE department IN (SELECT name FROM departments)
        
        Args:
            column: 要检查的列或表达式
            subquery_column: 包含候选值的列或表达式
            
        Returns:
            Expression: 代表IN子查询的表达式
        """
        from new_model.core.expression import Expression
        
        self.logger.info(f"创建IN子查询: {column.description} IN ...")
        
        # 获取列的描述
        col_desc = getattr(column, 'description', str(column))
        subquery_desc = getattr(subquery_column, 'description', str(subquery_column))
        
        # 创建一个简单的表达式，表示IN子查询
        # 这是一个简化实现，实际上需要更复杂的逻辑来处理实际的IN子查询
        def in_expr_func(row_var):
            # 简化实现：始终返回True
            from z3 import BoolVal
            return BoolVal(True, self.solver.ctx)
        
        return Expression(
            in_expr_func,
            self,
            f"{col_desc} IN ({subquery_desc})"
        )
        
    def check_and_get_model(self):
        """
        检查约束是否可满足，并返回一个模型（如果可满足）。
        
        Returns:
            Model | None: 可满足时返回一个模型，否则返回None
        """
        from z3 import sat
        
        self.logger.info("检查约束并获取模型")
        result = self.check()
        
        if result == sat:
            self.logger.info("约束可满足，生成模型")
            return self.get_model()
        else:
            self.logger.info(f"约束不可满足，结果: {result}")
            return None
