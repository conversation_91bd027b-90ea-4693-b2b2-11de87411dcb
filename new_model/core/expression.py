"""
表达式类模块
用于表示SQL中的表达式，并提供操作符重载
"""

from z3 import *
from typing import Callable, Any, Optional, Union, TYPE_CHECKING

# 避免循环导入，仅用于类型注解
if TYPE_CHECKING:
    from .column import Column # 导入 Column 以进行类型提示
    from .solver import SqlToZ3 # 导入 SqlToZ3 以进行类型提示

class Expression:
    """表示字段表达式的类"""
    def __init__(self, expr_func: Callable[[ExprRef], ExprRef], solver_wrapper: 'SqlToZ3', description: str = "", target_table_names: Optional[list] = None):
        """
        初始化表达式
        
        Args:
            expr_func: 函数，接受行ID变量返回Z3表达式
            solver_wrapper: SqlToZ3实例的引用
            description: 表达式描述，用于调试
            target_table_names: 此表达式引用的表名列表
        """
        if not callable(expr_func):
            raise TypeError(f"expr_func必须是可调用对象 (callable), 但得到的是 {type(expr_func)}")
        
        self.expr_func: Callable[[ExprRef], ExprRef] = expr_func
        self.solver_wrapper: 'SqlToZ3' = solver_wrapper
        self.description: str = description if description else f"DynamicExpr@{id(self)}" # 如果没有描述，提供一个默认的唯一描述
        self.logger = solver_wrapper.logger # 使用solver_wrapper的logger实例
        self.target_table_names = target_table_names or []

        self.logger.debug(f"Expression 对象已创建: '{self.description}'. Function: {expr_func}")
    
    def __call__(self, row_var=None):
        """
        调用表达式对象，应用表达式函数并返回结果。
        
        Args:
            row_var (ExprRef, optional): 行变量，传递给表达式函数。如果为None且表达式函数需要参数，
                                        会创建一个临时行变量。
                                        
        Returns:
            ExprRef: 表达式计算结果，通常为Z3表达式。
        """
        try:
            import inspect
            # 检查expr_func需要的参数个数
            sig = inspect.signature(self.expr_func)
            if len(sig.parameters) == 0:
                # 如果expr_func不需要参数，直接调用
                self.logger.info(f"Expression '{self.description}' 被调用，lambda不需要参数")
                result_expr = self.expr_func()
                self.logger.info(f"Expression '{self.description}' lambda执行结果: 类型={type(result_expr)}, 值={str(result_expr)[:100]}")
            else:
                # 如果expr_func需要参数但没有提供row_var
                if row_var is None:
                    import uuid
                    unique_id = uuid.uuid4().int
                    temp_row_var_name = f"expr_temp_row_1_{unique_id}"
                    self.logger.warning(f"Expression '{self.description}' 的 as_z3_expr() 在没有提供 row_var "
                                       f"的情况下被调用。已创建一个临时行变量 '{temp_row_var_name}'. "
                                       f"这种用法可能导致意外行为，因为表达式的上下文通常应由调用者明确指定。")
                    row_var = temp_row_var_name
                
                self.logger.info(f"Expression '{self.description}' 被调用，使用行变量: {row_var}, 类型={type(row_var)}")
                result_expr = self.expr_func(row_var)
                self.logger.info(f"Expression '{self.description}' lambda执行结果: 类型={type(result_expr)}, 值={str(result_expr)[:100]}")
                
            # 检查结果类型
            if isinstance(result_expr, bool):
                self.logger.info(f"Expression '{self.description}' 返回Python布尔值 {result_expr}，需要转换为Z3 BoolVal")
                ctx = getattr(self.solver_wrapper, 'solver', None)
                if ctx:
                    ctx = getattr(ctx, 'ctx', None)
                self.logger.info(f"转换布尔值时使用的上下文: {ctx}")
                # 使用Z3的BoolVal函数创建Z3布尔值
                result_expr = BoolVal(result_expr, ctx=getattr(self.solver_wrapper.solver, 'ctx', None))
                
            return result_expr
        except Exception as e:
            self.logger.error(f"在执行 Expression '{self.description}' 的 expr_func 时发生错误 (行变量: {row_var}): {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise
            
    def for_row(self, row_id: int) -> ExprRef:
        """
        为指定的行ID计算表达式的值。
        
        Args:
            row_id: 行ID，整数
            
        Returns:
            ExprRef: 表示该行上表达式计算结果的Z3表达式
        """
        self.logger.info(f"Expression.for_row调用，表达式: '{self.description}'，行ID: {row_id}")
        
        # 对于Expression，直接将row_id作为参数传递给__call__
        return self(row_id)
    
    def as_z3_expr(self, row_var: Optional[ExprRef] = None) -> ExprRef:
        """
        获取表达式对应的Z3表达式形式
        
        Args:
            row_var (Optional[ExprRef]): 计算表达式时使用的行变量。如果为None且表达式函数需要参数，
                                        将创建一个临时行变量。
        
        Returns:
            ExprRef: Z3表达式形式
        """
        return self(row_var) # 直接调用 __call__ 方法
    
    # --- 算术运算符 ---
    def __add__(self, other: Any) -> 'Expression':
        """
        重载加法 (+) 运算符 (self + other)。

        创建一个新的 `Expression` 对象，其 `expr_func` 会在求值时
        将当前表达式的结果与 `other`（常量、Column或另一个Expression）相加。

        Args:
            other (Any): 与此表达式相加的对象。可以是以下类型：
                         - Python 常量 (int, float, str)。对于str，表示字符串拼接。
                         - `Column` 对象。
                         - 另一个 `Expression` 对象。

        Returns:
            Expression: 代表加法操作的新 `Expression` 对象。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_str = "+"
        self.logger.debug(f"Expression运算 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        
        # 需要从 TYPE_CHECKING 移到这里，因为 isinstance 需要实际的类定义
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str)):
            # 表达式 + 常量
            new_expr_func = lambda row_var: self.expr_func(row_var) + other
            # 确保字符串间没有多余空格
            if isinstance(other, str):
                # 对于字符串，去掉类型中的引号
                other_str = other.replace('"', '').replace("'", '')
                new_description = f"{self.description} {op_str} {other_str}"
            else:
                new_description = f"{self.description} {op_str} {other}"
        elif isinstance(other, Column):
            # 表达式 + 列
            new_expr_func = lambda row_var: self.expr_func(row_var) + other(row_var)
            new_description = f"{self.description} {op_str} {other.column_name}"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            # 表达式 + 表达式
            new_expr_func = lambda row_var: self.expr_func(row_var) + other(row_var)
            # 尝试简化描述，删除表名前缀
            other_desc = other.description
            # 如果other的描述包含表名.列名格式，尝试提取出列名部分
            if '.' in other_desc and not other_desc.startswith('('):
                other_desc = other_desc.split('.')[-1]
            new_description = f"{self.description} {op_str} {other_desc}"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression运算: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __sub__(self, other: Any) -> 'Expression':
        """
        重载减法 (-) 运算符 (self - other)。
        不支持字符串的减法。

        Args:
            other (Any): 从此表达式中减去的对象 (int, float, Column, Expression)。

        Returns:
            Expression: 代表减法操作的新 `Expression`。
        """
        op_str = "-"
        self.logger.debug(f"Expression运算 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column

        # 合并target_table_names
        target_tables = self.target_table_names.copy()

        if isinstance(other, (int, float)): # str 不支持减法
            new_expr_func = lambda row_var: self.expr_func(row_var) - other
            new_description = f"{self.description} {op_str} {other}"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) - other(row_var)
            new_description = f"{self.description} {op_str} {other.column_name}"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) - other(row_var)
            # 尝试简化描述，删除表名前缀
            other_desc = other.description
            # 如果other的描述包含表名.列名格式，尝试提取出列名部分
            if '.' in other_desc and not other_desc.startswith('('):
                other_desc = other_desc.split('.')[-1]
            new_description = f"{self.description} {op_str} ({other_desc})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression运算: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __mul__(self, other: Any) -> 'Expression':
        """
        重载乘法 (*) 运算符 (self * other)。
        对于字符串类型，Z3可能将其解释为序列重复 (如果other是整数)。

        Args:
            other (Any): 与此表达式相乘的对象 (int, float, Column, Expression)。

        Returns:
            Expression: 代表乘法操作的新 `Expression`。
        """
        op_str = "*"
        self.logger.debug(f"Expression运算 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column

        # 合并target_table_names
        target_tables = self.target_table_names.copy()

        if isinstance(other, (int, float)): # Z3中 Seq * Int 是序列重复, Arith * Arith 是乘法
            new_expr_func = lambda row_var: self.expr_func(row_var) * other
            new_description = f"{self.description} {op_str} {other}"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) * other(row_var)
            new_description = f"({self.description} {op_str} {other.column_name})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) * other(row_var)
            # 尝试简化描述，删除表名前缀
            other_desc = other.description
            # 如果other的描述包含表名.列名格式，尝试提取出列名部分
            if '.' in other_desc and not other_desc.startswith('('):
                other_desc = other_desc.split('.')[-1]
            new_description = f"({self.description} {op_str} {other_desc})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression运算: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __truediv__(self, other: Any) -> 'Expression':
        """
        重载真除法 (/) 运算符 (self / other)。
        Z3要求除法操作数是数值类型 (Int, Real)。

        Args:
            other (Any): 除数 (int, float, Column, Expression)。

        Returns:
            Expression: 代表除法操作的新 `Expression`。
        """
        op_str = "/"
        self.logger.debug(f"Expression运算 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column

        # 合并target_table_names
        target_tables = self.target_table_names.copy()

        if isinstance(other, (int, float)):
            new_expr_func = lambda row_var: self.expr_func(row_var) / other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) / other(row_var)
            new_description = f"({self.description} {op_str} {other.column_name})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) / other(row_var)
            # 尝试简化描述，删除表名前缀
            other_desc = other.description
            # 如果other的描述包含表名.列名格式，尝试提取出列名部分
            if '.' in other_desc and not other_desc.startswith('('):
                other_desc = other_desc.split('.')[-1]
            new_description = f"({self.description} {op_str} {other_desc})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression运算: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)

    # --- 反向算术运算符 ---
    def __radd__(self, other: Any) -> 'Expression':
        """
        重载右加法 (+) 运算符 (other + self)。
        当左操作数不是Expression时触发。例如 10 + expr。

        Args:
            other (Any): 本表达式要加到的对象 (通常是常量)。

        Returns:
            Expression: 代表加法操作的新 `Expression`。
        """
        op_str = "+"
        self.logger.debug(f"Expression右运算 ({op_str}): {other} {op_str} {self.description}")
        
        # 反向操作不涉及更新 target_tables，因为右侧的常量没有表关联
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str)):
            new_expr_func = lambda row_var: other + self.expr_func(row_var)
            new_description = f"({other} {op_str} {self.description})"
            return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        self.logger.warning(f"不支持的Expression右运算: {other} {op_str} {self.description}")
        return NotImplemented
    
    def __rsub__(self, other: Any) -> 'Expression':
        """
        重载反向减法 (-) 运算符 (other - self)。
        
        当表达式出现在减号右侧时使用。
        例如: 10 - expr 将调用 expr.__rsub__(10)
        
        Args:
            other (Any): 减去表达式的对象 (通常是Python内置类型，如int, float)

        Returns:
            Expression: 代表反向减法操作的新表达式
        """
        op_str = "-"
        self.logger.debug(f"Expression右运算 ({op_str}): {other} {op_str} {self.description}")
        
        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        # 这里，other通常是Python内置类型，如int, float
        if isinstance(other, (int, float)):
            new_expr_func = lambda row_var: other - self.expr_func(row_var)
            new_description = f"{other} {op_str} ({self.description})"
        else:
            self.logger.warning(f"不支持的Expression反向运算: {other} {op_str} {self.description}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __rmul__(self, other: Any) -> 'Expression':
        """
        重载右乘法 (*) 运算符 (other * self)。
        例如 10 * expr。

        Args:
            other (Any): 与本表达式相乘的对象 (通常是常量)。

        Returns:
            Expression: 代表乘法操作的新 `Expression`。
        """
        op_str = "*"
        self.logger.debug(f"Expression右运算 ({op_str}): {other} {op_str} {self.description}")
        
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float)):
            new_expr_func = lambda row_var: other * self.expr_func(row_var)
            new_description = f"({other} {op_str} {self.description})"
            return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        self.logger.warning(f"不支持的Expression右运算: {other} {op_str} {self.description}")
        return NotImplemented
    
    def __rtruediv__(self, other: Any) -> 'Expression':
        """
        重载右除法 (/) 运算符 (other / self)。
        例如 10 / expr。

        Args:
            other (Any): 被本表达式除的对象 (通常是常量)。

        Returns:
            Expression: 代表除法操作的新 `Expression`。
        """
        op_str = "/"
        self.logger.debug(f"Expression右运算 ({op_str}): {other} {op_str} {self.description}")
        
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float)):
            new_expr_func = lambda row_var: other / self.expr_func(row_var)
            new_description = f"({other} {op_str} {self.description})"
            return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        self.logger.warning(f"不支持的Expression右运算: {other} {op_str} {self.description}")
        return NotImplemented
    
    # --- 比较运算符 ---
    def __eq__(self, other: Any) -> 'Expression':
        """
        重载等于 (==) 运算符 (self == other)。

        创建一个新的 `Expression` 对象，其 `expr_func` 会在求值时
        比较当前表达式的结果与 `other`（常量、Column或另一个Expression）是否相等。

        Args:
            other (Any): 与此表达式比较的对象。可以是以下类型：
                         - Python 常量 (int, float, str, bool)。
                         - `Column` 对象。
                         - 另一个 `Expression` 对象。

        Returns:
            Expression: 代表等于比较的新 `Expression` 对象。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_str = "=="
        self.logger.debug(f"Expression比较 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str, bool)):
            # 表达式 == 常量
            new_expr_func = lambda row_var: self.expr_func(row_var) == other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            # 表达式 == 列
            new_expr_func = lambda row_var: self.expr_func(row_var) == other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            # 表达式 == 表达式
            new_expr_func = lambda row_var: self.expr_func(row_var) == other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression比较: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __ne__(self, other: Any) -> 'Expression':
        """
        重载不等于 (!=) 运算符 (self != other)。

        Args:
            other (Any): 与此表达式比较的对象 (常量, Column, Expression)。

        Returns:
            Expression: 代表不等于比较的新 `Expression`。
        """
        op_str = "!="
        self.logger.debug(f"Expression比较 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str, bool)):
            new_expr_func = lambda row_var: self.expr_func(row_var) != other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) != other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) != other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression比较: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __gt__(self, other: Any) -> 'Expression':
        """
        重载大于 (>) 运算符 (self > other)。

        Args:
            other (Any): 与此表达式比较的对象 (常量, Column, Expression)。

        Returns:
            Expression: 代表大于比较的新 `Expression`。
        """
        op_str = ">"
        self.logger.debug(f"Expression比较 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str)):
            new_expr_func = lambda row_var: self.expr_func(row_var) > other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) > other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) > other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression比较: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __lt__(self, other: Any) -> 'Expression':
        """
        重载小于 (<) 运算符 (self < other)。

        Args:
            other (Any): 与此表达式比较的对象 (常量, Column, Expression)。

        Returns:
            Expression: 代表小于比较的新 `Expression`。
        """
        op_str = "<"
        self.logger.debug(f"Expression比较 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str)):
            new_expr_func = lambda row_var: self.expr_func(row_var) < other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) < other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) < other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression比较: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __ge__(self, other: Any) -> 'Expression':
        """
        重载大于等于 (>=) 运算符 (self >= other)。

        Args:
            other (Any): 与此表达式比较的对象 (常量, Column, Expression)。

        Returns:
            Expression: 代表大于等于比较的新 `Expression`。
        """
        op_str = ">="
        self.logger.debug(f"Expression比较 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str)):
            new_expr_func = lambda row_var: self.expr_func(row_var) >= other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) >= other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) >= other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression比较: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __le__(self, other: Any) -> 'Expression':
        """
        重载小于等于 (<=) 运算符 (self <= other)。

        Args:
            other (Any): 与此表达式比较的对象 (常量, Column, Expression)。

        Returns:
            Expression: 代表小于等于比较的新 `Expression`。
        """
        op_str = "<="
        self.logger.debug(f"Expression比较 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str)):
            new_expr_func = lambda row_var: self.expr_func(row_var) <= other
            new_description = f"({self.description} {op_str} {other})"
        elif isinstance(other, Column):
            new_expr_func = lambda row_var: self.expr_func(row_var) <= other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
        elif isinstance(other, Expression):
            new_expr_func = lambda row_var: self.expr_func(row_var) <= other(row_var)
            new_description = f"({self.description} {op_str} {other.description})"
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
        else:
            self.logger.warning(f"不支持的Expression比较: {self.description} {op_str} {type(other)}")
            return NotImplemented
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    # --- 逻辑运算符 ---
    def __and__(self, other: Any) -> 'Expression': # Corresponds to `&` operator
        """
        重载按位与 (&) 运算符 (self & other)，用于实现逻辑AND。

        创建一个新的 `Expression` 对象，其 `expr_func` 会在求值时
        对当前表达式的结果与 `other`（常量、Column或另一个Expression）执行逻辑AND操作。

        Args:
            other (Any): 与此表达式进行逻辑AND操作的对象。可以是以下类型：
                         - Python 常量 (int, float, str, bool)，会被解释为布尔值。
                         - `Column` 对象，其值会被解释为布尔表达式。
                         - 另一个 `Expression` 对象，其值会被解释为布尔表达式。
                         - `ColumnConstraint` 对象，其约束会被转换为等效的Z3布尔表达式。

        Returns:
            Expression: 代表逻辑AND操作的新 `Expression` 对象。

        Raises:
            NotImplementedError: 如果 `other` 的类型不受支持。
        """
        op_str = "&"
        self.logger.debug(f"Expression逻辑运算 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column, ColumnConstraint 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str, bool)) or (hasattr(other, 'sort') and other.sort() == BoolSort()):
            # 处理 Python 对象和 Z3 BoolRef 对象
            # 对于 Python 对象，转为布尔值
            # 对于 Z3 BoolRef 对象，直接使用
            if hasattr(other, 'sort') and other.sort() == BoolSort():
                # Z3 BoolRef 对象
                z3_bool_value = other
                
                def and_lambda_func(row_var):
                    return Z3And(self.expr_func(row_var), z3_bool_value)
                
                new_description = f"{self.description} & {other}"
            else:
                # Python 布尔值
                python_bool_value = bool(other)
                
                def and_lambda_func(row_var):
                    # 如果Python bool值是False，短路返回False (Z3 False)
                    if not python_bool_value:
                        return BoolVal(False, ctx=self.solver_wrapper.ctx)
                    # 否则返回左侧表达式的结果 (这是一种短路优化)
                    return self.expr_func(row_var)
                
                new_description = f"{self.description} & {other}"
                
            return Expression(and_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        elif isinstance(other, Column):
            def and_lambda_func(row_var):
                # 使用Z3的And操作符组合两个表达式
                return Z3And(self.expr_func(row_var), other(row_var))
            
            new_description = f"{self.description} & {other.description}"
            
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
                
            return Expression(and_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        elif isinstance(other, Expression):
            def and_lambda_func(row_var):
                # 使用Z3的And操作符组合两个表达式
                return Z3And(self.expr_func(row_var), other.expr_func(row_var))
            
            new_description = f"{self.description} & {other.description}"
            
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
                    
            return Expression(and_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        elif isinstance(other, ColumnConstraint):
            def and_lambda_func(row_var):
                # 对于ColumnConstraint，我们需要使用其as_z3_expr方法，并提供row_var
                return Z3And(self.expr_func(row_var), other.as_z3_expr(row_var_override=row_var))
            
            new_description = f"{self.description} & {other.description}"
            
            if other.column.table_name not in target_tables:
                target_tables.append(other.column.table_name)
                
            return Expression(and_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        else:
            self.logger.warning(f"不支持的Expression逻辑运算: {self.description} {op_str} {type(other)}")
            return NotImplemented
    
    def __or__(self, other: Any) -> 'Expression': # Corresponds to `|` operator
        """
        重载按位或 (|) 运算符 (self | other)，用于实现逻辑OR。

        格式和功能与 `__and__` 类似，但执行的是逻辑OR操作。
        """
        op_str = "|"
        self.logger.debug(f"Expression逻辑运算 ({op_str}): {self.description} {op_str} {getattr(other, 'description', str(other))}")
        from .column import Column, ColumnConstraint 

        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        if isinstance(other, (int, float, str, bool)) or (hasattr(other, 'sort') and other.sort() == BoolSort()):
            # 处理 Python 对象和 Z3 BoolRef 对象
            # 对于 Python 对象，转为布尔值
            # 对于 Z3 BoolRef 对象，直接使用
            if hasattr(other, 'sort') and other.sort() == BoolSort():
                # Z3 BoolRef 对象
                z3_bool_value = other
                
                def or_lambda_func(row_var):
                    return Z3Or(self.expr_func(row_var), z3_bool_value)
                
                new_description = f"{self.description} | {other}"
            else:
                # Python 布尔值
                python_bool_value = bool(other)
                
                def or_lambda_func(row_var):
                    # 如果Python bool值是True，短路返回True (Z3 True)
                    if python_bool_value:
                        return BoolVal(True, ctx=self.solver_wrapper.ctx)
                    # 否则返回左侧表达式的结果
                    return self.expr_func(row_var)
                
                new_description = f"{self.description} | {other}"
                
            return Expression(or_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        elif isinstance(other, Column):
            def or_lambda_func(row_var):
                return Z3Or(self.expr_func(row_var), other(row_var))
                
            new_description = f"{self.description} | {other.description}"
            
            if other.table_name not in target_tables:
                target_tables.append(other.table_name)
                
            return Expression(or_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        elif isinstance(other, Expression):
            def or_lambda_func(row_var):
                return Z3Or(self.expr_func(row_var), other.expr_func(row_var))
                
            new_description = f"{self.description} | {other.description}"
            
            for table_name in other.target_table_names:
                if table_name not in target_tables:
                    target_tables.append(table_name)
                    
            return Expression(or_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        elif isinstance(other, ColumnConstraint):
            def or_lambda_func(row_var):
                return Z3Or(self.expr_func(row_var), other.as_z3_expr(row_var_override=row_var))
                
            new_description = f"{self.description} | {other.description}"
            
            if other.column.table_name not in target_tables:
                target_tables.append(other.column.table_name)
                
            return Expression(or_lambda_func, self.solver_wrapper, new_description, target_table_names=target_tables)
        
        else:
            self.logger.warning(f"不支持的Expression逻辑运算: {self.description} {op_str} {type(other)}")
            return NotImplemented
    
    def __invert__(self) -> 'Expression': # Corresponds to `~` operator (for NOT)
        """
        重载按位取反 (~) 运算符 (在Python中模拟逻辑NOT)。
        创建一个新的 `Expression` 对象，其 `expr_func` 会在求值时
        对当前表达式的结果执行逻辑NOT操作。

        Returns:
            Expression: 代表逻辑NOT操作的新 `Expression` 对象。
        """
        op_str = "NOT"
        self.logger.debug(f"Expression逻辑运算 ({op_str}): {op_str} {self.description}")
        
        # 保持原有的target_table_names
        target_tables = self.target_table_names.copy()
        
        new_expr_func = lambda row_var: Z3Not(self.expr_func(row_var))
        new_description = f"~({self.description})"
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)
    
    def __repr__(self) -> str:
        """返回表达式的字符串表示形式"""
        return f"Expression({self.description})"
    
    # --- 日期相关扩展方法 ---
    def extract_year(self) -> 'Expression':
        """
        从日期表达式中提取年份
        
        Returns:
            Expression: 代表提取年份的表达式
        """
        self.logger.debug(f"日期提取年份: ExtractYear({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._create_extract_year_expression(self.expr_func(row_var)),
            self.solver_wrapper,
            f"ExtractYear({self.description})"
        )
    
    def extract_month(self) -> 'Expression':
        """
        从日期表达式中提取月份
        
        Returns:
            Expression: 代表提取月份的表达式
        """
        self.logger.debug(f"日期提取月份: ExtractMonth({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._create_extract_month_expression(self.expr_func(row_var)),
            self.solver_wrapper,
            f"ExtractMonth({self.description})"
        )
    
    def extract_day(self) -> 'Expression':
        """
        从日期表达式中提取天
        
        Returns:
            Expression: 代表提取天的表达式
        """
        self.logger.debug(f"日期提取天: ExtractDay({self.description})")
        
        return Expression(
            lambda row_var: self.solver_wrapper._create_extract_day_expression(self.expr_func(row_var)),
            self.solver_wrapper,
            f"ExtractDay({self.description})"
        )
    
    # --- 字符串方法扩展 ---
    def contains(self, substring: str) -> 'Expression':
        """
        检查字符串表达式是否包含给定的子串
        
        Args:
            substring (str): 要检查的子串
            
        Returns:
            Expression: 代表字符串包含检查的新 `Expression` 对象
        """
        self.logger.debug(f"Expression.contains: 检查 {self.description} 是否包含 '{substring}'")
        
        # 合并target_table_names
        target_tables = self.target_table_names.copy()
        
        # 使用Z3的Contains函数
        from z3 import Contains
        
        new_expr_func = lambda row_var: Contains(self.expr_func(row_var), substring)
        new_description = f"{self.description} CONTAINS {substring}"
        
        return Expression(new_expr_func, self.solver_wrapper, new_description, target_table_names=target_tables)