# `sql_to_z3` 模块设计与使用详解

## 1. 简介

`sql_to_z3` 模块的核心目标是将SQL查询中的关键概念（如表、列、条件表达式等）转换为[Z3定理证明器](https://github.com/Z3Prover/z3)的逻辑表达式。通过这种转换，我们可以利用Z3的强大功能来：

*   检查数据模型的一致性。
*   分析复杂约束的可满足性。
*   查找满足特定条件的示例数据（模型）。
*   对数据依赖关系进行推理。

### 核心组件

该模块主要由以下几个核心类构成：

1.  **`SqlToZ3`**：作为总体的协调者和Z3求解器的包装器。它负责管理表定义、列对应的Z3函数、用户添加的全局约束以及JOIN操作的条件。
2.  **`Table`**：代表数据库中的一个表。它管理表的结构（列名和类型），并提供插入数据（即为行添加约束）的方法。
3.  **`Column`**：代表表中的一个列。通过重载Python的运算符，`Column`对象可以方便地用于构建比较表达式和算术表达式。
4.  **`ColumnConstraint`**：表示一个列与常量之间的比较（例如 `age > 30`）。这是一个"延迟应用"的约束，只有当它被显式添加到`SqlToZ3`求解器时，或者在更复杂的表达式中被求值时，才会生成具体的Z3约束。
5.  **`Expression`**：代表一个可以被求值为Z3表达式的任意计算。它通常由`Column`对象的运算（如 `salary * 0.1`）或多个条件的逻辑组合（如 `cond1 AND cond2`）产生。

### 基本工作流程

使用`sql_to_z3`模块进行数据建模和分析通常遵循以下步骤：

1.  **初始化**：创建一个 `SqlToZ3` 实例。
    ```python
    from new_model.sql_to_z3 import SqlToZ3, Table, Column, Expression
    from z3 import IntSort, StringSort, BoolSort, RealSort, sat, unsat

    s = SqlToZ3()
    ```
2.  **定义表结构**：使用 `SqlToZ3.create_table()` 方法定义表的名称和列（及其Z3类型）。
    ```python
    users = s.create_table("users", {
        "id": IntSort(), 
        "name": StringSort(), 
        "age": IntSort(),
        "email_verified": BoolSort()
    })
    ```
3.  **插入数据/定义行级约束**：使用 `Table.insert()` 方法向表中添加数据。这实际上是为表中的新行或现有行添加Z3约束。
    ```python
    users.insert({"id": 1, "name": "Alice", "age": 30, "email_verified": True})
    users.insert({"id": 2, "name": "Bob", "age": 25, "email_verified": False})
    ```
4.  **构建条件和表达式**：使用 `Column` 对象及其重载的运算符来构建查询条件或计算表达式。
    ```python
    # 列与常量比较，生成 ColumnConstraint
    age_condition = users.age > 28

    # 列与列进行算术运算，生成 Expression
    age_plus_ten = users.age + 10
    ```
5.  **添加约束到求解器**：使用 `SqlToZ3.add()` 方法将构建的条件（`ColumnConstraint`、`Expression` 或直接的Z3 `BoolRef`）添加到求解器。
    ```python
    s.add(age_condition) 
    ```
6.  **检查可满足性**：调用 `SqlToZ3.check()` 检查当前所有约束是否可满足。
    ```python
    if s.check() == sat:
        print("约束集合可满足")
    else:
        print("约束集合不可满足")
    ```
7.  **获取模型**：如果可满足，调用 `SqlToZ3.get_model()` 获取一个满足所有约束的具体实例（模型）。
    ```python
    model = s.get_model()
    if model:
        print("模型已找到")
    ```
8.  **在模型中求值**：使用 `SqlToZ3.eval()` 方法在获取的模型中对特定的Z3表达式（通常是表中的某个字段在某一行上的值）进行求值。
    ```python
    # 需要一个行变量来指定对哪一行求值
    # 例如，如果我们想找到满足 age_condition 的 Alice 的信息
    # (这需要更复杂的查询构造，见后续示例)
    ```

## 2. SQL 到 Z3 的数学建模原理

将SQL查询和数据模型转换为Z3可以理解的约束，是 `sql_to_z3` 模块的核心功能。这个转换过程依赖于一套数学建模方法，将关系型数据库中的概念映射到Z3的逻辑构造中。下面我们详细拆解这一过程。

### 2.1. 核心概念的数学表示

1.  **表 (Table) 与 行 (Row)**:
    *   数学上，一个关系数据库表 $T$ 可以被视为一个模式，定义了一组列 $\{C_1, C_2, \ldots, C_k\}$ 及其对应的数据类型 $\{D_1, D_2, \ldots, D_k\}$。
    *   表中的每一行（或记录）在逻辑上可以通过一个唯一的非负整数索引 $i \in \mathbb{N}_0$ 来标识。
    *   在Z3中，行索引通常由一个 `IntSort` 类型的变量 $r$ 表示，即 $r \in \text{IntSort}$。

2.  **列 (Column) 作为函数**:
    *   表 $T$ 中的每一列 $C_j$（数据类型为 $D_j$）被建模为一个Z3中的**一元函数** $f_{T.C_j}$。
    *   此函数的定义域是行索引的集合（由 `IntSort` 表示），值域是列 $C_j$ 的数据类型 $D_j$ 对应的Z3排序 (Sort)。
        $$f_{T.C_j} : \text{IntSort} \rightarrow \text{Sort}(D_j)$$
        其中 $\text{Sort}(D_j)$ 是SQL类型 $D_j$ 对应的Z3排序，例如：
        *   $\text{Sort}(\text{INT}) = \text{IntSort}$
        *   $\text{Sort}(\text{STRING}) = \text{StringSort}$
        *   $\text{Sort}(\text{BOOLEAN}) = \text{BoolSort}$
        *   $\text{Sort}(\text{REAL}) = \text{RealSort}$
        *   $\text{Sort}(\text{DATE}) = \text{IntSort}$ （日期以整数形式表示，如20230101）
    *   **示例**: 对于表 `Employees` (简记为 $E$) 中的列 `Salary (REAL)`，其对应的Z3函数为 $f_{E.\text{Salary}} : \text{IntSort} \rightarrow \text{RealSort}$。给定一个行索引变量 $r_E$，表达式 $f_{E.\text{Salary}}(r_E)$ 代表了 `Employees` 表中由 $r_E$ 指向的那一行的 `Salary` 值。

3.  **表的大小 (Number of Rows)**:
    *   每个表 $T$ 维护一个行ID列表 $rows_T$，存储了所有已分配的有效行ID。
        $$rows_T = [id_1, id_2, \ldots, id_m]$$
    *   任何代表表 $T$ 中某一具体行的行索引变量 $r_T$ 都必须满足以下约束，以确保其有效性：
        $$r_T \in rows_T$$
    *   在Z3表达式中，这通常表示为：
        $$\text{Or}([r_T = id_1, r_T = id_2, \ldots, r_T = id_m])$$

4.  **数据实例 (Data Insertion)**:
    *   一条SQL `INSERT` 语句，例如 `INSERT INTO T (C_1, C_2, ..., C_k) VALUES (v_1, v_2, ..., v_k);`，在Z3中通过添加一组等式约束来实现。
    *   假设这条记录是插入到表 $T$ 中的第 $i$ 条记录（$i$ 是一个通过 `_get_next_row_id()` 生成的行ID）。
    *   其对应的Z3约束集合为：
        $$\{ f_{T.C_j}(i) = \text{Z3Val}(v_j) \mid j = 1, \ldots, k \}$$
        其中 $\text{Z3Val}(v_j)$ 是将SQL常量值 $v_j$ 转换为其对应的Z3常量表示（例如，`IntVal(10)`，`StringVal("Alice")`，`RealVal(60000.0)`）。对于日期类型，字符串形式如"2023-01-01"会被转换为整数形式，如`IntVal(20230101)`。
    *   同时，行ID被添加到表的行列表中：
        $$rows_T \gets rows_T \cup \{i\}$$

### 2.2. SQL操作到Z3约束的转换

1.  **`WHERE` 子句 (条件选择)**:
    *   考虑一个SQL查询 `SELECT ... FROM T WHERE P(C_1, C_2, ..., C_k);`，其中 $P$ 是一个作用于列值的逻辑谓词。
    *   该谓词 $P$ 会被转换为一个Z3布尔表达式 $P_{Z3}$。
    *   我们寻找的是一个行索引 $r_T \in \text{IntSort}$ (通常是一个自由变量)，它满足：
        1.  行有效性: $0 \le r_T < N_T$
        2.  谓词条件: $P_{Z3}(f_{T.C_1}(r_T), f_{T.C_2}(r_T), \ldots, f_{T.C_k}(r_T))$
    *   **示例**: `SELECT Name FROM Employees E WHERE E.Salary > 50000.0 AND E.ID < 10;`
        设 $r_E$ 是 `Employees` 表的一个行索引变量。对应的Z3约束为：
        $$(0 \le r_E < N_E) \land (f_{E.\text{Salary}}(r_E) > \text{RealVal}(50000.0)) \land (f_{E.\text{ID}}(r_E) < \text{IntVal}(10))$$

2.  **`JOIN` 操作 (表连接)**:
    *   考虑一个SQL `JOIN` 操作，例如 `SELECT ... FROM T_1 JOIN T_2 ON T_1.C_A = T_2.C_B;`
    *   我们需要引入两个独立的行索引变量：$r_1 \in \text{IntSort}$ 用于表 $T_1$，以及 $r_2 \in \text{IntSort}$ 用于表 $T_2$。
    *   相应的表大小变量为 $N_{T_1}$ 和 $N_{T_2}$。
    *   描述JOIN的核心Z3约束集合包括：
        1.  $T_1$ 行有效性: $0 \le r_1 < N_{T_1}$
        2.  $T_2$ 行有效性: $0 \le r_2 < N_{T_2}$
        3.  JOIN 条件: $f_{T_1.C_A}(r_1) = f_{T_2.C_B}(r_2)$
    *   如果JOIN查询还包含额外的 `WHERE` 条件，例如 `WHERE Q(T_1.C_X, T_2.C_Y)`，则会添加相应的Z3约束 $Q_{Z3}(f_{T_1.C_X}(r_1), f_{T_2.C_Y}(r_2))$。

### 2.3. 总结与 `sql_to_z3` 的角色

`sql_to_z3` 模块通过其类 (`SqlToZ3`, `Table`, `Column`, `ColumnConstraint`, `Expression`) 封装了上述数学建模的复杂性。

*   `Table.create_table()` 会自动为表的每一列创建Z3函数声明 (存储在 `SqlToZ3.column_functions`)。
*   `Table.insert()` 会将数据转换为对特定行ID的列函数应用的Z3约束，并管理表的行数变量 (`Table.rows_z3_var`)。
*   `Column` 对象的运算符重载 (如 `>`, `==`, `+`) 使得用户可以用类似SQL的语法构建 `ColumnConstraint` 或 `Expression` 对象。
*   当这些对象被 `SqlToZ3.add()` 方法处理时，它们会根据上下文（单表约束、JOIN约束等）生成恰当的Z3断言，包括创建自由行变量、添加行有效性约束，并将核心逻辑转换为Z3表达式。

通过这种方式，用户可以专注于描述其数据模型和查询逻辑，而将SQL结构到Z3逻辑约束的底层转换细节交给 `sql_to_z3` 模块处理。

## 3. 核心类详解

### 3.1. `SqlToZ3`

此类是整个转换框架的核心，扮演着Z3求解器的接口和状态管理器的角色。

*   **目的**：
    *   封装Z3求解器实例 (`z3.Solver`)。
    *   维护已定义的表 (`Table` 对象) 的注册表。
    *   存储每个表列对应的Z3函数（例如，`users.id` 列可能对应一个名为 `users.id_func` 的Z3函数，该函数接受一个`IntSort`的行ID，返回该列的Z3类型的值）。
    *   管理用户添加的全局约束列表。
    *   记录和处理多表JOIN操作产生的行变量对和约束。
    *   提供缓存机制 (`_constraint_cache`) 避免重复添加相同的约束。

*   **关键属性**：
    *   `self.solver: TypeAwareEnhancedSolver`: Z3求解器实例，使用自定义的增强型求解器。
    *   `self.tables: Dict[str, Table]`: 表名到 `Table` 实例的映射。
    *   `self.column_functions: Dict[str, z3.FuncDeclRef]`: 完全限定的列名（如 `tablename_colname`）到其对应Z3函数的映射。这些函数通常接受一个`IntSort`的行ID，返回该列的Z3类型的值。
    *   `self.constraints: List[Any]`: 已添加到求解器的原始约束列表，用于调试和追踪。
    *   `self.join_conditions: List[Dict[str, Any]]`: 存储JOIN操作产生的信息，每项包含 {'v1': 第一个变量, 't1': 第一个表名, 'v2': 第二个变量, 't2': 第二个表名}。
    *   `self._constraint_cache: List[z3.ExprRef]`: 用于缓存已转换为Z3表达式的约束，避免重复添加相同约束。
    *   `self.logger`: 日志记录器实例，从 `lineage_core.logger` 模块获取。
    *   `self._var_suffix_counter`: 用于生成唯一的Z3变量名后缀。
    *   `self.date_type`: DateType实例，用于处理日期类型。

*   **关键方法**：
    *   `__init__(self)`: 初始化求解器、日志记录器和内部状态。
    *   `create_table(self, table_name: str, columns_with_types: Dict[str, z3.SortRef]) -> Table`:
        *   创建一个新的 `Table` 对象并注册。
        *   为表中的每一列创建一个Z3函数（例如 `FuncDeclRef(f'{table_name}.{col_name}', IntSort(), col_type)`），并存储在 `self.column_functions` 中。
        *   支持日期类型列的创建，可以使用`DateType()`实例指定日期类型。
        *   **SQL 示例**: `CREATE TABLE products (pid INT, name STRING, price REAL, create_date DATE);`
        *   **转换**:
            ```python
            products_table = s.create_table("products", {
                "pid": IntSort(), 
                "name": StringSort(), 
                "price": RealSort(),
                "create_date": DateType()  # 使用DateType实例表示日期类型
            })
            # 内部会创建 s.column_functions['products.pid'] 等 Z3 函数
            ```
    *   `add(self, constraint: Any)`: 将约束添加到Z3求解器。
        *   **处理 `ColumnConstraint`**: 调用其 `apply()` 方法生成Z3表达式并添加。根据约束列的表名，可能会寻找已存在的JOIN行变量来使用。
        *   **处理 `Expression`**: 调用其 `as_z3_expr()` 方法（通常需要一个代表行的Z3变量，`add`方法会为此创建一个自由变量），然后添加结果。
        *   **处理4元素元组 (JOINs)**: 如果元组包含4个元素 `(var1, table1_name, var2, table2_name)`，解析为一个JOIN条件字典并记录到 `self.join_conditions`。
        *   **处理直接的Z3 `BoolRef`**: 直接添加到求解器。
        *   **处理Python `bool`**: 转换为Z3的 `BoolVal` 后添加。

        **各类型约束的示例代码**：

        ```python
        # 初始化solver和表
        s = SqlToZ3()
        users = s.create_table("users", {
            "id": IntSort(),
            "name": StringSort(),
            "age": IntSort(),
            "is_active": BoolSort()
        })
        
        # 1. 添加 ColumnConstraint
        # 这会自动为"users"表创建一个自由行变量，并添加该行的age>30的约束
        age_constraint = users.age > 30
        s.add(age_constraint)
        
        # 2. 添加 Expression
        # 先创建一个表达式：年龄加10后大于40
        age_plus_10 = users.age + 10
        age_expr = age_plus_10 > 40
        s.add(age_expr)
        
        # 3. 添加JOIN条件（元组）
        orders = s.create_table("orders", {
            "id": IntSort(),
            "user_id": IntSort(),
            "amount": RealSort()
        })
        # 通过相等比较创建JOIN，返回一个包含两个表行变量的元组
        join_condition = (users.id == orders.user_id)
        s.add(join_condition)
        
        # 获取JOIN行变量并添加条件到JOIN后的结果上
        user_row, order_row = join_condition
        s.add(users.age.as_z3_expr(user_row) > 25)  # 用户年龄>25
        s.add(orders.amount.as_z3_expr(order_row) > 100)  # 订单金额>100
        
        # 4. 添加直接的Z3 BoolRef
        from z3 import And, Or, Not, Bool
        premium_user = Bool("premium_user")
        # 直接创建Z3布尔表达式并添加
        direct_constraint = And(premium_user, orders.amount(order_row) > 200)
        s.add(direct_constraint)
        
        # 5. 添加Python bool值
        # 这在条件逻辑中很有用
        if some_condition:
            s.add(True)  # 转换为z3.BoolVal(True)
        else:
            s.add(False)  # 转换为z3.BoolVal(False)，会导致不可满足
        ```
    *   `_merge_join_conditions(self)`: （内部方法）处理由多次两两JOIN产生的传递性约束。例如，如果 `T1.a == T2.b` 且 `T2.b == T3.c`，此方法会尝试添加 `T1.a_row_var == T2.b_row_var_for_T1` 和 `T2.b_row_var_for_T3 == T3.c_row_var` 这样的等价关系，确保同一列在不同JOIN上下文中的行变量能够正确关联。
    *   `_convert_constraint(self, constraint: Any, row_var: Optional[z3.ExprRef] = None) -> z3.ExprRef`: （内部方法）将多种类型的输入（Python bool, `DateExpressionBoolRef`, `Expression`, `ColumnConstraint`）转换为可添加到求解器的Z3 `ExprRef`。
    *   `check(self) -> z3.CheckSatResult`: 调用 `self.solver.check()`，但在之前会调用 `_merge_join_conditions` 来确保JOIN条件被正确处理。
    *   `get_model(self) -> Optional[z3.ModelRef]`: 在 `check() == sat` 后调用 `self.solver.model()`。
    *   `eval(self, expression: z3.ExprRef, model: Optional[z3.ModelRef] = None, row_var: Optional[z3.ExprRef] = None) -> Any`:
        *   在给定的模型中对Z3表达式求值。
        *   如果 `expression` 是一个列的Z3函数（`FuncDeclRef`），并且提供了 `row_var`，它会计算 `function(row_var)`。
    *   逻辑操作辅助函数 (`Not(self, a)`, `And(self, *args)`, `Or(self, *args)`, `Implies(self, a, b)`, `Xor(self, a, b)`, `If(self, c, t, e)`):
        *   这些是对Z3逻辑操作的封装，允许输入为 `BoolRef`, `Expression`, `ColumnConstraint`, 或 Python `bool`。
        *   它们会智能地将输入转换为Z3表达式，并返回组合后的结果（可能是直接的 `BoolRef` 或新的 `Expression`）。
        *   例如, `s.And(col.age > 18, col.status == "active")`。
    *   `_get_unique_var_suffix(self) -> str`: 返回一个唯一的数字后缀，用于创建不冲突的Z3变量名。

### 3.2. `Table`

此类代表数据库中的一个表。

*   **目的**：
    *   存储表的名称和列定义（名称及Z3类型）。
    *   提供接口以将数据行（作为约束）添加到表中。
    *   管理表内唯一的行ID（逻辑上的）。
    *   通过 `__getattr__` 方便地访问其列 (`Column` 对象)。

*   **关键属性**：
    *   `self._name: str`: 表名。
    *   `self.solver_wrapper: SqlToZ3`: 对 `SqlToZ3` 实例的引用。
    *   `self.columns_with_types: Dict[str, z3.SortRef]`: 列名到Z3类型的映射。
    *   `self.logger`: 日志记录器。
    *   `self._next_row_id: int`: 用于分配下一个可用行ID的计数器 (从1开始)。
    *   `self.rows: List[int]`: 存储此表中已分配的行ID列表。

*   **关键方法**：
    *   `__init__(self, name: str, solver_wrapper: SqlToZ3, columns_with_types: Dict[str, z3.SortRef])`: 初始化表。
    *   `_get_next_row_id(self) -> int`: 获取并递增下一个行ID。
    *   `insert(self, values_dict: Dict[str, Any], for_all_rows: bool = False) -> Union[int, List[int]]`:
        *   这是核心的数据添加方法。它将 `values_dict` 中的每个条目转换为对表中特定行（或多行）的约束。
        *   `values_dict` 的值可以是：
            *   **Python常量**: 如 `{"age": 30}`。会生成 `table.age(row_id) == 30`。
            *   **`Column` 对象**: 通常用于引用其他表或其他行的列，或定义列间关系。例如 `{"manager_id": other_table.id}`。
            *   **`(source_table_name, source_column_name)` 元组**: 与 `Column` 对象类似，用于跨表引用。
            *   **`Expression` 对象**: 如 `{"bonus": self.salary * 0.1}`。会生成 `table.bonus(row_id) == (table.salary(row_id) * 0.1)`。
        *   **`for_all_rows=False` (默认)**:
            *   分配一个新的行ID `r_id = self._get_next_row_id()`。
            *   添加该行ID到 `self.rows` 列表。
            *   调用 `_add_constraints_for_row(r_id, value_spec, ...)` 为此行添加约束。
            *   **返回值**: 返回新插入行的ID（整数）。
        *   **`for_all_rows=True`**:
            *   这通常用于模拟SQL的 `INSERT INTO ... SELECT ... FROM` 操作，为源表中的每一行创建目标表中的一行。
            *   它会识别 `values_dict` 中引用的第一个源表，然后为该表的每一行在当前表中创建一个对应的新行。
            *   调用 `_add_constraints_for_row` 来设置各列的值，并建立与源表行的关联。
            *   **返回值**: 返回所有新插入行ID的列表。
        *   **SQL 示例 (单行插入)**: `INSERT INTO users (id, name, age) VALUES (1, 'Alice', 30);`
        *   **转换**:
            ```python
            row_id = users.insert({"id": 1, "name": "Alice", "age": 30})
            # 内部逻辑 (简化):
            # r_id = users._get_next_row_id() # 例如返回 1
            # users.rows.append(r_id) # 添加行ID到rows列表
            # 为各列添加约束 users.id(r_id) == 1 等
            # 返回 r_id
            ```
    *   `_add_constraints_for_row(self, current_row_var: z3.ExprRef, value_spec: Any, for_all_rows_context: bool, source_row_var_map: Dict[str, z3.ExprRef])`: （内部辅助方法）为`current_row_var`指定的行，根据`value_spec`的类型（常量, `Column`, `Expression`, 元组）添加Z3约束。
        *   如果 `value_spec` 是 `Column` 或元组 (引用其他表 `other_table`) 并且当前不是 `for_all_rows` 的迭代上下文（或者即使是，但该`other_table`不是迭代的主表），它会创建一个新的自由Z3行变量 `source_row_var` for `other_table`，添加行有效性约束 (`0 <= source_row_var < other_table.rows_z3_var`)，并将其用于从`other_table`取值。
    *   `__getattr__(self, name: str) -> Column`: 允许通过 `table_instance.column_name` 的语法获取一个代表该列的 `Column` 对象。
    *   `__repr__(self) -> str`: 返回表的可读表示，如 `<Table: users (rows_z3_var: users_rows_count_0)>`。

### 3.3. `Column`

此类代表数据库表中的一个列。它是构建条件和表达式的主要接口。

*   **目的**：
    *   封装列的元数据（表名、列名、对应的Z3函数）。
    *   通过重载Python的比较运算符 (`==`, `>`, `<`, `!=`, etc.) 和算术运算符 (`+`, `-`, `*`, `/`)，使得用户可以用自然的语法来创建约束和表达式。

*   **关键属性**：
    *   `self.table_name: str`: 列所属的表名。
    *   `self.column_name: str`: 列名。
    *   `self.solver_wrapper: SqlToZ3`: 对 `SqlToZ3` 实例的引用。
    *   `self.function: z3.FuncDeclRef`: 该列对应的Z3函数（从 `solver_wrapper.column_functions` 获取）。
    *   `self.description: str`: 列的描述，如 `users.age`。

*   **关键方法**：
    *   `__call__(self, row_var: z3.ExprRef) -> z3.ExprRef`: 核心方法。当 `Column` 对象被调用时（如 `users.age(row_variable)`），它返回该列在由`row_var`指定的行上的Z3值，即 `self.function(row_var)`。
    *   **比较运算符 (`__eq__`, `__ne__`, `__gt__`, `__lt__`, `__ge__`, `__le__`)**:
        *   **`Column == Constant`** (e.g., `users.age == 30`):
            *   返回一个 `ColumnConstraint` 对象，封装了这个比较。
            *   **SQL**: `WHERE age = 30`
            *   **转换**: `constraint = users.age == 30` (得到 `ColumnConstraint`)
                         `s.add(constraint)`
        *   **`Column == Column`** (e.g., `table1.col_a == table2.col_b`):
            *   这是定义 **JOIN 条件**的关键。
            *   内部调用 `_create_join_vars_and_row_constraints` 创建两个新的**自由Z3行变量** `var1` (for `self.table_name`) 和 `var2` (for `other.table_name`)。
            *   为这两个行变量添加行有效性约束。
            *   添加核心的JOIN约束到求解器： `s.add(self(var1) == other(var2))` (即 `self.function(var1) == other.function(var2)`)。
            *   返回一个4元素元组 `(var1, table1_name, var2, table2_name)`。这个元组会被 `SqlToZ3.add()` 捕获并转换为字典存入 `join_conditions`，用于后续可能的传递性JOIN合并。
            *   **SQL**: `FROM orders JOIN customers ON orders.customer_id = customers.id`
            *   **转换**:
                ```python
                join_condition_tuple = (orders.customer_id == customers.id)
                s.add(join_condition_tuple) 
                # 内部 __eq__ 逻辑:
                #   var_orders = Int("orders_row_...")
                #   var_customers = Int("customers_row_...")
                #   # 添加行有效性约束...
                #   s.add(orders.customer_id.function(var_orders) == customers.id.function(var_customers))
                #   return (var_orders, "orders", var_customers, "customers")
                ```
        *   **`Column == Expression`** (e.g., `employees.salary == (employees.bonus * Decimal('0.5'))`):
            *   返回一个新的 `Expression` 对象。其内部的 `expr_func` 会是类似 `lambda r: self(r) == other.as_z3_expr(r)` 的形式。
            *   **SQL**: `WHERE salary = base_salary + bonus_amount` (假设 `base_salary + bonus_amount` 是一个表达式)
            *   **转换**: `expr = employees.base_salary + employees.bonus_amount` (这是一个`Expression`)
                         `condition = employees.salary == expr` (这也是一个`Expression`)
                         `s.add(condition)`
    *   **算术运算符 (`__add__`, `__sub__`, `__mul__`, `__truediv__`) 和反向版本 (`__radd__`, etc.)**:
        *   当 `Column` 对象与常量、另一个 `Column` 或一个 `Expression` 进行算术运算时，它们总是返回一个新的 `Expression` 对象。
        *   例如, `users.age + 10` 返回一个 `Expression`，其 `expr_func` 是 `lambda r: users.age(r) + 10`。
        *   `items.price * items.quantity` 返回一个 `Expression`，其 `expr_func` 是 `lambda r: items.price(r) * items.quantity(r)`。
        *   **SQL**: `SELECT salary * 1.1 AS increased_salary FROM employees` (表达式部分)
        *   **转换**: `increased_salary_expr = employees.salary * 1.1` (返回 `Expression`)

### 3.4. `ColumnConstraint`

此类表示列与常量之间的比较，例如 `column == value`。它是一个"待应用"的或"上下文相关"的约束。

*   **目的**：
    *   封装一个简单的列-常量比较，而不立即将其转换为具体的Z3断言。
    *   允许这种比较在不同的上下文中被解释或应用（例如，作为 `Expression` 的一部分，或直接添加到求解器时针对特定行或自由行）。

*   **关键属性**：
    *   `self.column: Column`: 被比较的 `Column` 对象。
    *   `self.operator: Callable`: 比较运算符函数 (e.g., `operator.eq`, `operator.gt`)。
    *   `self.value: Any`: 与列进行比较的常量值。
    *   `self.solver_wrapper: SqlToZ3`: 引用。
    *   `self.description: str`: 如 `(users.age > 30)`。
    *   `self.row_var: Optional[z3.ExprRef]`: (高级) 如果此约束已明确绑定到某个Z3行变量，则存储于此。
    *   `self.z3_constraint: Optional[z3.BoolRef]`: 缓存的、已生成的Z3布尔表达式。
    *   `self.applied_to_solver: bool`: 标记此约束是否已通过 `apply()` 添加到求解器的主约束列表。

*   **关键方法**：
    *   `apply(self, join_var: Optional[z3.ExprRef] = None, force_reapply: bool = False)`:
        *   核心方法，将此列约束转换为实际的Z3断言并添加到求解器。
        *   **行变量处理**:
            *   如果提供了 `join_var` (通常来自 `SqlToZ3.add` 的JOIN上下文)，则使用它作为行变量。
            *   否则，如果 `self.row_var` 已设置，则使用它。
            *   否则（最常见的情况当 `s.add(column_constraint_obj)` 被调用时），创建一个新的**自由Z3行变量** `temp_row_var` for `self.column.table_name`。
            *   为这个 `temp_row_var` 添加行有效性约束: `s.add(z3.And(0 <= temp_row_var, temp_row_var < table.rows_z3_var))`。
        *   使用选定的行变量，通过 `_build_z3_expr_with_var(row_var_to_use)` 生成Z3比较表达式。
        *   如果 `force_reapply` 为 `True` 或约束尚未应用，则将Z3表达式添加到求解器并标记为已应用。
        *   **SQL示例 (通过 `s.add` 应用)**: `WHERE age > 25`
        *   **转换**:
            ```python
            cc = users.age > 25  # ColumnConstraint
            s.add(cc) 
            # s.add(ColumnConstraint) 内部会调用 cc.apply()
            # 在 apply() 内部 (简化):
            #   row_v = Int("users_row_...") # 创建自由行变量
            #   table = s.tables["users"]
            #   s.add(And(0 <= row_v, row_v < table.rows_z3_var)) # 行有效性
            #   z3_expr = cc._build_z3_expr_with_var(row_v) # 即 users.age.function(row_v) > 25
            #   s.add(z3_expr)
            #   cc.z3_constraint = z3_expr
            #   cc.applied_to_solver = True
            ```
    *   `as_z3_expr(self, row_var_override: Optional[z3.ExprRef] = None) -> z3.BoolRef`:
        *   返回此约束的Z3布尔表达式。
        *   如果提供了 `row_var_override`：使用它通过 `_build_z3_expr_with_var(row_var_override)` 临时构建Z3表达式。这主要用于 `Expression` 内部的逻辑组合，其中 `Expression` 的 `expr_func` 提供了行变量上下文。**此路径不会修改实例状态或添加到求解器。**
        *   如果没有 `row_var_override`：如果约束尚未生成Z3表达式 (`self.z3_constraint` is None)，则调用 `self.apply()` (这将使用默认的自由行变量逻辑并添加到求解器)。然后返回 `self.z3_constraint`。
    *   `_build_z3_expr_with_var(self, row_var: z3.ExprRef) -> z3.BoolRef`: (内部辅助方法) 使用给定的 `row_var` 生成Z3比较表达式，例如 `self.operator(self.column(row_var), converted_value)`。
    *   `__and__(self, other)`, `__or__(self, other)` (与其他 `ColumnConstraint` 或 `Expression` 或 `bool`):
        *   这些逻辑运算符返回一个新的 `Expression` 对象。
        *   如果 `other` 也是 `ColumnConstraint` (且来自同一个表，这是一个重要的隐含假设，否则语义可能混乱)，返回的 `Expression` 的 `expr_func` 会是类似 `lambda r: z3.And(self._build_z3_expr_with_var(r), other._build_z3_expr_with_var(r))`。这确保两个原始的 `ColumnConstraint` 都在传递给 `expr_func` 的同一个 `row_var` 上下文中求值。
        *   如果 `other` 是 `Expression`，则是 `lambda r: z3.And(self._build_z3_expr_with_var(r), other(r))`。
        *   **SQL**: `WHERE age > 20 AND city = 'NY'`
        *   **转换**:
            ```python
            constraint1 = users.age > 20  # ColumnConstraint
            constraint2 = users.city == 'NY' # ColumnConstraint
            
            # (constraint1 & constraint2) 返回一个 Expression
            combined_expr = constraint1 & constraint2 
            s.add(combined_expr)
            
            # combined_expr.expr_func (简化) 约等于:
            # lambda r_var: And(
            #     users.age.function(r_var) > 20,  # from constraint1.as_z3_expr(r_var)
            #     users.city.function(r_var) == "NY" # from constraint2.as_z3_expr(r_var)
            # )
            # s.add(Expression) 会为这个 combined_expr 创建一个自由行变量并应用。
            ```
    *   `__bool__(self)`: 总是返回 `True`，以允许 `if column_constraint_obj:` 这样的检查通过。它不表示约束的逻辑真值。

### 3.5. `Expression`

此类代表一个可以被求值为Z3表达式的任意计算，通常由列操作或逻辑组合产生。

*   **目的**：
    *   以统一的方式表示复杂的计算逻辑，这些逻辑最终会在特定的行上下文中被转换为Z3表达式。
    *   支持通过重载运算符进行链式操作，构建复杂的表达式树。

*   **关键属性**：
    *   `self.expr_func: Callable[[z3.ExprRef], z3.ExprRef]`: 这是核心。一个Python可调用对象（通常是lambda函数），它接受一个Z3行变量 (`z3.ExprRef`) 作为输入，并返回一个代表该表达式在该行上求值的Z3表达式 (`z3.ExprRef`)。
    *   `self.solver_wrapper: SqlToZ3`: 引用。
    *   `self.description: str`: 表达式的描述，用于调试，例如 `((users.age + 10) > 40)`。
    *   `self.logger`: 日志记录器。

*   **关键方法**：
    *   `__call__(self, row_var: z3.ExprRef) -> z3.ExprRef`: 执行 `self.expr_func(row_var)`，即在给定的 `row_var` 上下文中计算此表达式。
    *   `as_z3_expr(self, row_var: Optional[z3.ExprRef] = None) -> z3.ExprRef`:
        *   返回此表达式的Z3表示。
        *   如果提供了 `row_var`，则直接调用 `self(row_var)`。
        *   如果未提供 `row_var`：**这是一种不推荐的用法**，因为表达式的上下文通常应由外部调用者（如`SqlToZ3.add`或另一个`Expression`的`expr_func`)明确指定。在这种情况下，它会创建一个临时的、唯一的自由Z3行变量，并调用 `self(temp_row_var)`，同时发出警告。它**不会**为这个临时变量添加行有效性约束，因为`Expression`本身不直接绑定到特定表，这个责任在更高层。
    *   **算术、比较、逻辑运算符重载 (`__add__`, `__eq__`, `__and__`, `__invert__` (for NOT), etc.)**:
        *   当 `Expression` 对象与常量、`Column` 对象或其他 `Expression` 对象进行运算时，它们总是返回一个**新的 `Expression` 对象**。
        *   这个新的 `Expression` 的 `expr_func` 内部会包含相应的Z3操作，并确保操作数在传递给它的同一个 `row_var` 上下文中被正确求值。
            *   `expr1 + const` -> `Expression(lambda r: expr1(r) + const, ...)`
            *   `expr1 + column2` -> `Expression(lambda r: expr1(r) + column2(r), ...)`
            *   `expr1 + expr2` -> `Expression(lambda r: expr1(r) + expr2(r), ...)`
            *   `expr1 == const` -> `Expression(lambda r: expr1(r) == const, ...)`
            *   `~expr1` (NOT) -> `Expression(lambda r: z3.Not(expr1(r)), ...)`
            *   `expr1 & column_constraint2` (AND) -> `Expression(lambda r: z3.And(expr1(r), column_constraint2.as_z3_expr(r)), ...)`
        *   **SQL**: `WHERE (salary + bonus) * 1.1 > 50000 AND is_manager`
        *   **转换**:
            ```python
            expr_sum = employees.salary + employees.bonus  # Expression
            expr_total_comp = expr_sum * 1.1               # Expression
            cond_amount = expr_total_comp > 50000          # Expression (boolean)
            
            # employees.is_manager 是 Column, 假设它是 BoolSort
            # 当 Expression 与 Column (boolean) 进行逻辑运算时:
            # cond_manager = employees.is_manager 
            # (This might be better if employees.is_manager itself becomes an Expression via a helper,
            # or if Expression's __and__ can directly take a boolean Column and evaluate it with row_var)
            # For now, let's assume is_manager is already an Expression or ColumnConstraint
            # If it's a ColumnConstraint like `employees.is_manager == True`:
            cond_manager_cc = employees.is_manager == True # ColumnConstraint
            
            final_condition = cond_amount & cond_manager_cc # Expression & ColumnConstraint -> Expression
            s.add(final_condition)
            
            # final_condition.expr_func (简化) 约等于:
            # lambda r: And(
            #     ((employees.salary(r) + employees.bonus(r)) * 1.1) > 50000,
            #     employees.is_manager.function(r) == True # from cond_manager_cc.as_z3_expr(r)
            # )
            ```
    *   `__repr__(self) -> str`: 返回表达式的可读描述。

## 4. SQL 到 Z3 转换示例

### 4.1. 简单查询 (单表，WHERE条件)

*   **SQL**:
    ```sql
    CREATE TABLE employees (id INT, name STRING, department STRING, salary INT, active BOOL, hire_date DATE);
    INSERT INTO employees VALUES (1, 'Alice', 'HR', 60000, True, '2020-01-15');
    INSERT INTO employees VALUES (2, 'Bob', 'Engineering', 80000, True, '2018-07-01');
    INSERT INTO employees VALUES (3, 'Charlie', 'HR', 70000, False, '2022-03-10');

    -- 我们想找到满足以下条件的员工:
    -- 部门是 'HR' 并且 工资大于 65000 并且 是活跃员工 并且 入职日期在2019年之后
    -- SELECT name, salary FROM employees WHERE department = 'HR' AND salary > 65000 AND active = True AND hire_date > '2019-01-01';
    ```

*   **Python / Z3 转换**:
    ```python
    from new_model.sql_to_z3 import SqlToZ3, DateType
    from z3 import IntSort, StringSort, BoolSort, RealSort, sat, unsat

    s = SqlToZ3()
    employees = s.create_table("employees", {
        "id": IntSort(), "name": StringSort(), 
        "department": StringSort(), "salary": IntSort(),
        "active": BoolSort(), "hire_date": DateType()  # 使用DateType表示日期类型
    })

    employees.insert({"id": 1, "name": "Alice", "department": "HR", "salary": 60000, "active": True, "hire_date": "2020-01-15"})
    employees.insert({"id": 2, "name": "Bob", "department": "Engineering", "salary": 80000, "active": True, "hire_date": "2018-07-01"})
    employees.insert({"id": 3, "name": "Charlie", "department": "HR", "salary": 70000, "active": False, "hire_date": "2022-03-10"})
    
    # 构建WHERE条件
    cond_dept = (employees.department == "HR")          # ColumnConstraint
    cond_salary = (employees.salary > 65000)            # ColumnConstraint
    cond_active = (employees.active == True)            # ColumnConstraint
    cond_hire_date = (employees.hire_date > "2019-01-01")  # 日期类型可以直接使用字符串进行比较

    # 组合条件
    final_condition = s.And(cond_dept, cond_salary, cond_active, cond_hire_date)

    # 添加组合条件到求解器
    s.add(final_condition)

    # 检查是否有满足这些条件的员工
    if s.check() == sat:
        print("存在满足条件的员工。")
        model = s.get_model()
        
        # 声明一个特定的行变量，它必须满足条件
        result_row_var = Int("found_employee_row")
        s.add(And(0 <= result_row_var, result_row_var < len(employees.rows))) # 确保它在有效行ID内
        
        # 断言这一行 (result_row_var) 必须满足 final_condition
        s.add(final_condition.as_z3_expr(result_row_var))

        # 再次检查，现在我们试图具体化一个满足条件的行
        if s.check() == sat:
            model = s.get_model() # Get updated model
            print("找到一个具体满足条件的员工:")
            name_val = s.eval(employees.name(result_row_var), model)
            salary_val = s.eval(employees.salary(result_row_var), model)
            dept_val = s.eval(employees.department(result_row_var), model)
            active_val = s.eval(employees.active(result_row_var), model)
            hire_date_val = s.eval(employees.hire_date(result_row_var), model)
            
            # 注意：日期值在模型中是整数形式，可能需要转换回日期字符串形式
            hire_date_str = int_to_date(hire_date_val)  # 假设有一个函数将整数转换为日期字符串
            
            print(f"  Name: {name_val}, Salary: {salary_val}, Dept: {dept_val}, Active: {active_val}, Hire Date: {hire_date_str}")
            # 预期输出: Charlie, 70000, HR, False, 2022-03-10
    else:
        print("没有找到满足所有条件的员工。")
    ```

### 4.4. 日期类型操作

*   **SQL**:
    ```sql
    CREATE TABLE events (event_id INT, event_name STRING, event_date DATE, is_active BOOL);
    INSERT INTO events VALUES (1, 'Conference', '2023-05-15', True);
    INSERT INTO events VALUES (2, 'Workshop', '2023-06-20', True);
    INSERT INTO events VALUES (3, 'Seminar', '2023-04-10', False);

    -- 查询: 查找在特定日期范围内的活跃事件
    SELECT event_name, event_date
    FROM events
    WHERE event_date BETWEEN '2023-05-01' AND '2023-06-30' AND is_active = True;
    ```

*   **Python / Z3 转换**:
    ```python
    from new_model.sql_to_z3 import SqlToZ3, DateType
    from z3 import IntSort, StringSort, BoolSort, sat

    s = SqlToZ3()
    events = s.create_table("events", {
        "event_id": IntSort(),
        "event_name": StringSort(),
        "event_date": DateType(),  # 使用DateType表示日期类型
        "is_active": BoolSort()
    })

    # 插入数据，日期直接使用字符串形式
    events.insert({"event_id": 1, "event_name": "Conference", "event_date": "2023-05-15", "is_active": True})
    events.insert({"event_id": 2, "event_name": "Workshop", "event_date": "2023-06-20", "is_active": True})
    events.insert({"event_id": 3, "event_name": "Seminar", "event_date": "2023-04-10", "is_active": False})

    # 日期范围条件
    date_start = "2023-05-01"
    date_end = "2023-06-30"
    
    # 构建日期范围条件，日期可以直接使用字符串与DateType列进行比较
    date_range_condition = s.And(
        events.event_date >= date_start,
        events.event_date <= date_end
    )
    
    # 活跃状态条件
    active_condition = events.is_active == True
    
    # 组合条件
    final_condition = s.And(date_range_condition, active_condition)
    
    # 添加条件到求解器
    s.add(final_condition)
    
    # 检查是否有满足条件的事件
    if s.check() == sat:
        model = s.get_model()
        
        # 对于找到的特定行，我们可以这样查询
        result_row = Int("result_row")
        s.add(s.And(
            result_row >= 0,
            result_row < len(events.rows),
            final_condition.as_z3_expr(result_row)
        ))
        
        if s.check() == sat:
            model = s.get_model()
            event_id_val = s.eval(events.event_id(result_row), model)
            event_name_val = s.eval(events.event_name(result_row), model)
            event_date_val = s.eval(events.event_date(result_row), model)
            
            # 将整数形式的日期转换为可读形式
            def int_to_date(date_int):
                year = date_int // 10000
                month = (date_int % 10000) // 100
                day = date_int % 100
                return f"{year}-{month:02d}-{day:02d}"
            
            event_date_str = int_to_date(event_date_val)
            
            print(f"找到满足条件的事件: ID: {event_id_val}, 名称: {event_name_val}, 日期: {event_date_str}")
    else:
        print("没有找到满足条件的事件。")
    ```

## 5. 日志与调试

*   所有核心类 (`SqlToZ3`, `Table`, `Column`, `ColumnConstraint`, `Expression`) 都使用一个从 `SqlToZ3` 实例获取的共享 `logging.Logger` 实例。
*   默认日志级别为 `INFO`，但可以在创建 `SqlToZ3` 实例时指定 (例如 `SqlToZ3(log_level=logging.DEBUG)`)。
*   `DEBUG` 级别的日志提供了关于对象创建、方法调用、生成的Z3变量名和表达式的详细信息，这对于理解内部工作流程和调试转换逻辑非常有帮助。
*   `Expression` 和 `ColumnConstraint` 对象的 `description` 属性（例如 `(users.age > 30)` 或 `(employees.salary * 1.1)`）会在日志中输出，有助于追踪表达式的来源和构成。

## 6. 局限性与未来方向

当前 `sql_to_z3` 模块提供了一个将基本SQL概念映射到Z3的基础框架，但仍有一些局限性和潜在的未来扩展方向：
