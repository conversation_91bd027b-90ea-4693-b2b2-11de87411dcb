"""
SQL到Z3转换模块的包初始化文件。

此文件使得 `sql_to_z3` 目录可以被Python作为常规包导入。
它主要做了以下几件事情：
1. 导入此包内各个模块中定义的关键类，使它们可以直接通过 `sql_to_z3.ClassName` 的形式访问。
   例如，可以直接使用 `from new_model.sql_to_z3 import SqlToZ3`。
2. 定义 `__all__` 列表，它明确指定了当用户执行 `from new_model.sql_to_z3 import *` 时，
   哪些名称（主要是类名）会被导入到当前命名空间。这是一种良好的编程实践，
   有助于控制包的公共API，并避免意外导入不必要的内部名称。

此包的核心目的是提供一个将SQL概念（如表、列、表达式和约束）
映射到Z3逻辑表示的框架，从而允许使用Z3求解器来分析和推理SQL查询或数据模型。
"""

# 从各个模块导入核心类，使它们成为包API的一部分
from .expression import Expression
from .column import Column, ColumnConstraint
from .table import Table
from .solver import SqlToZ3
from lineage_core.z3_datetype import DateType

# __all__ 列表定义了 `from <package> import *` 时导出的公共接口
# 这有助于避免命名空间污染，并清晰地指明包的作者希望暴露哪些符号。
__all__ = [
    'Expression',       # 代表可求值的Z3表达式的类
    'Column',           # 代表数据库表中的列的类
    'ColumnConstraint', # 代表列与常量比较的待应用约束的类
    'Table',            # 代表数据库表的类
    'SqlToZ3',          # SQL到Z3转换的核心包装器和求解器交互类
    'DateType'          # 代表Z3中的日期类型的类
] 