# 数据血缘分析系统

本项目实现了一个完整的数据血缘分析系统，支持分析SQL语句之间的行级数据血缘关系。系统提供两种不同的血缘分析器实现，可满足不同场景的需求。

## 系统架构

系统由以下两个主要部分组成：

1. **lineage_core**: 原始血缘分析器实现，使用直接的Z3约束求解方法。
2. **new_model**: 新的血缘分析模型实现，使用更复杂的多阶段处理流程。

两种实现均支持相同的接口，可以根据需要灵活切换。

## 分析器类型

### 1. LineageAnalyzer (lineage_core)

原始的血缘分析器，直接将SQL转换为Z3约束并求解判断血缘关系。

特点：
- 直接从SQL中提取约束
- 单一阶段处理
- 简单直观的处理流程

### 2. LineageDynamicAnalyzer (lineage_core + new_model)

新的动态血缘分析器，使用new_model中的分析逻辑，提供更精细的分析过程。

特点：
- 多阶段处理：相关性分析、类型分析、依赖分析
- 更精确的表和列血缘分析
- 支持更复杂的SQL语句和表达式

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 使用LineageAnalyzer

```python
from lineage_core.lineage_analyzer import LineageAnalyzer

# 创建分析器实例
analyzer = LineageAnalyzer()

# 准备SQL语句
target_sql = "INSERT INTO target_table SELECT * FROM source_table WHERE condition = 'value'"
upstream_sql = "INSERT INTO source_table SELECT * FROM raw_data"

# 进行血缘分析
result = analyzer.analyze_lineage([target_sql, upstream_sql])

# 查看结果
print(f"有血缘关系: {result['result']}")
print(f"相关的上游SQL: {result['target']}")
print(f"收集的列: {result['collected_columns']}")
```

### 使用LineageDynamicAnalyzer

```python
from lineage_core.lineage_dynamic_analyzer import LineageDynamicAnalyzer

# 创建动态分析器实例
dynamic_analyzer = LineageDynamicAnalyzer()

# 使用与LineageAnalyzer相同的接口
result = dynamic_analyzer.analyze_lineage([target_sql, upstream_sql])

# 查看结果
print(f"有血缘关系: {result['result']}")
print(f"相关的上游SQL: {result['target']}")
print(f"收集的列: {result['collected_columns']}")
```

## 示例

查看示例代码：

```bash
python lineage_core/examples/dynamic_analyzer_example.py
```

此示例展示了两种分析器的使用和结果对比。

## 测试

运行测试：

```bash
# 测试原始分析器
pytest tests/regression_test.py

# 测试动态分析器
pytest lineage_core/tests/test_lineage_dynamic_analyzer.py
```

## 高级功能

### 指定字段类型

可以通过schema参数指定字段类型，提高分析精度：

```python
schema = {
    "orders.order_date": "DATE",
    "orders.order_amount": "FLOAT",
    "customers.customer_id": "INT"
}

result = analyzer.analyze_lineage(sql_list, schema=schema)
```

### 使用不同SQL方言

支持不同SQL方言：

```python
result = analyzer.analyze_lineage(sql_list, dialect="mysql")
```

## 更多资源

- 查看 `lineage_core/README.md` 获取lineage_core模块的详细信息
- 查看 `new_model/data_lineage_system_design.md` 了解新模型的设计思路
- 查看 `new_model/tests/` 目录中的测试用例，了解更多使用示例