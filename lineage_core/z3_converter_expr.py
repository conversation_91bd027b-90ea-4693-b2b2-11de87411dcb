"""
SQL表达式到Z3约束转换模块

本模块实现了将SQL表达式转换为Z3约束表达式的详细逻辑，是数据血缘分析的核心组件之一。
该模块支持各种SQL表达式类型的转换，包括比较操作、逻辑操作、算术操作、条件表达式等。

主要功能：
1. 将SQL比较表达式（=, !=, >, <, >=, <=）转换为Z3约束
2. 将SQL逻辑表达式（AND, OR, NOT）转换为Z3约束
3. 将SQL算术表达式（+, -, *, /）转换为Z3表达式
4. 处理特殊表达式，如CASE WHEN, BETWEEN, IN, LIKE, IS NULL等
5. 自动推断表达式类型，确保类型兼容性
6. 处理NULL值和可为NULL的数据类型

核心类：
- Z3ExpressionConverter: 负责将SQL表达式转换为Z3表达式的转换器

依赖：
- z3: 微软Z3约束求解器
- sqlglot: SQL解析和表达式处理库

使用场景：
本模块与 z3_converter_core.py 配合使用，主要用于数据血缘分析中的表达式转换。
通过将SQL查询条件和表达式转换为Z3约束，可以精确分析数据在不同表和列之间的流动关系。
"""

from datetime import datetime
import traceback
import z3
from sqlglot import expressions as exp
from typing import Dict, Any, Optional, Tuple, Set, List, Union, TYPE_CHECKING

from lineage_core.logger import logger

# 使用TYPE_CHECKING避免运行时导入，只在类型检查时导入
if TYPE_CHECKING:
    from lineage_core.z3_converter_core import Z3Converter


class Z3ExpressionConverter:
    """
    负责将SQL表达式转换为Z3约束表达式的转换器。

    该类处理各种SQL表达式类型的具体转换逻辑，
    如比较操作、逻辑操作、算术操作等。

    主要功能：
    1. 表达式类型推断：根据列名、字面量和上下文推断表达式类型
    2. 比较表达式转换：处理等于、不等于、大于、小于等比较操作
    3. 逻辑表达式转换：AND、OR、NOT等逻辑操作
    4. 算术表达式转换：加、减、乘、除等算术操作
    5. 特殊表达式转换：
       - CASE WHEN条件表达式
       - BETWEEN范围表达式
       - IN和NOT IN集合包含表达式
       - LIKE和NOT LIKE模式匹配表达式
       - IS NULL和IS NOT NULL空值检查
    6. NULL值处理：处理表达式中的NULL值和可为NULL的数据类型

    属性：
        converter: Z3Converter实例，提供上下文和类型管理
    """

    def __init__(self, converter: 'Z3Converter'):
        """
        初始化表达式转换器。

        Args:
            converter: Z3Converter实例，提供上下文和类型管理
        """
        self.converter = converter

    def _infer_types_from_comparison(self, left_expr: exp.Expression, right_expr: exp.Expression) -> str:
        """
        从比较表达式的两侧推断类型。

        Args:
            left_expr: 左侧表达式
            right_expr: 右侧表达式

        Returns:
            推断出的类型字符串
        """
        try:
            left_column_name = None
            right_column_name = None
            output_type = None

            # 先从字面量推断类型
            expr = None
            if isinstance(left_expr, exp.Literal):
                expr = left_expr
            elif isinstance(right_expr, exp.Literal):
                expr = right_expr

            if expr:
                value = expr.this if hasattr(expr, 'this') else None
                # 检查是否是字符串字面量
                is_string = getattr(expr, 'is_string', False)

                if is_string:
                    # 尝试解析为日期
                    try:
                        datetime.strptime(value, "%Y-%m-%d")
                        output_type = "Date"
                        logger.debug(f"从字符串字面量推断类型为 Date: {value}")
                    except ValueError:
                        try:
                            datetime.strptime(value, "%Y%m%d")
                            output_type = "Date"
                            logger.debug(f"从字符串字面量推断类型为 Date: {value}")
                        except ValueError:
                            output_type = "String"
                            logger.debug("从字符串字面量推断类型为 String")
                else:
                    # 尝试从值本身推断类型
                    try:
                        # 尝试转换为整数
                        int(value)
                        output_type = "Int"
                        logger.debug(f"从值 {value} 推断类型为 Int")
                    except (ValueError, TypeError):
                        try:
                            # 尝试转换为浮点数
                            float(value)
                            output_type = "Real"
                            logger.debug(f"从值 {value} 推断类型为 Real")
                        except (ValueError, TypeError):
                            # 检查是否为布尔值
                            if isinstance(value, str) and value.lower() in ('true', 'false'):
                                output_type = "Bool"
                                logger.debug(f"从值 {value} 推断类型为 Bool")
                            else:
                                # 默认作为字符串处理
                                output_type = "String"
                                logger.debug("默认将类型设为 String")
                return output_type

            # 两边都不是字面量，则从已有结果中推断类型
            left_type = None
            if isinstance(left_expr, exp.Column):
                left_column_name = self.converter._get_full_column_name(left_expr)
                if left_column_name in self.converter.z3_context:
                    left_type = str(self.converter.z3_context[left_column_name].sort())
                if left_column_name in self.converter.z3_context_null:
                    left_type = str(self.converter.z3_context_null[left_column_name].sort())
                logger.debug(f"从列 {left_column_name} 推断类型为 {left_type}")
            else:
                left_type = self._infer_expr_type(left_expr)
                logger.debug(f"从表达式推断类型为 {left_type}")

            right_type = None
            if isinstance(right_expr, exp.Column):
                right_column_name = self.converter._get_full_column_name(right_expr)
                if right_column_name in self.converter.z3_context:
                    right_type = str(self.converter.z3_context[right_column_name].sort())
                if right_column_name in self.converter.z3_context_null:
                    right_type = str(self.converter.z3_context_null[right_column_name].sort())
                logger.debug(f"从列 {right_column_name} 推断类型为 {right_type}")
            else:
                right_type = self._infer_expr_type(right_expr)
                logger.debug(f"从表达式推断类型为 {right_type}")

            # 类型匹配判断
            if left_type is not None and right_type is not None:
                if left_type != right_type:
                    if f"{left_type}".startswith('Nullable'):
                        logger.warning(f"两侧类型不匹配: {left_type} != {right_type}, 优先返回非Nullable类型")
                        return right_type
                    elif f"{right_type}".startswith('Nullable'):
                        logger.warning(f"两侧类型不匹配: {left_type} != {right_type}, 优先返回非Nullable类型")
                        return left_type
                    else:
                        logger.warning(f"两侧类型不匹配: {left_type} != {right_type}, 返回None")
                        return None
                else:
                    return left_type

            # 单侧类型匹配
            if left_type is not None and right_type is None:
                logger.debug(f"左侧类型匹配，最终推断类型为 {left_type}")
                return left_type

            if right_type is not None and left_type is None:
                logger.debug(f"右侧类型匹配，最终推断类型为 {right_type}")
                return right_type

            # 都不匹配，根据列名推断
            output_type = self.converter._infer_column_type(right_column_name)
            logger.debug(f"都不匹配，根据列名推断类型为 {output_type}")

            return output_type

        except Exception as e:
            logger.debug(f"从比较表达式推断类型时出错: {str(e)}")
            return None

    def _convert_like_pattern(self, pattern: str) -> z3.ReRef:
        """
        将SQL的LIKE模式转换为Z3的正则表达式对象。

        Args:
            pattern: SQL LIKE模式字符串

        Returns:
            Z3正则表达式对象
        """
        logger.debug(f"转换LIKE模式: {pattern}")
        try:
            components = []
            i = 0
            n = len(pattern)
            while i < n:
                current_char = pattern[i]
                if current_char == '_':
                    # 匹配任意单个字符
                    components.append(z3.Range("\x00", "\xff"))  # 所有字符范围
                    i += 1
                elif current_char == '%':
                    # 匹配零个或多个任意字符
                    components.append(z3.Star(z3.Range("\x00", "\xff")))
                    i += 1
                elif current_char == '\\' and i + 1 < n:
                    # 处理转义字符，例如\%转义为%
                    next_char = pattern[i + 1]
                    components.append(z3.Re(z3.StringVal(next_char)))
                    i += 2
                else:
                    # 普通字符直接匹配
                    components.append(z3.Re(z3.StringVal(current_char)))
                    i += 1

            if not components:
                # 空模式匹配空字符串
                return z3.Re("")

            # 将所有组件连接起来
            if len(components) == 1:
                return components[0]

            # 使用正确的方式连接正则表达式组件
            result = components[0]
            for comp in components[1:]:
                # 使用Concat操作符连接正则表达式
                result = z3.Concat(result, comp)
            return result
        except Exception as e:
            logger.warning(f"转换LIKE模式时出错: {str(e)}")
            # 返回一个匹配任何字符串的正则表达式
            return z3.Star(z3.Range("\x00", "\xff"))

    def _infer_expr_type(self, expr: exp.Expression) -> str:
        """
        递归推断表达式的类型。

        Args:
            expr: SQL表达式

        Returns:
            推断出的类型字符串 ("Int", "Real", "String", "Bool", "Date")
        """
        try:
            # 存储找到的所有类型信息，按优先级分类
            found_types = {
                'literal': None,  # 最高优先级：字面量类型
                'context': set(),  # 次优先级：从上下文中找到的类型
                'inferred': None,  # 最低优先级：通过名称推断的类型
            }

            def _process_literal(literal_expr) -> Optional[str]:
                """
                处理字面量表达式，推断其类型。

                Args:
                    literal_expr: 字面量表达式

                Returns:
                    推断的类型
                """
                value = literal_expr.this if hasattr(literal_expr, 'this') else None
                if value is None:
                    return None

                # 检查是否是字符串字面量
                if hasattr(literal_expr, 'is_string') and literal_expr.is_string:
                    # 尝试解析为日期
                    try:
                        datetime.strptime(value, "%Y-%m-%d")
                        return "Date"
                    except ValueError:
                        return "String"

                # 尝试从值本身推断类型
                try:
                    int(value)
                    return "Int"
                except (ValueError, TypeError):
                    try:
                        float(value)
                        return "Real"
                    except (ValueError, TypeError):
                        if isinstance(value, str) and value.lower() in ('true', 'false'):
                            return "Bool"
                        return "String"

            def _process_column(column_expr) -> Optional[str]:
                """
                处理列引用表达式，推断其类型。

                Args:
                    column_expr: 列引用表达式

                Returns:
                    推断的类型
                """
                column_name = self.converter._get_full_column_name(column_expr)
                if not column_name:
                    return None

                # 检查是否在上下文中存在
                if column_name in self.converter.z3_context:
                    var_type = str(self.converter.z3_context[column_name].sort())
                    return var_type
                if column_name in self.converter.z3_context_null:
                    var_type = str(self.converter.z3_context_null[column_name].sort())
                    return var_type

                # 如果在上下文中没找到，使用名称推断
                return self.converter._infer_column_type(column_name)

            def _recursive_type_search(current_expr):
                """
                递归搜索表达式中的类型信息。

                Args:
                    current_expr: 当前表达式
                """
                # 处理字面量
                if isinstance(current_expr, exp.Literal):
                    literal_type = _process_literal(current_expr)
                    if literal_type and not found_types['literal']:
                        found_types['literal'] = literal_type
                    return

                # 处理列引用
                if isinstance(current_expr, exp.Column):
                    column_type = _process_column(current_expr)
                    if column_type:
                        found_types['context'].add(column_type)
                    return

                # 递归处理子表达式
                if hasattr(current_expr, 'args'):
                    for arg_name, arg_value in current_expr.args.items():
                        if isinstance(arg_value, exp.Expression):
                            _recursive_type_search(arg_value)
                        elif isinstance(arg_value, list):
                            for item in arg_value:
                                if isinstance(item, exp.Expression):
                                    _recursive_type_search(item)

            # 开始递归搜索
            _recursive_type_search(expr)

            # 按优先级返回类型
            if found_types['literal'] is not None:
                logger.debug(f"从字面量推断出类型: {found_types['literal']}")
                return found_types['literal']

            if found_types['context']:
                # 如果上下文中有多个类型，优先选择数值类型
                context_type = None
                if "Int" in found_types['context']:
                    context_type = "Int"
                elif "Real" in found_types['context']:
                    context_type = "Real"
                elif "Bool" in found_types['context']:
                    context_type = "Bool"
                else:
                    context_type = next(iter(found_types['context']))  # 取第一个类型
                logger.debug(f"从上下文推断出类型: {context_type}")
                return context_type

            # 如果没有找到任何类型信息，使用默认的String类型
            logger.debug("未找到明确的类型信息，使用默认类型: String")
            return "String"

        except Exception as e:
            logger.warning(f"推断表达式类型时出错: {str(e)}")
            return "String"  # 发生错误时返回默认类型

    def _contains_exists(self, expr) -> bool:
        """
        检查表达式中是否包含EXISTS操作。

        Args:
            expr: SQL表达式

        Returns:
            如果包含EXISTS操作则返回True，否则返回False
        """
        # 如果表达式本身是EXISTS
        if isinstance(expr, exp.Exists):
            return True

        # 递归检查子表达式
        if hasattr(expr, 'args'):
            for arg_name, arg_value in expr.args.items():
                if isinstance(arg_value, exp.Expression) and self._contains_exists(arg_value):
                    return True
                elif isinstance(arg_value, list):
                    for item in arg_value:
                        if isinstance(item, exp.Expression) and self._contains_exists(item):
                            return True

        # 检查特定属性中的子表达式
        for attr_name in ['this', 'expression', 'query', 'subquery', 'condition']:
            if hasattr(expr, attr_name):
                attr_value = getattr(expr, attr_name)
                if isinstance(attr_value, exp.Expression) and self._contains_exists(attr_value):
                    return True

        return False

    def convert_expression_to_z3(self, expr: exp.Expression, ref_column_type: Optional[str] = None, target_columns: Optional[Set[str]] = None) -> Any:
        """
        将SQL表达式转换为Z3表达式。

        Args:
            expr: SQL表达式
            ref_column_type: 参考列类型（可选）
            target_columns: 收集的列名集合（可选）

        Returns:
            Z3表达式
        """
        try:
            # 检查表达式中是否包含EXISTS操作，当前逻辑处理不了EXIST表达式
            # 必须在最外层给出True，否则可能引起错误的剪枝
            if self._contains_exists(expr):
                return None

            # 处理括号表达式
            if isinstance(expr, exp.Paren):
                return self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)

            # 处理列引用
            elif isinstance(expr, exp.Column):
                column_name = self.converter._get_full_column_name(expr)
                if column_name:
                    return self.converter._get_or_create_z3_var(column_name, ref_column_type)
                return None

            # 处理字面量
            elif isinstance(expr, exp.Literal):
                value = expr.this if hasattr(expr, 'this') else None

                if value is None:
                    logger.debug("处理NULL字面量")
                    return None  # 返回None表示NULL值

                logger.debug(f"处理字面量: {value}, 类型: {type(value)}, is_string: {getattr(expr, 'is_string', None)}")

                # 根据字面量类型创建Z3字面量
                if hasattr(expr, 'is_string') and expr.is_string:
                    # 尝试解析为日期
                    try:
                        datetime.strptime(value, "%Y-%m-%d")
                        result = self.converter.date_type.from_string(str(value))
                    except ValueError:
                        try:
                            datetime.strptime(value, "%Y%m%d")
                            result = self.converter.date_type.from_string(str(value[:4] + "-" + value[4:6] + "-" + value[6:8]))
                        except ValueError:
                            # 明确标记为字符串的情况
                            result = z3.StringVal(str(value))
                else:
                    # 尝试将值转换为适当的类型
                    try:
                        # 尝试转换为整数
                        int_value = int(value)
                        result = z3.IntVal(int_value)
                    except (ValueError, TypeError):
                        try:
                            # 尝试转换为浮点数
                            float_value = float(value)
                            result = z3.RealVal(float_value)
                        except (ValueError, TypeError):
                            # 检查是否为布尔值
                            if isinstance(value, str) and value.lower() in ('true', 'false'):
                                bool_value = value.lower() == 'true'
                                result = z3.BoolVal(bool_value)
                            else:
                                # 默认作为字符串处理
                                result = z3.StringVal(str(value))

                logger.debug(f"字面量Z3结果: {result}, 类型: {result.sort()}")
                return result

            # 处理BETWEEN操作
            elif isinstance(expr, exp.Between):
                low = self.convert_expression_to_z3(expr.args["low"], ref_column_type, target_columns)
                high = self.convert_expression_to_z3(expr.args["high"], ref_column_type, target_columns)

                # 确保列与边界值类型一致
                column_type = f"{low.sort()}" if low is not None else ref_column_type
                column = self.convert_expression_to_z3(expr.args["this"], column_type, target_columns)

                if column is None or low is None or high is None:
                    logger.warning("BETWEEN操作中存在NULL值")
                    return z3.BoolVal(True)

                # 创建复合条件: column >= low AND column <= high
                return self.converter.solver.And(column >= low, column <= high)

            # 处理IN操作
            elif isinstance(expr, exp.In):
                # 先处理值列表
                values = []
                for val_expr in expr.args.get("expressions", []):
                    # 如果值列表中包含SELECT语句，则直接返回None
                    # 比如 IN (SELECT 1, 2, 3)
                    if list(val_expr.find_all(exp.Subquery)) > 0:
                        return None
                    val = self.convert_expression_to_z3(val_expr, ref_column_type, target_columns)
                    if val is not None:
                        values.append(val)

                if not values:
                    logger.warning("IN操作中值列表为空")
                    return None

                # 获取值的类型，用于确保列类型匹配
                val_type = f"{values[0].sort()}"

                # 再处理列，确保类型一致
                column = self.convert_expression_to_z3(expr.args["this"], val_type, target_columns)
                if column is None:
                    logger.warning("IN操作中列表达式为NULL")
                    return None

                # 构建等值比较列表
                column_values = [column == val for val in values]

                # 构建OR表达式：column IN (val1, val2, ...) 等价于 column = val1 OR column = val2 OR ...
                return self.converter.solver.Or(*column_values) if column_values else None

            # 处理NOT IN操作
            elif isinstance(expr, exp.Not) and isinstance(expr.args.get("this"), exp.In):
                in_expr = expr.args.get("this")
                # 先处理值列表
                values = []
                for val_expr in in_expr.args.get("expressions", []):
                    val = self.convert_expression_to_z3(val_expr, ref_column_type, target_columns)
                    if val is not None:
                        values.append(val)

                if not values:
                    logger.warning("NOT IN操作中值列表为空")
                    return None

                # 获取值的类型，用于确保列类型匹配
                val_type = f"{values[0].sort()}"

                # 再处理列，确保类型一致
                column = self.convert_expression_to_z3(in_expr.args["this"], val_type, target_columns)
                if column is None:
                    logger.warning("NOT IN操作中列表达式为NULL")
                    return None

                # 构建不等值比较列表
                column_values = [column != val for val in values]

                # 构建AND表达式：column NOT IN (val1, val2, ...) 等价于 column != val1 AND column != val2 AND ...
                return self.converter.solver.And(*column_values) if column_values else None

            # 处理比较操作
            elif isinstance(expr, (exp.EQ, exp.NEQ, exp.GT, exp.LT, exp.GTE, exp.LTE)):
                constraints = []
                left_expr = expr.args["this"]
                right_expr = expr.args["expression"]

                # 获取列名（如果存在）
                left_column_name = None
                right_column_name = None

                # 从比较两侧推断类型
                var_type = self._infer_types_from_comparison(left_expr, right_expr)

                # 转换左右表达式为Z3表达式
                left = None
                right = None

                if isinstance(left_expr, exp.Column):
                    left_column_name = self.converter._get_full_column_name(left_expr)
                    if left_column_name:
                        left = self.converter._get_or_create_z3_var(left_column_name, f'{var_type}')
                else:
                    left = self.convert_expression_to_z3(left_expr, f'{var_type}', target_columns)

                if isinstance(right_expr, exp.Column):
                    right_column_name = self.converter._get_full_column_name(right_expr)
                    if right_column_name:
                        right = self.converter._get_or_create_z3_var(right_column_name, f'{var_type}')
                else:
                    right = self.convert_expression_to_z3(right_expr, f'{var_type}', target_columns)

                # 处理NULL值的关键步骤：如果有column曾经定义为Nullable类型，则声明其为非NULL
                if not f'{var_type}'.startswith("Nullable"):
                    if left_column_name in self.converter.z3_context_null:
                        left_n = self.converter.z3_context_null[left_column_name]
                        nullable_type_info = self.converter._get_nullable_type_for_column(f'{left_n.sort()}'.replace('Nullable', ''))
                        constraints.append(nullable_type_info['is_some'](left_n))
                    if right_column_name in self.converter.z3_context_null:
                        right_n = self.converter.z3_context_null[right_column_name]
                        nullable_type_info = self.converter._get_nullable_type_for_column(f'{right_n.sort()}'.replace('Nullable', ''))
                        constraints.append(nullable_type_info['is_some'](right_n))

                if left is None or right is None:
                    return None
                print(f"left: {left.sort()}, right: {right.sort()}")
                # 添加比较条件
                if isinstance(expr, exp.EQ):
                    constraints.append(left == right)
                elif isinstance(expr, exp.NEQ):
                    constraints.append(left != right)
                elif isinstance(expr, exp.GT):
                    constraints.append(left > right)
                elif isinstance(expr, exp.LT):
                    constraints.append(left < right)
                elif isinstance(expr, exp.GTE):
                    constraints.append(left >= right)
                elif isinstance(expr, exp.LTE):
                    constraints.append(left <= right)

                # 返回所有条件的AND组合
                if len(constraints) == 1:
                    return constraints[0]
                else:
                    return self.converter.solver.And(*constraints)

            # 处理IS NULL 操作
            elif isinstance(expr, exp.Is):
                constraints = []
                column_expr = expr.args.get("this")
                null_expr = expr.args.get("expression")

                # 确保右侧是NULL
                if isinstance(null_expr, exp.Null):
                    column_name = self.converter._get_full_column_name(column_expr)
                    if column_name:
                        # 获取或创建列的Z3变量（确保使用可为NULL的类型）
                        column_var = self.converter._get_or_create_z3_var(column_name, "NullableString")
                        # 获取对应的可为NULL类型信息
                        nullable_type_info = self.converter._get_nullable_type_for_column("String")

                        # 添加IS NULL约束
                        constraints.append(nullable_type_info['is_null'](column_var))

                        # 如果同时存在非NULL变量，确保变量一致性
                        if column_name in self.converter.z3_context:
                            constraints.append(column_var['is_some'](self.converter.z3_context[column_name]))

                        return self.converter.solver.And(*constraints) if len(constraints) > 1 else constraints[0]
                    else: # IS NULL 前面是表达式的情况，目前不处理
                        logger.warning("无法从IS NULL表达式中获取列名")
                        return z3.BoolVal(True)

                # 如果不是IS NULL，则尝试处理为普通相等比较
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)
                left = self.convert_expression_to_z3(expr.args["this"], f'{right.sort()}' if right else ref_column_type, target_columns)

                if left is None or right is None:
                    return z3.BoolVal(True)

                return left == right

            # 处理IS NOT NULL和其他NOT表达式
            elif isinstance(expr, exp.Not):
                inner_expr = expr.args.get("this")

                # 处理IS NOT NULL
                if isinstance(inner_expr, exp.Is):
                    column_expr = inner_expr.args.get("this")
                    null_expr = inner_expr.args.get("expression")

                    # 确保右侧是NULL
                    if isinstance(null_expr, exp.Null):
                        column_name = self.converter._get_full_column_name(column_expr)
                        if column_name:
                            # 获取或创建列的Z3变量（确保使用可为NULL的类型）
                            column_var = self.converter._get_or_create_z3_var(column_name, "NullableString")
                            # 获取对应的可为NULL类型信息
                            nullable_type_info = self.converter._get_nullable_type_for_column("String")
                            # 返回IS NOT NULL检查
                            return nullable_type_info['is_some'](column_var)

                # 处理NOT LIKE
                if isinstance(inner_expr, exp.Like):
                    column_expr = inner_expr.args.get("this")
                    pattern_expr = inner_expr.args.get("expression")

                    column_z3 = self.convert_expression_to_z3(column_expr, "String", target_columns)

                    if isinstance(pattern_expr, exp.Literal) and hasattr(pattern_expr, 'this'):
                        pattern = str(pattern_expr.this)
                        z3_regex = self._convert_like_pattern(pattern)
                        return self.converter.solver.Not(z3.InRe(column_z3, z3_regex))

                # 如果不是特殊处理的情况，作为普通的NOT处理
                inner_z3 = self.convert_expression_to_z3(inner_expr, ref_column_type, target_columns)
                if inner_z3 is None:
                    return z3.BoolVal(True)
                return self.converter.solver.Not(inner_z3)

            # 处理AND操作
            elif isinstance(expr, exp.And):
                left = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)

                if left is None and right is not None:
                    return right
                elif left is not None and right is None:
                    return left
                elif left is None and right is None:
                    return None
                print(f"left: {left.sort()}, right: {right.sort()}")
                return self.converter.solver.And(left, right)

            # 处理OR操作
            elif isinstance(expr, exp.Or):
                left = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)

                if left is None and right is not None:
                    return right
                elif left is not None and right is None:
                    return left
                elif left is None and right is None:
                    return None
                return self.converter.solver.Or(left, right)

            # 处理算术操作
            elif isinstance(expr, exp.Add):  # 处理加法
                left_expr = expr.args["this"]
                right_expr = expr.args["expression"]
                ref_column_type = self._infer_types_from_comparison(left_expr, right_expr)
                left = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)

                if left is None and right is not None:
                    return right
                elif left is not None and right is None:
                    return left
                elif left is None and right is None:
                    return None

                return left + right

            elif isinstance(expr, exp.Sub):  # 处理减法
                left_expr = expr.args["this"]
                right_expr = expr.args["expression"]
                ref_column_type = self._infer_types_from_comparison(left_expr, right_expr)
                left = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)

                if left is None and right is not None:
                    return right
                elif left is not None and right is None:
                    return left
                elif left is None and right is None:
                    return None

                return left - right

            elif isinstance(expr, exp.Mul):  # 处理乘法
                left_expr = expr.args["this"]
                right_expr = expr.args["expression"]
                ref_column_type = self._infer_types_from_comparison(left_expr, right_expr)
                left = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)
                if left is None or right is None:
                    return None
                return left * right

            elif isinstance(expr, exp.Div):  # 处理除法
                left_expr = expr.args["this"]
                right_expr = expr.args["expression"]
                ref_column_type = self._infer_types_from_comparison(left_expr, right_expr)
                left = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                right = self.convert_expression_to_z3(expr.args["expression"], ref_column_type, target_columns)
                if left is None or right is None:
                    return None
                return left / right

            # 处理别名表达式
            elif isinstance(expr, exp.Alias):
                actual_expr = expr.args.get("this")
                return self.convert_expression_to_z3(actual_expr, ref_column_type, target_columns)

            # 处理布尔类型
            elif isinstance(expr, exp.Boolean):
                return z3.BoolVal(expr.this)

            # 处理负号操作
            elif isinstance(expr, exp.Neg):
                inner_expr = self.convert_expression_to_z3(expr.args["this"], ref_column_type, target_columns)
                if inner_expr is not None:
                    return -inner_expr
                return None

            # # 处理LIKE操作
            # elif isinstance(expr, exp.Like):
            #     constraints = []
            #     # 获取列表达式和模式表达式
            #     column_expr = expr.args.get("this")
            #     pattern_expr = expr.args.get("expression")

            #     # 列名（如果存在）
            #     column_name = None
            #     if isinstance(column_expr, exp.Column):
            #         column_name = self.converter._get_full_column_name(column_expr)

            #     # 转换列表达式为Z3表达式，确保为String类型
            #     column_z3 = self.convert_expression_to_z3(column_expr, 'String', target_columns)
            #     if column_z3 is None:
            #         return None

            #     # 如果列是Nullable类型，需要声明其为非NULL
            #     expr_n = self.converter.z3_context_null.get(column_name, None)
            #     if expr_n:
            #         nullable_type_info = self.converter._get_nullable_type_for_column("String")
            #         constraints.append(nullable_type_info['is_some'](expr_n))

            #     # 处理LIKE模式
            #     if isinstance(pattern_expr, exp.Literal) and hasattr(pattern_expr, 'this'):
            #         pattern = str(pattern_expr.this)

            #         # 将模式转换为Z3正则表达式
            #         z3_regex = self._convert_like_pattern(pattern)

            #         # 创建匹配约束: column MATCHES regex
            #         constraints.append(z3.InRe(column_z3, z3_regex))
            #         if len(constraints) == 1:
            #             return constraints[0]
            #         else:
            #             return self.converter.solver.And(*constraints)
            #     else:
            #         logger.warning("LIKE模式不是字面量")
            #         return None

            # 处理COALESCE函数 - 简化处理，只取第一个输入表达式
            # TODO：实现完整的COALESCE函数处理
            elif isinstance(expr, exp.Coalesce):
                return self.convert_expression_to_z3(expr.this, ref_column_type, target_columns)

            # 处理其他类型的表达式
            else:
                # 返回一个通用的True约束，避免整个转换失败
                return None

        except Exception as e:
            # 处理转换过程中的异常
            logger.warning(f"转换表达式时出错: {str(e)}")
            if hasattr(expr, 'sql'):
                logger.debug(f"表达式: {expr.sql()}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
            return None