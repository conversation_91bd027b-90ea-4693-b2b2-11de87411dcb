"""
自定义日期类型模块

该模块实现了一个自定义的日期类型，支持从字符串转换为日期类型，
支持日期加减整数操作（天数），以及支持日期类型变量的比较约束。

主要功能：
1. 创建自定义日期类型
2. 日期解析和格式化
3. 日期加减操作
4. 日期比较操作
"""

import z3
from datetime import datetime, timedelta, date
from typing import Union, List, Callable, Set, Dict, Any, Optional, Tuple


class DateType:
    """
    自定义日期类型

    提供日期的创建、解析、比较和加减天数操作。
    日期表示为从公元0000-01-01开始的天数（整数）。
    """

    def __init__(self) -> None:
        """
        初始化日期类型
        
        创建基本排序类型和辅助函数
        """
        # 基本排序类型
        self.int_sort = z3.IntSort()

        # 用于比较的辅助函数
        self._less_than = None
        self._equals = None
        
        # 基准日期：公元0000-01-01
        self.base_date = date(1, 1, 1)  # Python无法表示0000年，使用0001年作为基准
        self.base_date_adjustment = 366  # 从0000-01-01到0001-01-01的调整天数

    def date_to_days(self, year: int, month: int, day: int) -> int:
        """
        将日期转换为自公元0000-01-01以来的天数

        Args:
            year: 年份 (整数)
            month: 月份 (整数, 1-12)
            day: 日期 (整数, 1-31)

        Returns:
            自公元0000-01-01以来的天数
        """
        try:
            # 创建日期对象
            dt = date(year, month, day)
            # 计算天数差
            days = (dt - self.base_date).days + self.base_date_adjustment
            return days
        except ValueError as e:
            # 处理无效日期
            raise ValueError(f"无效日期: {year}-{month}-{day}, 错误: {e}")

    def days_to_date(self, days: int) -> date:
        """
        将天数转换为日期对象

        Args:
            days: 自公元0000-01-01以来的天数

        Returns:
            对应的日期对象
        """
        try:
            # 调整天数（考虑从0000-01-01到0001-01-01的差异）
            adjusted_days = days - self.base_date_adjustment
            
            # 检查日期范围，防止溢出
            if adjusted_days < -365*100 or adjusted_days > 365*9000:  # 限制在有意义的日期范围内
                return date(1970, 1, 1)  # 返回一个合理的默认日期
                
            # 计算日期
            return self.base_date + timedelta(days=adjusted_days)
        except (OverflowError, ValueError) as e:
            # 处理日期溢出或其他错误
            print(f"日期转换错误(天数={days}): {e}")
            return date(1970, 1, 1)  # 返回一个合理的默认日期

    def create_date(self, year: int, month: int, day: int) -> z3.ArithRef:
        """
        创建日期

        将日期转换为自公元0000-01-01以来的天数

        Args:
            year: 年份 (整数)
            month: 月份 (整数, 1-12)
            day: 日期 (整数, 1-31)

        Returns:
            Z3整数表达式，表示日期的天数形式
        """
        # 转换为天数
        days = self.date_to_days(year, month, day)
        # 转换为Z3整数值
        return z3.IntVal(days)

    def date_var(self, name: str) -> z3.ArithRef:
        """
        创建日期变量

        Args:
            name: 变量名称

        Returns:
            Z3整数表达式，表示一个日期变量
        """
        return z3.Int(name)

    def parse_date(self, date_str: str, format: str = '%Y-%m-%d') -> z3.ArithRef:
        """
        将字符串解析为日期

        Args:
            date_str: 日期字符串
            format: 日期格式

        Returns:
            Z3整数表达式，表示解析后的日期
        """
        # 解析日期字符串
        dt = datetime.strptime(date_str, format)
        # 创建日期
        return self.create_date(dt.year, dt.month, dt.day)

    def from_string(self, date_str: str, format: str = '%Y-%m-%d') -> z3.ArithRef:
        """
        从字符串创建日期（parse_date的别名）

        Args:
            date_str: 日期字符串
            format: 日期格式

        Returns:
            Z3整数表达式，表示解析后的日期
        """
        return self.parse_date(date_str, format)

    def date_to_str(self, date_expr: z3.ArithRef, format: str = '%Y-%m-%d') -> str:
        """
        将日期表达式转换为字符串

        Args:
            date_expr: Z3日期表达式（天数）
            format: 日期格式

        Returns:
            日期字符串表示
        """
        if z3.is_int_value(date_expr):
            try:
                # 获取日期的天数值
                days = date_expr.as_long()

                # 转换为日期对象
                dt = self.days_to_date(days)
                return dt.strftime(format)
            except Exception as e:
                return f"无效日期(天数={date_expr}): {e}"
        else:
            # 对于符号表达式，返回表达式本身
            return str(date_expr)

    def extract_year(self, date_expr: z3.ArithRef) -> z3.ArithRef:
        """
        提取日期的年份

        Args:
            date_expr: Z3日期表达式（天数）或DateExpression

        Returns:
            年份（Z3整数表达式）
        """
        # 如果是DateExpression，获取内部表达式
        if isinstance(date_expr, DateExpression):
            actual_date_expr = date_expr.date_expr
        else:
            actual_date_expr = date_expr

        # 我们需要创建一个函数来表示从天数转换到年份的映射
        # 由于Z3不支持直接操作日期，这里创建一个抽象的函数
        year_fn = z3.Function('year', z3.IntSort(), z3.IntSort())
        return year_fn(actual_date_expr)

    def extract_month(self, date_expr: z3.ArithRef) -> z3.ArithRef:
        """
        提取日期的月份

        Args:
            date_expr: Z3日期表达式（天数）或DateExpression

        Returns:
            月份（Z3整数表达式）
        """
        # 如果是DateExpression，获取内部表达式
        if isinstance(date_expr, DateExpression):
            actual_date_expr = date_expr.date_expr
        else:
            actual_date_expr = date_expr

        # 创建抽象函数表示从天数到月份的映射
        month_fn = z3.Function('month', z3.IntSort(), z3.IntSort())
        return month_fn(actual_date_expr)

    def extract_day(self, date_expr: z3.ArithRef) -> z3.ArithRef:
        """
        提取日期的日

        Args:
            date_expr: Z3日期表达式（天数）或DateExpression

        Returns:
            日（Z3整数表达式）
        """
        # 如果是DateExpression，获取内部表达式
        if isinstance(date_expr, DateExpression):
            actual_date_expr = date_expr.date_expr
        else:
            actual_date_expr = date_expr

        # 创建抽象函数表示从天数到天的映射
        day_fn = z3.Function('day', z3.IntSort(), z3.IntSort())
        return day_fn(actual_date_expr)

    def date_add(self, date_expr: z3.ArithRef, days: Union[int, z3.ArithRef]) -> Tuple[z3.ArithRef, Callable]:
        """
        日期加天数

        Args:
            date_expr: Z3日期表达式（天数或DateExpression）
            days: 要加的天数（整数或Z3表达式）

        Returns:
            日期加天数的结果（Z3整数表达式）和相关约束函数的元组
        """
        # 确保days是Z3表达式
        if isinstance(days, int):
            days_expr = z3.IntVal(days)
        else:
            days_expr = days

        # 如果是DateExpression，获取内部表达式
        if isinstance(date_expr, DateExpression):
            actual_date_expr = date_expr.date_expr
        else:
            actual_date_expr = date_expr

        # 日期加法变得简单：直接将日期（天数）与天数相加
        result_var = actual_date_expr + days_expr

        # 返回结果和一个空约束函数（不需要额外约束）
        def add_days_constraint(solver):
            # 在新表示法下，不需要额外约束
            pass

        return result_var, add_days_constraint

    def date_subtract(self, date_expr: z3.ArithRef, days: Union[int, z3.ArithRef]) -> Tuple[z3.ArithRef, Callable]:
        """
        日期减天数

        Args:
            date_expr: Z3日期表达式（天数或DateExpression）
            days: 要减的天数（整数或Z3表达式）

        Returns:
            日期减天数的结果（Z3整数表达式）和相关约束函数的元组
        """
        # 确保days是Z3表达式
        if isinstance(days, int):
            days_expr = z3.IntVal(days)
        else:
            days_expr = days

        # 如果是DateExpression，获取内部表达式
        if isinstance(date_expr, DateExpression):
            actual_date_expr = date_expr.date_expr
        else:
            actual_date_expr = date_expr

        # 日期减法：直接将日期（天数）减去天数
        result_var = actual_date_expr - days_expr

        # 返回结果和一个空约束函数
        def subtract_days_constraint(solver):
            # 在新表示法下，不需要额外约束
            pass

        return result_var, subtract_days_constraint

    def equals(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> z3.BoolRef:
        """
        日期相等比较

        Args:
            date1: 第一个日期表达式（天数或DateExpression）
            date2: 第二个日期表达式（天数或DateExpression）

        Returns:
            Z3布尔表达式，表示两个日期是否相等
        """
        # 处理DateExpression对象
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        return date1 == date2

    def not_equals(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> z3.BoolRef:
        """
        日期不等比较

        Args:
            date1: 第一个日期表达式（天数或DateExpression）
            date2: 第二个日期表达式（天数或DateExpression）

        Returns:
            Z3布尔表达式，表示两个日期是否不相等
        """
        # 处理DateExpression对象
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        return date1 != date2

    def less_than(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> z3.BoolRef:
        """
        日期小于比较

        Args:
            date1: 第一个日期表达式（天数或DateExpression）
            date2: 第二个日期表达式（天数或DateExpression）

        Returns:
            Z3布尔表达式，表示第一个日期是否小于第二个日期
        """
        # 处理DateExpression对象
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        return date1 < date2

    def less_than_or_equal(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> z3.BoolRef:
        """
        日期小于等于比较

        Args:
            date1: 第一个日期表达式（天数或DateExpression）
            date2: 第二个日期表达式（天数或DateExpression）

        Returns:
            Z3布尔表达式，表示第一个日期是否小于等于第二个日期
        """
        # 处理DateExpression对象
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        return date1 <= date2

    def greater_than(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> z3.BoolRef:
        """
        日期大于比较

        Args:
            date1: 第一个日期表达式（天数或DateExpression）
            date2: 第二个日期表达式（天数或DateExpression）

        Returns:
            Z3布尔表达式，表示第一个日期是否大于第二个日期
        """
        # 处理DateExpression对象
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        return date1 > date2

    def greater_than_or_equal(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> z3.BoolRef:
        """
        日期大于等于比较

        Args:
            date1: 第一个日期表达式（天数或DateExpression）
            date2: 第二个日期表达式（天数或DateExpression）

        Returns:
            Z3布尔表达式，表示第一个日期是否大于等于第二个日期
        """
        # 处理DateExpression对象
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        return date1 >= date2

    def date_diff(self, date1: Union[z3.ArithRef, 'DateExpression'], date2: Union[z3.ArithRef, 'DateExpression']) -> Tuple[z3.ArithRef, Callable]:
        """
        计算两个日期之间的天数差

        Args:
            date1: 第一个日期（Z3日期表达式或DateExpression）
            date2: 第二个日期（Z3日期表达式或DateExpression）

        Returns:
            日期差（Z3整数表达式）和相关约束函数的元组
        """
        # 如果输入是DateExpression对象，获取其内部的日期表达式
        if isinstance(date1, DateExpression):
            date1 = date1.date_expr
        if isinstance(date2, DateExpression):
            date2 = date2.date_expr

        # 日期差就是天数差
        result_var = date2 - date1

        # 返回结果和一个空约束函数
        def diff_constraint(solver):
            # 在新表示法下，不需要额外约束
            pass

        return result_var, diff_constraint

    def date_today(self) -> 'DateExpression':
        """
        获取今天的日期

        Returns:
            表示今天的DateExpression对象
        """
        today = date.today()
        days = self.date_to_days(today.year, today.month, today.day)
        date_expr = z3.IntVal(days)
        return DateExpression(self, date_expr)

    def to_date_expression(self, date_value: Union[int, z3.ArithRef, date, 'DateExpression']) -> 'DateExpression':
        """
        将各种日期值转换为DateExpression对象

        Args:
            date_value: 可以是整数、Z3表达式、datetime.date或DateExpression

        Returns:
            DateExpression对象
        """
        if isinstance(date_value, DateExpression):
            return date_value

        if isinstance(date_value, date):
            days = self.date_to_days(date_value.year, date_value.month, date_value.day)
            return DateExpression(self, z3.IntVal(days))

        if isinstance(date_value, int):
            return DateExpression(self, z3.IntVal(date_value))

        # 假设其他情况是Z3表达式
        return DateExpression(self, date_value)


# 以下是包装类，用于提供更友好的接口

class DateExpression:
    """
    日期表达式包装类

    用于封装Z3日期表达式并提供运算符重载功能
    """

    def __init__(self, date_type: 'DateType', date_expr: z3.ArithRef, constraints: Optional[List[Callable]] = None) -> None:
        """
        初始化日期表达式

        Args:
            date_type: DateType实例
            date_expr: Z3日期表达式（天数）
            constraints: 相关约束函数列表（可选）
        """
        self.date_type = date_type
        self.date_expr = date_expr
        self.constraints = constraints or []
        # 标记约束是否已应用
        self._constraints_applied = False
        
    def sort(self) -> str:
        """
        获取表达式的类型信息，模拟Z3表达式的sort函数
        
        Returns:
            类型信息字符串，返回"Date"表示日期类型
        """
        return "Date"

    def __add__(self, days: Union[int, z3.ArithRef]) -> 'DateExpression':
        """
        重载加法运算符 (+)

        Args:
            days: 要加的天数（整数或Z3表达式）

        Returns:
            新的DateExpression对象
        """
        result_expr, constraint_fn = self.date_type.date_add(self.date_expr, days)
        # 合并原有约束和新约束
        new_constraints = self.constraints.copy()
        new_constraints.append(constraint_fn)
        return DateExpression(self.date_type, result_expr, new_constraints)
    
    def __radd__(self, days: Union[int, z3.ArithRef]) -> 'DateExpression':
        """
        重载反向加法运算符 (+)

        Args:
            days: 要加的天数（整数或Z3表达式）

        Returns:
            新的DateExpression对象
        """
        return self.__add__(days)

    def __sub__(self, other: Union[int, z3.ArithRef, 'DateExpression']) -> Union['DateExpression', z3.ArithRef]:
        """
        重载减法运算符 (-)

        Args:
            other: 要减的天数（整数或Z3表达式）或另一个DateExpression对象

        Returns:
            如果other是天数，返回新的DateExpression对象
            如果other是DateExpression，返回两个日期之间的差值（天数）
        """
        # 当减去的是另一个日期时，计算日期差
        if isinstance(other, DateExpression):
            # 直接计算日期差，不需要使用date_diff方法
            # 日期差就是天数差，使用当前日期减去另一个日期
            return self.date_expr - other.date_expr

        # 原来的减去天数的逻辑
        result_expr, constraint_fn = self.date_type.date_subtract(self.date_expr, other)
        # 合并原有约束和新约束
        new_constraints = self.constraints.copy()
        new_constraints.append(constraint_fn)
        return DateExpression(self.date_type, result_expr, new_constraints)
    
    def __rsub__(self, other: Union[int, z3.ArithRef]) -> 'DateExpression':
        """
        重载反向减法运算符 (-)
        注意：这个实现假设right - left，其中left是DateExpression，right是其他类型
        此情况不常见，因为日期减去整数才有实际含义，反之则没有明确定义

        Args:
            other: 左操作数（通常是整数或Z3表达式）

        Returns:
            新的DateExpression对象（如果有意义的话）
        """
        # 由于日期被其他类型减通常没有定义，这里返回NotImplemented
        return NotImplemented

    def __eq__(self, other: Union['DateExpression', z3.ArithRef]) -> 'DateExpressionBoolRef':
        """
        重载等于运算符 (==)

        Args:
            other: 另一个DateExpression对象或Z3表达式

        Returns:
            Z3布尔表达式
        """
        if isinstance(other, DateExpression):
            # 确保两边的约束都会在使用这个表达式时应用
            result = self.date_type.equals(self.date_expr, other.date_expr)
            result = DateExpressionBoolRef(result, [self, other])
            return result
        # 处理其他类型（如Z3 Int表达式）
        result = self.date_type.equals(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result
    
    def __req__(self, other: z3.ArithRef) -> 'DateExpressionBoolRef':
        """
        重载反向等于运算符 (==)

        Args:
            other: Z3表达式作为左操作数

        Returns:
            Z3布尔表达式
        """
        result = self.date_type.equals(other, self.date_expr)
        result = DateExpressionBoolRef(result, [self])
        return result

    def __ne__(self, other: Union['DateExpression', z3.ArithRef]) -> 'DateExpressionBoolRef':
        """
        重载不等于运算符 (!=)

        Args:
            other: 另一个DateExpression对象或Z3表达式

        Returns:
            Z3布尔表达式
        """
        if isinstance(other, DateExpression):
            # 确保两边的约束都会在使用这个表达式时应用
            result = self.date_type.not_equals(self.date_expr, other.date_expr)
            result = DateExpressionBoolRef(result, [self, other])
            return result
        # 处理其他类型（如Z3 Int表达式）
        result = self.date_type.not_equals(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result
    
    def __rne__(self, other: z3.ArithRef) -> 'DateExpressionBoolRef':
        """
        重载反向不等于运算符 (!=)

        Args:
            other: Z3表达式作为左操作数

        Returns:
            Z3布尔表达式
        """
        result = self.date_type.not_equals(other, self.date_expr)
        result = DateExpressionBoolRef(result, [self])
        return result

    def __lt__(self, other: Union['DateExpression', z3.ArithRef]) -> 'DateExpressionBoolRef':
        """
        重载小于运算符 (<)

        Args:
            other: 另一个DateExpression对象或Z3表达式

        Returns:
            Z3布尔表达式
        """
        if isinstance(other, DateExpression):
            result = self.date_type.less_than(self.date_expr, other.date_expr)
            result = DateExpressionBoolRef(result, [self, other])
            return result
        # 处理其他类型（如Z3 Int表达式）
        result = self.date_type.less_than(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result
    
    def __rlt__(self, other: z3.ArithRef) -> 'DateExpressionBoolRef':
        """
        重载反向小于运算符 (<)，即other < self变为self > other

        Args:
            other: Z3表达式作为左操作数

        Returns:
            Z3布尔表达式
        """
        result = self.date_type.greater_than(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result

    def __le__(self, other: Union['DateExpression', z3.ArithRef]) -> 'DateExpressionBoolRef':
        """
        重载小于等于运算符 (<=)

        Args:
            other: 另一个DateExpression对象或Z3表达式

        Returns:
            Z3布尔表达式
        """
        if isinstance(other, DateExpression):
            result = self.date_type.less_than_or_equal(self.date_expr, other.date_expr)
            result = DateExpressionBoolRef(result, [self, other])
            return result
        # 处理其他类型（如Z3 Int表达式）
        result = self.date_type.less_than_or_equal(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result
    
    def __rle__(self, other: z3.ArithRef) -> 'DateExpressionBoolRef':
        """
        重载反向小于等于运算符 (<=)，即other <= self变为self >= other

        Args:
            other: Z3表达式作为左操作数

        Returns:
            Z3布尔表达式
        """
        result = self.date_type.greater_than_or_equal(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result

    def __gt__(self, other: Union['DateExpression', z3.ArithRef]) -> 'DateExpressionBoolRef':
        """
        重载大于运算符 (>)

        Args:
            other: 另一个DateExpression对象或Z3表达式

        Returns:
            Z3布尔表达式
        """
        if isinstance(other, DateExpression):
            result = self.date_type.greater_than(self.date_expr, other.date_expr)
            result = DateExpressionBoolRef(result, [self, other])
            return result
        # 处理其他类型（如Z3 Int表达式）
        result = self.date_type.greater_than(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result
    
    def __rgt__(self, other: z3.ArithRef) -> 'DateExpressionBoolRef':
        """
        重载反向大于运算符 (>)，即other > self变为self < other

        Args:
            other: Z3表达式作为左操作数

        Returns:
            Z3布尔表达式
        """
        result = self.date_type.less_than(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result

    def __ge__(self, other: Union['DateExpression', z3.ArithRef]) -> 'DateExpressionBoolRef':
        """
        重载大于等于运算符 (>=)

        Args:
            other: 另一个DateExpression对象或Z3表达式

        Returns:
            Z3布尔表达式
        """
        if isinstance(other, DateExpression):
            result = self.date_type.greater_than_or_equal(self.date_expr, other.date_expr)
            result = DateExpressionBoolRef(result, [self, other])
            return result
        # 处理其他类型（如Z3 Int表达式）
        result = self.date_type.greater_than_or_equal(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result
    
    def __rge__(self, other: z3.ArithRef) -> 'DateExpressionBoolRef':
        """
        重载反向大于等于运算符 (>=)，即other >= self变为self <= other

        Args:
            other: Z3表达式作为左操作数

        Returns:
            Z3布尔表达式
        """
        result = self.date_type.less_than_or_equal(self.date_expr, other)
        result = DateExpressionBoolRef(result, [self])
        return result

    def __str__(self) -> str:
        """
        将日期表达式转换为字符串

        Returns:
            日期的字符串表示
        """
        return self.to_str()

    def __repr__(self) -> str:
        """
        日期表达式的程序表示

        Returns:
            日期表达式的程序表示字符串
        """
        return f"DateExpression({self.to_str()})"

    def apply_constraints(self, solver: z3.Solver) -> None:
        """
        应用所有约束条件到求解器

        Args:
            solver: Z3 Solver实例
        """
        if not self._constraints_applied:
            for constraint_fn in self.constraints:
                constraint_fn(solver)
            self._constraints_applied = True

    def to_str(self, format: str = '%Y-%m-%d') -> str:
        """
        转换为字符串表示

        Args:
            format: 日期格式

        Returns:
            字符串表示
        """
        return self.date_type.date_to_str(self.date_expr, format)

    def year(self) -> z3.ArithRef:
        """
        获取日期的年份

        Returns:
            年份（Z3整数表达式）
        """
        return self.date_type.extract_year(self.date_expr)

    def month(self) -> z3.ArithRef:
        """
        获取日期的月份

        Returns:
            月份（Z3整数表达式）
        """
        return self.date_type.extract_month(self.date_expr)

    def day(self) -> z3.ArithRef:
        """
        获取日期的日

        Returns:
            日（Z3整数表达式）
        """
        return self.date_type.extract_day(self.date_expr)

    @staticmethod
    def today() -> 'DateExpression':
        """
        获取今天的日期（静态方法）

        Returns:
            表示今天的DateExpression对象
        """
        dt = DateType()
        today = date.today()
        days = dt.date_to_days(today.year, today.month, today.day)
        date_expr = z3.IntVal(days)
        return DateExpression(dt, date_expr)


# 增加一个自定义布尔引用类，跟踪相关的DateExpression对象
class DateExpressionBoolRef():
    """
    用于包装Z3的BoolRef对象，跟踪相关的DateExpression对象
    以便在需要时自动应用约束
    """
    def __init__(self, bool_ref: z3.BoolRef, date_expressions: List['DateExpression']) -> None:
        """
        初始化

        Args:
            bool_ref: Z3布尔表达式
            date_expressions: 相关的DateExpression对象列表
        """
        self._bool_ref = bool_ref
        self.date_expressions = date_expressions

    def __hash__(self) -> int:
        return hash(self._bool_ref)

    def __eq__(self, other: Union['DateExpressionBoolRef', Any]) -> bool:
        if isinstance(other, DateExpressionBoolRef):
            return self._bool_ref == other._bool_ref
        return self._bool_ref == other

    def __str__(self) -> str:
        return str(self._bool_ref)

    def __repr__(self) -> str:
        return repr(self._bool_ref)
    
    def sort(self) -> str:
        """
        获取表达式的类型信息，模拟Z3 BoolRef的sort函数
        
        Returns:
            类型信息字符串，返回"Bool"表示布尔类型
        """
        return "Bool"
        
    # 添加一个获取原始Z3 BoolRef的方法
    def get_z3_bool_ref(self) -> z3.BoolRef:
        """获取内部的Z3 BoolRef对象"""
        return self._bool_ref



# 扩展DateType，添加DateExpression支持
def extend_date_type(date_type_class: type) -> type:
    """
    扩展DateType类，添加返回DateExpression的方法
    
    Args:
        date_type_class: 要扩展的DateType类
        
    Returns:
        扩展后的DateType类
    """

    original_create_date = date_type_class.create_date
    original_parse_date = date_type_class.parse_date
    original_date_var = date_type_class.date_var

    def create_date_wrapper(self, year: int, month: int, day: int) -> 'DateExpression':
        date_expr = original_create_date(self, year, month, day)
        return DateExpression(self, date_expr)

    def parse_date_wrapper(self, date_str: str, format: str = '%Y-%m-%d') -> 'DateExpression':
        date_expr = original_parse_date(self, date_str, format)
        return DateExpression(self, date_expr)

    def date_var_wrapper(self, name: str) -> 'DateExpression':
        date_expr = original_date_var(self, name)
        return DateExpression(self, date_expr)

    def from_string_wrapper(self, date_str: str, format: str = '%Y-%m-%d') -> 'DateExpression':
        return parse_date_wrapper(self, date_str, format)

    # 保存原始方法
    date_type_class._original_create_date = original_create_date
    date_type_class._original_parse_date = original_parse_date
    date_type_class._original_date_var = original_date_var

    # 替换原始方法
    date_type_class.create_date = create_date_wrapper
    date_type_class.parse_date = parse_date_wrapper
    date_type_class.date_var = date_var_wrapper
    date_type_class.from_string = from_string_wrapper

    return date_type_class

# 应用扩展
DateType = extend_date_type(DateType)

def is_date_str(date_str: str) -> str | None:
    """
    判断字符串是否为日期格式
    """
    date_formats = ['%Y-%m-%d', '%Y%m%d', '%Y/%m/%d', '%d-%m-%Y', '%d/%m/%Y']
    for fmt in date_formats:
        try:
            datetime.strptime(date_str, fmt)
            return fmt
        except ValueError:
            continue
    return None
