from typing import Dict, List, Set, Optional, Any, Tuple, Union
import os
import traceback
from sqlglot import parse_one, exp

from lineage_core.logger import logger
from lineage_core.sql_parser import SQLParser
from lineage_core.z3_converter_core import Z3Converter
import lineage_core.sql_preprocessor as sql_preprocessor

class LineageAnalyzer:
    """
    SQL行级血缘分析器，负责分析数据之间的血缘关系
    
    该分析器通过解析SQL语句，构建Z3约束，判断数据之间的血缘关系。
    支持分析SELECT、INSERT、UPDATE等SQL语句类型，通过约束求解确定数据流向。
    
    主要功能：
    1. 解析SQL语句并提取表达式和过滤条件
    2. 将SQL表达式转换为Z3约束
    3. 通过约束求解判断数据血缘关系
    4. 支持多种SQL语句类型和复杂查询结构
    """
    
    def __init__(self) -> None:
        """
        初始化血缘分析器，创建SQL解析器和Z3转换器实例
        
        初始化过程包括：
        - 创建SQL解析器实例，用于解析SQL语句
        - 创建Z3转换器实例，用于构建和求解约束
        - 初始化目标列集合，用于追踪需要分析的列
        """
        logger.debug("初始化行级血缘分析器")
        self.sql_parser = SQLParser()
        self.z3_converter = Z3Converter()
        self.target_columns: Set[str] = set()  # 需要追踪的目标列集合
    
    def _extract_select_lineage(self, select_stmt: exp.Select, tables: List[str], columns: List[str]) -> None:
        """
        从SELECT语句中提取血缘关系信息
        
        递归处理SELECT语句的各个部分，包括SELECT列表、FROM子句、WHERE条件和JOIN条件，
        提取所有相关的表名和列名。
        
        Args:
            select_stmt: 解析后的SELECT语句AST
            tables: 用于存储提取的表名的列表
            columns: 用于存储提取的列名的列表
        """
        # 处理SELECT列表中的表达式
        if select_stmt.expressions:
            for expr in select_stmt.expressions:
                self._extract_expression_lineage(expr, tables, columns)
        
        # 处理FROM子句
        if select_stmt.from_:
            self._extract_from_lineage(select_stmt.from_, tables)
        
        # 处理WHERE子句中的条件表达式
        if select_stmt.where:
            self._extract_expression_lineage(select_stmt.where, tables, columns)
        
        # 处理JOIN子句
        if select_stmt.joins:
            for join in select_stmt.joins:
                if join.table:
                    if hasattr(join.table, 'name'):
                        tables.append(join.table.name.lower())  # 转换为小写以保持一致性
                    elif hasattr(join.table, 'alias'):
                        tables.append(join.table.alias.lower())  # 转换为小写以保持一致性
                
                # 处理JOIN条件
                if join.on:
                    self._extract_expression_lineage(join.on, tables, columns)

    def _extract_from_lineage(self, from_clause: Any, tables: List[str]) -> None:
        """
        从FROM子句中提取表名和子查询信息
        
        处理FROM子句中的表引用和子查询，提取所有相关的表名。
        对于子查询，会递归处理其SELECT语句。
        
        Args:
            from_clause: 解析后的FROM子句AST
            tables: 用于存储提取的表名的列表
        """
        # 处理直接的表名引用
        if hasattr(from_clause, 'name'):
            tables.append(from_clause.name.lower())  # 转换为小写以保持一致性
        elif hasattr(from_clause, 'alias'):
            tables.append(from_clause.alias.lower())  # 转换为小写以保持一致性
        
        # 处理子查询
        if hasattr(from_clause, 'this') and from_clause.this:
            if hasattr(from_clause.this, 'name'):
                tables.append(from_clause.this.name.lower())  # 转换为小写以保持一致性
            elif isinstance(from_clause.this, exp.Select):
                # 递归处理子查询的SELECT语句
                self._extract_select_lineage(from_clause.this, tables, [])

    def _extract_expression_lineage(self, expr: Any, tables: List[str], columns: List[str]) -> None:
        """
        从表达式中提取表名和列名信息
        
        递归处理各种类型的表达式，包括列引用、二元操作、函数调用和子查询等，
        提取所有相关的表名和列名。
        
        Args:
            expr: 解析后的表达式AST
            tables: 用于存储提取的表名的列表
            columns: 用于存储提取的列名的列表
        """
        # 处理列引用
        if isinstance(expr, exp.Column):
            columns.append(expr.name.lower())  # 转换为小写以保持一致性
            if expr.table:
                if hasattr(expr.table, 'name'):
                    tables.append(expr.table.name.lower())  # 转换为小写以保持一致性
                elif hasattr(expr.table, 'alias'):
                    tables.append(expr.table.alias.lower())  # 转换为小写以保持一致性
                else:
                    tables.append(str(expr.table).lower())  # 转换为小写以保持一致性
        
        # 处理二元操作（如 a + b, a = b 等）
        elif isinstance(expr, exp.Binary):
            self._extract_expression_lineage(expr.left, tables, columns)
            self._extract_expression_lineage(expr.right, tables, columns)
        
        # 处理函数调用
        elif isinstance(expr, exp.Anonymous):
            for arg in expr.args:
                self._extract_expression_lineage(arg, tables, columns)
        
        # 处理子查询
        elif isinstance(expr, exp.Select):
            self._extract_select_lineage(expr, tables, columns)
        
        # 处理其他类型的表达式（如列表、聚合等）
        elif hasattr(expr, 'expressions'):
            for sub_expr in expr.expressions:
                self._extract_expression_lineage(sub_expr, tables, columns)
    
    def _process_sql_item(self, sql_item: Union[str, List[str]], dialect: str = None, base_index: int = 0) -> Tuple[bool, List[str]]:
        """
        处理上游SQL项，分析其与目标SQL的血缘关系
        
        该方法支持处理单个SQL字符串或SQL字符串列表（表示OR关系）。
        通过解析SQL、提取约束并进行约束求解，判断上游SQL与目标SQL之间是否存在血缘关系。
        
        Args:
            sql_item: 上游SQL项，可以是SQL字符串或SQL字符串列表
            dialect: SQL方言，如果为None则使用全局配置
            base_index: 基础索引，用于生成SQL ID
            
        Returns:
            元组(has_lineage, sql_ids)，其中has_lineage表示是否有血缘关系，
            sql_ids是相关SQL的ID列表
        """
        sql_id = f"upstream_{base_index}"
        
        # 处理单个SQL字符串
        if isinstance(sql_item, str):
            logger.info("开始分析单个SQL语句")
            
            try:
                # 预处理SQL，添加表前缀到列引用
                processed_sql = sql_preprocessor.add_table_prefix_to_columns(sql_item, dialect=dialect)
                
                # 解析上游SQL
                ast = self.sql_parser.parse(processed_sql, dialect)
                
                # 根据SQL类型选择合适的处理方式
                if isinstance(ast, exp.Insert):
                    # 对于INSERT语句，提取其SELECT部分
                    if ast.expression and isinstance(ast.expression, exp.Select):
                        select_ast = ast.expression
                    else:
                        # 如果INSERT没有SELECT部分（例如VALUES），则无法进行血缘分析
                        logger.warning("INSERT语句没有SELECT部分，无法进行血缘分析")
                        return False, []
                elif isinstance(ast, exp.Update):
                    # 对于UPDATE语句，需要特殊处理
                    logger.warning("暂不支持UPDATE语句的血缘分析")
                    return False, []
                else:
                    # 如果是SELECT语句，直接使用
                    select_ast = ast
            except Exception as e:
                logger.error(f"解析SQL失败: {e}")
                logger.debug(f"SQL内容: {sql_item[:100]}{'...' if len(sql_item) > 100 else ''}")
                return False, []
            
            # 提取过滤条件（从SELECT部分）
            filters = self.sql_parser.extract_filters(select_ast)
            
            # 临时存储SQL中的约束
            upstream_constraints = []
            
            # 处理每个过滤条件
            for filter_expr in filters:
                # 转换过滤条件为Z3约束
                z3_constraint = self.z3_converter.convert_filter_to_z3(filter_expr, self.target_columns)
                
                if z3_constraint is not None:
                    upstream_constraints.append(z3_constraint)
                    logger.debug(f"添加过滤条件约束: {filter_expr}")
                
            # 提取SELECT表达式并添加到约束中
            try:
                select_exprs = self.sql_parser.extract_select_expressions(select_ast)
            except Exception as e:
                logger.error(f"提取SELECT表达式失败: {e}")
                return False, []
            
            # 处理每个与目标列集合相关的SELECT表达式
            for select_expr in select_exprs:
                # 获取输出列名（别名）
                alias_name = None
                if hasattr(select_expr, 'alias') and select_expr.alias:
                    alias_name = select_expr.alias.lower()  # 转换为小写以保持一致性
                elif hasattr(select_expr, 'output_name'):
                    alias_name = select_expr.output_name.lower()  # 转换为小写以保持一致性
                
                # 如果这个列在目标列集合中，分析其血缘
                if alias_name and alias_name in self.target_columns:
                    logger.debug(f"分析目标列 {alias_name} 的血缘")
                    
                    # 收集列名并转换表达式为Z3约束
                    z3_constraint = self.z3_converter.convert_select_expression_to_z3(
                        select_expr, alias_name, target_columns=self.target_columns
                    )
                    
                    # 添加约束到约束列表
                    if z3_constraint is not None:
                        upstream_constraints.append(z3_constraint)
                        logger.debug(f"添加SELECT表达式约束: {alias_name}")
            
            # 添加所有约束到求解器
            for constraint in upstream_constraints:
                self.z3_converter.add_constraint(constraint)
            
            # 检查约束可满足性
            is_satisfiable, _ = self.z3_converter.check_satisfiability()
            
            # 记录目标列集合状态
            logger.debug(f"当前目标列集合: {self.target_columns}")
            
            # 根据约束可满足性判断血缘关系
            if is_satisfiable is False:
                logger.info("上游SQL与目标SQL无血缘关系，可剪枝")
                return False, []
            
            logger.info(f"上游SQL与目标SQL有血缘关系")
            return True, [sql_id]
        
        # 处理SQL列表（OR关系）
        elif isinstance(sql_item, list):
            logger.info(f"开始分析SQL列表（OR关系），共{len(sql_item)}个SQL")
            
            # 保存当前Z3求解器状态
            current_constraints = self.z3_converter.get_constraints_copy()
            
            all_or_constraints = []
            
            # 收集所有SQL项的约束
            for i, sub_sql in enumerate(sql_item):
                logger.debug(f"处理SQL列表中的第{i+1}个SQL")
                
                # 预处理上游SQL
                try:
                    processed_sql = sql_preprocessor.add_table_prefix_to_columns(sub_sql, dialect=dialect)
                    
                    # 解析SQL并提取约束
                    ast = self.sql_parser.parse(processed_sql, dialect)
                    
                    # 处理SQL类型
                    if isinstance(ast, exp.Insert):
                        if ast.expression and isinstance(ast.expression, exp.Select):
                            select_ast = ast.expression
                        else:
                            logger.warning(f"SQL {i}: INSERT语句没有SELECT部分，跳过")
                            continue
                    elif isinstance(ast, exp.Update):
                        logger.warning(f"SQL {i}: 暂不支持UPDATE语句，跳过")
                        continue
                    else:
                        select_ast = ast
                    
                    # 提取过滤条件
                    filters = self.sql_parser.extract_filters(select_ast)
                    
                    # 临时存储约束
                    sql_constraints = []
                    
                    # 处理每个过滤条件
                    for filter_expr in filters:
                        z3_constraint = self.z3_converter.convert_filter_to_z3(filter_expr, self.target_columns)
                        
                        if z3_constraint is not None:
                            sql_constraints.append(z3_constraint)
                            logger.debug(f"SQL {i}: 添加过滤条件约束")
                    
                    # 提取SELECT表达式
                    select_exprs = self.sql_parser.extract_select_expressions(select_ast)
                    
                    # 处理与目标列相关的SELECT表达式
                    for select_expr in select_exprs:
                        alias_name = None
                        if hasattr(select_expr, 'alias') and select_expr.alias:
                            alias_name = select_expr.alias.lower()  # 转换为小写以保持一致性
                        elif hasattr(select_expr, 'output_name'):
                            alias_name = select_expr.output_name.lower()  # 转换为小写以保持一致性
                        
                        if alias_name and alias_name in self.target_columns:
                            z3_constraint = self.z3_converter.convert_select_expression_to_z3(
                                select_expr, alias_name
                            )
                            
                            if z3_constraint is not None:
                                sql_constraints.append(z3_constraint)
                                logger.debug(f"SQL {i}: 添加SELECT表达式约束: {alias_name}")
                    
                    # 将这个SQL的约束组合为AND关系
                    if sql_constraints:
                        combined_sql_constraint = self.z3_converter.combine_constraints_with_and(sql_constraints)
                        if combined_sql_constraint is not None:
                            all_or_constraints.append(combined_sql_constraint)
                            logger.debug(f"SQL {i}: 成功组合约束")
                
                except Exception as e:
                    logger.error(f"处理SQL {sql_id}_{i} 失败: {e}")
                    logger.debug(traceback.format_exc())
                    continue
            
            # 如果没有有效约束，缺省为有血缘关系
            if not all_or_constraints:
                logger.info("SQL列表中没有有效约束，默认为有血缘关系")
                return True, []
            
            # 重置求解器状态，并添加原始约束
            self.z3_converter.reset()
            for constraint in current_constraints:
                self.z3_converter.add_constraint(constraint)
            
            # 组合所有SQL的约束（OR关系）并添加到求解器
            combined_or_constraint = self.z3_converter.combine_constraints_with_or(all_or_constraints)
            if combined_or_constraint is not None:
                self.z3_converter.add_constraint(combined_or_constraint)
                logger.debug("成功组合所有SQL的OR约束")
            
            # 检查约束可满足性
            is_satisfiable, _ = self.z3_converter.check_satisfiability()
            
            # 根据约束可满足性判断血缘关系
            if is_satisfiable:
                logger.info("SQL列表与目标SQL有血缘关系")
                return True, [sql_id]
            else:
                logger.info("SQL列表与目标SQL无血缘关系")
                return False, []
        
        # 处理其他类型（不应该出现）
        logger.warning(f"未知的SQL项类型: {type(sql_item)}")
        return False, []

    def analyze_lineage(self, sqls: List[Any], dialect: str = None, schema: Optional[Dict[str, str]] = None) -> Dict[str, List[str]]:
        """
        分析一组SQL的数据血缘关系
        
        该方法是血缘分析的主入口，支持分析多个SQL语句之间的血缘关系，
        包括处理并行SQL（OR关系）。通过调用_process_sql_item方法处理每个SQL项，
        并汇总血缘分析结果。
        
        Args:
            sqls: SQL语句列表，可以包含嵌套列表表示OR关系
            dialect: SQL方言，如果为None则使用全局配置
            schema: 字段名称和对应的字段类型的字典，包括 INT FLOAT STRING 三种类型
            
        Returns:
            血缘关系字典，包含以下键：
            - target: 相关的上游SQL ID列表
            - collected_columns: 收集到的列名列表
            - result: 是否有血缘关系的布尔值
            - error: 可选，发生错误时的错误信息
        """
        logger.info("开始行级血缘分析")
       
        try:
            # 重置Z3求解器和上下文
            self.z3_converter.reset()
            self.target_columns = set()
            
            # 预处理SQL语句
            processed_sqls = sql_preprocessor.preprocess_sql_statements(sqls, dialect=dialect)
            sqls = processed_sqls
            logger.debug(f"预处理后的SQL数量: {len(sqls)}")
             
            # 设置字段类型信息
            # 注意: 设置schema有局限性，对于子查询生成的新表名，外部配置的schema可能无法匹配
            if schema:
                logger.info(f"使用提供的schema信息: {len(schema)}个字段")
                self.z3_converter.set_column_type(schema)
            else:
                logger.info("未提供schema信息，将尝试自动推断字段类型")
            
            # 初始化血缘关系字典
            lineage_map: Dict[str, List[str]] = {"target": [], "collected_columns": [], "result": True}

            # 如果SQL列表为空，直接返回
            if len(sqls) == 0:
                logger.warning("SQL列表为空，无法进行血缘分析")
                return lineage_map
            
            # 分析每个SQL项
            for i, sql_item in enumerate(sqls):
                logger.info(f"分析第{i+1}个SQL项")
                has_lineage, sql_ids = self._process_sql_item(sql_item, dialect, i)
                
                # 如果有血缘关系，添加到血缘图
                if has_lineage:
                    lineage_map["target"].extend(sql_ids)
                    logger.info(f"上游SQL项 {i} 与目标SQL有血缘关系")
                else:
                    logger.info(f"上游SQL项 {i} 与目标SQL无血缘关系, 停止分析")
                    lineage_map['result'] = False
                    return lineage_map
            
            # 添加收集到的列名
            lineage_map["collected_columns"] = list(self.target_columns)
            logger.info(f"血缘分析完成，收集到{len(self.target_columns)}个相关列")
            return lineage_map

        except Exception as e:
            logger.error(f"行级血缘分析失败: {e}")
            logger.error(f"错误堆栈信息: {traceback.format_exc()}")
            # 默认认为有血缘关系，确保血缘分析不会遗漏
            return {"target": [], "error": [str(e)], "result": True}

    def _get_full_column_name(self, column_expr: exp.Column) -> Optional[str]:
        """
        获取列的完整名称，包括schema和表名前缀
        
        构建完整的列名，格式为schema_table_name_column_name（如果有schema和表名）。
        所有名称都转换为小写以保持一致性。
        
        Args:
            column_expr: 列表达式对象
            
        Returns:
            完整的列名，如果无法获取则返回None
        """
        try:
            # 获取列名
            column_name = column_expr.output_name.lower() if hasattr(column_expr, 'output_name') else None
            
            # 如果无法获取列名，返回None
            if not column_name:
                logger.warning("无法获取列名")
                return None
            
            # 构建完整的列名，包含schema和表名前缀
            prefixes = []
            
            # 处理schema前缀
            if hasattr(column_expr, 'db') and column_expr.db:
                schema_name = column_expr.db.output_name.lower() if hasattr(column_expr.db, 'output_name') else str(column_expr.db).lower()
                if schema_name:
                    prefixes.append(schema_name)  # 已经转换为小写
            
            # 处理表名前缀
            if hasattr(column_expr, 'table') and column_expr.table:
                table_name = column_expr.table.output_name.lower() if hasattr(column_expr.table, 'output_name') else str(column_expr.table).lower()
                if table_name:
                    prefixes.append(table_name)  # 已经转换为小写
            
            # 组合完整的列名
            if column_name and prefixes:
                full_column_name = "_".join(prefixes + [column_name])  # 已经转换为小写
                logger.debug(f"处理带schema和表名的列引用: {full_column_name}")
                return full_column_name
            else:
                # 只有列名，无前缀
                return column_name
            
        except Exception as e:
            logger.warning(f"获取列名时出错: {str(e)}")
            return None
