import logging
import sys
from typing import Optional


def setup_logger(name: str = "row_lineage", log_level: str = "INFO") -> logging.Logger:
    """
    设置并返回一个配置好的logger对象
    
    Args:
        name: logger的名称
        log_level: 日志级别，可以是 DEBUG, INFO, WARNING, ERROR, CRITICAL
        
    Returns:
        配置好的logger对象
    """
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    
    level = level_map.get(log_level.upper(), logging.INFO)
    
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除已有的handler
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    
    # 设置日志格式
    formatter = logging.Formatter(
        '%(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%M:%S'
    )
    console_handler.setFormatter(formatter)
    
    # 添加处理器到logger
    logger.addHandler(console_handler)
    
    return logger


# 默认logger实例
logger = setup_logger(log_level="DEBUG") 