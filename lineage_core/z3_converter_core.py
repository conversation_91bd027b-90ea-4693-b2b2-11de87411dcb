"""
SQL到Z3约束转换核心模块

本模块实现了将SQL语法树转换为Z3约束条件的核心功能，是数据血缘分析的关键组件。
通过将SQL查询条件和表达式转换为Z3求解器可处理的约束形式，实现了对数据流动关系的精确分析。

主要功能：
1. 将SQL表达式（如WHERE子句、JOIN条件、SELECT投影等）转换为Z3约束
2. 支持各种SQL运算符和函数的转换，包括比较、逻辑、算术运算等
3. 处理NULL值和类型转换
4. 提供约束可满足性检查，用于验证数据流动路径的有效性
5. 自动推断和管理列的数据类型

核心类：
- Z3Converter: 负责SQL到Z3约束的转换和管理

依赖：
- z3: 微软Z3约束求解器
- sqlglot: SQL解析和表达式处理库

使用场景：
本模块主要用于数据血缘分析，通过将SQL查询条件转换为Z3约束，
可以精确分析数据在不同表和列之间的流动关系，识别数据转换和过滤规则。
"""

from datetime import datetime
import traceback
import z3
from sqlglot import expressions as exp
from typing import Dict, Any, Optional, Tuple, Set, List, Union

from lineage_core.logger import logger
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver
from lineage_core.z3_datetype import DateType

class Z3Converter:
    """
    负责将SQL表达式转换为Z3约束的转换器。

    该类将SQL语法树转换为Z3约束条件，用于后续的血缘分析处理。
    支持各种SQL表达式类型，包括比较、逻辑、算术运算等。

    主要功能：
    1. 管理Z3求解器和约束条件
    2. 自动推断和管理列的数据类型
    3. 处理NULL值和可为NULL的数据类型
    4. 将SQL过滤条件和SELECT表达式转换为Z3约束
    5. 组合多个约束条件（AND/OR逻辑）
    6. 检查约束条件的可满足性

    属性：
        solver: Z3求解器实例
        column_types: 列名到类型的映射字典
        schema: 用户提供的schema信息
        nullable_types: 可为NULL的数据类型和相关函数
        z3_context: 列名到Z3变量的映射
        z3_context_null: nullable列名到Z3变量的映射
        constraints: 所有添加的约束列表
    """

    def __init__(self) -> None:
        """初始化Z3转换器。"""
        logger.info("初始化Z3转换器")
        self.solver: TypeAwareEnhancedSolver = TypeAwareEnhancedSolver()  # 默认关闭不满足约束跟踪
        # 存储列名到类型的映射
        self.column_types: Dict[str, str] = {}
        # 存储用户提供的schema信息
        self.schema: Dict[str, str] = {}
        # 存储可为NULL的数据类型和相关函数
        self.nullable_types: Dict[str, Dict[str, Any]] = {}
        # 存储列名到Z3变量的映射
        self.z3_context: Dict[str, Any] = {}
        # 存储nullable列名到Z3变量的映射
        self.z3_context_null: Dict[str, Any] = {}
        # 存储所有添加的约束，用于跟踪和管理
        self.constraints: List[z3.BoolRef] = []
        # 日期类型
        self.date_type = DateType()
        # 表达式转换器 - 延迟导入避免循环依赖
        self._expr_converter = None

    @property
    def expr_converter(self):
        """懒加载表达式转换器，避免循环导入"""
        if self._expr_converter is None:
            from lineage_core.z3_converter_expr import Z3ExpressionConverter
            self._expr_converter = Z3ExpressionConverter(self)
        return self._expr_converter
        
    def reset(self) -> None:
        """重置Z3求解器和上下文。"""
        logger.debug("重置Z3求解器和上下文")
        self.solver = TypeAwareEnhancedSolver()  # 重建求解器，默认关闭跟踪
        self.column_types = {}
        self.nullable_types = {}
        self.z3_context = {}
        self.z3_context_null = {}
        self.constraints = []

    def set_column_type(self, schema: Dict[str, str]) -> None:
        """
        设置列的类型信息。

        Args:
            schema: 字段名称和对应的字段类型的字典，包括 INT FLOAT STRING DATE 四种类型
        """
        logger.info(f"设置列类型信息: {len(schema)}个字段")

        # 将schema中的列名转换为小写
        lowercase_schema = {k.lower(): v for k, v in schema.items()}
        self.schema = lowercase_schema

        # 将schema中的类型映射到Z3类型
        for column_name, column_type in lowercase_schema.items():
            if column_type.upper() == "INT":
                self.column_types[column_name] = "Int"
            elif column_type.upper() == "FLOAT":
                self.column_types[column_name] = "Real"
            elif column_type.upper() == "STRING":
                self.column_types[column_name] = "String"
            elif column_type.upper() == "DATE":
                self.column_types[column_name] = "Date"
            else:
                logger.warning(f"未知的列类型: {column_type}，将使用默认类型String")
                self.column_types[column_name] = "String"

            logger.debug(f"输入Schema 设置列 {column_name} 的类型为 {self.column_types[column_name]}")

    def _infer_column_type(self, column_name: str, value: Optional[Any] = None) -> str:
        """
        根据列名和值推断类型。

        Args:
            column_name: 列名
            value: 与该列比较的值（可选）

        Returns:
            推断的类型（"Int", "Real", "String", "Bool", "Date"）
        """
        # 确保列名为小写
        column_name = column_name.lower() if column_name else column_name

        # 如果已经在列类型映射中，直接返回
        if column_name in self.column_types:
            return self.column_types[column_name]

        # 优先根据值推断类型
        if value is not None:
            if isinstance(value, int):
                return "Int"
            elif isinstance(value, float):
                return "Real"
            elif isinstance(value, bool):
                return "Bool"
            elif isinstance(value, str):
                # 尝试解析字符串为日期
                try:
                    datetime.strptime(value, "%Y-%m-%d")
                    logger.debug(f"字符串 '{value}' 被识别为日期类型")
                    return "Date"
                except Exception as e:
                    logger.debug(f"尝试将字符串解析为日期时出错: {str(e)}")
                return "String"

        # 如果无法根据值推断，则根据列名推断
        column_name_lower = column_name.lower()

        # 日期时间类型
        if any(x in column_name_lower for x in ['date', 'start_dt', 'end_dt', 'created_at', 'updated_at']):
            return "Date"

        # 浮点数类型
        if any(x in column_name_lower for x in ['price', 'amount', 'cost', 'fee', 'tax', 'total', 'rate']):
            return "Real"

        # 字符串类型 - 通常状态和名称都是字符串
        if any(x in column_name_lower for x in ['status', 'name', 'code', 'type', 'description']):
            return "String"

        # 布尔类型
        if any(x in column_name_lower for x in ['has_', 'enable', 'active', 'flag']):
            return "Bool"

        # 数量和ID通常是整数
        if any(x in column_name_lower for x in ['quantity', 'qty']):
            return "Int"

        # 默认为字符串类型
        return "String"

    def _create_nullable_type(self, base_sort_name: str, base_sort: z3.SortRef) -> Dict[str, Any]:
        """
        创建一个可为空的数据类型。

        Args:
            base_sort_name: 基础类型名称
            base_sort: Z3基础类型

        Returns:
            可为空的数据类型、构造函数和判断函数的字典
        """
        # 检查是否已经创建过该类型
        if base_sort_name in self.nullable_types:
            return self.nullable_types[base_sort_name]

        # 创建新的可为NULL数据类型
        nullable = z3.Datatype(f'Nullable{base_sort_name}')
        nullable.declare('null')  # NULL值构造函数
        nullable.declare('some', ('value', base_sort))  # 非NULL值构造函数
        nullable = nullable.create()

        # 保存类型和相关函数
        type_info = {
            'type': nullable,  # 类型
            'null': nullable.null,  # NULL构造函数
            'some': nullable.some,  # 非NULL构造函数
            'is_null': nullable.is_null,  # NULL判断函数
            'is_some': nullable.is_some  # 非NULL判断函数
        }

        self.nullable_types[base_sort_name] = type_info
        return type_info

    def _get_nullable_type_for_column(self, base_sort_name: str) -> Dict[str, Any]:
        """
        获取列的可为NULL数据类型。

        Args:
            base_sort_name: 基础类型名称

        Returns:
            可为NULL的数据类型信息字典
        """
        # 优先检查是否已经创建过该类型
        if base_sort_name in self.nullable_types:
            return self.nullable_types[base_sort_name]

        # 如果没有创建过，则创建新的可为NULL数据类型
        if base_sort_name == 'Int':
            return self._create_nullable_type('Int', z3.IntSort())
        elif base_sort_name == 'Real':
            return self._create_nullable_type('Real', z3.RealSort())
        elif base_sort_name == 'Bool':
            return self._create_nullable_type('Bool', z3.BoolSort())
        else:  # String或其他类型
            return self._create_nullable_type('String', z3.StringSort())

    def _get_or_create_z3_var(self, column_name: str, ref_column_type: Optional[str] = None) -> Any:
        """
        获取或创建列的Z3变量。

        Args:
            column_name: 列名
            ref_column_type: 参考列类型（可选）

        Returns:
            列的Z3变量
        """
        # 确保列名为小写
        column_name = column_name.lower() if column_name else column_name

        # 如果列还没有变量，需要先推断类型
        if ref_column_type is None:
            column_type = self._infer_column_type(column_name)
        else:
            column_type = ref_column_type

        # 优先返回已存在的变量
        if column_type.startswith('Nullable'):
            if column_name in self.z3_context_null:
                return self.z3_context_null[column_name]
        else:
            if column_name in self.z3_context:
                return self.z3_context[column_name]

        # 创建新变量
        if column_type.startswith('Nullable'):
            base_sort_name = column_type.replace('Nullable', '')
            nullable_type_info = self._get_nullable_type_for_column(base_sort_name)
            z3_var = z3.Const(column_name, nullable_type_info['type'])
            self.z3_context_null[column_name] = z3_var
            logger.debug(f"为列 {column_name} 创建可为NULL的Z3变量，类型为 {column_type}")
        else:
            # 使用普通数据类型
            if column_type == "Int":
                z3_var = z3.Int(column_name)
            elif column_type == "Real":
                z3_var = z3.Real(column_name)
            elif column_type == "Bool":
                z3_var = z3.Bool(column_name)
            elif column_type == "Date":
                z3_var = self.date_type.date_var(column_name)
            else:  # 默认为String
                z3_var = z3.String(column_name)
            logger.debug(f"为列 {column_name} 创建普通Z3变量，类型为 {column_type}")
            # 记录列变量和类型
            self.z3_context[column_name] = z3_var

        return z3_var

    def add_constraint(self, constraint: Optional[z3.BoolRef]) -> None:
        """
        添加约束到求解器。

        Args:
            constraint: Z3约束表达式
        """
        if constraint is None:
            logger.warning("尝试添加空约束，已忽略")
            return

        # 检查是否是 a == a 这种恒真条件
        if isinstance(constraint, z3.BoolRef) and constraint.decl().kind() == z3.Z3_OP_EQ:
            args = constraint.children()
            if len(args) == 2 and args[0].eq(args[1]):
                logger.debug("忽略恒真条件: a == a")
                return

        logger.debug(f"添加约束: {constraint}")

        try:
            self.solver.add(constraint)
            self.constraints.append(constraint)  # 同时保存到约束列表
        except Exception as e:
            logger.error(f"添加约束时发生错误: {str(e)}, 约束: {constraint}")

    def check_satisfiability(self) -> Tuple[Optional[bool], Optional[Dict[str, Any]]]:
        """
        检查当前约束是否可满足。

        Returns:
            元组 (is_satisfiable, model)，其中is_satisfiable表示是否可满足，
            model是满足约束的变量赋值（如果可满足）
        """
        logger.debug("检查约束可满足性")

        try:
            result = self.solver.check()

            if result == z3.sat:
                return True, None
            elif result == z3.unsat:
                logger.debug("约束不可满足")
                return False, None
            else:
                logger.warning("约束求解未知")
                return None, None
        except Exception as e:
            logger.error(f"检查约束可满足性时发生错误: {str(e)}")
            return None, None

    def convert_filter_to_z3(self, filter_expr: exp.Expression, target_columns: Optional[Set[str]] = None) -> Optional[z3.BoolRef]:
        """
        将SQL过滤条件转换为Z3约束。

        Args:
            filter_expr: SQL过滤条件表达式
            target_columns: 用于收集表达式中涉及的列名的集合

        Returns:
            Z3约束表达式，如果无法转换则返回None
        """
        try:
            # 初始化target_columns（如果为None）
            if target_columns is None:
                target_columns = set()

            # 收集表达式中的列名
            self._collect_columns_from_expr(filter_expr, target_columns)

            # 转换过滤条件为Z3约束
            z3_constraint = self.expr_converter.convert_expression_to_z3(filter_expr)

            logger.debug(f"过滤条件转换结果: {z3_constraint}")
            return z3_constraint

        except Exception as e:
            # 输出更详细的错误信息
            logger.warning(f"无法将过滤条件转换为Z3约束: {str(e)}")
            logger.debug(f"原始表达式: {filter_expr.sql() if hasattr(filter_expr, 'sql') else str(filter_expr)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
            return None

    def _get_full_column_name(self, column_expr: exp.Column) -> Optional[str]:
        """
        获取列的完整名称，包括schema和表名前缀。

        Args:
            column_expr: 列表达式

        Returns:
            完整的列名（如果有schema和表名，则为 schema_table_name_column_name 格式）
        """
        try:
            # 获取列名
            column_name = column_expr.output_name.lower() if hasattr(column_expr, 'output_name') else None

            # 构建完整的列名，包含schema和表名前缀
            prefixes = []

            # 处理schema前缀
            if hasattr(column_expr, 'db') and column_expr.db:
                schema_name = column_expr.db.output_name.lower() if hasattr(column_expr.db, 'output_name') else str(column_expr.db).lower()
                if schema_name:
                    prefixes.append(schema_name)

            # 处理表名前缀
            if hasattr(column_expr, 'table') and column_expr.table:
                table_name = column_expr.table.output_name.lower() if hasattr(column_expr.table, 'output_name') else str(column_expr.table).lower()
                if table_name:
                    prefixes.append(table_name)

            # 组合完整的列名
            if column_name and prefixes:
                column_name = "_".join(prefixes + [column_name])
                logger.debug(f"处理带schema和表名的列引用: {column_name}")

            return column_name
        except Exception as e:
            logger.warning(f"获取列名时出错: {str(e)}")
            return None

    def _collect_columns_from_expr(self, expr: exp.Expression, target_columns: Set[str]) -> None:
        """
        从表达式中收集列名。

        Args:
            expr: SQL表达式
            target_columns: 已收集的列名集合，将被直接修改
        """
        try:
            # 如果是列引用，添加列名到集合
            if isinstance(expr, exp.Column):
                column_name = self._get_full_column_name(expr)
                if column_name:
                    target_columns.add(column_name)

            # 递归处理复杂表达式的子表达式
            if hasattr(expr, 'args'):
                for arg_name, arg_value in expr.args.items():
                    if isinstance(arg_value, exp.Expression):
                        self._collect_columns_from_expr(arg_value, target_columns)
                    elif isinstance(arg_value, list):
                        for item in arg_value:
                            if isinstance(item, exp.Expression):
                                self._collect_columns_from_expr(item, target_columns)
        except Exception as e:
            logger.debug(f"收集列名时出错: {str(e)}")

    def convert_select_expression_to_z3(self, select_expr: exp.Expression, alias_name: str, target_columns: Optional[Set[str]] = None) -> Optional[z3.BoolRef]:
        """
        将SELECT表达式转换为Z3约束。

        Args:
            select_expr: SELECT表达式
            alias_name: 表达式别名
            target_columns: 用于收集表达式中涉及的列名的集合

        Returns:
            Z3约束表达式，如果无法转换则返回None
        """
        try:
            # 初始化target_columns（如果为None）
            if target_columns is None:
                target_columns = set()

            # 收集表达式中的列名
            self._collect_columns_from_expr(select_expr, target_columns)

            # 处理别名表达式
            actual_expr = select_expr
            if isinstance(select_expr, exp.Alias):
                actual_expr = select_expr.args.get("this")

            # 创建一个等式表达式：alias = actual_expr
            eq_expr = exp.EQ(
                this=exp.Column(this=alias_name),
                expression=actual_expr
            )

            # 使用现有的表达式转换逻辑处理等式
            return self.expr_converter.convert_expression_to_z3(eq_expr, target_columns=target_columns)

        except Exception as e:
            logger.warning(f"无法将SELECT表达式转换为Z3约束: {str(e)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
            return None

    def combine_constraints_with_and(self, constraints: List[z3.BoolRef]) -> Optional[z3.BoolRef]:
        """
        将多个约束条件通过AND逻辑组合在一起。

        Args:
            constraints: 约束条件列表

        Returns:
            组合后的约束条件，如果列表为空则返回None
        """
        if not constraints:
            return None

        # 过滤掉None值
        valid_constraints = [c for c in constraints if c is not None]

        if not valid_constraints:
            return None

        if len(valid_constraints) == 1:
            return valid_constraints[0]

        # 使用type_aware_solver.And组合所有约束
        try:
            return self.solver.And(*valid_constraints)
        except Exception as e:
            logger.warning(f"组合AND约束时出错: {str(e)}")
            return valid_constraints[0] if valid_constraints else None

    def combine_constraints_with_or(self, constraints: List[z3.BoolRef]) -> Optional[z3.BoolRef]:
        """
        将多个约束条件通过OR逻辑组合在一起。

        Args:
            constraints: 约束条件列表

        Returns:
            组合后的约束条件，如果列表为空则返回None
        """
        if not constraints:
            return None

        # 过滤掉None值
        valid_constraints = [c for c in constraints if c is not None]

        if not valid_constraints:
            return None

        if len(valid_constraints) == 1:
            return valid_constraints[0]

        # 使用type_aware_solver.Or组合所有约束
        try:
            return self.solver.Or(*valid_constraints)
        except Exception as e:
            logger.warning(f"组合OR约束时出错: {str(e)}")
            return valid_constraints[0] if valid_constraints else None

    def get_constraints_copy(self) -> List[z3.BoolRef]:
        """
        获取当前约束条件的副本。

        Returns:
            当前约束条件的列表副本
        """
        return self.constraints.copy()

    def get_last_constraints(self) -> Optional[z3.BoolRef]:
        """
        获取最后添加的约束。

        Returns:
            最后添加的约束，如果没有约束则返回None
        """
        return self.constraints[-1] if self.constraints else None