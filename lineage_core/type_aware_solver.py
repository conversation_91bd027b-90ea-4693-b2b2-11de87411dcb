"""
类型感知求解器模块

该模块实现了一个类型感知的增强型Z3求解器，能够处理自定义类型（如DateExpression）与Z3原生类型的混合运算。
主要功能包括：
1. 类型推断和传播
2. 约束缓存和分析
3. 处理混合类型比较和布尔运算
4. 跟踪不满足约束并提供诊断信息
"""

import z3
from typing import Union, List, Set, Dict, Any, Callable, Optional, Tuple

# 需要从z3_datetype导入的类和函数
from lineage_core.z3_datetype import DateExpression, DateExpressionBoolRef, DateType
from lineage_core.logger import logger


class TypeAwareEnhancedSolver:
    """
    类型感知的增强型求解器，通过约束缓存机制解决类型转换问题
    """
    def __init__(self, track_unsat: bool = True) -> None:
        """
        初始化类型感知求解器
        
        Args:
            track_unsat: 是否跟踪不满足约束条件，默认为False
        """
        # 保存原始Z3求解器作为内部求解器
        self._internal_solver = z3.Solver()
        # 约束缓存，存储所有添加的约束但不立即添加到求解器
        self._constraint_cache = []
        # 变量类型映射，跟踪每个变量的类型信息
        self._var_type_map = {}
        # 变量引用关系图，记录变量间的依赖关系
        self._var_dependency_graph = {}
        # 跟踪不满足约束条件的选项
        self._track_unsat = track_unsat
        # 跟踪约束与名称的映射
        self._tracked_constraints = []
        
        # 如果启用跟踪，设置unsat_core选项
        if self._track_unsat:
            self._internal_solver.set(unsat_core=True)
        
        # 应用Z3比较行为补丁，支持Z3变量与DateExpression对象直接比较
        self._wrap_z3_comparison()
    
    def _wrap_z3_comparison(self):
        """
        使用猴子补丁修改Z3的比较行为，使其支持与DateExpression对象比较
        """
        # 保存原始的比较方法
        original_gt = z3.ArithRef.__gt__
        original_ge = z3.ArithRef.__ge__
        original_lt = z3.ArithRef.__lt__
        original_le = z3.ArithRef.__le__
        original_eq = z3.ArithRef.__eq__
        
        def patched_gt(self, other):
            if isinstance(other, DateExpression):
                # 如果右侧是DateExpression，使用DateExpression的反向比较
                return other.__rgt__(self)
            return original_gt(self, other)
        
        def patched_ge(self, other):
            if isinstance(other, DateExpression):
                # 如果右侧是DateExpression，使用DateExpression的反向比较
                return other.__rge__(self)
            return original_ge(self, other)
        
        def patched_lt(self, other):
            if isinstance(other, DateExpression):
                # 如果右侧是DateExpression，使用DateExpression的反向比较
                return other.__rlt__(self)
            return original_lt(self, other)
        
        def patched_le(self, other):
            if isinstance(other, DateExpression):
                # 如果右侧是DateExpression，使用DateExpression的反向比较
                return other.__rle__(self)
            return original_le(self, other)
        
        def patched_eq(self, other):
            if isinstance(other, DateExpression):
                # 如果右侧是DateExpression，使用DateExpression的反向比较
                return other.__req__(self)
            return original_eq(self, other)
        
        # 应用补丁
        z3.ArithRef.__gt__ = patched_gt
        z3.ArithRef.__ge__ = patched_ge
        z3.ArithRef.__lt__ = patched_lt
        z3.ArithRef.__le__ = patched_le
        z3.ArithRef.__eq__ = patched_eq
     
    # 添加自定义比较方法，支持Z3变量与DateExpression对象直接比较
    def date_compare(self, left, right, operator='<'):
        """
        自定义比较方法，支持Z3变量与DateExpression对象直接比较
        
        Args:
            left: 左操作数（Z3变量或DateExpression）
            right: 右操作数（Z3变量或DateExpression）
            operator: 比较运算符，可选值: '<', '<=', '>', '>=', '==', '!='
            
        Returns:
            比较结果的布尔表达式
        """
        date_type = DateType()
        
        # 如果左操作数是DateExpression
        if isinstance(left, DateExpression):
            if operator == '<':
                return left < right
            elif operator == '<=':
                return left <= right
            elif operator == '>':
                return left > right
            elif operator == '>=':
                return left >= right
            elif operator == '==':
                return left == right
            elif operator == '!=':
                return left != right
        
        # 如果右操作数是DateExpression
        if isinstance(right, DateExpression):
            # 将左操作数转换为DateExpression
            left_date = DateExpression(date_type, left)
            
            if operator == '<':
                return left_date < right
            elif operator == '<=':
                return left_date <= right
            elif operator == '>':
                return left_date > right
            elif operator == '>=':
                return left_date >= right
            elif operator == '==':
                return left_date == right
            elif operator == '!=':
                # 使用not和等于来表示不等
                return not (left_date == right)
        
        # 如果都不是DateExpression，返回普通的Z3比较
        if operator == '<':
            return left < right
        elif operator == '<=':
            return left <= right
        elif operator == '>':
            return left > right
        elif operator == '>=':
            return left >= right
        elif operator == '==':
            return left == right
        elif operator == '!=':
            return left != right

    def _record_dependency(self, var1, var2) -> None:
        """
        记录变量之间的依赖关系
        
        Args:
            var1: 第一个变量
            var2: 第二个变量
        """
        var1_str = str(var1)
        var2_str = str(var2)

        if var1_str not in self._var_dependency_graph:
            self._var_dependency_graph[var1_str] = set()

        self._var_dependency_graph[var1_str].add(var2_str)

    def _propagate_types(self) -> None:
        """根据已知类型信息传播类型到相关变量"""
        # 持续传播直到没有新的类型信息可以推断
        changed = True
        iteration = 0
        max_iterations = 10  # 防止无限循环
        
        while changed and iteration < max_iterations:
            iteration += 1
            changed = False

            # 遍历所有变量依赖关系
            for var, dependencies in self._var_dependency_graph.items():
                # 如果当前变量已知类型是date
                if var in self._var_type_map and self._var_type_map[var] == 'date':
                    # 传播类型到依赖的变量
                    for dep_var in dependencies:
                        if dep_var not in self._var_type_map or self._var_type_map[dep_var] != 'date':
                            self._var_type_map[dep_var] = 'date'
                            changed = True
            
            # 反向传播：如果一个变量依赖于日期类型的变量，它也应该是日期类型
            for var in list(self._var_dependency_graph.keys()):
                # 检查依赖项中是否有日期类型
                dependencies = self._var_dependency_graph.get(var, set())
                for dep_var in dependencies:
                    if dep_var in self._var_type_map and self._var_type_map[dep_var] == 'date':
                        # 如果依赖项是日期类型，当前变量也应该是日期类型
                        if var not in self._var_type_map or self._var_type_map[var] != 'date':
                            self._var_type_map[var] = 'date'
                            changed = True
                            break
            
            # 检查算术表达式：如 b = a + 3，如果a是日期，b也应该是日期
            # 这需要一个更深入的分析，这里用一个简单的启发式方法
            for expr in self._constraint_cache:
                if z3.is_expr(expr) and z3.is_eq(expr):
                    left, right = expr.children()
                    left_str = str(left)
                    right_str = str(right)
                    
                    # 如果右侧是算术表达式，检查其操作数是否有日期类型
                    if z3.is_arith(right) and len(right.children()) == 2:
                        op_left, op_right = right.children()
                        op_left_str = str(op_left)
                        
                        if op_left_str in self._var_type_map and self._var_type_map[op_left_str] == 'date':
                            if left_str not in self._var_type_map or self._var_type_map[left_str] != 'date':
                                self._var_type_map[left_str] = 'date'
                                changed = True
                    
                    # 同样检查左侧
                    if z3.is_arith(left) and len(left.children()) == 2:
                        op_left, op_right = left.children()
                        op_left_str = str(op_left)
                        
                        if op_left_str in self._var_type_map and self._var_type_map[op_left_str] == 'date':
                            if right_str not in self._var_type_map or self._var_type_map[right_str] != 'date':
                                self._var_type_map[right_str] = 'date'
                                changed = True

    def _analyze_constraint(self, expr) -> None:
        """
        分析约束中的变量和类型关系

        Args:
            expr: 要分析的表达式
        """
        # 如果是DateExpression类型，记录其类型
        if isinstance(expr, DateExpression):
            var_name = str(expr.date_expr)
            self._var_type_map[var_name] = 'date'
            return

        # 如果是DateExpressionBoolRef类型，记录其关联的DateExpression
        if isinstance(expr, DateExpressionBoolRef):
            for date_expr in expr.date_expressions:
                var_name = str(date_expr.date_expr)
                self._var_type_map[var_name] = 'date'
            return

        # 分析Z3表达式中的变量关系
        if z3.is_expr(expr):
            # 处理等式关系 (a == b)
            # 处理比较关系 (a < b, a > b, a <= b, a >= b)
            if z3.is_eq(expr) or z3.is_lt(expr) or z3.is_le(expr) or z3.is_gt(expr) or z3.is_ge(expr):
                left, right = expr.children()
                self._record_dependency(left, right)
                self._record_dependency(right, left)
                
                # 检查是否有一方是日期类型，如果是，则另一方也应该是日期类型
                left_str = str(left)
                right_str = str(right)
                
                if left_str in self._var_type_map and self._var_type_map[left_str] == 'date':
                    self._var_type_map[right_str] = 'date'
                elif right_str in self._var_type_map and self._var_type_map[right_str] == 'date':
                    self._var_type_map[left_str] = 'date'

            # 处理算术操作 (a + b, a - b)
            elif z3.is_arith(expr) and len(expr.children()) == 2:
                if str(expr.decl()) in ['+', '-', '*', '/']:
                    result = expr
                    left, right = expr.children()

                    # 记录结果与操作数之间的依赖关系
                    if hasattr(expr, 'result_var'):
                        self._record_dependency(expr.result_var, left)
                        self._record_dependency(expr.result_var, right)
                    else:
                        # 对于没有明确结果变量的表达式，记录操作数之间的依赖关系以及结果与操作数的关系
                        result_str = str(result)
                        left_str = str(left)
                        right_str = str(right)
                        
                        self._record_dependency(left, right)
                        self._record_dependency(right, left)  # 双向依赖，确保类型传播
                        self._record_dependency(result, left)
                        self._record_dependency(result, right)

                        # 如果是加减法，可能涉及日期操作
                        if str(expr.decl()) in ['+', '-']:
                            # 如果左操作数是日期，结果也应该是日期
                            if left_str in self._var_type_map and self._var_type_map[left_str] == 'date':
                                if result_str not in self._var_type_map:
                                    self._var_type_map[result_str] = 'date'

    def add(self, *exprs) -> None:
        """
        缓存约束而不是直接添加到求解器

        Args:
            *exprs: 要添加的表达式
        """
        for expr in exprs:
            # 处理DateExpressionBoolRef对象
            if isinstance(expr, DateExpressionBoolRef):
                # 直接添加DateExpressionBoolRef中包含的约束
                # 请求DateExpressionBoolRef中的所有关联DateExpression应用它们的约束
                for date_expr in expr.date_expressions:
                    # 记录日期类型信息
                    var_name = str(date_expr.date_expr)
                    self._var_type_map[var_name] = 'date'
                    # 添加未应用的约束到缓存
                    for constraint in date_expr.constraints:
                        if constraint not in self._constraint_cache:
                            self._constraint_cache.append(constraint)
                
                # 添加布尔表达式本身到缓存
                self._constraint_cache.append(expr._bool_ref)
                continue
                
            # 处理DateExpression对象
            if isinstance(expr, DateExpression):
                # 记录日期类型信息
                var_name = str(expr.date_expr)
                self._var_type_map[var_name] = 'date'
                # 添加未应用的约束到缓存
                for constraint in expr.constraints:
                    if constraint not in self._constraint_cache:
                        self._constraint_cache.append(constraint)
                
                # 添加日期表达式本身到缓存
                self._constraint_cache.append(expr.date_expr)
                continue
            
            # 处理Z3的复合布尔运算
            if z3.is_expr(expr):
                # 处理Not操作
                if z3.is_not(expr):
                    inner_expr = expr.arg(0)
                    if isinstance(inner_expr, DateExpressionBoolRef):
                        not_result = z3.Not(inner_expr.get_z3_bool_ref())
                        self._constraint_cache.append(not_result)
                        for date_expr in inner_expr.date_expressions:
                            date_expr.apply_constraints(self._internal_solver)
                        continue
                
                # 处理And操作
                elif z3.is_and(expr):
                    has_date_expr = False
                    converted_args = []
                    
                    # 检查是否有DateExpressionBoolRef参数
                    for i in range(expr.num_args()):
                        arg = expr.arg(i)
                        if isinstance(arg, DateExpressionBoolRef):
                            has_date_expr = True
                            converted_args.append(arg.get_z3_bool_ref())
                            for date_expr in arg.date_expressions:
                                date_expr.apply_constraints(self._internal_solver)
                        else:
                            converted_args.append(arg)
                    
                    if has_date_expr:
                        and_result = z3.And(*converted_args)
                        self._constraint_cache.append(and_result)
                        continue
                
                # 处理Or操作
                elif z3.is_or(expr):
                    has_date_expr = False
                    converted_args = []
                    
                    # 检查是否有DateExpressionBoolRef参数
                    for i in range(expr.num_args()):
                        arg = expr.arg(i)
                        if isinstance(arg, DateExpressionBoolRef):
                            has_date_expr = True
                            converted_args.append(arg.get_z3_bool_ref())
                            for date_expr in arg.date_expressions:
                                date_expr.apply_constraints(self._internal_solver)
                        else:
                            converted_args.append(arg)
                    
                    if has_date_expr:
                        or_result = z3.Or(*converted_args)
                        self._constraint_cache.append(or_result)
                        continue
                
                # 处理Implies操作
                elif str(expr.decl()) == 'implies' and expr.num_args() == 2:
                    a, b = expr.arg(0), expr.arg(1)
                    has_date_expr = False
                    
                    if isinstance(a, DateExpressionBoolRef):
                        has_date_expr = True
                        a = a.get_z3_bool_ref()
                        for date_expr in a.date_expressions:
                            date_expr.apply_constraints(self._internal_solver)
                    
                    if isinstance(b, DateExpressionBoolRef):
                        has_date_expr = True
                        b = b.get_z3_bool_ref()
                        for date_expr in b.date_expressions:
                            date_expr.apply_constraints(self._internal_solver)
                    
                    if has_date_expr:
                        implies_result = z3.Implies(a, b)
                        self._constraint_cache.append(implies_result)
                        continue
            
            # 特殊处理Z3比较表达式
            if z3.is_expr(expr) and (z3.is_gt(expr) or z3.is_ge(expr) or z3.is_lt(expr) or z3.is_le(expr) or z3.is_eq(expr)):
                left, right = expr.children()
                
                # 检查右侧是否为DateExpression
                if isinstance(right, DateExpression):
                    # 确定操作符类型
                    op = None
                    if z3.is_gt(expr):
                        op = '>'
                    elif z3.is_ge(expr):
                        op = '>='
                    elif z3.is_lt(expr):
                        op = '<'
                    elif z3.is_le(expr):
                        op = '<='
                    elif z3.is_eq(expr):
                        op = '=='
                    
                    if op:
                        # 使用date_compare替代
                        transformed_expr = self.date_compare(left, right, operator=op)
                        self._constraint_cache.append(transformed_expr)
                        self._analyze_constraint(transformed_expr)
                        continue
            
            # 缓存普通Z3约束
            self._constraint_cache.append(expr)
            # 分析约束中的变量和它们可能的类型
            self._analyze_constraint(expr)
        
        # 每次添加约束后都执行一次类型传播
        self._propagate_types()

    # 添加一个处理Not操作的便捷方法
    def Not(self, expr):
        """
        处理否定操作，支持DateExpressionBoolRef
        
        Args:
            expr: 要否定的表达式
            
        Returns:
            否定后的Z3布尔表达式
        """
        if isinstance(expr, DateExpressionBoolRef):
            # 使用内部Z3 BoolRef
            return z3.Not(expr.get_z3_bool_ref())
        return z3.Not(expr)
    
    def And(self, *args):
        """
        处理逻辑与操作，支持DateExpressionBoolRef
        
        Args:
            *args: 要进行逻辑与的表达式列表
            
        Returns:
            逻辑与后的Z3布尔表达式
        """
        # 转换所有DateExpressionBoolRef为Z3 BoolRef
        converted_args = []
        for arg in args:
            if isinstance(arg, DateExpressionBoolRef):
                converted_args.append(arg.get_z3_bool_ref())
            else:
                converted_args.append(arg)
        return z3.And(*converted_args)
    
    def Or(self, *args):
        """
        处理逻辑或操作，支持DateExpressionBoolRef
        
        Args:
            *args: 要进行逻辑或的表达式列表
            
        Returns:
            逻辑或后的Z3布尔表达式
        """
        # 转换所有DateExpressionBoolRef为Z3 BoolRef
        converted_args = []
        for arg in args:
            if isinstance(arg, DateExpressionBoolRef):
                converted_args.append(arg.get_z3_bool_ref())
            else:
                converted_args.append(arg)
        return z3.Or(*converted_args)
    
    def Implies(self, a, b):
        """
        处理逻辑蕴含操作，支持DateExpressionBoolRef
        
        Args:
            a: 前提
            b: 结论
            
        Returns:
            逻辑蕴含后的Z3布尔表达式
        """
        if isinstance(a, DateExpressionBoolRef):
            a = a.get_z3_bool_ref()
        if isinstance(b, DateExpressionBoolRef):
            b = b.get_z3_bool_ref()
        return z3.Implies(a, b)
    
    def Xor(self, a, b):
        """
        处理逻辑异或操作，支持DateExpressionBoolRef
        
        Args:
            a: 第一个操作数
            b: 第二个操作数
            
        Returns:
            逻辑异或后的Z3布尔表达式
        """
        if isinstance(a, DateExpressionBoolRef):
            a = a.get_z3_bool_ref()
        if isinstance(b, DateExpressionBoolRef):
            b = b.get_z3_bool_ref()
        return z3.Xor(a, b)
    
    def If(self, cond, then_expr, else_expr):
        """
        处理条件表达式，支持DateExpressionBoolRef
        
        Args:
            cond: 条件
            then_expr: 条件为真时的表达式
            else_expr: 条件为假时的表达式
            
        Returns:
            条件表达式的Z3表达式
        """
        if isinstance(cond, DateExpressionBoolRef):
            cond = cond.get_z3_bool_ref()
        return z3.If(cond, then_expr, else_expr)

    def model(self) -> z3.ModelRef:
        """
        获取求解模型
        
        Returns:
            Z3求解模型
        """
        return self._internal_solver.model()

    def reset(self) -> None:
        """
        重置求解器状态
        """
        # 先完全释放旧的求解器实例
        self._internal_solver = None
        
        # 创建全新的求解器实例
        self._internal_solver = z3.Solver()
        
        # 清空所有内部数据结构
        self._constraint_cache = []
        self._var_type_map = {}
        self._var_dependency_graph = {}
        self._tracked_constraints = []
        
        # 如果启用跟踪，重新设置unsat_core选项
        if self._track_unsat:
            self._internal_solver.set(unsat_core=True)

    # 添加跟踪不满足约束的功能
    def enable_unsat_tracking(self, enable: bool = True) -> None:
        """
        启用或禁用不满足约束跟踪
        
        Args:
            enable: 是否启用不满足约束跟踪
        """
        if enable != self._track_unsat:
            self._track_unsat = enable
            self.reset()  # 重置求解器状态以应用新设置

    def get_unsat_core_with_constraints(self) -> List[Tuple[str, Any]]:
        """
        获取不满足核心约束及其对应的原始表达式
        
        Returns:
            不满足核心约束及其对应的原始表达式列表
        """
        if not self._track_unsat:
            logger.warning("未启用不满足约束跟踪功能，无法获取不满足核心约束")
            return []
            
        # 获取不满足核心
        core = self._internal_solver.unsat_core()
        result = []
        
        # 匹配不满足核心与原始约束
        for name in core:
            for tracked_name, constraint in self._tracked_constraints:
                if str(tracked_name) == str(name):
                    result.append((tracked_name, constraint))
                    
        return result

    # 代理其他原始Solver方法
    def __getattr__(self, name: str) -> Any:
        """
        代理方法到内部求解器
        
        只有当属性不是当前类的方法时才代理到内部求解器
        
        Args:
            name: 方法或属性名称
            
        Returns:
            内部求解器的方法或属性
        """
        # 检查是否是我们自己的方法
        if name.startswith('_') and hasattr(self.__class__, name):
            return getattr(self.__class__, name).__get__(self, self.__class__)
        return getattr(self._internal_solver, name)

    def _convert_constraint(self, expr) -> Union[z3.ExprRef, 'DateExpression', 'DateExpressionBoolRef']:
        """
        根据类型信息转换约束表达式

        Args:
            expr: 要转换的表达式

        Returns:
            转换后的表达式
        """
        # 如果已经是DateExpression，无需转换
        if isinstance(expr, DateExpression) or isinstance(expr, DateExpressionBoolRef):
            return expr

        # 尝试将整数表达式转换为日期表达式
        if z3.is_expr(expr):
            # 检查表达式中的变量是否应该是日期类型
            vars_in_expr = self._get_vars_in_expr(expr)
            should_be_date = False

            for var in vars_in_expr:
                if var in self._var_type_map and self._var_type_map[var] == 'date':
                    should_be_date = True
                    break

            if should_be_date:
                # 转换整数表达式为日期表达式
                return self._convert_to_date_expr(expr)

            # 处理比较操作（等于、大于、小于等）
            if z3.is_eq(expr) or z3.is_lt(expr) or z3.is_le(expr) or z3.is_gt(expr) or z3.is_ge(expr):
                left, right = expr.children()
                left_str = str(left)
                right_str = str(right)
                date_type = DateType()

                # 检查任一边是否需要被视为日期类型
                left_is_date = left_str in self._var_type_map and self._var_type_map[left_str] == 'date'
                right_is_date = right_str in self._var_type_map and self._var_type_map[right_str] == 'date'

                # 如果任一边是日期类型，处理日期比较
                if left_is_date or right_is_date:
                    # 将非日期类型的一方转换为日期类型
                    if left_is_date and not isinstance(left, DateExpression):
                        left = DateExpression(date_type, left)
                    elif not left_is_date and right_is_date:
                        # 如果右边是日期但左边不是，左边可能需要被视为日期
                        if not left_str in self._var_type_map:
                            self._var_type_map[left_str] = 'date'
                        left = DateExpression(date_type, left)

                    if right_is_date and not isinstance(right, DateExpression):
                        right = DateExpression(date_type, right)
                    elif not right_is_date and left_is_date:
                        # 如果左边是日期但右边不是，右边可能需要被视为日期
                        if not right_str in self._var_type_map:
                            self._var_type_map[right_str] = 'date'
                        right = DateExpression(date_type, right)

                    # 根据比较类型创建对应的日期比较表达式
                    if z3.is_eq(expr):
                        return left == right
                    elif z3.is_lt(expr):
                        return left < right
                    elif z3.is_le(expr):
                        return left <= right
                    elif z3.is_gt(expr):
                        return left > right
                    elif z3.is_ge(expr):
                        return left >= right

        # 默认返回原始表达式
        return expr

    def _get_vars_in_expr(self, expr) -> Set[str]:
        """
        获取表达式中的所有变量名
        
        Args:
            expr: Z3表达式
            
        Returns:
            变量名集合
        """
        vars_set = set()

        if z3.is_const(expr) and not z3.is_bool(expr) and not z3.is_int_value(expr):
            vars_set.add(str(expr))

        for child in expr.children():
            vars_set.update(self._get_vars_in_expr(child))

        return vars_set

    def _convert_to_date_expr(self, expr) -> DateExpression:
        """
        将整数表达式转换为日期表达式
        
        Args:
            expr: Z3整数表达式
            
        Returns:
            日期表达式
        """
        # 创建DateType实例
        date_type = DateType()

        # 处理不同类型的表达式
        if z3.is_const(expr) and not z3.is_bool(expr) and not z3.is_int_value(expr):
            # 变量
            return DateExpression(date_type, expr)

        elif z3.is_int_value(expr):
            # 整数常量
            return DateExpression(date_type, expr)

        elif z3.is_arith(expr) and len(expr.children()) == 2:
            # 算术表达式
            op = str(expr.decl())
            left, right = expr.children()

            # 递归转换操作数
            left_str = str(left)
            right_str = str(right)

            # 检查左操作数是否应该是日期类型
            if left_str in self._var_type_map and self._var_type_map[left_str] == 'date':
                left_date = self._convert_to_date_expr(left)

                # 对于加减法，右操作数通常是天数
                if op == '+':
                    return left_date + right
                elif op == '-':
                    # 检查右操作数是否也是日期类型
                    if right_str in self._var_type_map and self._var_type_map[right_str] == 'date':
                        right_date = self._convert_to_date_expr(right)
                        return left_date - right_date
                    else:
                        return left_date - right

            # 如果无法特殊处理，简单地封装整个表达式
            return DateExpression(date_type, expr)

        # 默认情况：直接将表达式封装为DateExpression
        return DateExpression(date_type, expr)

    def check(self) -> z3.CheckSatResult:
        """
        处理缓存的约束并执行求解

        Returns:
            求解结果
        """
        # 最后一次类型传播
        self._propagate_types()

        # 清空跟踪约束列表
        self._tracked_constraints = []
        constraint_idx = 0
        
        # 创建一个用于跟踪唯一名称的集合
        used_names = set()
        
        # 存储所有转换后的表达式
        all_converted_exprs = []

        # 确保所有DateExpression对象的约束都被应用
        for expr in self._constraint_cache:
            if isinstance(expr, DateExpressionBoolRef):
                for date_expr in expr.date_expressions:
                    date_expr.apply_constraints(self._internal_solver)
                # BoolRef对象将在下面的循环中处理
            elif isinstance(expr, DateExpression):
                expr.apply_constraints(self._internal_solver)
                # 日期表达式本身将在下面的循环中处理

        # 处理并添加所有缓存的约束
        for expr in self._constraint_cache:
            # 根据类型信息转换表达式
            converted_expr = self._convert_constraint(expr)
            all_converted_exprs.append((converted_expr, expr))

        # 先用add方法添加所有约束（不跟踪），确保求解器内部状态正确
        if not self._track_unsat:
            for converted_expr, _ in all_converted_exprs:
                if isinstance(converted_expr, DateExpressionBoolRef):
                    self._internal_solver.add(converted_expr._bool_ref)
                elif isinstance(converted_expr, DateExpression):
                    self._internal_solver.add(converted_expr.date_expr)
                else:
                    self._internal_solver.add(converted_expr)
        else:
            # 如果启用跟踪，使用assert_and_track并确保名称唯一
            for converted_expr, orig_expr in all_converted_exprs:
                # 为表达式生成唯一标识
                expr_str = str(converted_expr)
                expr_hash = hash(expr_str)
                base_name = f"constraint_{constraint_idx}_{expr_hash}"
                name = base_name
                
                # 确保名称唯一
                counter = 0
                while name in used_names:
                    counter += 1
                    name = f"{base_name}_{counter}"
                
                used_names.add(name)
                
                # 添加带跟踪的约束
                try:
                    if isinstance(converted_expr, DateExpressionBoolRef):
                        self._internal_solver.assert_and_track(converted_expr._bool_ref, name)
                    elif isinstance(converted_expr, DateExpression):
                        self._internal_solver.assert_and_track(converted_expr.date_expr, name)
                    else:
                        self._internal_solver.assert_and_track(converted_expr, name)
                    
                    self._tracked_constraints.append((name, orig_expr))
                except Exception as e:
                    # 如果跟踪失败，退回到普通添加
                    logger.warning(f"跟踪约束失败，使用普通add方法: {e}")
                    if isinstance(converted_expr, DateExpressionBoolRef):
                        self._internal_solver.add(converted_expr._bool_ref)
                    elif isinstance(converted_expr, DateExpression):
                        self._internal_solver.add(converted_expr.date_expr)
                    else:
                        self._internal_solver.add(converted_expr)
                
                constraint_idx += 1

        # 清空约束缓存
        self._constraint_cache = []

        # 执行求解
        try:
            result = self._internal_solver.check()
            
            # 如果启用了跟踪且结果是不可满足的，输出不满足核心约束
            if self._track_unsat and result == z3.unsat:
                unsat_core_constraints = self.get_unsat_core_with_constraints()
                logger.warning("============== 不可满足核心约束: =================")
                tactic = z3.Tactic('ctx-solver-simplify')
                for name, constraint in unsat_core_constraints:
                    simplified_goal = tactic(constraint)
                    logger.warning(f"{name}: {simplified_goal}")
                    
            return result
        except Exception as e:
            logger.error(f"求解器检查时发生错误: {str(e)}")
            # 重置求解器状态以避免后续错误
            self._internal_solver.reset()
            if self._track_unsat:
                self._internal_solver.set(unsat_core=True)
            # 返回未知结果
            return z3.unknown 