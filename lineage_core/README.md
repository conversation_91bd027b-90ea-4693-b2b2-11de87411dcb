# 行级数据血缘分析

本模块提供了行级数据血缘分析的功能，支持分析 SQL 语句之间的数据血缘关系。

## 分析器类型

系统提供了两种血缘分析器：

### 1. LineageAnalyzer

原始血缘分析器，使用基于 Z3 约束求解的方法分析数据血缘关系。

特点：
- 直接解析 SQL 并构建 Z3 约束
- 支持分析 SELECT、INSERT、UPDATE 等 SQL 语句类型
- 通过约束求解判断数据血缘关系

### 2. LineageDynamicAnalyzer

新的动态血缘分析器，使用 new_model 中的模块进行分析，提供更先进的计算逻辑。

特点：
- 使用 new_model 中的模块（relevance_analyzer、type_analyzer、z3_converter 等）
- 分析过程分为相关性分析、类型分析、表级依赖分析等多个阶段
- 能够处理更复杂的 SQL 语句和表达式

两种分析器提供相同的接口，可以无缝切换使用。

## 使用方法

### 基本用法

```python
from lineage_core.lineage_analyzer import LineageAnalyzer
# 或者使用动态分析器
from lineage_core.lineage_dynamic_analyzer import LineageDynamicAnalyzer

# 创建分析器实例
analyzer = LineageAnalyzer()  # 或 LineageDynamicAnalyzer()

# 准备 SQL 语句
target_sql = "INSERT INTO target_table SELECT * FROM source_table WHERE condition = 'value'"
upstream_sql = "INSERT INTO source_table SELECT * FROM raw_data"

# 进行血缘分析
result = analyzer.analyze_lineage([target_sql, upstream_sql])

# 查看结果
print(f"有血缘关系: {result['result']}")
print(f"相关的上游 SQL: {result['target']}")
print(f"收集的列: {result['collected_columns']}")
```

### 高级用法

#### 添加字段类型信息

```python
# 定义字段类型信息
schema = {
    "table1.column1": "INT",
    "table2.column2": "STRING",
    "table3.column3": "FLOAT"
}

# 使用 schema 进行分析
result = analyzer.analyze_lineage(sql_list, schema=schema)
```

#### 指定 SQL 方言

```python
# 指定 SQL 方言
result = analyzer.analyze_lineage(sql_list, dialect="mysql")
```

## 分析结果

分析结果是一个字典，包含以下字段：

- `result`：布尔值，表示是否存在血缘关系
- `target`：列表，包含有血缘关系的上游 SQL ID
- `collected_columns`：列表，包含收集到的相关列名
- `error`：可选，包含错误信息（如果分析过程中出现错误）

## 示例

详细使用示例请参考：

- `lineage_core/examples/dynamic_analyzer_example.py`：动态分析器示例
- `lineage_core/tests/test_lineage_dynamic_analyzer.py`：动态分析器测试

## 算法原理

1. **SQL 解析**：将 SQL 语句解析为抽象语法树（AST）
2. **相关性分析**：分析 SQL 语句中的表和列之间的相关性
3. **类型分析**：推断列的数据类型
4. **依赖分析**：基于 Z3 约束求解分析表级依赖关系
5. **血缘判断**：根据依赖关系判断是否存在血缘关系

## 注意事项

- 分析过程中优先使用用户提供的 schema 信息，未提供时自动推断
- 为了确保血缘分析的完整性，对于错误情况默认认为存在血缘关系
- 动态分析器与原始分析器在处理复杂查询时可能会有不同的分析结果 