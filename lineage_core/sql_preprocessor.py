#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SQL预处理模块

该模块提供了一系列函数，用于预处理SQL语句，包括：
1. 将CREATE TABLE语句转换为INSERT语句
2. 拆分WITH子句
3. 拆分子查询
4. 拆分UNION操作
5. 为列添加表名前缀

这些预处理操作有助于简化数据血缘分析，将复杂SQL转换为更易于处理的形式。
"""

# 标准库导入
import logging
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Optional, Any, Tuple, Union

# 第三方库导入
import sqlglot
from sqlglot import expressions as exp, parse_one
from sqlglot.expressions import Expression, Table

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 本地模块导入
from lineage_core.split_sql import split_with, split_union
from lineage_core.split_subquery import split_subquery
from lineage_core.config import get_sql_dialect

# 配置日志
logger = logging.getLogger(__name__)

def preprocess_sql_statements(sql_statements: List[str], dialect: Optional[str] = None) -> List[str]:
    """
    预处理SQL语句列表，只处理INSERT语句，其他语句将被跳过
    
    处理流程：
    1. 检查是否为INSERT语句，非INSERT语句直接跳过
    2. 拆分WITH子句为独立语句
    3. 拆分子查询为独立语句
    4. 拆分UNION操作为独立语句
    5. 为INSERT语句中的目标字段添加到SELECT语句的别名部分
    
    Args:
        sql_statements: SQL语句列表，可以是字符串或字符串列表（OR关系）
        dialect: SQL方言，如果为None则使用全局配置
        
    Returns:
        List[str]: 处理后的SQL语句列表，每个元素可能是字符串或字符串列表（OR关系）
        
    Raises:
        Exception: 处理过程中发生错误时抛出
    """
    try:
        logger.debug(f"开始预处理SQL语句列表，共{len(sql_statements)}条语句")
        processed_sqls = []
        
        # 第一步：检查是否为INSERT语句
        logger.debug("步骤1：检查SQL语句类型")
        for i, sql_item in enumerate(sql_statements):
            if isinstance(sql_item, str):
                # 如果是单个SQL字符串
                try:
                    parsed = parse_one(sql_item, dialect=get_sql_dialect())
                    if isinstance(parsed, exp.Insert):
                        processed_sqls.append(sql_item)
                        logger.debug(f"处理第{i+1}条SQL语句：确认是INSERT语句")
                    else:
                        logger.debug(f"跳过第{i+1}条SQL语句：非INSERT语句")
                except Exception as e:
                    logger.warning(f"解析第{i+1}条SQL语句失败: {str(e)}，跳过处理")
            elif isinstance(sql_item, list):
                # 如果是SQL列表（OR关系）
                processed_sql_list = []
                for j, sub_sql in enumerate(sql_item):
                    try:
                        parsed = parse_one(sub_sql, dialect=get_sql_dialect())
                        if isinstance(parsed, exp.Insert):
                            processed_sql_list.append(sub_sql)
                            logger.debug(f"处理第{i+1}条SQL语句的第{j+1}个子语句：确认是INSERT语句")
                        else:
                            logger.debug(f"跳过第{i+1}条SQL语句的第{j+1}个子语句：非INSERT语句")
                    except Exception as e:
                        logger.warning(f"解析第{i+1}条SQL语句的第{j+1}个子语句失败: {str(e)}，跳过处理")
                
                if processed_sql_list:
                    processed_sqls.append(processed_sql_list)

        if not processed_sqls:
            logger.info("没有找到有效的INSERT语句，返回空列表")
            return []

        # 第二步：处理WITH语句
        logger.debug("步骤2：拆分WITH子句")
        sql = processed_sqls.copy()
        processed_sqls = []
        for i, sql_item in enumerate(sql):
            if isinstance(sql_item, str):
                # 如果是单个SQL字符串
                sql_parts = split_with(sql_item)
                if len(sql_parts) > 1:
                    logger.info(f"SQL语句{i+1}包含WITH子句，已拆分为{len(sql_parts)}个部分")
                processed_sqls.extend(sql_parts[::-1])  # 反转顺序，确保依赖关系正确
            elif isinstance(sql_item, list):
                # 如果是SQL列表（OR关系）
                processed_sql_list = []
                for j, sub_sql in enumerate(sql_item):
                    sql_parts = split_with(sub_sql)
                    if len(sql_parts) > 1:
                        # 如果拆分出多个部分，将它们全部添加为独立的SQL
                        logger.info(f"SQL语句{i+1}的子语句{j+1}包含WITH子句，已拆分为{len(sql_parts)}个部分")
                        processed_sqls.extend(sql_parts[::-1])
                    else:
                        processed_sql_list.append(sub_sql)
                
                if processed_sql_list:
                    processed_sqls.append(processed_sql_list)
        
        # 第三步：处理子查询
        logger.debug("步骤3：拆分子查询")
        sql = processed_sqls.copy()
        processed_sqls = []
        for i, sql_item in enumerate(sql):
            if isinstance(sql_item, str):
                # 如果是单个SQL字符串
                sql_parts = split_subquery(sql_item)
                if len(sql_parts) > 1:
                    logger.info(f"SQL语句{i+1}包含子查询，已拆分为{len(sql_parts)}个部分")
                processed_sqls.extend(sql_parts)
            elif isinstance(sql_item, list):
                # 如果是SQL列表（OR关系）
                processed_sql_list = []
                for j, sub_sql in enumerate(sql_item):
                    sql_parts = split_subquery(sub_sql)
                    if len(sql_parts) > 1:
                        # 如果拆分出多个部分，将它们全部添加为独立的SQL
                        logger.info(f"SQL语句{i+1}的子语句{j+1}包含子查询，已拆分为{len(sql_parts)}个部分")
                        processed_sqls.extend(sql_parts)
                    else:
                        processed_sql_list.append(sub_sql)
                
                if processed_sql_list:
                    processed_sqls.append(processed_sql_list)
        
        # 第四步：拆分UNION操作
        logger.debug("步骤4：拆分UNION操作")
        processed_sql_statements = []
        for i, sql in enumerate(processed_sqls):
            # 尝试拆分每个SQL语句
            split_results = split_union(sql)
            # 如果拆分成功（返回多个语句），则添加所有拆分后的语句
            # 否则保留原始语句
            # List 要以 nested list形式存在，在后续构建约束时，nested list 为 OR 关系
            if len(split_results) > 1:
                logger.info(f"SQL语句{i+1}包含UNION操作，已拆分为{len(split_results)}个部分")
                processed_sql_statements.append(split_results)
            else:
                processed_sql_statements.append(sql)
        
        # 第五步：处理INSERT语句，将目标字段添加到SELECT语句中
        logger.debug("步骤5：为INSERT语句添加目标字段到SELECT语句")
        final_processed_sql_statements = []
        for i, sql in enumerate(processed_sql_statements):
            if isinstance(sql, str):
                # 如果是单个SQL字符串
                try:
                    processed_sql = add_alias_to_select_columns(sql)
                    final_processed_sql_statements.append(processed_sql)
                    logger.debug(f"为SQL语句{i+1}添加了目标字段到SELECT语句")
                except Exception as e:
                    logger.warning(f"为SQL语句{i+1}添加目标字段失败: {str(e)}，保留原始SQL")
                    final_processed_sql_statements.append(sql)
            elif isinstance(sql, list):
                # 如果是SQL列表（OR关系）
                processed_sql_list = []
                for j, sub_sql in enumerate(sql):
                    try:
                        processed_sub_sql = add_alias_to_select_columns(sub_sql)
                        processed_sql_list.append(processed_sub_sql)
                        logger.debug(f"为SQL语句{i+1}的子语句{j+1}添加了目标字段到SELECT语句")
                    except Exception as e:
                        logger.warning(f"为SQL语句{i+1}的子语句{j+1}添加目标字段失败: {str(e)}，保留原始SQL")
                        processed_sql_list.append(sub_sql)
                
                if processed_sql_list:
                    final_processed_sql_statements.append(processed_sql_list)

        logger.info(f"SQL预处理完成，共处理{len(sql_statements)}条语句，生成{len(final_processed_sql_statements)}条处理后的语句")
        return final_processed_sql_statements
    except Exception as e:
        logger.error(f"SQL预处理过程中发生错误: {str(e)}")
        # 发生错误时返回空列表
        return []

def extract_table_mappings(parsed_sql: Expression) -> Dict[str, str]:
    """
    从解析后的SQL AST中提取表名和别名的映射关系
    
    Args:
        parsed_sql: SQLGlot解析后的SQL AST
        
    Returns:
        Dict[str, str]: 表别名到真实表名的映射，格式为 {"alias": "real_name"}
                        如果表没有别名，则使用 {"table_name": "table_name"}
                        对于带schema的表，使用 {"alias": "schema_name.table_name"}
                        子查询的别名映射为 {"alias": "__SUBQUERY__"}
                        
    Raises:
        Exception: 提取过程中发生错误时抛出
    """
    try:
        logger.debug("开始从SQL AST提取表名和别名映射关系")
        table_mappings: Dict[str, str] = {}
        
        def process_node(node: Expression, inside_subquery: bool = False) -> None:
            """
            递归处理AST节点，提取表名和别名
            
            Args:
                node: 当前处理的AST节点
                inside_subquery: 是否在子查询内部，默认为False
            """
            try:
                # 处理子查询
                if isinstance(node, exp.Subquery):
                    if node.args.get("alias"):
                        alias = node.args["alias"].name
                        table_mappings[alias] = "__SUBQUERY__"
                        logger.debug(f"发现子查询，别名: {alias}")
                    # 子查询内部的表不影响外部映射
                    process_node(node.this, inside_subquery=True)
                    return
                
                # 处理表引用，但仅处理不在子查询内的表
                if isinstance(node, exp.Table) and not inside_subquery:
                    # 获取完整的表名（包含schema）
                    table_name = node.name
                    if node.args.get("db"):
                        schema_name = node.args["db"].name
                        table_name = f"{schema_name}.{table_name}"
                    
                    # 检查是否有别名
                    alias = None
                    if node.args.get("alias"):
                        alias = node.args["alias"].name
                    
                    if alias:
                        table_mappings[alias] = table_name
                        logger.debug(f"映射表别名到真实表名: {alias} -> {table_name}")
                    else:
                        # 如果没有别名，表名映射到自身
                        table_mappings[table_name] = table_name
                        logger.debug(f"表没有别名，映射到自身: {table_name}")
                
                # 递归处理能包含其他节点的节点类型
                if isinstance(node, exp.Select):
                    # 处理SELECT语句的FROM子句
                    if node.args.get("from"):
                        process_node(node.args["from"].this, inside_subquery)
                    
                    # 处理JOIN
                    for join in node.args.get("joins", []):
                        process_node(join.this, inside_subquery)
                
                # 处理INSERT语句
                elif isinstance(node, exp.Insert):
                    # 处理INSERT...SELECT语句的SELECT部分
                    if node.args.get("expression") and isinstance(node.args["expression"], exp.Select):
                        process_node(node.args["expression"], inside_subquery)
                        
                # 处理FROM子句
                elif isinstance(node, exp.From) and not inside_subquery:
                    process_node(node.this, inside_subquery)
                    
                # 处理JOIN子句
                elif isinstance(node, exp.Join) and not inside_subquery:
                    process_node(node.this, inside_subquery)
            except Exception as e:
                logger.warning(f"处理AST节点时发生错误: {str(e)}，节点类型: {type(node).__name__}")
                # 继续处理其他节点，不中断整个过程
        
        # 从根节点开始处理
        process_node(parsed_sql)
        logger.info(f"成功提取表映射关系，共{len(table_mappings)}个映射")
        logger.debug(f"提取的表映射详情: {table_mappings}")
        
        return table_mappings
    except Exception as e:
        logger.error(f"提取表映射关系时发生错误: {str(e)}")
        # 出现错误时返回空映射，让调用者决定如何处理
        return {}

def add_table_prefix_to_columns(sql: str, table_mapping: Optional[Dict[str, str]] = None, dialect: Optional[str] = None) -> str:
    """
    在SQL语句中为列添加表名前缀，增强列的可追踪性
    
    处理逻辑:
    1. 为WHERE和HAVING子句中的列添加表名前缀
    2. 对于INSERTE语句，为输出字段添加目标表名作为前缀
    3. 将表别名替换为真实表名
    
    Args:
        sql: SQL字符串或完整SQL语句
        table_mapping: 表别名到真实表名的映射，如果为None，将自动从SQL中提取
        dialect: SQL方言，如果为None则使用全局配置
    
    Returns:
        str: 处理后的SQL语句，所有列都添加了适当的表名前缀
        
    Raises:
        ValueError: 当SQL解析失败或表映射提取失败时抛出
        Exception: 转换过程中发生其他错误时抛出
    """
    try:
        # 如果未指定方言，使用全局配置的方言
        if dialect is None:
            dialect = get_sql_dialect()
            
        parsed = sqlglot.parse_one(sql, dialect=dialect)
        logger.debug(f"成功解析SQL: {sql}")
    except Exception as e:
        error_msg = f"无法解析SQL: {e}"
        logger.error(error_msg)
        raise ValueError(error_msg) from e
    
    # 如果没有提供表映射，自动提取
    if table_mapping is None:
        try:
            table_mapping = extract_table_mappings(parsed)
            logger.debug(f"自动提取表映射: {table_mapping}")
        except Exception as e:
            error_msg = f"提取表映射失败: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg) from e
    
    # 获取INSERT目标表名
    target_table = None
    schema_name = None
    if isinstance(parsed, exp.Insert):
        table_node = parsed.find(exp.Table)
        target_table = table_node.name
        # 如果存在 schema，将其添加到表名前
        if table_node.args.get("db"):
            schema_name = table_node.args["db"].name
        logger.debug(f"目标表名: {target_table}")
    
    # 为INSERT语句中的SELECT部分添加目标表前缀
    if target_table and isinstance(parsed, exp.Insert) and "expression" in parsed.args:
        select_stmt = parsed.args["expression"]
        if isinstance(select_stmt, exp.Select):
            # 处理SELECT表达式
            new_expressions = []
            for expr in select_stmt.args.get("expressions", []):
                # 处理别名表达式
                if isinstance(expr, exp.Alias):
                    alias_name = expr.args["alias"].name
                    if schema_name:
                        new_alias = f"{schema_name}_{target_table}_{alias_name}"
                    else:
                        new_alias = f"{target_table}_{alias_name}"
                    expr.args["alias"].args["this"] = new_alias
                    
                    # 检查Alias的this部分是否为Column
                    this_expr = expr.args.get("this")
                    
                    # 判断是否为常量或特殊标识符
                    is_constant = False
                    if isinstance(this_expr, exp.Column):
                        # 检查列标识符是否带引号，带引号通常表示常量或特殊标识符
                        if hasattr(this_expr, 'args') and 'this' in this_expr.args:
                            column_identifier = this_expr.args['this']
                            if hasattr(column_identifier, 'args') and 'quoted' in column_identifier.args:
                                is_constant = column_identifier.args['quoted']
                                logger.debug(f"检测到标识符 {this_expr.name} 的quoted属性为 {is_constant}")
                    
                    # 只为非常量的普通列添加表前缀
                    if isinstance(this_expr, exp.Column) and not this_expr.table and not is_constant:
                        for alias, real_name in table_mapping.items():
                            if real_name != "__SUBQUERY__":
                                this_expr.args["table"] = exp.Identifier(this=real_name, quoted=False)
                                logger.debug(f"为别名表达式中的列 {this_expr.name} 添加表前缀 {real_name}")
                                break
                
                # 处理普通列
                elif isinstance(expr, exp.Column):
                    column_name = expr.name
                    if schema_name:
                        new_alias = f"{schema_name}_{target_table}_{column_name}"
                    else:
                        new_alias = f"{target_table}_{column_name}"
                    
                    # 检查列标识符是否带引号
                    is_constant = False
                    if hasattr(expr, 'args') and 'this' in expr.args:
                        column_identifier = expr.args['this']
                        if hasattr(column_identifier, 'args') and 'quoted' in column_identifier.args:
                            is_constant = column_identifier.args['quoted']
                            logger.debug(f"检测到列 {column_name} 的quoted属性为 {is_constant}")
                    
                    # 只为非常量的普通列添加表前缀
                    if not expr.table and not is_constant:
                        for alias, real_name in table_mapping.items():
                            if real_name != "__SUBQUERY__":
                                expr.args["table"] = exp.Identifier(this=real_name, quoted=False)
                                logger.debug(f"为列 {column_name} 添加表前缀 {real_name}")
                                break
                    
                    expr = exp.Alias(
                        this=expr,
                        alias=exp.Identifier(this=new_alias, quoted=False)
                    )
                
                # 对于其他类型的表达式（如常量、函数调用等），只添加别名前缀
                else:
                    # 为非列表达式创建带有目标表前缀的别名
                    expr_alias = f"{target_table}_expr_{len(new_expressions)}"
                    expr = exp.Alias(
                        this=expr,
                        alias=exp.Identifier(this=expr_alias, quoted=False)
                    )
                    logger.debug(f"为非列表达式创建别名 {expr_alias}")
                
                new_expressions.append(expr)
            
            if new_expressions:
                select_stmt.args["expressions"] = new_expressions
                logger.debug(f"为INSERT/UPDATE语句添加了{len(new_expressions)}个列前缀")
            
            # 处理HAVING子句
            if "having" in select_stmt.args and target_table:
                having_expr = select_stmt.args["having"]
                
                def _add_target_prefix_to_having(node: Expression) -> Expression:
                    """为HAVING子句中的列添加目标表前缀（支持schema.table格式）"""
                    if isinstance(node, exp.Column) and not node.table:
                        # 检查列标识符是否带引号
                        is_constant = False
                        if hasattr(node, 'args') and 'this' in node.args:
                            column_identifier = node.args['this']
                            if hasattr(column_identifier, 'args') and 'quoted' in column_identifier.args:
                                is_constant = column_identifier.args['quoted']
                        
                        # 只为非常量的普通列添加表前缀
                        if not is_constant:
                            # 如果存在 schema_name，添加 schema 前缀
                            if schema_name:
                                node.args["table"] = exp.Identifier(this=target_table, quoted=False)
                                node.args["db"] = exp.Identifier(this=schema_name, quoted=False)
                                logger.debug(f"为HAVING子句中的列 {node.name} 添加带schema的表前缀 {schema_name}.{target_table}")
                            else:
                                node.args["table"] = exp.Identifier(this=target_table, quoted=False)
                                logger.debug(f"为HAVING子句中的列 {node.name} 添加目标表前缀 {target_table}")
                    return node
                
                # 转换HAVING子句
                select_stmt.args["having"] = having_expr.transform(_add_target_prefix_to_having)
                logger.debug(f"处理了HAVING子句，添加了目标表前缀 {target_table}")
    
    def _add_prefix(node: Expression) -> Expression:
        """为列引用添加表前缀的转换函数"""
        # 只处理列引用，不处理字面量或其他表达式
        if isinstance(node, exp.Column):
            # 获取列名和可能的表别名
            column_name = node.name
            table_alias = node.table
            
            # 检查列标识符是否带引号
            is_constant = False
            if hasattr(node, 'args') and 'this' in node.args:
                column_identifier = node.args['this']
                if hasattr(column_identifier, 'args') and 'quoted' in column_identifier.args:
                    is_constant = column_identifier.args['quoted']
            
            # 只处理非常量的普通列
            if is_constant:
                return node
            
            # 如果列已经有表别名前缀，直接修改表别名为真实表名
            if table_alias and table_alias in table_mapping:
                real_table = table_mapping[table_alias]
                if real_table != "__SUBQUERY__":  # 不处理子查询
                    # 直接修改表标识符的名称
                    node.args["table"].args["this"] = real_table
                    logger.debug(f"将列 {column_name} 的表别名 {table_alias} 替换为真实表名 {real_table}")
                    return node
            # 如果列没有表别名前缀
            elif not table_alias:
                # 如果只有一个表，直接使用它
                if len(table_mapping) == 1:
                    alias, real_name = next(iter(table_mapping.items()))
                    if real_name != "__SUBQUERY__":  # 不为子查询添加前缀
                        # 添加表前缀
                        node.args["table"] = exp.Identifier(this=real_name, quoted=False)
                        logger.debug(f"为列 {column_name} 添加唯一表前缀 {real_name}")
                        return node
                # 如果有多个表，尝试找到不是子查询的第一个表
                elif table_mapping:
                    for alias, real_name in table_mapping.items():
                        if real_name != "__SUBQUERY__":
                            # 添加表前缀
                            node.args["table"] = exp.Identifier(this=real_name, quoted=False)
                            logger.debug(f"为列 {column_name} 添加表前缀 {real_name}（多表情况）")
                            return node
        
        # 对于非列引用的节点，直接返回原节点，不做修改
        return node
    
    # 遍历并转换AST
    try:
        transformed = parsed.transform(_add_prefix)
        logger.info(f"成功为SQL添加表前缀")
        
        # 提取出转换后的SQL
        result = transformed.sql()
        return result
    except Exception as e:
        error_msg = f"转换SQL时发生错误: {e}"
        logger.error(error_msg)
        raise ValueError(error_msg) from e

def add_alias_to_select_columns(sql: str) -> str:
    """
    将 INSERT 语句中的目标字段添加到 SELECT 语句中的 Alias 部分
    
    处理逻辑:
    1. 解析 SQL 语句
    2. 如果是 INSERT 语句，提取目标字段列表
    3. 为 SELECT 语句中的每个表达式添加对应的目标字段名作为别名
    
    Args:
        sql: SQL 字符串
        
    Returns:
        str: 处理后的 SQL 语句，SELECT 表达式中包含目标字段名作为别名
        
    Raises:
        ValueError: 当 SQL 解析失败时抛出
        Exception: 处理过程中发生其他错误时抛出
    """
    try:
        parsed = sqlglot.parse_one(sql, dialect=get_sql_dialect())
        logger.debug(f"准备添加 target 字段: {sql}")
        
        # 添加更多调试信息
        logger.debug(f"解析后的 SQL 类型: {type(parsed).__name__}")
        if hasattr(parsed, 'args'):
            logger.debug(f"解析后的 SQL 参数: {parsed.args}")
    except Exception as e:
        error_msg = f"无法解析 SQL: {e}"
        logger.error(error_msg)
        logger.error(f"原始 SQL: \n {sql}")
        raise ValueError(error_msg) from e
    
    # 只处理 INSERT 语句
    if not isinstance(parsed, exp.Insert):
        logger.debug("不是 INSERT 语句，跳过处理")
        return sql
    
    # 检查是否有目标字段列表
    target_columns = []
    if "this" in parsed.args:
        schema = parsed.args["this"]
        # 检查 schema 是否已经是 Schema 对象
        if isinstance(schema, exp.Schema):
            if "expressions" in schema.args:
                for expr in schema.args["expressions"]:
                    if isinstance(expr, exp.Identifier):
                        target_columns.append(expr.name)
        # 检查 schema 是否是 Table 对象，且其 this 属性是 Schema 对象
        elif isinstance(schema, exp.Table) and isinstance(schema.this, exp.Schema):
            if "expressions" in schema.this.args:
                for expr in schema.this.args["expressions"]:
                    if isinstance(expr, exp.Identifier):
                        target_columns.append(expr.name)
    
    # 如果没有目标字段，跳过处理
    if not target_columns:
        logger.debug("没有找到目标字段，跳过处理")
        return sql
    
    # 检查是否有 SELECT 表达式
    if "expression" not in parsed.args or not isinstance(parsed.args["expression"], exp.Select):
        logger.debug("INSERT 语句没有 SELECT 表达式，跳过处理")
        return sql
    
    # 获取 SELECT 表达式
    select_stmt = parsed.args["expression"]
    
    # 检查 SELECT 表达式中的字段数量是否与目标字段数量匹配
    select_expressions = select_stmt.args.get("expressions", [])
    if len(select_expressions) != len(target_columns):
        logger.warning(f"SELECT 表达式中的字段数量 ({len(select_expressions)}) 与目标字段数量 ({len(target_columns)}) 不匹配，跳过处理")
        return sql
    
    # 为 SELECT 表达式中的每个字段添加目标字段名作为别名
    new_expressions = []
    for i, expr in enumerate(select_expressions):
        # 如果已经有别名，保留原别名
        if isinstance(expr, exp.Alias):
            new_expressions.append(expr)
        else:
            # 创建新的别名表达式
            new_expr = exp.Alias(
                this=expr,
                alias=exp.Identifier(this=target_columns[i], quoted=False)
            )
            new_expressions.append(new_expr)
    
    # 更新 SELECT 表达式
    select_stmt.args["expressions"] = new_expressions
    logger.debug(f"为 SELECT 语句添加了 {len(new_expressions)} 个目标字段别名")
    
    # 提取出转换后的 SQL
    result = parsed.sql()
    logger.debug(f"添加结果为： {result}")

    return result

# 示例用法
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 测试 add_target_columns_to_select 函数
    test_sql = """
    INSERT INTO VT_1_67108864 (
        STAT_DT          /* 统计日期 */
        ,HOST_CUST_ID          /* 核心客户号 */
        ,ASES_INPUT_DT          /* 评定输入日期 */
    )
    SELECT
        DATE'2023-08-15'          /* 统计日期 */
        ,T1.CSR_ID          /* 核心客户号 */
        ,COALESCE(T3.IPT_DTE,DATE'0001-01-01')          /* 评定输入日期 */
    FROM table
    """
    result = add_alias_to_select_columns(test_sql)
    print("\n测试 add_target_columns_to_select 函数:")
    print("原始 SQL:")
    print(test_sql)
    print("处理后 SQL:")
    print(result)
    
    # 测试 preprocess_sql_statements 函数
    sqls = ["""
    CREATE TABLE IF not EXISTS tmp.tmp_a AS(
        SELECT t1.SUBJECT_CODE,
        sum(case when t1.subject_code LIKE '1001%'          --现金
                OR t1.subject_code LIKE '2502%'                               --应付债券
                then t1.end_balance * t2.cny_exchange else 0 end ) as a1
        from dwd.dwd_fc_alm_occurbalance t1
    )
    """,
    """
    INSERT INTO target_table
    SELECT  
    abc + 100 as name, 
    100 as sss,
    "ddd" as value 
    FROM (select abc from tbl1)
    """,
    """
    INSERT INTO target_table
    SELECT * FROM (SELECT id, name FROM users) u1
    WHERE age > 18 AND name LIKE 'A%'
    UNION ALL
    SELECT * FROM (SELECT id, name FROM users1) u2
    WHERE age > 18 AND name LIKE 'B%'
    """
    ]
    rslt = preprocess_sql_statements(sqls)
    print("\n测试 preprocess_sql_statements 函数 (示例1):")
    print("原始 SQL:")
    for sql in sqls:
        print(sql)
    print("处理后 SQL:")
    for sql in rslt:
        print(sql)

    # 测试 preprocess_sql_statements 函数
    sqls = ["""
    INSERT INTO test.target_table
    SELECT * FROM test.u1
    WHERE age > 18 AND name LIKE 'A%'
    UNION ALL
    SELECT * FROM test.u2
    WHERE age > 18 AND name LIKE 'B%'
    """
    ]
    rslt = preprocess_sql_statements(sqls)
    print("\n测试 preprocess_sql_statements 函数 (示例2):")
    print("原始 SQL:")
    for sql in sqls:
        print(sql)
    print("处理后 SQL:")
    for sql in rslt:
        print(sql)

    # 测试 preprocess_sql_statements 函数，包含目标字段的 INSERT 语句
    sqls = [
        """
        INSERT INTO VT_1_67108864 (
   STAT_DT          /* 统计日期 */
   ,HOST_CUST_ID          /* 核心客户号 */
   ,ASES_INPUT_DT          /* 评定输入日期 */
   ,RTL_LOAN_FINAL_MODIF_DTTM          /* 零售贷款最后修改时间戳 */
   ,RTL_LOAN_INTVW_IND          /* 零售贷款面签标志 */
   ,CUST_LEVEL_VAL          /* 客户等级分值 */
   ,RTL_LOAN_RISK_RATING_LEVEL          /* 零售贷款风险评级等级 */
   ,CUST_TYPE_CD          /* 客户类型代码 */
   ,CUST_TYPE_DESC          /* 客户类型描述 */
   ,RTL_LOAN_ACCT_ORG_NUM          /* 零售贷款账务机构号 */
   ,ORG_LEVEL_CD          /* 机构层级代码 */
   ,RTL_LOAN_L1_BRCH_ACCT_ORG_NUM          /* 零售贷款一级分行账务机构名称 */
   ,RTL_LOAN_ACCT_ORG_SUPER_CODE          /* 零售贷款账务机构上级编码 */
   ,RTL_LOAN_ACCT_ORG_SHNAME          /* 零售贷款账务机构简称 */
   ,RTL_LOAN_ACCT_ORG_TYPE_CD          /* 零售贷款账务机构类型代码 */
   ,RTL_LOAN_FINAL_MODIF_ORG_NAME          /* 零售贷款最后修改机构名称 */
   ,RTL_LOAN_FINAL_MODIF_CHAN          /* 零售贷款最后修改渠道 */
   ,RTL_LOAN_ONE_DAY_REPT_RTNG_IND          /* 零售贷款一天内重评标志 */
)
SELECT
   DATE'2023-08-15'          /* 统计日期 */
   ,T1.CSR_ID          /* 核心客户号 */
   ,COALESCE(T3.IPT_DTE,DATE'0001-01-01')          /* 评定输入日期 */
   ,T3.LST_AMD_TME          /* 零售贷款最后修改时间戳 */
   ,COALESCE(T3.INTERVIEW_FLAG,'')          /* 零售贷款面签标志 */
   ,COALESCE(T3.CSR_LVL_SRE,'-100')          /* 客户等级分值 */
   ,COALESCE(T3.GRD,'-100')          /* 零售贷款风险评级等级 */
   ,COALESCE(T1.CSR_TYP_CD,'')          /* 客户类型代码 */
   ,CASE WHEN CSR_TYP_CD='1' THEN  '个人'  WHEN CSR_TYP_CD='2' THEN  '对公'ELSE '' END          /* 客户类型描述 */
   ,T2.OPN_ORG          /* 零售贷款账务机构号 */
   , '1'          /* 机构层级代码 */
   ,T5.SUPER_ORG_NAME          /* 零售贷款一级分行账务机构名称 */
   ,T4.ORG_SUPER_CODE          /* 零售贷款账务机构上级编码 */
   ,T4.ORG_SHNAME          /* 零售贷款账务机构简称 */
   ,T4.ORG_TYPE_CD          /* 零售贷款账务机构类型代码 */
   ,COALESCE(CASE WHEN T3.LST_AMD_ORG IN ('0002','9001','9019') THEN T4.ORG_SHNAME ELSE T6.SUBOR_ORG_NAME END,'')          /* 零售贷款最后修改机构名称 */
   ,COALESCE(T3.LABEL,'')          /* 零售贷款最后修改渠道 */
   ,COUNT(1) OVER(PARTITION BY T3.CSR_ID,T3.IPT_DTE)          /* 零售贷款一天内重评标志 */
FROM VT_1_65536 T1
        """
    ]
    rslt = preprocess_sql_statements(sqls)
    print("\n测试 preprocess_sql_statements 函数 (示例3 - 包含目标字段的 INSERT 语句):")
    print("原始 SQL:")
    for sql in sqls:
        print(sql)
    print("处理后 SQL:")
    for sql in rslt:
        print(sql)

    sql = """
    INSERT INTO intermediate_table 
    SELECT 
    column1 as c1,
    column2 as c2,
    column3 as c3
FROM 
    source_table
    """
    print(f"原始 SQL \n {sql}")
    print(f"添加表名后 SQL \n {add_table_prefix_to_columns(sql)}")

    sql = """
    INSERT INTO intermediate_table 
    (
        c1,
        c2,
        c3
    )
    SELECT 
    column1,
    column2,
    column3
FROM 
    source_table
    """
    print(f"原始 SQL \n {sql}")
    sql1 = add_alias_to_select_columns(sql)
    print(f"with target columns SQL \n {sql1}")
    print(f"添加表名后 SQL \n {add_table_prefix_to_columns(sql1)}")