#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""子查询拆分模块

该模块提供了将复杂SQL语句中的子查询拆分为独立语句的功能。
主要用于简化数据血缘分析，将嵌套的SQL语句转换为等价的线性执行序列。

主要功能：
1. 将子查询提取为独立的临时表
2. 递归处理嵌套子查询
3. 支持INSERT和SELECT语句中的子查询拆分
"""

# 标准库导入
import os
import sys
import logging
from typing import List, Optional, Tuple, Union, Set

# 第三方库导入
import sqlglot
from sqlglot import expressions as exp

# 本地模块导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lineage_core.config import get_sql_dialect

# 配置日志
logger = logging.getLogger(__name__)

def split_subquery(sql: str) -> List[str]:
    """
    将包含子查询的 SQL 语句转换为不含子查询的等价形式。

    本函数将复杂SQL语句中的子查询提取为独立的INSERT语句，并将原始查询中的子查询
    替换为对应的临时表引用。这种转换有助于简化数据血缘分析，将嵌套的SQL语句
    转换为线性执行序列。

    支持的语句类型：
        - SELECT 语句
        - INSERT INTO ... SELECT 语句

    处理流程：
        1. 预处理SQL，将Oracle的nvl函数转换为标准SQL的COALESCE函数
        2. 解析SQL语句为抽象语法树(AST)
        3. 递归查找并提取所有子查询
        4. 为每个子查询创建临时表和对应的INSERT语句
        5. 将原始查询中的子查询替换为对应的临时表引用

    局限性：
        - 递归处理嵌套的子查询，但可能无法处理非常复杂的嵌套结构
        - 支持Oracle的nvl函数，会自动转换为COALESCE
        - 子查询必须有别名，否则可能无法正确处理

    Args:
        sql: 输入的 SQL 语句字符串

    Returns:
        转换后的 SQL 语句列表，每个元素为一个 SQL 语句字符串。
        如果无法处理，则返回只包含原始 SQL 的列表。
    """
    try:
        # 预处理SQL，将nvl替换为COALESCE
        sql = sql.replace('nvl(', 'COALESCE(')

        # 解析 SQL 语句
        parsed = sqlglot.parse_one(sql, dialect=get_sql_dialect())

        # 获取目标表名，用于临时表前缀
        target_table_prefix = "temp"
        if isinstance(parsed, exp.Insert):
            schema_obj = parsed.args.get("this")
            if schema_obj:
                if isinstance(schema_obj, exp.Schema):
                    target_table = schema_obj.this
                    if target_table:
                        target_table_prefix = target_table.sql().replace('"', '').replace('`', '').replace("'", "")
                else:
                    target_table_prefix = schema_obj.sql().replace('"', '').replace('`', '').replace("'", "")

        # 检查是否是 INSERT 语句
        if isinstance(parsed, exp.Insert):
            # 获取 INSERT 语句的目标表和列
            schema_obj = parsed.args.get("this")
            if not schema_obj:
                return [sql]

            target_table = None
            target_table_name = None
            if isinstance(schema_obj, exp.Schema):
                # 获取完整的表名（包括数据库名）
                target_table = schema_obj.this
                # 使用 sql() 方法获取完整表名，包括数据库名
                target_table_name = target_table.sql() if target_table else "temp"
                # 移除可能的引号
                target_table_name = target_table_name.replace('"', '').replace('`', '').replace("'", "")
            else:
                target_table = schema_obj
                target_table_name = schema_obj.sql()

            columns = schema_obj.expressions if hasattr(schema_obj, "expressions") else None

            # 获取 INSERT 语句的查询部分
            query = parsed.args.get("expression", None)

            # 如果查询部分不存在或不是 SELECT，则返回原始 SQL
            if query is None or (not isinstance(query, exp.Select) and not isinstance(query, exp.Union)):
                return [sql]

            # 处理 SELECT 语句中的子查询
            processed_query, subqueries = _find_subqueries(query, target_table_prefix)

            # 如果没有找到子查询，返回原始 SQL
            if not subqueries:
                return [sql]

            result = []
            processed_subqueries = set()  # 用于跟踪已处理的子查询

            # 为每个子查询创建 INSERT 语句
            for temp_table_name, subquery, alias in subqueries:
                # 检查是否已经处理过相同的子查询
                subquery_sql = subquery.sql(pretty=True)
                if subquery_sql not in processed_subqueries:
                    processed_subqueries.add(subquery_sql)
                    # 确保子查询SQL以分号结尾
                    if not subquery_sql.strip().endswith(';'):
                        subquery_sql = subquery_sql.rstrip() + ';'
                    subquery_insert = f"INSERT INTO {temp_table_name} {subquery_sql}"
                    result.append(subquery_insert)

            # 创建新的 INSERT 语句
            new_insert = exp.Insert(
                this=exp.Schema(
                    this=target_table.copy() if target_table else None,
                    expressions=[col.copy() for col in columns] if columns else None
                ),
                expression=processed_query
            )

            # 转换为 SQL
            insert_sql = new_insert.sql(pretty=True)
            if not insert_sql.strip().endswith(';'):
                insert_sql = insert_sql.rstrip() + ';'
            result.append(insert_sql)

            return result

        # 处理 SELECT 语句
        elif isinstance(parsed, exp.Select):
            # 查找子查询
            processed_query, subqueries = _find_subqueries(parsed, target_table_prefix)

            # 如果没有找到子查询，返回原始 SQL
            if not subqueries:
                return [sql]

            result = []
            processed_subqueries = set()  # 用于跟踪已处理的子查询

            # 为每个子查询创建 INSERT 语句
            for temp_table_name, subquery, alias in subqueries:
                # 检查是否已经处理过相同的子查询
                subquery_sql = subquery.sql(pretty=True)
                if subquery_sql not in processed_subqueries:
                    processed_subqueries.add(subquery_sql)
                    # 确保子查询SQL以分号结尾
                    if not subquery_sql.strip().endswith(';'):
                        subquery_sql = subquery_sql.rstrip() + ';'
                    subquery_insert = f"INSERT INTO {temp_table_name} {subquery_sql}"
                    result.append(subquery_insert)

            # 转换为 SQL
            select_sql = processed_query.sql(pretty=True)
            if not select_sql.strip().endswith(';'):
                select_sql = select_sql.rstrip() + ';'
            result.append(select_sql)

            return result

        # 其他类型的语句，返回原始 SQL
        else:
            return [sql]

    except Exception as e:
        # 出现任何异常，返回原始 SQL
        print(f"拆分子查询时出错: {e}")
        return [sql]

def _find_subqueries(query: exp.Expression, table_prefix: str = "temp") -> Tuple[exp.Expression, List[Tuple[str, exp.Expression, str]]]:
    """
    递归查找SQL语句中的所有子查询，并在找到时立即替换为临时表名

    参数:
        query: SQL表达式
        table_prefix: 临时表名前缀
    返回:
        元组，包含:
        - 替换后的查询表达式
        - 子查询列表，每个元素为(临时表名, 子查询表达式, 别名)的元组
    """
    subqueries = []
    temp_table_counter = 0

    def _process_subquery(node: exp.Expression, path: str = "", parent_node: exp.Expression = None) -> exp.Expression:
        """递归处理表达式中的子查询"""
        nonlocal temp_table_counter

        # 初始化一个新节点，用于构建结果
        result_node = node

        # 如果是子查询，处理它
        if isinstance(node, exp.Subquery):
            # 检查父节点是否为 IN 操作符，如果是则不处理该子查询
            if parent_node is not None and (
                isinstance(parent_node, exp.In) or 
                (isinstance(parent_node, exp.Not) and isinstance(parent_node.args.get("this"), exp.In))
            ):
                # 子查询在 IN 操作符内，直接返回原始节点
                return node

            # 获取子查询和别名
            subquery = node.args.get("this")
            alias = node.args.get("alias")

            # 处理子查询内部
            if subquery:
                processed_subquery = _process_subquery(subquery, f"{path}.inner", node)

                # 如果子查询有别名，创建临时表
                if alias:
                    alias_name = alias.this.this if hasattr(alias, "this") and hasattr(alias.this, "this") else f"subquery_{temp_table_counter}"

                    # 创建临时表名，使用前缀
                    temp_table_name = f"{table_prefix}_table_{temp_table_counter}"
                    temp_table_counter += 1

                    # 添加当前子查询到结果列表
                    subqueries.append((temp_table_name, processed_subquery, alias_name))

                    # 创建新的表引用，保留原始别名
                    result_node = exp.Table(this=exp.Identifier(this=temp_table_name))
                    if alias:
                        result_node = exp.Alias(
                            this=result_node,
                            alias=alias.copy()
                        )
                else:
                    # 如果子查询没有别名，直接使用处理后的表达式
                    result_node = processed_subquery

            # 处理joins（如果存在）
            joins = node.args.get("joins", [])
            if joins:
                processed_joins = []
                for join in joins:
                    processed_join = _process_subquery(join, f"{path}.join", node)
                    processed_joins.append(processed_join)

                # 如果result_node是Subquery或Select，并且有joins，我们需要特殊处理
                if isinstance(result_node, (exp.Subquery, exp.Select)):
                    # 将joins添加到result_node
                    if "joins" in result_node.args:
                        result_node.args["joins"].extend(processed_joins)
                    else:
                        result_node.args["joins"] = processed_joins
                else:
                    # 如果result_node不是Subquery或Select，我们需要创建一个新的Subquery
                    result_node = exp.Subquery(
                        this=result_node,
                        joins=processed_joins
                    )

            return result_node

        # 处理Join表达式
        elif isinstance(node, exp.Join):
            # 处理Join的this部分
            join_this = node.args.get("this")
            if join_this:
                node.args["this"] = _process_subquery(join_this, f"{path}.join_this", node)

            # 处理Join的on部分
            join_on = node.args.get("on")
            if join_on and isinstance(join_on, exp.Expression):
                node.args["on"] = _process_subquery(join_on, f"{path}.join_on", node)

            return node

        # 递归处理所有子节点
        for key, value in list(node.args.items()):
            if isinstance(value, exp.Expression):
                node.args[key] = _process_subquery(value, f"{path}.{key}", node)
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, exp.Expression):
                        value[i] = _process_subquery(item, f"{path}.{key}_{i}", node)

        return node

    # 开始递归处理
    processed_query = _process_subquery(query.copy())

    # 按照路径长度排序，确保先处理最内层的子查询
    subqueries.sort(key=lambda x: len(x[0].split('.')), reverse=True)

    # 去重，保留第一个出现的子查询
    seen = set()
    unique_subqueries = []
    for temp_table_name, subquery, alias in subqueries:
        subquery_sql = subquery.sql(pretty=True)
        if subquery_sql not in seen:
            seen.add(subquery_sql)
            unique_subqueries.append((temp_table_name, subquery, alias))

    # 处理FROM子句中的不必要括号
    if isinstance(processed_query, exp.Select):
        from_expr = processed_query.args.get("from")
        if from_expr and isinstance(from_expr, exp.From):
            from_this = from_expr.args.get("this")
            if from_this and isinstance(from_this, exp.Subquery):
                # 如果FROM子句是一个没有别名的子查询，直接使用其内部查询
                if not from_this.args.get("alias"):
                    # 保存子查询中的joins
                    subquery_this = from_this.args.get("this")
                    subquery_joins = from_this.args.get("joins", [])

                    # 如果子查询有内容且有JOIN，需要确保JOIN被保留
                    if subquery_this and subquery_joins:
                        # 创建新的FROM表达式，保留子查询内容及其JOIN
                        processed_query.args["from"] = exp.From(this=subquery_this)
                        # 将子查询的joins添加到主查询的joins中
                        if "joins" in processed_query.args:
                            processed_query.args["joins"].extend(subquery_joins)
                        else:
                            processed_query.args["joins"] = subquery_joins
                    else:
                        # 如果没有JOIN，直接使用子查询内容
                        processed_query.args["from"] = exp.From(this=subquery_this)

    return processed_query, unique_subqueries

def test_split_subquery_new():
    """
    测试修改后的split_subquery函数的功能
    """
    # 测试用例1: FROM 子句中的子查询
    sql1 = """
    INSERT INTO dbname.target_table (col1, col2)
    SELECT a, b FROM (
        SELECT x AS a, y AS b FROM source_table WHERE x > 10
    ) subquery
    WHERE b < 20
    """

    # 测试用例2: JOIN 子句中的子查询
    sql2 = """
    INSERT INTO target_table (col1, col2, col3)
    SELECT t1.a, t1.b, t2.c
    FROM table1 t1
    JOIN (
        SELECT id, AVG(value) AS c FROM table2 GROUP BY id
    ) t2 ON t1.id = t2.id
    WHERE t1.a > 10
    """

    # 测试用例3: 多个子查询
    sql3 = """
    INSERT INTO target_table (col1, col2, col3)
    SELECT t1.a, t2.b, t3.c
    FROM (SELECT id, value AS a FROM table1) t1
    JOIN (SELECT id, value AS b FROM table2) t2 ON t1.id = t2.id
    LEFT JOIN (SELECT id, value AS c FROM table3) t3 ON t1.id = t3.id
    """

    # 测试用例4: 嵌套子查询
    sql4 = """
    INSERT INTO target_table (col1, col2)
    SELECT a, b FROM (
        SELECT x AS a, y AS b FROM (
            SELECT id AS x, value AS y FROM source_table WHERE id > 5
        ) inner_subquery WHERE x > 10
    ) outer_subquery
    WHERE b < 20
    """

    # 测试用例5: 简化的复杂子查询
    sql5 = """
    INSERT INTO ABC
    SELECT d2, d3
    FROM (
        SELECT nvl(SUM(abs(t1.end_balance) * t2.cny_exchange),0) AS d2
        FROM dwd.dwd_fc_alm_occurbalance t1
        LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
        ON t1.currency_code = t2.currency_code
        AND t2.dt = '********'
        WHERE t1.subject_name NOT LIKE '%结转%'
        AND t1.dt = '********'
        AND (t1.subject_code LIKE '2012%' OR t1.subject_code LIKE '2002%')
    ) t1
    LEFT JOIN (
        SELECT nvl(SUM(CASE WHEN t1.PROD_TYPE IN('********','********')
                           THEN abs(t2.TOTAL_AMOUNT*t3.cny_exchange)
                      END),0) AS d3
        FROM odscw.ods_enscbank_mb_acct t1
        LEFT JOIN odscw.ODS_ENSCBANK_MB_ACCT_BALANCE_HIST t2
        ON t1.internal_key = t2.internal_key
        AND t2.dt = '********'
        LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t3
        ON t1.ccy = t3.currency_code
        AND t3.dt = '********'
        WHERE t2.AMT_TYPE = 'BAL' AND t1.dt = '********'
    ) t2 ON 1=1
    """
    sql6 = '''
    INSERT INTO tmp.tmp_d
SELECT
  d2,
  d3
FROM (
  (
    SELECT
      t2.cny_exchange AS d2
    FROM dwd.dwd_fc_alm_occurbalance AS t1
    LEFT JOIN DWD.DWD_PI AS t2
      ON t1.currency_code = t2.currency_code
  ) AS t1
  LEFT JOIN (
    SELECT 0 AS d3
    FROM odscw.ods_enscbank_mb_acct AS t1
    LEFT JOIN odscw.ODS_ENSCBANK AS t2
      ON t1.internal_key = t2.internal_key
    LEFT JOIN DWD.DWD_PI_MK AS t3
      ON t1.ccy = t3.currency_code
    ) AS t2
        ON 1 = 1
    )
    '''
    
    # 测试用例7: IN操作符内的子查询
    sql7 = """
    INSERT INTO result_table
    SELECT 
        t1.id, 
        t1.name,
        t1.code
    FROM source_table t1
    WHERE t1.status = 'ACTIVE'
    AND NOT t1.code IN (
        SELECT
            LOAN_LOAN_NO
        FROM ODBVIEW.SE3_ATPY_INSTR
        WHERE loan_status = 'VALID'
    )
    """
    # 测试所有用例
    test_cases = [
        ("测试用例1: FROM 子句中的子查询", sql1),
        ("测试用例2: JOIN 子句中的子查询", sql2),
        ("测试用例3: 多个子查询", sql3),
        ("测试用例4: 嵌套子查询", sql4),
        ("测试用例5: 简化的复杂子查询", sql5),
        ("测试用例6: bug fix 语句", sql6),
        ("测试用例7: IN操作符内的子查询", sql7),
    ]

    for desc, sql in test_cases:
        print(f"\n{desc}")
        print(f"原始 SQL: \n{sqlglot.parse_one(sql).sql(pretty=True)}")
        result = split_subquery(sql)
        print(f"拆分结果 ({len(result)} 条语句):")
        for i, split_sql_stmt in enumerate(result, 1):
            print(f"语句 {i}: \n {split_sql_stmt} \n")

if __name__ == "__main__":
    test_split_subquery_new()
