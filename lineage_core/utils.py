"""SQL血缘分析工具辅助函数模块

本模块提供了一系列用于SQL血缘分析的辅助函数，包括SQL文件读取、血缘结果保存、
SQL语句解析、表名提取等功能。这些函数共同支持SQL血缘分析的核心功能。
"""

import sqlglot
from sqlglot import exp
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import sys
from pathlib import Path

# 添加项目根目录到系统路径，确保可以导入项目内的其他模块
parent_dir = str(Path(__file__).resolve().parent.parent)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入项目内的其他模块
from lineage_core.logger import logger
from lineage_core.config import get_sql_dialect


def read_sql_file(file_path: str) -> str:
    """读取SQL文件内容
    
    读取指定路径的SQL文件，并返回其内容。函数会检查文件是否存在，
    并处理可能出现的权限错误和其他异常。
    
    Args:
        file_path: SQL文件的完整路径
        
    Returns:
        str: SQL文件的完整内容
        
    Raises:
        FileNotFoundError: 当指定的文件不存在时抛出
        PermissionError: 当没有权限读取文件时抛出
        Exception: 当发生其他读取错误时抛出
    """
    if not os.path.exists(file_path):
        error_msg = f"SQL文件不存在: {file_path}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)
        
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        logger.debug(f"成功读取SQL文件: {file_path}")
        return sql_content
    except PermissionError as e:
        error_msg = f"无权限读取SQL文件: {file_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise
    except Exception as e:
        error_msg = f"读取SQL文件失败: {file_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise


def save_lineage_result(lineage_map: Dict[str, List[str]], output_path: str) -> None:
    """保存血缘分析结果到JSON文件
    
    将血缘分析的结果保存为JSON格式文件。函数会自动创建输出目录（如果不存在），
    并处理可能出现的权限错误和其他异常。
    
    Args:
        lineage_map: 血缘关系字典，包含目标表与上游表的映射关系
        output_path: 输出JSON文件的完整路径
        
    Raises:
        PermissionError: 当没有权限写入文件时抛出
        OSError: 当创建输出目录失败时抛出
        Exception: 当发生其他保存错误时抛出
    """
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            logger.debug(f"创建输出目录: {output_dir}")
            
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(lineage_map, f, ensure_ascii=False, indent=2)
        logger.info(f"血缘分析结果已保存到: {output_path}")
    except PermissionError as e:
        error_msg = f"无权限写入文件: {output_path}, 错误: {str(e)}"
        logger.error(error_msg)
        raise
    except OSError as e:
        error_msg = f"创建输出目录失败: {os.path.dirname(output_path)}, 错误: {str(e)}"
        logger.error(error_msg)
        raise
    except Exception as e:
        error_msg = f"保存血缘分析结果失败: {str(e)}"
        logger.error(error_msg)
        raise


def validate_sql_files(target_sql_path: str, upstream_sql_paths: List[str]) -> bool:
    """验证SQL文件是否存在
    
    检查目标SQL文件和所有上游SQL文件是否存在，确保后续分析可以正常进行。
    
    Args:
        target_sql_path: 目标SQL文件的完整路径
        upstream_sql_paths: 上游SQL文件路径列表
        
    Returns:
        bool: 如果所有文件都存在返回True，否则返回False
    """
    # 验证目标SQL文件
    if not target_sql_path:
        logger.warning("目标SQL文件路径为空")
        return False
        
    if not os.path.exists(target_sql_path):
        logger.warning(f"目标SQL文件不存在: {target_sql_path}")
        return False
    
    # 验证上游SQL文件
    missing_files = []
    for file_path in upstream_sql_paths:
        if not file_path:
            logger.warning("上游SQL文件路径包含空值")
            return False
            
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.warning(f"以下上游SQL文件不存在: {', '.join(missing_files)}")
        return False
    
    logger.debug("所有SQL文件验证通过")
    return True


def format_lineage_result(lineage_map: Dict[str, List[str]], 
                         upstream_sql_paths: List[str]) -> Dict[str, Any]:
    """格式化血缘分析结果，增加文件路径信息
    
    将原始血缘分析结果转换为更易于理解和使用的格式，添加文件路径信息，
    并标记每个上游文件是否与目标文件有血缘关系。
    
    Args:
        lineage_map: 血缘关系字典，包含目标与上游文件的关系
        upstream_sql_paths: 上游SQL文件路径列表
        
    Returns:
        Dict[str, Any]: 格式化后的血缘关系字典，包含以下结构:
            - target: 目标文件的血缘信息
                - related_upstream: 与目标相关的上游文件列表
            - upstream_files: 所有上游文件的信息
    """
    # 输入参数验证
    if not lineage_map:
        logger.warning("血缘关系字典为空")
        
    if not upstream_sql_paths:
        logger.warning("上游SQL文件路径列表为空")
    
    # 初始化结果结构
    formatted_result = {
        "target": {
            "related_upstream": []
        },
        "upstream_files": {}
    }
    
    # 添加相关的上游文件
    target_upstreams = lineage_map.get("target", [])
    for upstream_id in target_upstreams:
        try:
            index = int(upstream_id.split("_")[1])
            if index < len(upstream_sql_paths):
                formatted_result["target"]["related_upstream"].append({
                    "id": upstream_id,
                    "file_path": upstream_sql_paths[index]
                })
            else:
                logger.warning(f"上游文件索引超出范围: {index}, 最大索引: {len(upstream_sql_paths)-1}")
        except (IndexError, ValueError) as e:
            logger.warning(f"解析上游ID失败: {upstream_id}, 错误: {str(e)}")
    
    # 添加所有上游文件信息
    for i, path in enumerate(upstream_sql_paths):
        upstream_id = f"upstream_{i}"
        formatted_result["upstream_files"][upstream_id] = {
            "file_path": path,
            "has_lineage": upstream_id in target_upstreams
        }
    
    logger.debug(f"血缘分析结果格式化完成，共有 {len(upstream_sql_paths)} 个上游文件")
    return formatted_result


def get_full_table_name(table_obj) -> str:
    """获取完整的表名（包含schema）
    
    Args:
        table_obj: sqlglot解析出的表对象
        
    Returns:
        str: 完整的表名，如果有schema则格式为'schema.table'，否则只返回表名
    """
    if isinstance(table_obj, exp.Schema):
        # 处理带schema的表名
        schema = str(table_obj.this.db)
        table = str(table_obj.this.this)
        return f"{schema}.{table}" if schema else table
    elif isinstance(table_obj, exp.Table) and hasattr(table_obj, 'db'):
        # 处理带db属性的Table
        schema = str(table_obj.db)
        table = str(table_obj.this)
        return f"{schema}.{table}" if schema else table
    return str(table_obj)


def extract_tables(sql: str) -> Optional[Tuple[List[str], List[str]]]:
    """从SQL字符串中提取输入表和输出表
    
    分析SQL语句，提取其中的输入表（被查询的表）和输出表（被写入的表）。
    只接受INSERT语句，其他类型的SQL语句将返回None。
    支持处理带schema的表名、CTE表达式、子查询等复杂SQL结构。
    
    Args:
        sql: 要分析的SQL查询字符串
    
    Returns:
        Optional[Tuple[List[str], List[str]]]: 如果输入是INSERT语句，返回包含两个列表的元组：
            - 第一个列表: 排序后的输入表名称列表（格式：schema.table_name）
            - 第二个列表: 排序后的输出表名称列表（格式：schema.table_name）
        如果输入不是INSERT语句，返回None
            
    Raises:
        Exception: 当SQL解析失败时，会记录错误但不抛出异常，而是返回None
    """
    input_tables = set()    # 存储输入表名的集合
    output_tables = set()   # 存储输出表名的集合
    cte_names = set()       # 存储CTE名称的集合

    try:
        # 解析SQL字符串为表达式
        expression = sqlglot.parse_one(sql, dialect=get_sql_dialect())
        
        # 如果不是INSERT语句，直接返回None
        if not isinstance(expression, exp.Insert):
            logger.warning(f"非INSERT语句，已跳过: {sql[:100]}...")
            return None
        
        # 收集所有CTE名称
        for cte in expression.find_all(exp.CTE):
            cte_names.add(str(cte.alias_or_name))
        
        # 处理INSERT语句中的输出表
        if hasattr(expression, 'this') and expression.this:
            table_name = get_full_table_name(expression.this)
            if table_name:  # 确保表名不为空
                output_tables.add(table_name)
                # 从输入表中移除输出表（如果存在）
                input_tables.discard(table_name)
        
        # 收集所有表引用作为潜在的输入表
        for table in expression.find_all(exp.Table):
            parent = table.parent
            # 如果父节点是Schema且是输出表的一部分，跳过
            if isinstance(parent, exp.Schema) and isinstance(parent.parent, exp.Insert):
                continue
                
            table_name = get_full_table_name(table)
            # 排除CTE定义的临时表和输出表
            if table_name and table_name not in cte_names and table_name not in output_tables:
                input_tables.add(table_name)
        
    except Exception as e:
        logger.error(f"解析SQL时出错: {e}")
        return None
    
    # 返回排序后的表名列表
    return sorted(input_tables), sorted(output_tables)


def build_sql_list_from_str(sql_str: str) -> List[str]:
    """从字符串中构建SQL语句列表
    
    将包含多个SQL语句的字符串解析为单独的SQL语句列表。
    会自动跳过DROP语句、空语句和解析错误的语句，只保留有效的SQL语句。
    
    Args:
        sql_str: 包含一个或多个SQL语句的字符串
        
    Returns:
        List[str]: 解析后的SQL语句列表
    """
    sql_statements = []
    error_count = 0
    
    # 首先按分号分割SQL语句
    raw_statements = [stmt.strip() for stmt in sql_str.split(';\n') if stmt.strip()]
    
    for raw_stmt in raw_statements:
        try:
            # 尝试解析单个语句
            parsed_stmts = sqlglot.parse(raw_stmt, dialect=get_sql_dialect())
            
            for stmt in parsed_stmts:
                if stmt:
                    # 跳过 DROP 语句
                    if stmt.key == 'drop':
                        logger.debug("跳过DROP语句")
                        continue
                        
                    sql = stmt.sql()
                    if sql.strip():  # 确保不是空语句
                        sql_statements.append(sql)
        except Exception as e:
            error_count += 1
            error_msg = f"解析SQL语句时出错，已跳过: {str(e)}\n错误语句: {raw_stmt[:100]}..."
            logger.warning(error_msg)
            # 继续处理下一条语句，不抛出异常
    
    # 输出解析结果信息
    if error_count > 0:
        logger.info(f"SQL解析完成，共有 {error_count} 条语句解析失败并被跳过")
    
    logger.debug(f"成功解析SQL语句，共获取 {len(sql_statements)} 条有效语句")
    return sql_statements


def main():
    """测试模块功能的主函数
    
    包含多种SQL查询样例，用于测试extract_tables和convert_create_table_to_insert函数的功能。
    样例包括：INSERT, UPDATE, WITH, 子查询, CREATE TABLE等多种SQL语句类型。
    """
    # 简单的INSERT语句
    sql1 = """
    INSERT INTO target_table
    SELECT * FROM source_table WHERE id > 100;
    """
    
    # 带有JOIN的SQL
    sql2 = """
    INSERT INTO result_table
    SELECT a.id, b.name, c.value
    FROM table_a a
    JOIN table_b b ON a.id = b.id
    LEFT JOIN table_c c ON a.id = c.id
    WHERE a.date > '2023-01-01';
    """
    
    # 使用WITH语句(CTE)的SQL
    sql3 = """
    WITH temp_data AS (
        SELECT id, name, value
        FROM raw_data
        WHERE status = 'active'
    )
    INSERT INTO processed_data
    SELECT id, name, value
    FROM temp_data
    WHERE value > 1000;
    """
    
    # 复杂的子查询
    sql4 = """
    INSERT INTO sales_summary
    SELECT region, product, 
           (SELECT SUM(amount) FROM daily_sales WHERE region_id = r.id) as total
    FROM regions r
    JOIN products p ON r.product_id = p.id
    WHERE r.active = true;
    """
    
    # 多表INSERT
    sql5 = """
    CREATE TABLE IF not EXISTS tmp.tmp_e AS(
        SELECT
            nvl(sum(amount),0) e1
        FROM odscw.ods_cpisms_stl_withholding
        WHERE 1=1
          and dt = '********'
          AND STARTDATE = FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'), 'yyyy-mm-dd')
    );
    """
    
    # UPDATE语句
    sql6 = """
    UPDATE customer_accounts
    SET balance = balance - (
        SELECT amount FROM transactions WHERE transaction_id = '12345'
    )
    WHERE customer_id IN (SELECT customer_id FROM transactions WHERE transaction_id = '12345');
    """
    
    # 嵌套WITH语句
    sql7 = """
    WITH 
    level1 AS (
        SELECT id, name FROM base_table WHERE status = 'active'
    ),
    level2 AS (
        SELECT l.id, l.name, d.department 
        FROM level1 l
        JOIN departments d ON l.id = d.id
    )
    INSERT INTO report_table
    SELECT id, name, department FROM level2;
    """
    # 示例8: CREATE TABLE语句转换为INSERT语句
    sql8 = """
    CREATE TABLE IF not EXISTS tmp.tmp_d AS(
    -- 财务公司存款含存入保证金余额(折人民币,境内),承兑保证金存款余额
        select 
        d2,d3
        from (
            (SELECT nvl(SUM(abs(t1.end_balance) * t2.cny_exchange),0) AS d2 -- 财务公司存款含存入保证金余额(折人民币,境内)
            from dwd.dwd_fc_alm_occurbalance t1
            LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
            on t1.currency_code = t2.currency_code
            and t2.dt = '********'
            WHERE t1.subject_name not like '%结转%'
                and t1.dt = '********'
            AND (t1.subject_code like '2012%' or t1.subject_code like '2002%')) t1
        left join 
            (SELECT
                nvl(sum(CASE when t1.PROD_TYPE in('********','********') THEN abs(t2.TOTAL_AMOUNT*t3.cny_exchange) END ),0) AS d3 -- 承兑保证金存款余额
            FROM odscw.ods_enscbank_mb_acct t1  -- 神码账户主表
                    LEFT JOIN odscw.ODS_ENSCBANK_MB_ACCT_BALANCE_HIST t2  -- 余额表
                            ON t1.internal_key = t2.internal_key
                            and t2.dt = '********'
                    LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t3
                            ON t1.ccy = t3.currency_code
                                and t3.dt = '********'
            WHERE
                    t2.AMT_TYPE = 'BAL' and t1.dt = '********') t2
        on 1=1
    )
    );
    """
    sql9 = """
    CREATE TABLE VT_LOAN_AGT_SUM_TMP5 (
      ACCT_NUM VARCHAR(60), /* 协议号 */
      AGT_MODIF_NUM VARCHAR(30), /* 协议修饰符 */
      OVDUE_BEGIN_DT DATE FORMAT 'YYYYMMDD' , /* 逾期起始日期 */
      PRIN_OVDUE_BEGIN_DT DATE FORMAT 'YYYYMMDD' , /* 本金逾期起始日期 */
      OWE_INT_BEGIN_DT DATE FORMAT 'YYYYMMDD' /* 欠息起始日期  */
    ); 
    """

    # 测试所有SQL样例
    sql_samples = [
        ("简单INSERT", sql1),
        ("带JOIN的INSERT", sql2),
        ("WITH语句", sql3),
        ("复杂子查询", sql4),
        ("多表INSERT", sql5),
        ("UPDATE语句", sql6),
        ("嵌套WITH语句", sql7),
        ("CREATE TABLE语句转换为INSERT语句", sql8),
        ("CREATE TABLE无需转换", sql9)
    ]
    
    for name, sql in sql_samples:
        result = extract_tables(sql)
        if result:
            input_tables, output_tables = result
            print(f"\n{name}:")
            print(f"SQL: {sql.strip()}")
            print(f"输入表: {input_tables}")
            print(f"输出表: {output_tables}")
            print("-" * 50)
        else:
            print(f"\n{name}:")
            print(f"SQL: {sql.strip()}")
            print("-" * 50)

if __name__ == "__main__":
    main()