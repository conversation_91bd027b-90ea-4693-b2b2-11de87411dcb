from typing import Dict, List, Set, Optional, Any, Tuple, Union
import os
import traceback
import sys
from pathlib import Path
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))
from lineage_core.logger import logger

# 导入 new_model 中的模块
from new_model.sql_parser import parse_all_sqls
from new_model.relevance_analyzer import analyze_relevance
from new_model.type_analyzer import analyze_column_types
from new_model.z3_converter import Z3Converter
import sqlglot

class LineageDynamicAnalyzer:
    """
    SQL行级血缘动态分析器，基于新模型实现数据血缘分析
    
    该分析器通过使用 new_model 中的模块实现，提供与原 LineageAnalyzer 相同的接口，
    但使用更先进的血缘计算逻辑。
    
    主要功能：
    1. 解析SQL语句并提取语法树
    2. 分析SQL语句中的表和列之间的相关性
    3. 推断字段类型
    4. 通过Z3约束求解判断数据血缘关系
    """
    
    def __init__(self) -> None:
        """
        初始化血缘动态分析器
        """
        logger.debug("初始化行级血缘动态分析器")
        # 不需要初始化 SQLParser 和 Z3Converter 实例，将在 analyze_lineage 方法中按需创建
        # 在分析过程中也不需要维护 target_columns 集合，由 relevance_analyzer 提供
    
    def analyze_lineage(self, sqls: List[Any], dialect: str = None, schema: Optional[Dict[str, str]] = None) -> Dict[str, List[str]]:
        """
        分析一组SQL的数据血缘关系
        
        该方法是血缘分析的主入口，支持分析多个SQL语句之间的血缘关系。
        使用 new_model 中的模块进行分析，但保持与原 LineageAnalyzer 相同的接口。
        
        Args:
            sqls: SQL语句列表，可以包含嵌套列表表示OR关系
            dialect: SQL方言，如果为None则使用全局配置
            schema: 字段名称和对应的字段类型的字典，包括 INT FLOAT STRING 三种类型
            
        Returns:
            血缘关系字典，包含以下键：
            - target: 相关的上游SQL ID列表
            - collected_columns: 收集到的列名列表
            - result: 是否有血缘关系的布尔值
            - error: 可选，发生错误时的错误信息
        """
        logger.info("开始行级血缘动态分析")
       
        try:
            # 初始化血缘关系字典
            lineage_map: Dict[str, List[str]] = {"target": [], "collected_columns": [], "result": True}

            # 如果SQL列表为空，直接返回
            if len(sqls) == 0:
                logger.warning("SQL列表为空，无法进行血缘分析")
                return lineage_map
            
            # 预处理SQL语句
            # 将复合SQL（如OR关系表示的SQL列表）展平为SQL字符串列表
            sql_statements_str = []
            for sql_item in sqls:
                if isinstance(sql_item, str):
                    # 分割多语句SQL
                    split_statements = [stmt.strip() for stmt in sql_item.split(';') if stmt.strip()]
                    sql_statements_str.extend(split_statements)
                elif isinstance(sql_item, list):
                    # 处理SQL列表（OR关系）
                    for sub_sql in sql_item:
                        if isinstance(sub_sql, str):
                            split_statements = [stmt.strip() for stmt in sub_sql.split(';') if stmt.strip()]
                            sql_statements_str.extend(split_statements)
            
            logger.debug(f"预处理后的SQL数量: {len(sql_statements_str)}")
            
            # 解析SQL为AST
            parsed_sqls = parse_all_sqls(sql_statements_str, dialect=dialect)
            if not parsed_sqls:
                logger.error("SQL解析失败，无法进行血缘分析")
                lineage_map["result"] = False
                lineage_map["error"] = ["SQL解析失败"]
                return lineage_map
            
            logger.info(f"成功解析 {len(parsed_sqls)} 条SQL语句")
            
            # 我们需要确定：
            # 1. 第一个SQL是目标SQL，其余为上游SQL
            # 2. 从目标SQL中提取目标表名
            
            target_sql = parsed_sqls[0]
            upstream_sqls = parsed_sqls[1:] if len(parsed_sqls) > 1 else []
            
            # 从目标SQL中提取目标表名
            target_table = None
            
            # 导入工具函数
            from lineage_core.utils import get_full_table_name
            
            # 提取目标表方法一：检查INSERT语句结构
            if isinstance(target_sql, sqlglot.exp.Insert):
                 target_table = get_full_table_name(target_sql.this).lower()
            
            if not target_table:
                logger.error("无法从任何SQL中提取表名，无法进行血缘分析")
                lineage_map["result"] = False
                lineage_map["error"] = ["无法确定目标表名"]
                return lineage_map
            
            logger.info(f"从目标SQL中提取到的表名: {target_table}")
            all_parsed_sqls = parsed_sqls
            
            # 分析相关性
            # 我们调用 analyze_relevance 来获取相关列和合格AST列表
            initial_schemas = {}  # 转换输入的schema格式如果需要
            try:
                relevant_columns, qualified_ast_list = analyze_relevance(
                    parsed_sqls=all_parsed_sqls,
                    initial_schemas=initial_schemas,
                    final_target_table_name=target_table,
                    final_target_column_name=None  # 分析所有列
                )
                
                if not relevant_columns:
                    logger.warning("未找到相关列，分析可能不完整")
                
                if not qualified_ast_list:
                    logger.warning("未找到合格AST，血缘分析可能不准确")
                    lineage_map["result"] = False
                    return lineage_map
                
                logger.info(f"相关性分析完成，找到 {len(relevant_columns)} 个表的相关列")
            except Exception as e:
                logger.error(f"相关性分析失败: {e}")
                logger.debug(traceback.format_exc())
                lineage_map["result"] = False
                lineage_map["error"] = [str(e)]
                return lineage_map
            
            # 分析列类型
            try:
                column_types, dependency_graph, defining_expressions = analyze_column_types(
                    qualified_sqls=qualified_ast_list,
                    relevant_columns=relevant_columns,
                    initial_schemas=initial_schemas  # 可能需要转换格式
                )
                
                if not column_types:
                    logger.warning("未找到列类型信息，将使用默认类型")
                
                logger.info(f"类型分析完成，找到 {len(column_types)} 个列的类型信息")
            except Exception as e:
                logger.error(f"类型分析失败: {e}")
                logger.debug(traceback.format_exc())
                lineage_map["result"] = False
                lineage_map["error"] = [str(e)]
                return lineage_map
            
            # 使用Z3转换器进行表级依赖分析
            try:
                z3_converter = Z3Converter(column_types=column_types)
                z3_converter.load_sql_to_z3(qualified_ast_list)
                
                # 收集所有源表名
                source_tables = set()
                # 首先尝试从upstream_sqls中提取源表
                for i, sql in enumerate(upstream_sqls):
                    # 导入工具函数
                    from lineage_core.utils import get_full_table_name
                    
                    # 尝试从SQL AST中提取源表
                    if hasattr(sql, 'this') and sql.this:
                        # 处理FROM子句
                        if hasattr(sql.this, 'from_') and sql.this.from_:
                            from_clause = sql.this.from_
                            if hasattr(from_clause, 'table') and from_clause.table:
                                source_tables.add(get_full_table_name(from_clause.table).lower())
                            elif hasattr(from_clause, 'name'):
                                source_tables.add(str(from_clause.name).lower())
                            
                            # 处理JOIN子句
                            if hasattr(sql.this, 'joins') and sql.this.joins:
                                for join in sql.this.joins:
                                    if hasattr(join, 'table') and join.table:
                                        source_tables.add(get_full_table_name(join.table).lower())
                    elif hasattr(sql, 'from_') and sql.from_:
                        from_clause = sql.from_
                        if hasattr(from_clause, 'table') and from_clause.table:
                            source_tables.add(get_full_table_name(from_clause.table).lower())
                        elif hasattr(from_clause, 'name'):
                            source_tables.add(str(from_clause.name).lower())
                
                # 如果从SQL解析中无法直接获取源表，使用相关性分析结果中的表
                if not source_tables and target_table:
                    # 尝试从第一个SQL语句中获取目标表作为源表
                    if len(parsed_sqls) > 0 and hasattr(parsed_sqls[0], 'table') and hasattr(parsed_sqls[0].table, 'name'):
                        first_sql_target = parsed_sqls[0].table.name.lower()
                        if first_sql_target != target_table:
                            logger.info(f"使用第一个SQL语句的目标表 {first_sql_target} 作为源表")
                            source_tables.add(first_sql_target)
                    elif len(parsed_sqls) > 0 and hasattr(parsed_sqls[0], 'this') and hasattr(parsed_sqls[0].this, 'table') and hasattr(parsed_sqls[0].this.table, 'name'):
                        first_sql_target = parsed_sqls[0].this.table.name.lower()
                        if first_sql_target != target_table:
                            logger.info(f"使用第一个SQL语句的目标表 {first_sql_target} 作为源表")
                            source_tables.add(first_sql_target)
                
                # 如果仍然无法获取源表，尝试从相关性分析中提取潜在源表
                if not source_tables and target_table:
                    # 从相关性分析中提取源表 - 所有非目标表都视为潜在源表
                    logger.info(f"从相关性分析中提取源表名，目标表: {target_table}")
                    relevant_tables = set(relevant_columns.keys())
                    for table_name in relevant_tables:
                        # 忽略临时表和目标表本身
                        if table_name != target_table and not table_name.startswith('tmp_'):
                            source_tables.add(table_name)
                    logger.info(f"提取到的潜在源表: {source_tables}")
                
                # 如果源表列表仍为空，则默认认为有血缘关系
                if not source_tables:
                    logger.warning("未找到源表，默认认为有血缘关系")
                    lineage_map["result"] = True
                    # 将所有相关列添加到收集的列中
                    for table, columns in relevant_columns.items():
                        lineage_map["collected_columns"].extend([f"{table}.{col}" for col in columns])
                    return lineage_map
                
                # 进行表级依赖分析 - 参考测试代码的处理方式
                source_tables_list = list(source_tables)
                logger.info(f"进行表级依赖分析，目标表: {target_table}, 源表: {source_tables_list}")
                
                # 逐个分析每个源表与目标表的血缘关系
                has_lineage = False
                processed_target_table = target_table.lower()
                
                for i, source_table in enumerate(source_tables_list):
                    processed_source_table = source_table.lower()
                    # 对每个源表单独进行依赖分析
                    dependencies = z3_converter.determine_table_level_dependencies(
                        target_table=processed_target_table,
                        source_tables=[processed_source_table],  # 每次只分析一个源表
                        relevant_columns=relevant_columns
                    )
                    
                    # 检查依赖结果
                    has_dependency = dependencies.get(processed_source_table, False)
                    if has_dependency:
                        has_lineage = True
                        sql_id = f"upstream_{i}"
                        lineage_map["target"].append(sql_id)
                        logger.info(f"表 {source_table} 与目标表 {target_table} 有血缘关系")
                    else:
                        logger.info(f"表 {source_table} 与目标表 {target_table} 无血缘关系")
                
                lineage_map["result"] = has_lineage
                
                # 收集所有相关列
                for table, columns in relevant_columns.items():
                    lineage_map["collected_columns"].extend([f"{table}.{col}" for col in columns])
                
                logger.info(f"血缘分析完成，结果: {lineage_map['result']}")
                
            except Exception as e:
                logger.error(f"Z3分析失败: {e}")
                logger.debug(traceback.format_exc())
                # 默认认为有血缘关系
                lineage_map["result"] = True
                lineage_map["error"] = [str(e)]
            
            return lineage_map
            
        except Exception as e:
            logger.error(f"行级血缘分析失败: {e}")
            logger.error(f"错误堆栈信息: {traceback.format_exc()}")
            # 默认认为有血缘关系，确保血缘分析不会遗漏
            return {"target": [], "error": [str(e)], "result": True} 

def main():
    """
    主函数：读取SQL文件并执行数据血缘分析
    
    用法：python -m lineage_core.lineage_dynamic_analyzer <sql文件路径> [schema_file]
    
    参数：
        sql文件路径: 包含SQL语句的文件，第一条SQL为目标SQL，其余为上游SQL
        schema_file: 可选参数，指定包含字段类型信息的JSON文件路径
    """
    import json
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='数据血缘动态分析工具')
    parser.add_argument('sql_file', help='SQL文件路径，第一条SQL为目标SQL，其余为上游SQL', nargs='?', default='lineage_graph/sql_examples/00_SELF_JOIN.sql')
    parser.add_argument('--schema', '-s', help='可选的schema文件路径（JSON格式）', default=None)
    parser.add_argument('--dialect', '-d', help='SQL方言，例如mysql, postgresql等', default=None)
    args = parser.parse_args()
    
    # 读取SQL文件
    try:
        with open(args.sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 按分号分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        if not sql_statements:
            print("错误：SQL文件中没有找到有效的SQL语句")
            sys.exit(1)
        
        logger.info(f"从文件 {args.sql_file} 中读取到 {len(sql_statements)} 条SQL语句")
    except Exception as e:
        print(f"读取SQL文件时出错: {e}")
        sys.exit(1)
    
    # 读取schema文件（如果指定）
    schema = None
    if args.schema:
        try:
            with open(args.schema, 'r', encoding='utf-8') as f:
                schema = json.load(f)
            logger.info(f"从文件 {args.schema} 中读取到schema信息")
        except Exception as e:
            print(f"读取schema文件时出错: {e}")
            sys.exit(1)
    
    # 创建分析器实例并执行分析
    analyzer = LineageDynamicAnalyzer()
    result = analyzer.analyze_lineage(sqls=sql_statements, dialect=args.dialect, schema=schema)
    
    # 输出分析结果
    print("\n数据血缘分析结果:")
    print(f"是否存在血缘关系: {'是' if result['result'] else '否'}")
    
    if 'error' in result:
        print(f"分析过程中的错误: {result['error']}")
    
    if result['target']:
        print("\n相关上游SQL ID列表:")
        for target in result['target']:
            print(f"- {target}")
    
    if result['collected_columns']:
        print("\n收集到的相关列:")
        for column in result['collected_columns']:
            print(f"- {column}")
    
    return result

if __name__ == "__main__":
    main() 