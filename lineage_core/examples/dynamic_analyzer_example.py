#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LineageDynamicAnalyzer 使用示例

本示例展示了如何使用 LineageDynamicAnalyzer 进行数据血缘分析，
并与原始的 LineageAnalyzer 进行对比。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from lineage_core.lineage_dynamic_analyzer import LineageDynamicAnalyzer
from lineage_core.lineage_analyzer import LineageAnalyzer
from lineage_core.logger import logger

def main():
    """主函数"""
    logger.info("开始数据血缘分析示例")
    
    # 创建分析器实例
    dynamic_analyzer = LineageDynamicAnalyzer()
    original_analyzer = LineageAnalyzer()
    
    # 示例SQL - 订单数据分析
    # 目标SQL：从多个源表中获取数据生成报表
    target_sql = """
    INSERT INTO monthly_report (customer_id, total_amount, order_count, avg_order_value, last_order_date)
    SELECT 
        c.customer_id,
        SUM(o.order_amount) as total_amount,
        COUNT(o.order_id) as order_count,
        AVG(o.order_amount) as avg_order_value,
        MAX(o.order_date) as last_order_date
    FROM customers c
    JOIN orders o ON c.customer_id = o.customer_id
    WHERE 
        o.order_date >= '2023-01-01'
        AND o.order_date < '2023-02-01'
        AND o.status = 'completed'
    GROUP BY c.customer_id
    """
    
    # 上游SQL - 订单表
    orders_sql = """
    INSERT INTO orders (order_id, customer_id, order_amount, order_date, status)
    SELECT 
        id as order_id,
        customer_ref as customer_id,
        total as order_amount,
        created_at as order_date,
        order_status as status
    FROM raw_orders
    WHERE order_status != 'deleted'
    """
    
    # 上游SQL - 客户表
    customers_sql = """
    INSERT INTO customers (customer_id, customer_name, customer_type, registration_date)
    SELECT 
        id as customer_id,
        name as customer_name,
        type as customer_type,
        created_at as registration_date
    FROM raw_customers
    WHERE is_active = 1
    """
    
    # 将所有SQL组合到一个列表中，目标SQL在第一位
    sql_list = [target_sql, orders_sql, customers_sql]
    
    # 定义一些字段类型信息
    schema = {
        "orders.order_date": "DATE",
        "orders.order_amount": "FLOAT",
        "customers.customer_id": "INT"
    }
    
    # 使用两种分析器进行分析
    logger.info("使用 LineageDynamicAnalyzer 进行分析")
    dynamic_result = dynamic_analyzer.analyze_lineage(sql_list, schema=schema)
    
    logger.info("使用 LineageAnalyzer 进行分析")
    original_result = original_analyzer.analyze_lineage(sql_list, schema=schema)
    
    # 打印结果对比
    logger.info("========== 分析结果对比 ==========")
    logger.info(f"动态分析器结果: {dynamic_result}")
    logger.info(f"原始分析器结果: {original_result}")
    
    # 检查动态分析器是否识别出依赖关系
    has_lineage_dynamic = dynamic_result.get("result", False)
    logger.info(f"动态分析器识别出血缘关系: {has_lineage_dynamic}")
    
    # 收集的相关列
    dynamic_columns = dynamic_result.get("collected_columns", [])
    logger.info(f"动态分析器收集的相关列数量: {len(dynamic_columns)}")
    if dynamic_columns:
        logger.info(f"前5个相关列: {dynamic_columns[:5]}")
    
    # 对比分析
    has_lineage_original = original_result.get("result", False)
    logger.info(f"原始分析器识别出血缘关系: {has_lineage_original}")
    logger.info(f"两种分析器结果一致: {has_lineage_dynamic == has_lineage_original}")
    
    # 测试过滤条件的情况
    logger.info("\n测试过滤条件的情况...")
    
    # 目标SQL：只选择VIP客户
    filter_target_sql = """
    INSERT INTO vip_report (customer_id, total_amount)
    SELECT customer_id, SUM(order_amount) as total_amount
    FROM orders o
    JOIN customers c ON o.customer_id = c.customer_id
    WHERE c.customer_type = 'VIP'
    GROUP BY customer_id
    """
    
    # 上游SQL：只插入普通客户
    filter_upstream_sql = """
    INSERT INTO customers (customer_id, customer_name, customer_type)
    SELECT id, name, 'REGULAR'
    FROM raw_customers
    """
    
    filter_sql_list = [filter_target_sql, filter_upstream_sql]
    
    logger.info("使用 LineageDynamicAnalyzer 分析过滤条件情况")
    filter_dynamic_result = dynamic_analyzer.analyze_lineage(filter_sql_list)
    
    logger.info("使用 LineageAnalyzer 分析过滤条件情况")
    filter_original_result = original_analyzer.analyze_lineage(filter_sql_list)
    
    logger.info("========== 过滤条件分析结果 ==========")
    logger.info(f"动态分析器结果: {filter_dynamic_result.get('result', False)}")
    logger.info(f"原始分析器结果: {filter_original_result.get('result', False)}")
    
    logger.info("\n血缘分析示例完成")

if __name__ == "__main__":
    main() 