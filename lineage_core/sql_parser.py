import traceback
from typing import Dict, List, Optional, Set, Union

import sqlglot
from sqlglot import expressions as exp

from lineage_core.logger import logger
from lineage_core.config import get_sql_dialect


class SQLParser:
    """SQL解析器，负责将SQL语句解析为抽象语法树(AST)并提取相关信息
    
    该类提供了一系列方法用于解析SQL语句、提取过滤条件、提取表达式和表引用信息等功能。
    所有方法都包含异常处理，确保在解析过程中出现问题时能够提供有用的错误信息。
    """
    
    def __init__(self) -> None:
        """初始化SQL解析器
        
        创建一个新的SQL解析器实例，用于后续的SQL解析和信息提取操作。
        """
        logger.debug("初始化SQL解析器实例")
    
    def parse(self, sql: str, dialect: Optional[str] = None) -> exp.Expression:
        """
        解析SQL语句为抽象语法树(AST)
        
        Args:
            sql: SQL语句字符串
            dialect: SQL方言，如果为None则使用全局配置
            
        Returns:
            解析后的AST表达式
            
        Raises:
            sqlglot.errors.ParseError: SQL解析错误
            Exception: 其他异常
        """
        try:
            # 如果未指定方言，使用全局配置的方言
            if dialect is None:
                dialect = get_sql_dialect()
                
            logger.debug(f"开始解析SQL语句，使用方言: {dialect}")
            logger.debug(f"SQL语句: {sql[:100]}{'...' if len(sql) > 100 else ''}")
            
            # 使用sqlglot解析SQL
            parsed_expression = sqlglot.parse_one(sql, dialect=dialect)
            logger.debug("SQL解析成功完成")
            return parsed_expression
        except sqlglot.errors.ParseError as e:
            # 特别处理解析错误，提供更详细的错误上下文
            error_position = getattr(e, 'position', None)
            error_context = sql[max(0, error_position-20):min(len(sql), error_position+20)] if error_position else "未知位置"
            logger.error(f"SQL解析失败: {str(e)}")
            logger.error(f"错误位置附近: {error_context}")
            raise
        except Exception as e:
            # 处理其他异常
            logger.error(f"SQL解析过程中发生未预期异常: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise
    
    def extract_filters(self, ast: exp.Expression) -> List[exp.Expression]:
        """
        从AST中提取所有过滤条件（WHERE、HAVING和JOIN条件）
        
        Args:
            ast: SQL的AST表达式
            
        Returns:
            过滤条件表达式列表
        """
        filter_expressions = []
        
        try:
            logger.debug("开始从AST中提取过滤条件")
            
            # 处理子查询中的过滤条件
            if isinstance(ast, exp.Select) and ast.args.get("from"):
                from_clause = ast.args.get("from")
                if isinstance(from_clause, exp.From):
                    for expr in from_clause.args.get("expressions", []):
                        if isinstance(expr, exp.Subquery) and expr.args.get("this"):
                            # 递归处理子查询中的过滤条件
                            logger.debug("处理子查询中的过滤条件")
                            subquery_filters = self.extract_filters(expr.args.get("this"))
                            filter_expressions.extend(subquery_filters)
            
            # 提取WHERE条件
            if isinstance(ast, exp.Select) and ast.args.get("where"):
                logger.debug("提取WHERE子句过滤条件")
                where_clause = ast.args.get("where")
                if isinstance(where_clause, exp.Where) and where_clause.args.get("this"):
                    filter_expressions.append(where_clause.args.get("this"))
                else:
                    filter_expressions.append(where_clause)
            
            # 提取HAVING条件
            if isinstance(ast, exp.Select) and ast.args.get("having"):
                logger.debug("提取HAVING子句过滤条件")
                having_clause = ast.args.get("having")
                if isinstance(having_clause, exp.Having) and having_clause.args.get("this"):
                    filter_expressions.append(having_clause.args.get("this"))
                else:
                    filter_expressions.append(having_clause)
            
            # 提取JOIN条件
            if isinstance(ast, exp.Select) and ast.args.get("joins"):
                logger.debug("提取JOIN条件")
                for join in ast.args.get("joins", []):
                    if isinstance(join, exp.Join) and join.args.get("on"):
                        filter_expressions.append(join.args.get("on"))
            
            logger.debug(f"成功提取 {len(filter_expressions)} 个过滤条件")
        except Exception as e:
            logger.warning(f"提取过滤条件时发生错误: {str(e)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
        
        return filter_expressions
    
    def _extract_join_conditions(self, node: exp.Expression, filter_list: List[exp.Expression]) -> None:
        """
        从JOIN操作中提取条件表达式
        
        Args:
            node: JOIN节点表达式
            filter_list: 用于存储提取的过滤条件的列表
        """
        try:
            if isinstance(node, exp.Join) and node.args.get("on"):
                logger.debug(f"提取JOIN条件: {node.__class__.__name__}")
                join_condition = node.args.get("on")
                filter_list.append(join_condition)
                
                # 递归处理嵌套JOIN
                if node.args.get("this"):
                    logger.debug("处理嵌套JOIN的左侧")
                    self._extract_join_conditions(node.args.get("this"), filter_list)
                if node.args.get("expression"):
                    logger.debug("处理嵌套JOIN的右侧")
                    self._extract_join_conditions(node.args.get("expression"), filter_list)
            elif isinstance(node, exp.Subquery) and node.args.get("this"):
                # 处理子查询中的过滤条件
                logger.debug("处理JOIN中的子查询过滤条件")
                subquery_filters = self.extract_filters(node.args.get("this"))
                filter_list.extend(subquery_filters)
        except Exception as e:
            logger.warning(f"提取JOIN条件时发生错误: {str(e)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
    
    def extract_select_expressions(self, ast: exp.Expression) -> List[exp.Expression]:
        """
        从AST中提取SELECT表达式列表
        
        Args:
            ast: SQL的AST表达式
            
        Returns:
            SELECT表达式列表，如果不是SELECT语句则返回空列表
        """
        try:
            if not isinstance(ast, exp.Select):
                logger.warning(f"无法从非Select表达式中提取SELECT字段: {type(ast).__name__}")
                return []
            
            logger.debug("开始提取SELECT表达式")
            select_expressions = ast.args.get("expressions", [])
            logger.debug(f"成功提取 {len(select_expressions)} 个SELECT表达式")
            return select_expressions
        except Exception as e:
            logger.warning(f"提取SELECT表达式时发生错误: {str(e)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
            return []
    
    def extract_columns_from_filter(self, filter_expr: exp.Expression) -> Set[str]:
        """
        从过滤条件中提取所有列名
        
        Args:
            filter_expr: 过滤条件表达式
            
        Returns:
            列名集合，包含所有在过滤条件中引用的列
        """
        column_names = set()
        
        def _extract_columns(node: exp.Expression) -> None:
            """
            递归提取节点中的列名
            
            Args:
                node: AST节点
            """
            if isinstance(node, exp.Column):
                # 获取完整列名（包括表名前缀，如果有的话）
                column_full_name = node.output_name
                column_names.add(column_full_name)
                logger.debug(f"提取到列名: {column_full_name}")
            
            # 递归处理子节点
            for child_key, child_value in node.args.items():
                if isinstance(child_value, exp.Expression):
                    _extract_columns(child_value)
                elif isinstance(child_value, list):
                    for item in child_value:
                        if isinstance(item, exp.Expression):
                            _extract_columns(item)
        
        try:
            logger.debug(f"开始从过滤条件提取列名: {filter_expr.__class__.__name__}")
            _extract_columns(filter_expr)
            logger.debug(f"从过滤条件中提取的列: {column_names}")
        except Exception as e:
            logger.warning(f"提取列名时发生错误: {str(e)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
        
        return column_names
    
    def get_table_references(self, ast: exp.Expression) -> List[Dict[str, str]]:
        """
        获取SQL中引用的所有表
        
        Args:
            ast: SQL的AST表达式
            
        Returns:
            表引用信息列表，每项包含表名(name)和别名(alias)
        """
        table_references = []
        
        try:
            logger.debug("开始提取SQL中的表引用信息")
            if isinstance(ast, exp.Select):
                from_clause = ast.args.get("from")
                if from_clause:
                    table_references.extend(self._extract_tables_from_node(from_clause))
                else:
                    logger.debug("SQL中没有FROM子句")
            else:
                logger.debug(f"不支持从非Select表达式中提取表引用: {type(ast).__name__}")
            
            logger.debug(f"SQL中引用的表: {table_references}")
        except Exception as e:
            logger.warning(f"提取表引用信息时发生错误: {str(e)}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
        
        return table_references
    
    def _extract_tables_from_node(self, node: exp.Expression) -> List[Dict[str, str]]:
        """
        从表达式节点中提取表引用信息
        
        Args:
            node: AST节点
            
        Returns:
            表引用信息列表，每项包含表名(name)和别名(alias)
        """
        table_list = []
        
        try:
            if isinstance(node, exp.Table):
                # 单表引用
                logger.debug(f"提取表引用: {node.name} (别名: {node.alias_or_name})")
                table_info = {
                    "name": node.name,
                    "alias": node.alias_or_name
                }
                table_list.append(table_info)
            elif isinstance(node, exp.Join):
                # 连接操作，需要递归提取左右两侧表信息
                logger.debug(f"处理JOIN操作: {node.__class__.__name__}")
                if "this" in node.args and "expression" in node.args:
                    # 提取JOIN左侧表信息
                    left_tables = self._extract_tables_from_node(node.args["this"])
                    # 提取JOIN右侧表信息
                    right_tables = self._extract_tables_from_node(node.args["expression"])
                    table_list.extend(left_tables)
                    table_list.extend(right_tables)
                else:
                    logger.warning(f"JOIN操作缺少必要参数 'this' 或 'expression': {node}")
            elif isinstance(node, (exp.From, exp.Subquery)):
                # FROM子句或子查询
                logger.debug(f"处理 {node.__class__.__name__}")
                if "expressions" in node.args:
                    # 处理FROM子句中的多个表达式
                    for expr in node.args["expressions"]:
                        table_list.extend(self._extract_tables_from_node(expr))
                elif "this" in node.args:
                    # 处理子查询
                    if isinstance(node, exp.Subquery) and isinstance(node.args["this"], exp.Select):
                        # 为子查询添加别名信息
                        logger.debug(f"处理子查询，别名: {node.alias}")
                        subquery_tables = self.get_table_references(node.args["this"])
                        if node.alias:
                            # 标记这是一个子查询表
                            table_list.append({"name": "<subquery>", "alias": node.alias})
                        table_list.extend(subquery_tables)
                    else:
                        table_list.extend(self._extract_tables_from_node(node.args["this"]))
                else:
                    logger.warning(f"FROM或子查询缺少必要参数 'expressions' 或 'this': {node}")
            else:
                logger.debug(f"未处理的节点类型: {type(node).__name__}")
        except Exception as e:
            logger.warning(f"提取表引用信息时发生错误: {str(e)}, 节点类型: {type(node).__name__}")
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
        
        return table_list