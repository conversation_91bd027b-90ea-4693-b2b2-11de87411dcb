"""
全局配置模块，用于管理所有可配置的参数
"""

# SQL解析相关配置
# SQL_DIALECT = "oracle"  # 默认SQL方言
# SQL_DIALECT = "postgres"  # 默认SQL方言
SQL_DIALECT = "teradata"  # 默认SQL方言

def set_sql_dialect(dialect: str) -> None:
    """
    设置全局SQL方言
    
    Args:
        dialect: SQL方言名称，如 "postgres", "mysql", "oracle", "sqlite", "snowflake" 等
    """
    global SQL_DIALECT
    SQL_DIALECT = dialect
    
def get_sql_dialect() -> str:
    """
    获取当前SQL方言设置
    
    Returns:
        str: 当前设置的SQL方言
    """
    return SQL_DIALECT 