#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""SQL拆分工具模块

该模块提供了一系列函数，用于将复杂的SQL语句拆分为更简单的等价形式。
主要功能包括：
1. 拆分CASE WHEN表达式
2. 拆分UNION操作
3. 拆分WITH子句(CTE)

这些函数可以帮助数据血缘分析工具更容易地处理复杂SQL语句。
"""

# 标准库导入
import os
import sys
import logging
from typing import List, Optional, Union

# 第三方库导入
import sqlglot
from sqlglot import expressions as exp

# 本地模块导入
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from lineage_core.config import get_sql_dialect

# 配置日志
logger = logging.getLogger(__name__)


def split_case_when(sql: str) -> List[str]:
    """
    将包含 CASE WHEN 的 SQL 语句转换为不含 CASE WHEN 的等价形式。

    本函数将包含 CASE WHEN 表达式的 SQL 语句拆分为多个不含 CASE WHEN 的等价 SQL 语句，
    每个 CASE WHEN 分支对应一个独立的 SQL 语句。这种转换有助于简化数据血缘分析。

    支持的语句类型：
        - SELECT 语句
        - INSERT INTO ... SELECT 语句

    支持的 CASE WHEN 语法：
        1. 简单型: CASE expr WHEN value THEN result ...
        2. 搜索型: CASE WHEN condition THEN result ...

    局限性：
        - 只处理最外层的 CASE WHEN，不处理嵌套的 CASE WHEN
        - 如果有多个 CASE WHEN, 只处理第一个遇到的
        - 不是完全等价，原始 CASE WHEN 按顺序判断条件，转化后为平行判断
        - 不处理聚合函数中的 CASE WHEN 表达式

    Args:
        sql: 输入的 SQL 语句字符串

    Returns:
        转换后的 SQL 语句列表，每个元素为一个 SQL 语句字符串。
        如果无法处理，则返回只包含原始 SQL 的列表。
    """
    try:
        # 解析 SQL 语句为抽象语法树(AST)
        ast = sqlglot.parse_one(sql, dialect=get_sql_dialect())
        logger.debug(f"成功解析SQL语句: {sql[:100]}...")

        # 处理 INSERT 语句
        if isinstance(ast, exp.Insert):
            logger.debug("检测到INSERT语句，开始处理")
            # 获取 INSERT 语句的目标表和列
            schema_obj = ast.args.get("this")
            if not schema_obj:
                logger.warning("INSERT语句缺少目标表信息，返回原始SQL")
                return [sql]

            target_table = schema_obj.this
            columns = schema_obj.expressions if hasattr(schema_obj, "expressions") else None

            # 获取 INSERT 语句的查询部分
            query = ast.args.get("expression")

            # 如果查询部分不存在或不是 SELECT，则返回原始 SQL
            if not query or not isinstance(query, exp.Select):
                logger.warning("INSERT语句的查询部分不是SELECT语句，返回原始SQL")
                return [sql]

            # 转换 SELECT 部分
            select_part = query
            transformed_selects = _transform_select_case_when(select_part)

            # 如果没有转换成功，返回原始 SQL
            if len(transformed_selects) == 1 and transformed_selects[0] == select_part.sql(pretty=True):
                logger.info("SELECT部分没有可转换的CASE WHEN表达式，返回原始SQL")
                return [sql]

            # 为每个转换后的 SELECT 创建新的 INSERT 语句
            result = []
            for i, transformed_select in enumerate(transformed_selects, 1):
                logger.debug(f"处理第{i}个转换后的SELECT语句")
                try:
                    # 解析转换后的 SELECT 语句
                    parsed_select = sqlglot.parse_one(transformed_select, dialect=get_sql_dialect())

                    # 创建新的 INSERT 语句
                    new_insert = exp.Insert(
                        this=exp.Schema(
                            this=target_table.copy() if target_table else None,
                            expressions=[col.copy() for col in columns] if columns else None
                        ),
                        expression=parsed_select
                    )

                    # 确保新INSERT语句的SELECT部分包含原始的FROM和JOIN
                    select_expr = new_insert.args.get('expression')
                    if select_expr:
                        # 复制FROM子句
                        if not select_expr.args.get('from') and query.args.get('from'):
                            select_expr.args['from'] = query.args.get('from').copy()

                        # 复制JOIN子句
                        if not select_expr.args.get('joins') and query.args.get('joins'):
                            select_expr.args['joins'] = [j.copy() for j in query.args.get('joins')]

                    # 转换为SQL字符串并确保以分号结尾
                    insert_sql = new_insert.sql(pretty=True)
                    if not insert_sql.strip().endswith(';'):
                        insert_sql = insert_sql.rstrip() + ';'
                    result.append(insert_sql)
                except Exception as e:
                    logger.error(f"处理转换后的SELECT语句时出错: {e}")
                    # 如果处理单个分支失败，继续处理其他分支
                    continue

            # 如果没有成功生成任何INSERT语句，返回原始SQL
            if not result:
                logger.warning("未能生成任何有效的转换语句，返回原始SQL")
                return [sql]

            logger.info(f"成功将INSERT语句拆分为{len(result)}个语句")
            return result

        # 处理 SELECT 语句
        elif isinstance(ast, exp.Select):
            logger.debug("检测到SELECT语句，开始处理")
            result = _transform_select_case_when(ast)
            logger.info(f"成功将SELECT语句拆分为{len(result)}个语句")
            return result

        # 其他类型的语句，返回原始 SQL
        else:
            logger.info(f"不支持处理的SQL类型: {type(ast).__name__}，返回原始SQL")
            return [sql]

    except Exception as e:
        # 出现任何异常，返回原始 SQL
        logger.error(f"转换CASE WHEN时出错: {str(e)}")
        return [sql]

def _transform_select_case_when(ast: exp.Expression) -> List[str]:
    """
    转换 SELECT 语句中的 CASE WHEN 表达式

    参数:
        ast: SELECT 语句的 AST，类型为 sqlglot.expressions.Expression
    返回:
        list[str]: 转换后的 SQL 语句列表，每个元素为一个 SQL 语句字符串
    """
    # 确保是 SELECT 语句
    if not isinstance(ast, exp.Select):
        return [ast.sql(pretty=True)]  # 非 SELECT 语句，返回原始 SQL

    # 找到 CASE WHEN 表达式
    case_expr: Optional[exp.Case] = None
    case_projection: Optional[Union[exp.Alias, exp.Case]] = None
    for projection in ast.expressions:
        # 简单类型的 CASE WHEN 可能嵌套在一个别名表达式中
        if isinstance(projection, exp.Alias) and isinstance(projection.this, exp.Case):
            case_expr = projection.this
            case_projection = projection
            break
        elif isinstance(projection, exp.Case):
            case_expr = projection
            case_projection = projection
            break

    if not case_expr:
        return [ast.sql(pretty=True)]  # 未找到 CASE WHEN 表达式，返回原始 SQL

    # 检查是否在聚合函数内部
    if case_projection and hasattr(case_projection, 'parent') and any(
        isinstance(case_projection.parent, agg)
        for agg in [exp.Sum, exp.Avg, exp.Count, exp.Max, exp.Min]
    ):
        return [ast.sql(pretty=True)]  # CASE WHEN 在聚合函数内，返回原始 SQL

    # 判断 CASE 类型
    is_simple_case: bool = 'this' in case_expr.args and case_expr.args['this'] is not None

    # 获取条件列表和默认值
    case_conditions: List[exp.If] = case_expr.args.get('ifs', [])
    default: Optional[exp.Expression] = case_expr.args.get('default')  # ELSE 子句的值

    # 获取原始的 FROM、JOIN 和 WHERE 子句
    from_clause: Optional[exp.From] = ast.args.get('from')
    joins: List[exp.Join] = ast.args.get('joins', [])
    where_clause: Optional[exp.Where] = ast.args.get('where')

    # 生成每个分支的 SELECT 语句
    select_statements: List[exp.Select] = []
    sql_list: List[str] = []  # 存储最终的 SQL 字符串列表

    # 处理每个条件
    for if_obj in case_conditions:
        # 获取结果值
        then: exp.Expression = if_obj.args['true']

        # 获取或构建条件
        if is_simple_case:
            # 简单型 CASE: 创建 expr = value 条件
            # CASE expr WHEN value THEN result
            cond: exp.EQ = exp.EQ(
                this=case_expr.args['this'].copy(),
                expression=if_obj.args['this']
            )
        else:
            # 搜索型 CASE: 直接使用条件
            # CASE WHEN condition THEN result
            cond: exp.Expression = if_obj.args['this']

        # 准备新的投影表达式列表
        new_expressions: List[exp.Expression] = []
        for proj in ast.expressions:
            if proj == case_projection:
                # 如果是 Alias 包装的 CASE
                if isinstance(case_projection, exp.Alias):
                    new_proj: exp.Alias = exp.Alias(
                        this=then,
                        alias=case_projection.alias
                    )
                else:
                    new_proj: exp.Expression = then
            else:
                new_proj: exp.Expression = proj.copy()
            new_expressions.append(new_proj)

        # 处理 WHERE 条件
        if where_clause:
            new_where: exp.Expression = exp.and_(where_clause.this.copy(), cond)
        else:
            new_where: exp.Expression = cond

        # 创建新的 SELECT 语句
        new_select: exp.Select = exp.Select(
            expressions=new_expressions,
            from_=from_clause.copy() if from_clause else None,
            joins=[j.copy() for j in joins],
            where=exp.Where(this=new_where)
        )

        # 复制其他属性（如 GROUP BY, HAVING 等）
        for key, value in ast.args.items():
            if key not in ['expressions', 'from', 'joins', 'where'] and value is not None:
                new_select.args[key] = value.copy() if hasattr(value, 'copy') else value

        select_statements.append(new_select)
        sql_list.append(new_select.sql(pretty=True))

    # 处理 ELSE 子句（如果存在）
    if default:
        # 生成否定条件
        not_conds: List[exp.Expression] = []
        for if_obj in case_conditions:
            if is_simple_case:
                # 简单型 CASE: expr != value 条件
                not_conds.append(exp.NEQ(
                    this=case_expr.args['this'].copy(),
                    expression=if_obj.args['this']
                ))
            else:
                # 搜索型 CASE: NOT condition
                not_conds.append(exp.not_(if_obj.args['this']))

        # 处理 ELSE 条件（处理 not_conds 为空的情况）
        if not_conds:
            else_cond: exp.Expression = exp.and_(*not_conds)
            # 处理 WHERE 条件
            if where_clause:
                new_where: exp.Expression = exp.and_(where_clause.this.copy(), else_cond)
            else:
                new_where: exp.Expression = else_cond
        else:
            # 如果没有否定条件（没有 WHEN 子句），只用原始 WHERE
            new_where: Optional[exp.Expression] = where_clause.this.copy() if where_clause else None

        # 准备新的投影表达式列表
        new_expressions: List[exp.Expression] = []
        for proj in ast.expressions:
            if proj == case_projection:
                # 如果是 Alias 包装的 CASE
                if isinstance(case_projection, exp.Alias):
                    new_proj: exp.Alias = exp.Alias(
                        this=default,
                        alias=case_projection.alias
                    )
                else:
                    new_proj: exp.Expression = default
            else:
                new_proj: exp.Expression = proj.copy()
            new_expressions.append(new_proj)

        # 创建 ELSE 对应的 SELECT 语句
        new_select: exp.Select = exp.Select(
            expressions=new_expressions,
            from_=from_clause.copy() if from_clause else None,
            joins=[j.copy() for j in joins],
            where=exp.Where(this=new_where) if new_where else None
        )

        # 复制其他属性（如 GROUP BY, HAVING 等）
        for key, value in ast.args.items():
            if key not in ['expressions', 'from', 'joins', 'where'] and value is not None:
                new_select.args[key] = value.copy() if hasattr(value, 'copy') else value

        select_statements.append(new_select)
        sql_list.append(new_select.sql(pretty=True))

    # 检查是否有生成的语句
    if not select_statements:
        return [ast.sql(pretty=True)]  # 没有生成语句，返回原始 SQL

    # 检查并确保每个 SQL 语句以分号结尾
    for i, sql in enumerate(sql_list):
        if not sql.strip().endswith(';'):
            sql_list[i] = sql.rstrip() + ';'
    # 返回 SQL 字符串列表
    return sql_list

def split_union(sql: Union[str, List[str]]) -> List[str]:
    """
    将包含 UNION 的 INSERT 语句拆分为多个独立的 INSERT 语句。

    Args:
        sql: 输入的 SQL 语句

    Returns:
        拆分后的 SQL 语句列表，如果不是 INSERT 语句或不包含 UNION，则返回原始 SQL
    """
    # 如果输入是列表，说明已经处理过 UNION 操作了，直接返回
    if isinstance(sql, list):
        return sql

    try:
        # 解析 SQL 语句
        parsed = sqlglot.parse_one(sql, dialect=get_sql_dialect())

        # 检查是否是 INSERT 语句
        if not isinstance(parsed, exp.Insert):
            return [sql]

        # 获取 INSERT 语句的目标表和列
        # 修正：表名和列名在 this 属性中
        schema_obj = parsed.args.get("this")
        if not schema_obj:
            return [sql]

        # 获取 INSERT 语句的查询部分
        query = parsed.args.get("expression")

        # 如果查询部分不存在或不是 UNION 操作，则返回原始 SQL
        if not query or not isinstance(query, exp.Union):
            return [sql]

        # 拆分 UNION 查询
        split_queries = _split_union_query(query)

        # 为每个拆分的查询创建新的 INSERT 语句
        result = []
        for split_query in split_queries:
            # 创建新的 INSERT 语句，确保复制表名和列名
            new_insert = exp.Insert(
                this=schema_obj,
                expression=split_query,
            )

            # 转换为SQL时保留表名和列名
            split_sql_str = new_insert.sql()
            result.append(split_sql_str+";")

        return result

    except Exception as e:
        # 如果解析失败，返回原始 SQL
        print(f"解析 SQL 时出错: {e}")
        return [sql]


def _split_union_query(query: exp.Expression) -> List[exp.Expression]:
    """
    递归拆分 UNION 查询
    """
    if not isinstance(query, exp.Union):
        return [query]

    result = []

    # 处理左侧查询
    left = query.args.get("this")
    if left:
        if isinstance(left, exp.Union):
            result.extend(_split_union_query(left))
        else:
            result.append(left)

    # 处理右侧查询
    right = query.args.get("expression")
    if right:
        if isinstance(right, exp.Union):
            result.extend(_split_union_query(right))
        else:
            result.append(right)

    return result

def split_with(sql: str) -> List[str]:
    """
    将包含 WITH 语句的 SQL 拆分为独立的 SQL 片段。

    Args:
        sql: 输入的 SQL 语句

    Returns:
        拆分后的 SQL 语句列表，包括每个 CTE 定义和主查询
    """
    try:
        # 解析 SQL 语句
        parsed = sqlglot.parse_one(sql, dialect=get_sql_dialect())

        # 检查是否包含 WITH 子句
        # 对于 INSERT 语句，WITH 子句在 expression 属性中
        if isinstance(parsed, exp.Insert):
            expression = parsed.args.get("expression")
            if expression:
                ctes = expression.args.get("with")
            else:
                ctes = None

            if not ctes or not ctes.expressions:
                return [sql]  # 不包含 WITH 子句，返回原始 SQL

            result = []

            # 提取每个 CTE 定义为独立的 SQL
            for cte in ctes.expressions:
                cte_name = cte.alias
                cte_query = cte.this

                # 将 CTE 查询转换为 INSERT INTO 语句
                cte_sql = f"INSERT INTO {cte_name} AS {cte_query.sql(pretty=True)};"
                result.append(cte_sql)

            # 移除原始 SQL 中的 WITH 子句，只保留主查询
            main_query = parsed.copy()

            # 修改 expression 中的 WITH
            if main_query.args.get("expression"):
                expression = main_query.args.get("expression").copy()
                expression.args.pop("with", None)
                main_query.args["expression"] = expression

            # 获取目标表和列
            target_table = None
            columns = None
            schema_obj = parsed.args.get("this")

            if schema_obj:
                if hasattr(schema_obj, "this") and schema_obj.this:
                    target_table = schema_obj.this.sql()

                if hasattr(schema_obj, "expressions") and schema_obj.expressions:
                    columns_str = ", ".join([col.sql() for col in schema_obj.expressions])
                    columns = f"({columns_str})"

            # 构建新的 INSERT 语句
            if target_table:
                # 获取主查询部分（SELECT 语句）
                select_part = main_query.args.get("expression")
                if select_part:
                    select_sql = select_part.sql(pretty=True)
                    if columns:
                        main_sql = f"INSERT INTO {target_table} {columns} {select_sql};"
                    else:
                        main_sql = f"INSERT INTO {target_table} {select_sql};"
                    result.append(main_sql)
                else:
                    # 如果没有 SELECT 部分，返回原始 SQL
                    return [sql]
            else:
                # 如果没有目标表，返回原始 SQL
                return [sql]

            return result

        # 处理 UPDATE 语句 - 检查是否有子查询
        # !!! 这段处理还有问题，目标表没有添加到最后的sql中，后面在看看
        elif isinstance(parsed, exp.Update):
            # 检查 FROM 子句中是否有子查询
            from_clause = parsed.args.get("from")
            if not from_clause or not from_clause.args.get("this"):
                return [sql]  # 没有 FROM 子句或子查询

            subquery = from_clause.args.get("this")
            if not isinstance(subquery, exp.Subquery):
                return [sql]  # FROM 子句不是子查询

            # 获取子查询和别名
            cte_query = subquery.args.get("this")
            cte_alias = subquery.args.get("alias")

            if not cte_query or not cte_alias:
                return [sql]  # 子查询或别名不存在

            cte_name = cte_alias.this.this if hasattr(cte_alias, "this") and hasattr(cte_alias.this, "this") else None

            if not cte_name:
                return [sql]  # 无法获取子查询别名

            result = []

            # 将子查询转换为 INSERT INTO 语句
            cte_sql = f"INSERT INTO {cte_name} AS {cte_query.sql(pretty=True)};"
            result.append(cte_sql)

            # 创建不带子查询的 UPDATE 语句
            # 这里需要假设子查询已经作为表存在
            main_query = parsed.copy()

            # 移除 FROM 子句中的子查询
            main_query.args.pop("from", None)

            # 生成新的 UPDATE 语句
            main_sql = main_query.sql(pretty=True) + ";"
            result.append(main_sql)

            return result

        # 处理 SELECT 语句
        elif isinstance(parsed, exp.Select):
            ctes = parsed.args.get("with")
            if not ctes or not ctes.expressions:
                return [sql]  # 不包含 WITH 子句，返回原始 SQL

            result = []

            # 提取每个 CTE 定义为独立的 SQL
            for cte in ctes.expressions:
                cte_name = cte.alias
                cte_query = cte.this

                # 将 CTE 查询转换为 INSERT INTO 语句
                cte_sql = f"INSERT INTO {cte_name} AS {cte_query.sql(pretty=True)};"
                result.append(cte_sql)

            # 移除原始 SQL 中的 WITH 子句，只保留主查询
            main_query = parsed.copy()
            main_query.args.pop("with", None)

            # 添加主查询
            result.append(main_query.sql(pretty=True) + ";")

            return result

        # 其他类型的语句
        else:
            return [sql]

    except Exception as e:
        # 如果解析失败，返回原始 SQL
        print(f"解析 WITH 语句时出错: {e}")
        return [sql]

def main():
    """
    测试 split_sql 函数的功能
    """
    # 测试用例1: 简单的 INSERT 语句带 UNION
    sql1 = """
    INSERT INTO target_table (col1, col2, col3)
    SELECT a, b, c FROM table1
    UNION
    SELECT x, y, z FROM table2
    """

    # 测试用例2: INSERT 语句带 UNION ALL
    sql2 = """
    INSERT INTO target_table
    SELECT a, b, c FROM table1
    UNION ALL
    SELECT x, y, z FROM table2
    """

    # 测试用例3: 多个 UNION 操作
    sql3 = """
    INSERT INTO target_table (col1, col2)
    SELECT a, b FROM table1
    UNION
    SELECT c, d FROM table2
    UNION
    SELECT e, f FROM table3
    """

    # 测试用例4: 非 INSERT 语句
    sql4 = """
    SELECT * FROM table1
    UNION
    SELECT * FROM table2
    """

    # 测试用例5: 不包含 UNION 的 INSERT 语句
    sql5 = """
    INSERT INTO target_table (col1, col2)
    SELECT a, b FROM table1
    """

    # 测试用例6: 子查询中包含 UNION 的 INSERT 语句
    sql6 = """
    INSERT INTO target_table (col1, col2)
    SELECT * FROM (
        SELECT a, b FROM table1
        UNION
        SELECT c, d FROM table2
    ) subquery
    """

    # 测试用例7: 带 WHERE 条件的 UNION 查询
    sql7 = """
    INSERT INTO target_table (col1, col2)
    SELECT a, b FROM table1 WHERE a > 10
    UNION
    SELECT c, d FROM table2 WHERE c < 20
    """

    # 测试用例8: 带复杂 WHERE 条件和 JOIN 的 UNION 查询
    sql8 = """
    INSERT INTO target_table (col1, col2, col3)
    SELECT t1.a, t1.b, t2.c
    FROM table1 t1
    JOIN table2 t2 ON t1.id = t2.id
    WHERE t1.a > 10 AND t2.c IS NOT NULL
    UNION
    SELECT t3.x, t3.y, t4.z
    FROM table3 t3
    LEFT JOIN table4 t4 ON t3.id = t4.id
    WHERE t3.x < 100 OR t4.z = 'test'
    """

    # 测试用例9: 带 GROUP BY 和 HAVING 的 UNION 查询
    sql9 = """
    INSERT INTO target_table (dept, avg_salary)
    SELECT dept, AVG(salary) as avg_salary
    FROM employees
    WHERE active = true
    GROUP BY dept
    HAVING AVG(salary) > 5000
    UNION
    SELECT 'All', AVG(salary) as avg_salary
    FROM employees
    WHERE active = true
    HAVING AVG(salary) > 3000
    """

    # 测试所有用例
    test_cases = [
        ("测试用例1: 简单的 INSERT 语句带 UNION", sql1),
        ("测试用例2: INSERT 语句带 UNION ALL", sql2),
        ("测试用例3: 多个 UNION 操作", sql3),
        ("测试用例4: 非 INSERT 语句", sql4),
        ("测试用例5: 不包含 UNION 的 INSERT 语句", sql5),
        ("测试用例6: 子查询中包含 UNION 的 INSERT 语句", sql6),
        ("测试用例7: 带 WHERE 条件的 UNION 查询", sql7),
        ("测试用例8: 带复杂 WHERE 条件和 JOIN 的 UNION 查询", sql8),
        ("测试用例9: 带 GROUP BY 和 HAVING 的 UNION 查询", sql9),
    ]

    for desc, sql in test_cases:
        print(f"\n{desc}")
        print(f"原始 SQL: {sql}")
        result = split_union(sql)
        print(f"拆分结果 ({len(result)} 条语句):")
        for i, split_sql_stmt in enumerate(result, 1):
            print(f"  语句 {i}: {split_sql_stmt}")

def test_transform_case_when():
    # 测试示例
    sql = '''
    SELECT
        T1.b AS code,
        CASE T1.b
            WHEN "s001" THEN T2.amount
            WHEN "s002" THEN T3.amount
            ELSE T1.amount
        END AS amount
    FROM T1
    INNER JOIN T2 ON T1.a = T2.a
    INNER JOIN T3 ON T1.a = T3.a
    WHERE T1.c > 0
    '''
    sql = '''
        INSERT INTO all_orders
        SELECT
            order_date,
            case when raw_orders4.code = 'S001' then raw_orders4.price
            when raw_orders4.code = 'S002' then raw_orders5.price - 1000
            else 0 end as price
        FROM
            raw_orders4 inner join raw_orders5 on raw_orders4.a = raw_orders5.a
        WHERE
            price > 100;
    '''
    try:
        transformed_sql = split_case_when(sql)
        print("原始 SQL:")
        print(sql)
        print("\n转换后的 SQL:")
        for i, split_sql_stmt in enumerate(transformed_sql, 1):
            print(f"  语句 {i}:\n {split_sql_stmt}")
            print("\n")
    except Exception as e:
        print(f"错误: {e}")


def test_split_with():
    """
    测试 split_with 函数的功能
    """
    # 测试用例1: 简单的 WITH 语句
    sql1 = """
    INSERT INTO acount_table
    WITH cte1 AS (
        SELECT a, b FROM table1 WHERE a > 10
    )
    SELECT * FROM cte1 WHERE b < 20
    """

    # 测试用例2: 多个 CTE 定义
    sql2 = """
    INSERT INTO acount_table
    WITH
    cte1 AS (
        SELECT a, b FROM table1 WHERE a > 10
    ),
    cte2 AS (
        SELECT c, d FROM table2 WHERE c < 20
    )
    SELECT cte1.a, cte2.c
    FROM cte1 JOIN cte2 ON cte1.b = cte2.d
    """

    # 测试用例3: 嵌套 CTE 定义
    sql3 = """
    WITH
    cte1 AS (
        SELECT a, b FROM table1
    ),
    cte2 AS (
        SELECT a, SUM(b) as total_b
        FROM cte1
        GROUP BY a
    )
    SELECT * FROM cte2 WHERE total_b > 100
    """

    # 测试用例4: INSERT 语句中的 WITH 子句
    sql4 = """
    INSERT INTO target_table
    WITH cte1 AS (
        SELECT a, b FROM table1 WHERE a > 10
    )
    SELECT * FROM cte1 WHERE b < 20
    """

    # 测试用例5: UPDATE 语句中的 WITH 子句
    sql5 = """
    UPDATE target_table
        SET col1 = cte1.a
        FROM (
            SELECT a, b FROM table1 WHERE a > 10
        ) AS cte1
        WHERE target_table.some_column = cte1.b;  -- 需要添加连接条件
    """

    # 测试所有用例
    test_cases = [
        ("测试用例1: 简单的 WITH 语句", sql1),
        ("测试用例2: 多个 CTE 定义", sql2),
        ("测试用例3: 嵌套 CTE 定义", sql3),
        ("测试用例4: INSERT 语句中的 WITH 子句", sql4),
        ("测试用例5: UPDATE 语句中的 WITH 子句", sql5),
    ]

    for desc, sql in test_cases:
        print(f"\n{desc}")
        print(f"原始 SQL: {sql}")
        result = split_with(sql)
        print(f"拆分结果 ({len(result)} 条语句):")
        for i, split_sql_stmt in enumerate(result, 1):
            print(f"  语句 {i}: {split_sql_stmt}")


if __name__ == "__main__":
    main()
    test_transform_case_when()
    test_split_with()