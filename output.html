<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="" xml:lang="">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>z3model</title>
  <style>
    html {
      color: #1a1a1a;
      background-color: #fdfdfd;
    }
    body {
      margin: 0 auto;
      max-width: 36em;
      padding-left: 50px;
      padding-right: 50px;
      padding-top: 50px;
      padding-bottom: 50px;
      hyphens: auto;
      overflow-wrap: break-word;
      text-rendering: optimizeLegibility;
      font-kerning: normal;
    }
    @media (max-width: 600px) {
      body {
        font-size: 0.9em;
        padding: 12px;
      }
      h1 {
        font-size: 1.8em;
      }
    }
    @media print {
      html {
        background-color: white;
      }
      body {
        background-color: transparent;
        color: black;
        font-size: 12pt;
      }
      p, h2, h3 {
        orphans: 3;
        widows: 3;
      }
      h2, h3, h4 {
        page-break-after: avoid;
      }
    }
    p {
      margin: 1em 0;
    }
    a {
      color: #1a1a1a;
    }
    a:visited {
      color: #1a1a1a;
    }
    img {
      max-width: 100%;
    }
    svg {
      height: auto;
      max-width: 100%;
    }
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.4em;
    }
    h5, h6 {
      font-size: 1em;
      font-style: italic;
    }
    h6 {
      font-weight: normal;
    }
    ol, ul {
      padding-left: 1.7em;
      margin-top: 1em;
    }
    li > ol, li > ul {
      margin-top: 0;
    }
    blockquote {
      margin: 1em 0 1em 1.7em;
      padding-left: 1em;
      border-left: 2px solid #e6e6e6;
      color: #606060;
    }
    code {
      font-family: Menlo, Monaco, Consolas, 'Lucida Console', monospace;
      font-size: 85%;
      margin: 0;
      hyphens: manual;
    }
    pre {
      margin: 1em 0;
      overflow: auto;
    }
    pre code {
      padding: 0;
      overflow: visible;
      overflow-wrap: normal;
    }
    .sourceCode {
     background-color: transparent;
     overflow: visible;
    }
    hr {
      border: none;
      border-top: 1px solid #1a1a1a;
      height: 1px;
      margin: 1em 0;
    }
    table {
      margin: 1em 0;
      border-collapse: collapse;
      width: 100%;
      overflow-x: auto;
      display: block;
      font-variant-numeric: lining-nums tabular-nums;
    }
    table caption {
      margin-bottom: 0.75em;
    }
    tbody {
      margin-top: 0.5em;
      border-top: 1px solid #1a1a1a;
      border-bottom: 1px solid #1a1a1a;
    }
    th {
      border-top: 1px solid #1a1a1a;
      padding: 0.25em 0.5em 0.25em 0.5em;
    }
    td {
      padding: 0.125em 0.5em 0.25em 0.5em;
    }
    header {
      margin-bottom: 4em;
      text-align: center;
    }
    #TOC li {
      list-style: none;
    }
    #TOC ul {
      padding-left: 1.3em;
    }
    #TOC > ul {
      padding-left: 0;
    }
    #TOC a:not(:hover) {
      text-decoration: none;
    }
    code{white-space: pre-wrap;}
    span.smallcaps{font-variant: small-caps;}
    div.columns{display: flex; gap: min(4vw, 1.5em);}
    div.column{flex: auto; overflow-x: auto;}
    div.hanging-indent{margin-left: 1.5em; text-indent: -1.5em;}
    /* The extra [class] is a hack that increases specificity enough to
       override a similar rule in reveal.js */
    ul.task-list[class]{list-style: none;}
    ul.task-list li input[type="checkbox"] {
      font-size: inherit;
      width: 0.8em;
      margin: 0 0.8em 0.2em -1.6em;
      vertical-align: middle;
    }
    /* CSS for syntax highlighting */
    html { -webkit-text-size-adjust: 100%; }
    pre > code.sourceCode { white-space: pre; position: relative; }
    pre > code.sourceCode > span { display: inline-block; line-height: 1.25; }
    pre > code.sourceCode > span:empty { height: 1.2em; }
    .sourceCode { overflow: visible; }
    code.sourceCode > span { color: inherit; text-decoration: inherit; }
    div.sourceCode { margin: 1em 0; }
    pre.sourceCode { margin: 0; }
    @media screen {
    div.sourceCode { overflow: auto; }
    }
    @media print {
    pre > code.sourceCode { white-space: pre-wrap; }
    pre > code.sourceCode > span { text-indent: -5em; padding-left: 5em; }
    }
    pre.numberSource code
      { counter-reset: source-line 0; }
    pre.numberSource code > span
      { position: relative; left: -4em; counter-increment: source-line; }
    pre.numberSource code > span > a:first-child::before
      { content: counter(source-line);
        position: relative; left: -1em; text-align: right; vertical-align: baseline;
        border: none; display: inline-block;
        -webkit-touch-callout: none; -webkit-user-select: none;
        -khtml-user-select: none; -moz-user-select: none;
        -ms-user-select: none; user-select: none;
        padding: 0 4px; width: 4em;
        color: #aaaaaa;
      }
    pre.numberSource { margin-left: 3em; border-left: 1px solid #aaaaaa;  padding-left: 4px; }
    div.sourceCode
      {   }
    @media screen {
    pre > code.sourceCode > span > a:first-child::before { text-decoration: underline; }
    }
    code span.al { color: #ff0000; font-weight: bold; } /* Alert */
    code span.an { color: #60a0b0; font-weight: bold; font-style: italic; } /* Annotation */
    code span.at { color: #7d9029; } /* Attribute */
    code span.bn { color: #40a070; } /* BaseN */
    code span.bu { color: #008000; } /* BuiltIn */
    code span.cf { color: #007020; font-weight: bold; } /* ControlFlow */
    code span.ch { color: #4070a0; } /* Char */
    code span.cn { color: #880000; } /* Constant */
    code span.co { color: #60a0b0; font-style: italic; } /* Comment */
    code span.cv { color: #60a0b0; font-weight: bold; font-style: italic; } /* CommentVar */
    code span.do { color: #ba2121; font-style: italic; } /* Documentation */
    code span.dt { color: #902000; } /* DataType */
    code span.dv { color: #40a070; } /* DecVal */
    code span.er { color: #ff0000; font-weight: bold; } /* Error */
    code span.ex { } /* Extension */
    code span.fl { color: #40a070; } /* Float */
    code span.fu { color: #06287e; } /* Function */
    code span.im { color: #008000; font-weight: bold; } /* Import */
    code span.in { color: #60a0b0; font-weight: bold; font-style: italic; } /* Information */
    code span.kw { color: #007020; font-weight: bold; } /* Keyword */
    code span.op { color: #666666; } /* Operator */
    code span.ot { color: #007020; } /* Other */
    code span.pp { color: #bc7a00; } /* Preprocessor */
    code span.sc { color: #4070a0; } /* SpecialChar */
    code span.ss { color: #bb6688; } /* SpecialString */
    code span.st { color: #4070a0; } /* String */
    code span.va { color: #19177c; } /* Variable */
    code span.vs { color: #4070a0; } /* VerbatimString */
    code span.wa { color: #60a0b0; font-weight: bold; font-style: italic; } /* Warning */
  </style>
  <script
  src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-chtml-full.js"
  type="text/javascript"></script>
</head>
<body>
<h1 id="sql_to_z3-模块设计与使用详解"><code>sql_to_z3</code>
模块设计与使用详解</h1>
<h2 id="简介">1. 简介</h2>
<p><code>sql_to_z3</code>
模块的核心目标是将SQL查询中的关键概念（如表、列、条件表达式等）转换为<a
href="https://github.com/Z3Prover/z3">Z3定理证明器</a>的逻辑表达式。通过这种转换，我们可以利用Z3的强大功能来：</p>
<ul>
<li>检查数据模型的一致性。</li>
<li>分析复杂约束的可满足性。</li>
<li>查找满足特定条件的示例数据（模型）。</li>
<li>对数据依赖关系进行推理。</li>
</ul>
<h3 id="核心组件">核心组件</h3>
<p>该模块主要由以下几个核心类构成：</p>
<ol type="1">
<li><strong><code>SqlToZ3</code></strong>：作为总体的协调者和Z3求解器的包装器。它负责管理表定义、列对应的Z3函数、用户添加的全局约束以及JOIN操作的条件。</li>
<li><strong><code>Table</code></strong>：代表数据库中的一个表。它管理表的结构（列名和类型），并提供插入数据（即为行添加约束）的方法。</li>
<li><strong><code>Column</code></strong>：代表表中的一个列。通过重载Python的运算符，<code>Column</code>对象可以方便地用于构建比较表达式和算术表达式。</li>
<li><strong><code>ColumnConstraint</code></strong>：表示一个列与常量之间的比较（例如
<code>age &gt; 30</code>）。这是一个”延迟应用”的约束，只有当它被显式添加到<code>SqlToZ3</code>求解器时，或者在更复杂的表达式中被求值时，才会生成具体的Z3约束。</li>
<li><strong><code>Expression</code></strong>：代表一个可以被求值为Z3表达式的任意计算。它通常由<code>Column</code>对象的运算（如
<code>salary * 0.1</code>）或多个条件的逻辑组合（如
<code>cond1 AND cond2</code>）产生。</li>
</ol>
<h3 id="基本工作流程">基本工作流程</h3>
<p>使用<code>sql_to_z3</code>模块进行数据建模和分析通常遵循以下步骤：</p>
<ol type="1">
<li><p><strong>初始化</strong>：创建一个 <code>SqlToZ3</code> 实例。</p>
<div class="sourceCode" id="cb1"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb1-1"><a href="#cb1-1" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> new_model.sql_to_z3 <span class="im">import</span> SqlToZ3, Table, Column, Expression</span>
<span id="cb1-2"><a href="#cb1-2" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> z3 <span class="im">import</span> IntSort, StringSort, BoolSort, RealSort, sat, unsat</span>
<span id="cb1-3"><a href="#cb1-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb1-4"><a href="#cb1-4" aria-hidden="true" tabindex="-1"></a>s <span class="op">=</span> SqlToZ3()</span></code></pre></div></li>
<li><p><strong>定义表结构</strong>：使用
<code>SqlToZ3.create_table()</code>
方法定义表的名称和列（及其Z3类型）。</p>
<div class="sourceCode" id="cb2"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb2-1"><a href="#cb2-1" aria-hidden="true" tabindex="-1"></a>users <span class="op">=</span> s.create_table(<span class="st">&quot;users&quot;</span>, {</span>
<span id="cb2-2"><a href="#cb2-2" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;id&quot;</span>: IntSort(), </span>
<span id="cb2-3"><a href="#cb2-3" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;name&quot;</span>: StringSort(), </span>
<span id="cb2-4"><a href="#cb2-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;age&quot;</span>: IntSort(),</span>
<span id="cb2-5"><a href="#cb2-5" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;email_verified&quot;</span>: BoolSort()</span>
<span id="cb2-6"><a href="#cb2-6" aria-hidden="true" tabindex="-1"></a>})</span></code></pre></div></li>
<li><p><strong>插入数据/定义行级约束</strong>：使用
<code>Table.insert()</code>
方法向表中添加数据。这实际上是为表中的新行或现有行添加Z3约束。</p>
<div class="sourceCode" id="cb3"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb3-1"><a href="#cb3-1" aria-hidden="true" tabindex="-1"></a>users.insert({<span class="st">&quot;id&quot;</span>: <span class="dv">1</span>, <span class="st">&quot;name&quot;</span>: <span class="st">&quot;Alice&quot;</span>, <span class="st">&quot;age&quot;</span>: <span class="dv">30</span>, <span class="st">&quot;email_verified&quot;</span>: <span class="va">True</span>})</span>
<span id="cb3-2"><a href="#cb3-2" aria-hidden="true" tabindex="-1"></a>users.insert({<span class="st">&quot;id&quot;</span>: <span class="dv">2</span>, <span class="st">&quot;name&quot;</span>: <span class="st">&quot;Bob&quot;</span>, <span class="st">&quot;age&quot;</span>: <span class="dv">25</span>, <span class="st">&quot;email_verified&quot;</span>: <span class="va">False</span>})</span></code></pre></div></li>
<li><p><strong>构建条件和表达式</strong>：使用 <code>Column</code>
对象及其重载的运算符来构建查询条件或计算表达式。</p>
<div class="sourceCode" id="cb4"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb4-1"><a href="#cb4-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 列与常量比较，生成 ColumnConstraint</span></span>
<span id="cb4-2"><a href="#cb4-2" aria-hidden="true" tabindex="-1"></a>age_condition <span class="op">=</span> users.age <span class="op">&gt;</span> <span class="dv">28</span></span>
<span id="cb4-3"><a href="#cb4-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb4-4"><a href="#cb4-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 列与列进行算术运算，生成 Expression</span></span>
<span id="cb4-5"><a href="#cb4-5" aria-hidden="true" tabindex="-1"></a>age_plus_ten <span class="op">=</span> users.age <span class="op">+</span> <span class="dv">10</span></span></code></pre></div></li>
<li><p><strong>添加约束到求解器</strong>：使用
<code>SqlToZ3.add()</code>
方法将构建的条件（<code>ColumnConstraint</code>、<code>Expression</code>
或直接的Z3 <code>BoolRef</code>）添加到求解器。</p>
<div class="sourceCode" id="cb5"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb5-1"><a href="#cb5-1" aria-hidden="true" tabindex="-1"></a>s.add(age_condition) </span></code></pre></div></li>
<li><p><strong>检查可满足性</strong>：调用 <code>SqlToZ3.check()</code>
检查当前所有约束是否可满足。</p>
<div class="sourceCode" id="cb6"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb6-1"><a href="#cb6-1" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> s.check() <span class="op">==</span> sat:</span>
<span id="cb6-2"><a href="#cb6-2" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="st">&quot;约束集合可满足&quot;</span>)</span>
<span id="cb6-3"><a href="#cb6-3" aria-hidden="true" tabindex="-1"></a><span class="cf">else</span>:</span>
<span id="cb6-4"><a href="#cb6-4" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="st">&quot;约束集合不可满足&quot;</span>)</span></code></pre></div></li>
<li><p><strong>获取模型</strong>：如果可满足，调用
<code>SqlToZ3.get_model()</code>
获取一个满足所有约束的具体实例（模型）。</p>
<div class="sourceCode" id="cb7"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb7-1"><a href="#cb7-1" aria-hidden="true" tabindex="-1"></a>model <span class="op">=</span> s.get_model()</span>
<span id="cb7-2"><a href="#cb7-2" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> model:</span>
<span id="cb7-3"><a href="#cb7-3" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="st">&quot;模型已找到&quot;</span>)</span></code></pre></div></li>
<li><p><strong>在模型中求值</strong>：使用 <code>SqlToZ3.eval()</code>
方法在获取的模型中对特定的Z3表达式（通常是表中的某个字段在某一行上的值）进行求值。</p>
<div class="sourceCode" id="cb8"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb8-1"><a href="#cb8-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 需要一个行变量来指定对哪一行求值</span></span>
<span id="cb8-2"><a href="#cb8-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 例如，如果我们想找到满足 age_condition 的 Alice 的信息</span></span>
<span id="cb8-3"><a href="#cb8-3" aria-hidden="true" tabindex="-1"></a><span class="co"># (这需要更复杂的查询构造，见后续示例)</span></span></code></pre></div></li>
</ol>
<h2 id="sql-到-z3-的数学建模原理">2. SQL 到 Z3 的数学建模原理</h2>
<p>将SQL查询和数据模型转换为Z3可以理解的约束，是 <code>sql_to_z3</code>
模块的核心功能。这个转换过程依赖于一套数学建模方法，将关系型数据库中的概念映射到Z3的逻辑构造中。下面我们详细拆解这一过程。</p>
<h3 id="核心概念的数学表示">2.1. 核心概念的数学表示</h3>
<ol type="1">
<li><strong>表 (Table) 与 行 (Row)</strong>:
<ul>
<li>数学上，一个关系数据库表 <span class="math inline">\(T\)</span>
可以被视为一个模式，定义了一组列 <span class="math inline">\(\{C_1, C_2,
\ldots, C_k\}\)</span> 及其对应的数据类型 <span
class="math inline">\(\{D_1, D_2, \ldots, D_k\}\)</span>。</li>
<li>表中的每一行（或记录）在逻辑上可以通过一个唯一的非负整数索引 <span
class="math inline">\(i \in \mathbb{N}_0\)</span> 来标识。</li>
<li>在Z3中，行索引通常由一个 <code>IntSort</code> 类型的变量 <span
class="math inline">\(r\)</span> 表示，即 <span class="math inline">\(r
\in \text{IntSort}\)</span>。</li>
</ul></li>
<li><strong>列 (Column) 作为函数</strong>:
<ul>
<li>表 <span class="math inline">\(T\)</span> 中的每一列 <span
class="math inline">\(C_j\)</span>（数据类型为 <span
class="math inline">\(D_j\)</span>）被建模为一个Z3中的<strong>一元函数</strong>
<span class="math inline">\(f_{T.C_j}\)</span>。</li>
<li>此函数的定义域是行索引的集合（由 <code>IntSort</code>
表示），值域是列 <span class="math inline">\(C_j\)</span> 的数据类型
<span class="math inline">\(D_j\)</span> 对应的Z3排序 (Sort)。 <span
class="math display">\[f_{T.C_j} : \text{IntSort} \rightarrow
\text{Sort}(D_j)\]</span> 其中 <span
class="math inline">\(\text{Sort}(D_j)\)</span> 是SQL类型 <span
class="math inline">\(D_j\)</span> 对应的Z3排序，例如：
<ul>
<li><span class="math inline">\(\text{Sort}(\text{INT}) =
\text{IntSort}\)</span></li>
<li><span class="math inline">\(\text{Sort}(\text{STRING}) =
\text{StringSort}\)</span></li>
<li><span class="math inline">\(\text{Sort}(\text{BOOLEAN}) =
\text{BoolSort}\)</span></li>
<li><span class="math inline">\(\text{Sort}(\text{REAL}) =
\text{RealSort}\)</span></li>
<li><span class="math inline">\(\text{Sort}(\text{DATE}) =
\text{IntSort}\)</span> （日期以整数形式表示，如20230101）</li>
</ul></li>
<li><strong>示例</strong>: 对于表 <code>Employees</code> (简记为 <span
class="math inline">\(E\)</span>) 中的列
<code>Salary (REAL)</code>，其对应的Z3函数为 <span
class="math inline">\(f_{E.\text{Salary}} : \text{IntSort} \rightarrow
\text{RealSort}\)</span>。给定一个行索引变量 <span
class="math inline">\(r_E\)</span>，表达式 <span
class="math inline">\(f_{E.\text{Salary}}(r_E)\)</span> 代表了
<code>Employees</code> 表中由 <span class="math inline">\(r_E\)</span>
指向的那一行的 <code>Salary</code> 值。</li>
</ul></li>
<li><strong>表的大小 (Number of Rows)</strong>:
<ul>
<li>每个表 <span class="math inline">\(T\)</span> 维护一个行ID列表 <span
class="math inline">\(rows_T\)</span>，存储了所有已分配的有效行ID。
<span class="math display">\[rows_T = [id_1, id_2, \ldots,
id_m]\]</span></li>
<li>任何代表表 <span class="math inline">\(T\)</span>
中某一具体行的行索引变量 <span class="math inline">\(r_T\)</span>
都必须满足以下约束，以确保其有效性： <span class="math display">\[r_T
\in rows_T\]</span></li>
<li>在Z3表达式中，这通常表示为： <span
class="math display">\[\text{Or}([r_T = id_1, r_T = id_2, \ldots, r_T =
id_m])\]</span></li>
</ul></li>
<li><strong>数据实例 (Data Insertion)</strong>:
<ul>
<li>一条SQL <code>INSERT</code> 语句，例如
<code>INSERT INTO T (C_1, C_2, ..., C_k) VALUES (v_1, v_2, ..., v_k);</code>，在Z3中通过添加一组等式约束来实现。</li>
<li>假设这条记录是插入到表 <span class="math inline">\(T\)</span> 中的第
<span class="math inline">\(i\)</span> 条记录（<span
class="math inline">\(i\)</span> 是一个通过
<code>_get_next_row_id()</code> 生成的行ID）。</li>
<li>其对应的Z3约束集合为： <span class="math display">\[\{ f_{T.C_j}(i)
= \text{Z3Val}(v_j) \mid j = 1, \ldots, k \}\]</span> 其中 <span
class="math inline">\(\text{Z3Val}(v_j)\)</span> 是将SQL常量值 <span
class="math inline">\(v_j\)</span>
转换为其对应的Z3常量表示（例如，<code>IntVal(10)</code>，<code>StringVal("Alice")</code>，<code>RealVal(60000.0)</code>）。对于日期类型，字符串形式如”2023-01-01”会被转换为整数形式，如<code>IntVal(20230101)</code>。</li>
<li>同时，行ID被添加到表的行列表中： <span class="math display">\[rows_T
\gets rows_T \cup \{i\}\]</span></li>
</ul></li>
</ol>
<h3 id="sql操作到z3约束的转换">2.2. SQL操作到Z3约束的转换</h3>
<ol type="1">
<li><strong><code>WHERE</code> 子句 (条件选择)</strong>:
<ul>
<li>考虑一个SQL查询
<code>SELECT ... FROM T WHERE P(C_1, C_2, ..., C_k);</code>，其中 <span
class="math inline">\(P\)</span> 是一个作用于列值的逻辑谓词。</li>
<li>该谓词 <span class="math inline">\(P\)</span>
会被转换为一个Z3布尔表达式 <span
class="math inline">\(P_{Z3}\)</span>。</li>
<li>我们寻找的是一个行索引 <span class="math inline">\(r_T \in
\text{IntSort}\)</span> (通常是一个自由变量)，它满足：
<ol type="1">
<li>行有效性: <span class="math inline">\(0 \le r_T &lt;
N_T\)</span></li>
<li>谓词条件: <span class="math inline">\(P_{Z3}(f_{T.C_1}(r_T),
f_{T.C_2}(r_T), \ldots, f_{T.C_k}(r_T))\)</span></li>
</ol></li>
<li><strong>示例</strong>:
<code>SELECT Name FROM Employees E WHERE E.Salary &gt; 50000.0 AND E.ID &lt; 10;</code>
设 <span class="math inline">\(r_E\)</span> 是 <code>Employees</code>
表的一个行索引变量。对应的Z3约束为： <span class="math display">\[(0 \le
r_E &lt; N_E) \land (f_{E.\text{Salary}}(r_E) &gt;
\text{RealVal}(50000.0)) \land (f_{E.\text{ID}}(r_E) &lt;
\text{IntVal}(10))\]</span></li>
</ul></li>
<li><strong><code>JOIN</code> 操作 (表连接)</strong>:
<ul>
<li>考虑一个SQL <code>JOIN</code> 操作，例如
<code>SELECT ... FROM T_1 JOIN T_2 ON T_1.C_A = T_2.C_B;</code></li>
<li>我们需要引入两个独立的行索引变量：<span class="math inline">\(r_1
\in \text{IntSort}\)</span> 用于表 <span
class="math inline">\(T_1\)</span>，以及 <span class="math inline">\(r_2
\in \text{IntSort}\)</span> 用于表 <span
class="math inline">\(T_2\)</span>。</li>
<li>相应的表大小变量为 <span class="math inline">\(N_{T_1}\)</span> 和
<span class="math inline">\(N_{T_2}\)</span>。</li>
<li>描述JOIN的核心Z3约束集合包括：
<ol type="1">
<li><span class="math inline">\(T_1\)</span> 行有效性: <span
class="math inline">\(0 \le r_1 &lt; N_{T_1}\)</span></li>
<li><span class="math inline">\(T_2\)</span> 行有效性: <span
class="math inline">\(0 \le r_2 &lt; N_{T_2}\)</span></li>
<li>JOIN 条件: <span class="math inline">\(f_{T_1.C_A}(r_1) =
f_{T_2.C_B}(r_2)\)</span></li>
</ol></li>
<li>如果JOIN查询还包含额外的 <code>WHERE</code> 条件，例如
<code>WHERE Q(T_1.C_X, T_2.C_Y)</code>，则会添加相应的Z3约束 <span
class="math inline">\(Q_{Z3}(f_{T_1.C_X}(r_1),
f_{T_2.C_Y}(r_2))\)</span>。</li>
</ul></li>
</ol>
<h3 id="总结与-sql_to_z3-的角色">2.3. 总结与 <code>sql_to_z3</code>
的角色</h3>
<p><code>sql_to_z3</code> 模块通过其类 (<code>SqlToZ3</code>,
<code>Table</code>, <code>Column</code>, <code>ColumnConstraint</code>,
<code>Expression</code>) 封装了上述数学建模的复杂性。</p>
<ul>
<li><code>Table.create_table()</code> 会自动为表的每一列创建Z3函数声明
(存储在 <code>SqlToZ3.column_functions</code>)。</li>
<li><code>Table.insert()</code>
会将数据转换为对特定行ID的列函数应用的Z3约束，并管理表的行数变量
(<code>Table.rows_z3_var</code>)。</li>
<li><code>Column</code> 对象的运算符重载 (如 <code>&gt;</code>,
<code>==</code>, <code>+</code>) 使得用户可以用类似SQL的语法构建
<code>ColumnConstraint</code> 或 <code>Expression</code> 对象。</li>
<li>当这些对象被 <code>SqlToZ3.add()</code>
方法处理时，它们会根据上下文（单表约束、JOIN约束等）生成恰当的Z3断言，包括创建自由行变量、添加行有效性约束，并将核心逻辑转换为Z3表达式。</li>
</ul>
<p>通过这种方式，用户可以专注于描述其数据模型和查询逻辑，而将SQL结构到Z3逻辑约束的底层转换细节交给
<code>sql_to_z3</code> 模块处理。</p>
<h2 id="核心类详解">3. 核心类详解</h2>
<h3 id="sqltoz3">3.1. <code>SqlToZ3</code></h3>
<p>此类是整个转换框架的核心，扮演着Z3求解器的接口和状态管理器的角色。</p>
<ul>
<li><strong>目的</strong>：
<ul>
<li>封装Z3求解器实例 (<code>z3.Solver</code>)。</li>
<li>维护已定义的表 (<code>Table</code> 对象) 的注册表。</li>
<li>存储每个表列对应的Z3函数（例如，<code>users.id</code>
列可能对应一个名为 <code>users.id_func</code>
的Z3函数，该函数接受一个<code>IntSort</code>的行ID，返回该列的Z3类型的值）。</li>
<li>管理用户添加的全局约束列表。</li>
<li>记录和处理多表JOIN操作产生的行变量对和约束。</li>
<li>提供缓存机制 (<code>_constraint_cache</code>)
避免重复添加相同的约束。</li>
</ul></li>
<li><strong>关键属性</strong>：
<ul>
<li><code>self.solver: TypeAwareEnhancedSolver</code>:
Z3求解器实例，使用自定义的增强型求解器。</li>
<li><code>self.tables: Dict[str, Table]</code>: 表名到
<code>Table</code> 实例的映射。</li>
<li><code>self.column_functions: Dict[str, z3.FuncDeclRef]</code>:
完全限定的列名（如
<code>tablename_colname</code>）到其对应Z3函数的映射。这些函数通常接受一个<code>IntSort</code>的行ID，返回该列的Z3类型的值。</li>
<li><code>self.constraints: List[Any]</code>:
已添加到求解器的原始约束列表，用于调试和追踪。</li>
<li><code>self.join_conditions: List[Dict[str, Any]]</code>:
存储JOIN操作产生的信息，每项包含 {‘v1’: 第一个变量, ‘t1’: 第一个表名,
‘v2’: 第二个变量, ‘t2’: 第二个表名}。</li>
<li><code>self._constraint_cache: List[z3.ExprRef]</code>:
用于缓存已转换为Z3表达式的约束，避免重复添加相同约束。</li>
<li><code>self.logger</code>: 日志记录器实例，从
<code>lineage_core.logger</code> 模块获取。</li>
<li><code>self._var_suffix_counter</code>:
用于生成唯一的Z3变量名后缀。</li>
<li><code>self.date_type</code>: DateType实例，用于处理日期类型。</li>
</ul></li>
<li><strong>关键方法</strong>：
<ul>
<li><p><code>__init__(self)</code>:
初始化求解器、日志记录器和内部状态。</p></li>
<li><p><code>create_table(self, table_name: str, columns_with_types: Dict[str, z3.SortRef]) -&gt; Table</code>:</p>
<ul>
<li><p>创建一个新的 <code>Table</code> 对象并注册。</p></li>
<li><p>为表中的每一列创建一个Z3函数（例如
<code>FuncDeclRef(f'{table_name}.{col_name}', IntSort(), col_type)</code>），并存储在
<code>self.column_functions</code> 中。</p></li>
<li><p>支持日期类型列的创建，可以使用<code>DateType()</code>实例指定日期类型。</p></li>
<li><p><strong>SQL 示例</strong>:
<code>CREATE TABLE products (pid INT, name STRING, price REAL, create_date DATE);</code></p></li>
<li><p><strong>转换</strong>:</p>
<div class="sourceCode" id="cb9"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb9-1"><a href="#cb9-1" aria-hidden="true" tabindex="-1"></a>products_table <span class="op">=</span> s.create_table(<span class="st">&quot;products&quot;</span>, {</span>
<span id="cb9-2"><a href="#cb9-2" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;pid&quot;</span>: IntSort(), </span>
<span id="cb9-3"><a href="#cb9-3" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;name&quot;</span>: StringSort(), </span>
<span id="cb9-4"><a href="#cb9-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;price&quot;</span>: RealSort(),</span>
<span id="cb9-5"><a href="#cb9-5" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;create_date&quot;</span>: DateType()  <span class="co"># 使用DateType实例表示日期类型</span></span>
<span id="cb9-6"><a href="#cb9-6" aria-hidden="true" tabindex="-1"></a>})</span>
<span id="cb9-7"><a href="#cb9-7" aria-hidden="true" tabindex="-1"></a><span class="co"># 内部会创建 s.column_functions[&#39;products.pid&#39;] 等 Z3 函数</span></span></code></pre></div></li>
</ul></li>
<li><p><code>add(self, constraint: Any)</code>:
将约束添加到Z3求解器。</p>
<ul>
<li><strong>处理 <code>ColumnConstraint</code></strong>: 调用其
<code>apply()</code>
方法生成Z3表达式并添加。根据约束列的表名，可能会寻找已存在的JOIN行变量来使用。</li>
<li><strong>处理 <code>Expression</code></strong>: 调用其
<code>as_z3_expr()</code>
方法（通常需要一个代表行的Z3变量，<code>add</code>方法会为此创建一个自由变量），然后添加结果。</li>
<li><strong>处理4元素元组 (JOINs)</strong>: 如果元组包含4个元素
<code>(var1, table1_name, var2, table2_name)</code>，解析为一个JOIN条件字典并记录到
<code>self.join_conditions</code>。</li>
<li><strong>处理直接的Z3 <code>BoolRef</code></strong>:
直接添加到求解器。</li>
<li><strong>处理Python <code>bool</code></strong>: 转换为Z3的
<code>BoolVal</code> 后添加。</li>
</ul>
<p><strong>各类型约束的示例代码</strong>：</p>
<div class="sourceCode" id="cb10"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb10-1"><a href="#cb10-1" aria-hidden="true" tabindex="-1"></a><span class="co"># 初始化solver和表</span></span>
<span id="cb10-2"><a href="#cb10-2" aria-hidden="true" tabindex="-1"></a>s <span class="op">=</span> SqlToZ3()</span>
<span id="cb10-3"><a href="#cb10-3" aria-hidden="true" tabindex="-1"></a>users <span class="op">=</span> s.create_table(<span class="st">&quot;users&quot;</span>, {</span>
<span id="cb10-4"><a href="#cb10-4" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;id&quot;</span>: IntSort(),</span>
<span id="cb10-5"><a href="#cb10-5" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;name&quot;</span>: StringSort(),</span>
<span id="cb10-6"><a href="#cb10-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;age&quot;</span>: IntSort(),</span>
<span id="cb10-7"><a href="#cb10-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;is_active&quot;</span>: BoolSort()</span>
<span id="cb10-8"><a href="#cb10-8" aria-hidden="true" tabindex="-1"></a>})</span>
<span id="cb10-9"><a href="#cb10-9" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-10"><a href="#cb10-10" aria-hidden="true" tabindex="-1"></a><span class="co"># 1. 添加 ColumnConstraint</span></span>
<span id="cb10-11"><a href="#cb10-11" aria-hidden="true" tabindex="-1"></a><span class="co"># 这会自动为&quot;users&quot;表创建一个自由行变量，并添加该行的age&gt;30的约束</span></span>
<span id="cb10-12"><a href="#cb10-12" aria-hidden="true" tabindex="-1"></a>age_constraint <span class="op">=</span> users.age <span class="op">&gt;</span> <span class="dv">30</span></span>
<span id="cb10-13"><a href="#cb10-13" aria-hidden="true" tabindex="-1"></a>s.add(age_constraint)</span>
<span id="cb10-14"><a href="#cb10-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-15"><a href="#cb10-15" aria-hidden="true" tabindex="-1"></a><span class="co"># 2. 添加 Expression</span></span>
<span id="cb10-16"><a href="#cb10-16" aria-hidden="true" tabindex="-1"></a><span class="co"># 先创建一个表达式：年龄加10后大于40</span></span>
<span id="cb10-17"><a href="#cb10-17" aria-hidden="true" tabindex="-1"></a>age_plus_10 <span class="op">=</span> users.age <span class="op">+</span> <span class="dv">10</span></span>
<span id="cb10-18"><a href="#cb10-18" aria-hidden="true" tabindex="-1"></a>age_expr <span class="op">=</span> age_plus_10 <span class="op">&gt;</span> <span class="dv">40</span></span>
<span id="cb10-19"><a href="#cb10-19" aria-hidden="true" tabindex="-1"></a>s.add(age_expr)</span>
<span id="cb10-20"><a href="#cb10-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-21"><a href="#cb10-21" aria-hidden="true" tabindex="-1"></a><span class="co"># 3. 添加JOIN条件（元组）</span></span>
<span id="cb10-22"><a href="#cb10-22" aria-hidden="true" tabindex="-1"></a>orders <span class="op">=</span> s.create_table(<span class="st">&quot;orders&quot;</span>, {</span>
<span id="cb10-23"><a href="#cb10-23" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;id&quot;</span>: IntSort(),</span>
<span id="cb10-24"><a href="#cb10-24" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;user_id&quot;</span>: IntSort(),</span>
<span id="cb10-25"><a href="#cb10-25" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;amount&quot;</span>: RealSort()</span>
<span id="cb10-26"><a href="#cb10-26" aria-hidden="true" tabindex="-1"></a>})</span>
<span id="cb10-27"><a href="#cb10-27" aria-hidden="true" tabindex="-1"></a><span class="co"># 通过相等比较创建JOIN，返回一个包含两个表行变量的元组</span></span>
<span id="cb10-28"><a href="#cb10-28" aria-hidden="true" tabindex="-1"></a>join_condition <span class="op">=</span> (users.<span class="bu">id</span> <span class="op">==</span> orders.user_id)</span>
<span id="cb10-29"><a href="#cb10-29" aria-hidden="true" tabindex="-1"></a>s.add(join_condition)</span>
<span id="cb10-30"><a href="#cb10-30" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-31"><a href="#cb10-31" aria-hidden="true" tabindex="-1"></a><span class="co"># 获取JOIN行变量并添加条件到JOIN后的结果上</span></span>
<span id="cb10-32"><a href="#cb10-32" aria-hidden="true" tabindex="-1"></a>user_row, order_row <span class="op">=</span> join_condition</span>
<span id="cb10-33"><a href="#cb10-33" aria-hidden="true" tabindex="-1"></a>s.add(users.age.as_z3_expr(user_row) <span class="op">&gt;</span> <span class="dv">25</span>)  <span class="co"># 用户年龄&gt;25</span></span>
<span id="cb10-34"><a href="#cb10-34" aria-hidden="true" tabindex="-1"></a>s.add(orders.amount.as_z3_expr(order_row) <span class="op">&gt;</span> <span class="dv">100</span>)  <span class="co"># 订单金额&gt;100</span></span>
<span id="cb10-35"><a href="#cb10-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-36"><a href="#cb10-36" aria-hidden="true" tabindex="-1"></a><span class="co"># 4. 添加直接的Z3 BoolRef</span></span>
<span id="cb10-37"><a href="#cb10-37" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> z3 <span class="im">import</span> And, Or, Not, Bool</span>
<span id="cb10-38"><a href="#cb10-38" aria-hidden="true" tabindex="-1"></a>premium_user <span class="op">=</span> Bool(<span class="st">&quot;premium_user&quot;</span>)</span>
<span id="cb10-39"><a href="#cb10-39" aria-hidden="true" tabindex="-1"></a><span class="co"># 直接创建Z3布尔表达式并添加</span></span>
<span id="cb10-40"><a href="#cb10-40" aria-hidden="true" tabindex="-1"></a>direct_constraint <span class="op">=</span> And(premium_user, orders.amount(order_row) <span class="op">&gt;</span> <span class="dv">200</span>)</span>
<span id="cb10-41"><a href="#cb10-41" aria-hidden="true" tabindex="-1"></a>s.add(direct_constraint)</span>
<span id="cb10-42"><a href="#cb10-42" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb10-43"><a href="#cb10-43" aria-hidden="true" tabindex="-1"></a><span class="co"># 5. 添加Python bool值</span></span>
<span id="cb10-44"><a href="#cb10-44" aria-hidden="true" tabindex="-1"></a><span class="co"># 这在条件逻辑中很有用</span></span>
<span id="cb10-45"><a href="#cb10-45" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> some_condition:</span>
<span id="cb10-46"><a href="#cb10-46" aria-hidden="true" tabindex="-1"></a>    s.add(<span class="va">True</span>)  <span class="co"># 转换为z3.BoolVal(True)</span></span>
<span id="cb10-47"><a href="#cb10-47" aria-hidden="true" tabindex="-1"></a><span class="cf">else</span>:</span>
<span id="cb10-48"><a href="#cb10-48" aria-hidden="true" tabindex="-1"></a>    s.add(<span class="va">False</span>)  <span class="co"># 转换为z3.BoolVal(False)，会导致不可满足</span></span></code></pre></div></li>
<li><p><code>_merge_join_conditions(self)</code>:
（内部方法）处理由多次两两JOIN产生的传递性约束。例如，如果
<code>T1.a == T2.b</code> 且 <code>T2.b == T3.c</code>，此方法会尝试添加
<code>T1.a_row_var == T2.b_row_var_for_T1</code> 和
<code>T2.b_row_var_for_T3 == T3.c_row_var</code>
这样的等价关系，确保同一列在不同JOIN上下文中的行变量能够正确关联。</p></li>
<li><p><code>_convert_constraint(self, constraint: Any, row_var: Optional[z3.ExprRef] = None) -&gt; z3.ExprRef</code>:
（内部方法）将多种类型的输入（Python bool,
<code>DateExpressionBoolRef</code>, <code>Expression</code>,
<code>ColumnConstraint</code>）转换为可添加到求解器的Z3
<code>ExprRef</code>。</p></li>
<li><p><code>check(self) -&gt; z3.CheckSatResult</code>: 调用
<code>self.solver.check()</code>，但在之前会调用
<code>_merge_join_conditions</code> 来确保JOIN条件被正确处理。</p></li>
<li><p><code>get_model(self) -&gt; Optional[z3.ModelRef]</code>: 在
<code>check() == sat</code> 后调用
<code>self.solver.model()</code>。</p></li>
<li><p><code>eval(self, expression: z3.ExprRef, model: Optional[z3.ModelRef] = None, row_var: Optional[z3.ExprRef] = None) -&gt; Any</code>:</p>
<ul>
<li>在给定的模型中对Z3表达式求值。</li>
<li>如果 <code>expression</code>
是一个列的Z3函数（<code>FuncDeclRef</code>），并且提供了
<code>row_var</code>，它会计算 <code>function(row_var)</code>。</li>
</ul></li>
<li><p>逻辑操作辅助函数 (<code>Not(self, a)</code>,
<code>And(self, *args)</code>, <code>Or(self, *args)</code>,
<code>Implies(self, a, b)</code>, <code>Xor(self, a, b)</code>,
<code>If(self, c, t, e)</code>):</p>
<ul>
<li>这些是对Z3逻辑操作的封装，允许输入为 <code>BoolRef</code>,
<code>Expression</code>, <code>ColumnConstraint</code>, 或 Python
<code>bool</code>。</li>
<li>它们会智能地将输入转换为Z3表达式，并返回组合后的结果（可能是直接的
<code>BoolRef</code> 或新的 <code>Expression</code>）。</li>
<li>例如,
<code>s.And(col.age &gt; 18, col.status == "active")</code>。</li>
</ul></li>
<li><p><code>_get_unique_var_suffix(self) -&gt; str</code>:
返回一个唯一的数字后缀，用于创建不冲突的Z3变量名。</p></li>
</ul></li>
</ul>
<h3 id="table">3.2. <code>Table</code></h3>
<p>此类代表数据库中的一个表。</p>
<ul>
<li><strong>目的</strong>：
<ul>
<li>存储表的名称和列定义（名称及Z3类型）。</li>
<li>提供接口以将数据行（作为约束）添加到表中。</li>
<li>管理表内唯一的行ID（逻辑上的）。</li>
<li>通过 <code>__getattr__</code> 方便地访问其列 (<code>Column</code>
对象)。</li>
</ul></li>
<li><strong>关键属性</strong>：
<ul>
<li><code>self._name: str</code>: 表名。</li>
<li><code>self.solver_wrapper: SqlToZ3</code>: 对 <code>SqlToZ3</code>
实例的引用。</li>
<li><code>self.columns_with_types: Dict[str, z3.SortRef]</code>:
列名到Z3类型的映射。</li>
<li><code>self.logger</code>: 日志记录器。</li>
<li><code>self._next_row_id: int</code>: 用于分配下一个可用行ID的计数器
(从1开始)。</li>
<li><code>self.rows: List[int]</code>: 存储此表中已分配的行ID列表。</li>
</ul></li>
<li><strong>关键方法</strong>：
<ul>
<li><code>__init__(self, name: str, solver_wrapper: SqlToZ3, columns_with_types: Dict[str, z3.SortRef])</code>:
初始化表。</li>
<li><code>_get_next_row_id(self) -&gt; int</code>:
获取并递增下一个行ID。</li>
<li><code>insert(self, values_dict: Dict[str, Any], for_all_rows: bool = False) -&gt; Union[int, List[int]]</code>:
<ul>
<li><p>这是核心的数据添加方法。它将 <code>values_dict</code>
中的每个条目转换为对表中特定行（或多行）的约束。</p></li>
<li><p><code>values_dict</code> 的值可以是：</p>
<ul>
<li><strong>Python常量</strong>: 如 <code>{"age": 30}</code>。会生成
<code>table.age(row_id) == 30</code>。</li>
<li><strong><code>Column</code> 对象</strong>:
通常用于引用其他表或其他行的列，或定义列间关系。例如
<code>{"manager_id": other_table.id}</code>。</li>
<li><strong><code>(source_table_name, source_column_name)</code>
元组</strong>: 与 <code>Column</code> 对象类似，用于跨表引用。</li>
<li><strong><code>Expression</code> 对象</strong>: 如
<code>{"bonus": self.salary * 0.1}</code>。会生成
<code>table.bonus(row_id) == (table.salary(row_id) * 0.1)</code>。</li>
</ul></li>
<li><p><strong><code>for_all_rows=False</code> (默认)</strong>:</p>
<ul>
<li>分配一个新的行ID <code>r_id = self._get_next_row_id()</code>。</li>
<li>添加该行ID到 <code>self.rows</code> 列表。</li>
<li>调用 <code>_add_constraints_for_row(r_id, value_spec, ...)</code>
为此行添加约束。</li>
<li><strong>返回值</strong>: 返回新插入行的ID（整数）。</li>
</ul></li>
<li><p><strong><code>for_all_rows=True</code></strong>:</p>
<ul>
<li>这通常用于模拟SQL的 <code>INSERT INTO ... SELECT ... FROM</code>
操作，为源表中的每一行创建目标表中的一行。</li>
<li>它会识别 <code>values_dict</code>
中引用的第一个源表，然后为该表的每一行在当前表中创建一个对应的新行。</li>
<li>调用 <code>_add_constraints_for_row</code>
来设置各列的值，并建立与源表行的关联。</li>
<li><strong>返回值</strong>: 返回所有新插入行ID的列表。</li>
</ul></li>
<li><p><strong>SQL 示例 (单行插入)</strong>:
<code>INSERT INTO users (id, name, age) VALUES (1, 'Alice', 30);</code></p></li>
<li><p><strong>转换</strong>:</p>
<div class="sourceCode" id="cb11"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb11-1"><a href="#cb11-1" aria-hidden="true" tabindex="-1"></a>row_id <span class="op">=</span> users.insert({<span class="st">&quot;id&quot;</span>: <span class="dv">1</span>, <span class="st">&quot;name&quot;</span>: <span class="st">&quot;Alice&quot;</span>, <span class="st">&quot;age&quot;</span>: <span class="dv">30</span>})</span>
<span id="cb11-2"><a href="#cb11-2" aria-hidden="true" tabindex="-1"></a><span class="co"># 内部逻辑 (简化):</span></span>
<span id="cb11-3"><a href="#cb11-3" aria-hidden="true" tabindex="-1"></a><span class="co"># r_id = users._get_next_row_id() # 例如返回 1</span></span>
<span id="cb11-4"><a href="#cb11-4" aria-hidden="true" tabindex="-1"></a><span class="co"># users.rows.append(r_id) # 添加行ID到rows列表</span></span>
<span id="cb11-5"><a href="#cb11-5" aria-hidden="true" tabindex="-1"></a><span class="co"># 为各列添加约束 users.id(r_id) == 1 等</span></span>
<span id="cb11-6"><a href="#cb11-6" aria-hidden="true" tabindex="-1"></a><span class="co"># 返回 r_id</span></span></code></pre></div></li>
</ul></li>
<li><code>_add_constraints_for_row(self, current_row_var: z3.ExprRef, value_spec: Any, for_all_rows_context: bool, source_row_var_map: Dict[str, z3.ExprRef])</code>:
（内部辅助方法）为<code>current_row_var</code>指定的行，根据<code>value_spec</code>的类型（常量,
<code>Column</code>, <code>Expression</code>, 元组）添加Z3约束。
<ul>
<li>如果 <code>value_spec</code> 是 <code>Column</code> 或元组
(引用其他表 <code>other_table</code>) 并且当前不是
<code>for_all_rows</code>
的迭代上下文（或者即使是，但该<code>other_table</code>不是迭代的主表），它会创建一个新的自由Z3行变量
<code>source_row_var</code> for
<code>other_table</code>，添加行有效性约束
(<code>0 &lt;= source_row_var &lt; other_table.rows_z3_var</code>)，并将其用于从<code>other_table</code>取值。</li>
</ul></li>
<li><code>__getattr__(self, name: str) -&gt; Column</code>: 允许通过
<code>table_instance.column_name</code> 的语法获取一个代表该列的
<code>Column</code> 对象。</li>
<li><code>__repr__(self) -&gt; str</code>: 返回表的可读表示，如
<code>&lt;Table: users (rows_z3_var: users_rows_count_0)&gt;</code>。</li>
</ul></li>
</ul>
<h3 id="column">3.3. <code>Column</code></h3>
<p>此类代表数据库表中的一个列。它是构建条件和表达式的主要接口。</p>
<ul>
<li><strong>目的</strong>：
<ul>
<li>封装列的元数据（表名、列名、对应的Z3函数）。</li>
<li>通过重载Python的比较运算符 (<code>==</code>, <code>&gt;</code>,
<code>&lt;</code>, <code>!=</code>, etc.) 和算术运算符 (<code>+</code>,
<code>-</code>, <code>*</code>,
<code>/</code>)，使得用户可以用自然的语法来创建约束和表达式。</li>
</ul></li>
<li><strong>关键属性</strong>：
<ul>
<li><code>self.table_name: str</code>: 列所属的表名。</li>
<li><code>self.column_name: str</code>: 列名。</li>
<li><code>self.solver_wrapper: SqlToZ3</code>: 对 <code>SqlToZ3</code>
实例的引用。</li>
<li><code>self.function: z3.FuncDeclRef</code>: 该列对应的Z3函数（从
<code>solver_wrapper.column_functions</code> 获取）。</li>
<li><code>self.description: str</code>: 列的描述，如
<code>users.age</code>。</li>
</ul></li>
<li><strong>关键方法</strong>：
<ul>
<li><code>__call__(self, row_var: z3.ExprRef) -&gt; z3.ExprRef</code>:
核心方法。当 <code>Column</code> 对象被调用时（如
<code>users.age(row_variable)</code>），它返回该列在由<code>row_var</code>指定的行上的Z3值，即
<code>self.function(row_var)</code>。</li>
<li><strong>比较运算符 (<code>__eq__</code>, <code>__ne__</code>,
<code>__gt__</code>, <code>__lt__</code>, <code>__ge__</code>,
<code>__le__</code>)</strong>:
<ul>
<li><strong><code>Column == Constant</code></strong> (e.g.,
<code>users.age == 30</code>):
<ul>
<li>返回一个 <code>ColumnConstraint</code> 对象，封装了这个比较。</li>
<li><strong>SQL</strong>: <code>WHERE age = 30</code></li>
<li><strong>转换</strong>: <code>constraint = users.age == 30</code>
(得到 <code>ColumnConstraint</code>) <code>s.add(constraint)</code></li>
</ul></li>
<li><strong><code>Column == Column</code></strong> (e.g.,
<code>table1.col_a == table2.col_b</code>):
<ul>
<li><p>这是定义 <strong>JOIN 条件</strong>的关键。</p></li>
<li><p>内部调用 <code>_create_join_vars_and_row_constraints</code>
创建两个新的<strong>自由Z3行变量</strong> <code>var1</code> (for
<code>self.table_name</code>) 和 <code>var2</code> (for
<code>other.table_name</code>)。</p></li>
<li><p>为这两个行变量添加行有效性约束。</p></li>
<li><p>添加核心的JOIN约束到求解器：
<code>s.add(self(var1) == other(var2))</code> (即
<code>self.function(var1) == other.function(var2)</code>)。</p></li>
<li><p>返回一个4元素元组
<code>(var1, table1_name, var2, table2_name)</code>。这个元组会被
<code>SqlToZ3.add()</code> 捕获并转换为字典存入
<code>join_conditions</code>，用于后续可能的传递性JOIN合并。</p></li>
<li><p><strong>SQL</strong>:
<code>FROM orders JOIN customers ON orders.customer_id = customers.id</code></p></li>
<li><p><strong>转换</strong>:</p>
<div class="sourceCode" id="cb12"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb12-1"><a href="#cb12-1" aria-hidden="true" tabindex="-1"></a>join_condition_tuple <span class="op">=</span> (orders.customer_id <span class="op">==</span> customers.<span class="bu">id</span>)</span>
<span id="cb12-2"><a href="#cb12-2" aria-hidden="true" tabindex="-1"></a>s.add(join_condition_tuple) </span>
<span id="cb12-3"><a href="#cb12-3" aria-hidden="true" tabindex="-1"></a><span class="co"># 内部 __eq__ 逻辑:</span></span>
<span id="cb12-4"><a href="#cb12-4" aria-hidden="true" tabindex="-1"></a><span class="co">#   var_orders = Int(&quot;orders_row_...&quot;)</span></span>
<span id="cb12-5"><a href="#cb12-5" aria-hidden="true" tabindex="-1"></a><span class="co">#   var_customers = Int(&quot;customers_row_...&quot;)</span></span>
<span id="cb12-6"><a href="#cb12-6" aria-hidden="true" tabindex="-1"></a><span class="co">#   # 添加行有效性约束...</span></span>
<span id="cb12-7"><a href="#cb12-7" aria-hidden="true" tabindex="-1"></a><span class="co">#   s.add(orders.customer_id.function(var_orders) == customers.id.function(var_customers))</span></span>
<span id="cb12-8"><a href="#cb12-8" aria-hidden="true" tabindex="-1"></a><span class="co">#   return (var_orders, &quot;orders&quot;, var_customers, &quot;customers&quot;)</span></span></code></pre></div></li>
</ul></li>
<li><strong><code>Column == Expression</code></strong> (e.g.,
<code>employees.salary == (employees.bonus * Decimal('0.5'))</code>):
<ul>
<li>返回一个新的 <code>Expression</code> 对象。其内部的
<code>expr_func</code> 会是类似
<code>lambda r: self(r) == other.as_z3_expr(r)</code> 的形式。</li>
<li><strong>SQL</strong>:
<code>WHERE salary = base_salary + bonus_amount</code> (假设
<code>base_salary + bonus_amount</code> 是一个表达式)</li>
<li><strong>转换</strong>:
<code>expr = employees.base_salary + employees.bonus_amount</code>
(这是一个<code>Expression</code>)
<code>condition = employees.salary == expr</code>
(这也是一个<code>Expression</code>) <code>s.add(condition)</code></li>
</ul></li>
</ul></li>
<li><strong>算术运算符 (<code>__add__</code>, <code>__sub__</code>,
<code>__mul__</code>, <code>__truediv__</code>) 和反向版本
(<code>__radd__</code>, etc.)</strong>:
<ul>
<li>当 <code>Column</code> 对象与常量、另一个 <code>Column</code> 或一个
<code>Expression</code> 进行算术运算时，它们总是返回一个新的
<code>Expression</code> 对象。</li>
<li>例如, <code>users.age + 10</code> 返回一个
<code>Expression</code>，其 <code>expr_func</code> 是
<code>lambda r: users.age(r) + 10</code>。</li>
<li><code>items.price * items.quantity</code> 返回一个
<code>Expression</code>，其 <code>expr_func</code> 是
<code>lambda r: items.price(r) * items.quantity(r)</code>。</li>
<li><strong>SQL</strong>:
<code>SELECT salary * 1.1 AS increased_salary FROM employees</code>
(表达式部分)</li>
<li><strong>转换</strong>:
<code>increased_salary_expr = employees.salary * 1.1</code> (返回
<code>Expression</code>)</li>
</ul></li>
</ul></li>
</ul>
<h3 id="columnconstraint">3.4. <code>ColumnConstraint</code></h3>
<p>此类表示列与常量之间的比较，例如
<code>column == value</code>。它是一个”待应用”的或”上下文相关”的约束。</p>
<ul>
<li><strong>目的</strong>：
<ul>
<li>封装一个简单的列-常量比较，而不立即将其转换为具体的Z3断言。</li>
<li>允许这种比较在不同的上下文中被解释或应用（例如，作为
<code>Expression</code>
的一部分，或直接添加到求解器时针对特定行或自由行）。</li>
</ul></li>
<li><strong>关键属性</strong>：
<ul>
<li><code>self.column: Column</code>: 被比较的 <code>Column</code>
对象。</li>
<li><code>self.operator: Callable</code>: 比较运算符函数 (e.g.,
<code>operator.eq</code>, <code>operator.gt</code>)。</li>
<li><code>self.value: Any</code>: 与列进行比较的常量值。</li>
<li><code>self.solver_wrapper: SqlToZ3</code>: 引用。</li>
<li><code>self.description: str</code>: 如
<code>(users.age &gt; 30)</code>。</li>
<li><code>self.row_var: Optional[z3.ExprRef]</code>: (高级)
如果此约束已明确绑定到某个Z3行变量，则存储于此。</li>
<li><code>self.z3_constraint: Optional[z3.BoolRef]</code>:
缓存的、已生成的Z3布尔表达式。</li>
<li><code>self.applied_to_solver: bool</code>: 标记此约束是否已通过
<code>apply()</code> 添加到求解器的主约束列表。</li>
</ul></li>
<li><strong>关键方法</strong>：
<ul>
<li><code>apply(self, join_var: Optional[z3.ExprRef] = None, force_reapply: bool = False)</code>:
<ul>
<li><p>核心方法，将此列约束转换为实际的Z3断言并添加到求解器。</p></li>
<li><p><strong>行变量处理</strong>:</p>
<ul>
<li>如果提供了 <code>join_var</code> (通常来自 <code>SqlToZ3.add</code>
的JOIN上下文)，则使用它作为行变量。</li>
<li>否则，如果 <code>self.row_var</code> 已设置，则使用它。</li>
<li>否则（最常见的情况当 <code>s.add(column_constraint_obj)</code>
被调用时），创建一个新的<strong>自由Z3行变量</strong>
<code>temp_row_var</code> for <code>self.column.table_name</code>。</li>
<li>为这个 <code>temp_row_var</code> 添加行有效性约束:
<code>s.add(z3.And(0 &lt;= temp_row_var, temp_row_var &lt; table.rows_z3_var))</code>。</li>
</ul></li>
<li><p>使用选定的行变量，通过
<code>_build_z3_expr_with_var(row_var_to_use)</code>
生成Z3比较表达式。</p></li>
<li><p>如果 <code>force_reapply</code> 为 <code>True</code>
或约束尚未应用，则将Z3表达式添加到求解器并标记为已应用。</p></li>
<li><p><strong>SQL示例 (通过 <code>s.add</code> 应用)</strong>:
<code>WHERE age &gt; 25</code></p></li>
<li><p><strong>转换</strong>:</p>
<div class="sourceCode" id="cb13"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb13-1"><a href="#cb13-1" aria-hidden="true" tabindex="-1"></a>cc <span class="op">=</span> users.age <span class="op">&gt;</span> <span class="dv">25</span>  <span class="co"># ColumnConstraint</span></span>
<span id="cb13-2"><a href="#cb13-2" aria-hidden="true" tabindex="-1"></a>s.add(cc) </span>
<span id="cb13-3"><a href="#cb13-3" aria-hidden="true" tabindex="-1"></a><span class="co"># s.add(ColumnConstraint) 内部会调用 cc.apply()</span></span>
<span id="cb13-4"><a href="#cb13-4" aria-hidden="true" tabindex="-1"></a><span class="co"># 在 apply() 内部 (简化):</span></span>
<span id="cb13-5"><a href="#cb13-5" aria-hidden="true" tabindex="-1"></a><span class="co">#   row_v = Int(&quot;users_row_...&quot;) # 创建自由行变量</span></span>
<span id="cb13-6"><a href="#cb13-6" aria-hidden="true" tabindex="-1"></a><span class="co">#   table = s.tables[&quot;users&quot;]</span></span>
<span id="cb13-7"><a href="#cb13-7" aria-hidden="true" tabindex="-1"></a><span class="co">#   s.add(And(0 &lt;= row_v, row_v &lt; table.rows_z3_var)) # 行有效性</span></span>
<span id="cb13-8"><a href="#cb13-8" aria-hidden="true" tabindex="-1"></a><span class="co">#   z3_expr = cc._build_z3_expr_with_var(row_v) # 即 users.age.function(row_v) &gt; 25</span></span>
<span id="cb13-9"><a href="#cb13-9" aria-hidden="true" tabindex="-1"></a><span class="co">#   s.add(z3_expr)</span></span>
<span id="cb13-10"><a href="#cb13-10" aria-hidden="true" tabindex="-1"></a><span class="co">#   cc.z3_constraint = z3_expr</span></span>
<span id="cb13-11"><a href="#cb13-11" aria-hidden="true" tabindex="-1"></a><span class="co">#   cc.applied_to_solver = True</span></span></code></pre></div></li>
</ul></li>
<li><code>as_z3_expr(self, row_var_override: Optional[z3.ExprRef] = None) -&gt; z3.BoolRef</code>:
<ul>
<li>返回此约束的Z3布尔表达式。</li>
<li>如果提供了 <code>row_var_override</code>：使用它通过
<code>_build_z3_expr_with_var(row_var_override)</code>
临时构建Z3表达式。这主要用于 <code>Expression</code>
内部的逻辑组合，其中 <code>Expression</code> 的 <code>expr_func</code>
提供了行变量上下文。<strong>此路径不会修改实例状态或添加到求解器。</strong></li>
<li>如果没有 <code>row_var_override</code>：如果约束尚未生成Z3表达式
(<code>self.z3_constraint</code> is None)，则调用
<code>self.apply()</code>
(这将使用默认的自由行变量逻辑并添加到求解器)。然后返回
<code>self.z3_constraint</code>。</li>
</ul></li>
<li><code>_build_z3_expr_with_var(self, row_var: z3.ExprRef) -&gt; z3.BoolRef</code>:
(内部辅助方法) 使用给定的 <code>row_var</code> 生成Z3比较表达式，例如
<code>self.operator(self.column(row_var), converted_value)</code>。</li>
<li><code>__and__(self, other)</code>, <code>__or__(self, other)</code>
(与其他 <code>ColumnConstraint</code> 或 <code>Expression</code> 或
<code>bool</code>):
<ul>
<li><p>这些逻辑运算符返回一个新的 <code>Expression</code>
对象。</p></li>
<li><p>如果 <code>other</code> 也是 <code>ColumnConstraint</code>
(且来自同一个表，这是一个重要的隐含假设，否则语义可能混乱)，返回的
<code>Expression</code> 的 <code>expr_func</code> 会是类似
<code>lambda r: z3.And(self._build_z3_expr_with_var(r), other._build_z3_expr_with_var(r))</code>。这确保两个原始的
<code>ColumnConstraint</code> 都在传递给 <code>expr_func</code> 的同一个
<code>row_var</code> 上下文中求值。</p></li>
<li><p>如果 <code>other</code> 是 <code>Expression</code>，则是
<code>lambda r: z3.And(self._build_z3_expr_with_var(r), other(r))</code>。</p></li>
<li><p><strong>SQL</strong>:
<code>WHERE age &gt; 20 AND city = 'NY'</code></p></li>
<li><p><strong>转换</strong>:</p>
<div class="sourceCode" id="cb14"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb14-1"><a href="#cb14-1" aria-hidden="true" tabindex="-1"></a>constraint1 <span class="op">=</span> users.age <span class="op">&gt;</span> <span class="dv">20</span>  <span class="co"># ColumnConstraint</span></span>
<span id="cb14-2"><a href="#cb14-2" aria-hidden="true" tabindex="-1"></a>constraint2 <span class="op">=</span> users.city <span class="op">==</span> <span class="st">&#39;NY&#39;</span> <span class="co"># ColumnConstraint</span></span>
<span id="cb14-3"><a href="#cb14-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-4"><a href="#cb14-4" aria-hidden="true" tabindex="-1"></a><span class="co"># (constraint1 &amp; constraint2) 返回一个 Expression</span></span>
<span id="cb14-5"><a href="#cb14-5" aria-hidden="true" tabindex="-1"></a>combined_expr <span class="op">=</span> constraint1 <span class="op">&amp;</span> constraint2 </span>
<span id="cb14-6"><a href="#cb14-6" aria-hidden="true" tabindex="-1"></a>s.add(combined_expr)</span>
<span id="cb14-7"><a href="#cb14-7" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb14-8"><a href="#cb14-8" aria-hidden="true" tabindex="-1"></a><span class="co"># combined_expr.expr_func (简化) 约等于:</span></span>
<span id="cb14-9"><a href="#cb14-9" aria-hidden="true" tabindex="-1"></a><span class="co"># lambda r_var: And(</span></span>
<span id="cb14-10"><a href="#cb14-10" aria-hidden="true" tabindex="-1"></a><span class="co">#     users.age.function(r_var) &gt; 20,  # from constraint1.as_z3_expr(r_var)</span></span>
<span id="cb14-11"><a href="#cb14-11" aria-hidden="true" tabindex="-1"></a><span class="co">#     users.city.function(r_var) == &quot;NY&quot; # from constraint2.as_z3_expr(r_var)</span></span>
<span id="cb14-12"><a href="#cb14-12" aria-hidden="true" tabindex="-1"></a><span class="co"># )</span></span>
<span id="cb14-13"><a href="#cb14-13" aria-hidden="true" tabindex="-1"></a><span class="co"># s.add(Expression) 会为这个 combined_expr 创建一个自由行变量并应用。</span></span></code></pre></div></li>
</ul></li>
<li><code>__bool__(self)</code>: 总是返回 <code>True</code>，以允许
<code>if column_constraint_obj:</code>
这样的检查通过。它不表示约束的逻辑真值。</li>
</ul></li>
</ul>
<h3 id="expression">3.5. <code>Expression</code></h3>
<p>此类代表一个可以被求值为Z3表达式的任意计算，通常由列操作或逻辑组合产生。</p>
<ul>
<li><strong>目的</strong>：
<ul>
<li>以统一的方式表示复杂的计算逻辑，这些逻辑最终会在特定的行上下文中被转换为Z3表达式。</li>
<li>支持通过重载运算符进行链式操作，构建复杂的表达式树。</li>
</ul></li>
<li><strong>关键属性</strong>：
<ul>
<li><code>self.expr_func: Callable[[z3.ExprRef], z3.ExprRef]</code>:
这是核心。一个Python可调用对象（通常是lambda函数），它接受一个Z3行变量
(<code>z3.ExprRef</code>)
作为输入，并返回一个代表该表达式在该行上求值的Z3表达式
(<code>z3.ExprRef</code>)。</li>
<li><code>self.solver_wrapper: SqlToZ3</code>: 引用。</li>
<li><code>self.description: str</code>: 表达式的描述，用于调试，例如
<code>((users.age + 10) &gt; 40)</code>。</li>
<li><code>self.logger</code>: 日志记录器。</li>
</ul></li>
<li><strong>关键方法</strong>：
<ul>
<li><code>__call__(self, row_var: z3.ExprRef) -&gt; z3.ExprRef</code>:
执行 <code>self.expr_func(row_var)</code>，即在给定的
<code>row_var</code> 上下文中计算此表达式。</li>
<li><code>as_z3_expr(self, row_var: Optional[z3.ExprRef] = None) -&gt; z3.ExprRef</code>:
<ul>
<li>返回此表达式的Z3表示。</li>
<li>如果提供了 <code>row_var</code>，则直接调用
<code>self(row_var)</code>。</li>
<li>如果未提供
<code>row_var</code>：<strong>这是一种不推荐的用法</strong>，因为表达式的上下文通常应由外部调用者（如<code>SqlToZ3.add</code>或另一个<code>Expression</code>的<code>expr_func</code>)明确指定。在这种情况下，它会创建一个临时的、唯一的自由Z3行变量，并调用
<code>self(temp_row_var)</code>，同时发出警告。它<strong>不会</strong>为这个临时变量添加行有效性约束，因为<code>Expression</code>本身不直接绑定到特定表，这个责任在更高层。</li>
</ul></li>
<li><strong>算术、比较、逻辑运算符重载 (<code>__add__</code>,
<code>__eq__</code>, <code>__and__</code>, <code>__invert__</code> (for
NOT), etc.)</strong>:
<ul>
<li><p>当 <code>Expression</code> 对象与常量、<code>Column</code>
对象或其他 <code>Expression</code>
对象进行运算时，它们总是返回一个<strong>新的 <code>Expression</code>
对象</strong>。</p></li>
<li><p>这个新的 <code>Expression</code> 的 <code>expr_func</code>
内部会包含相应的Z3操作，并确保操作数在传递给它的同一个
<code>row_var</code> 上下文中被正确求值。</p>
<ul>
<li><code>expr1 + const</code> -&gt;
<code>Expression(lambda r: expr1(r) + const, ...)</code></li>
<li><code>expr1 + column2</code> -&gt;
<code>Expression(lambda r: expr1(r) + column2(r), ...)</code></li>
<li><code>expr1 + expr2</code> -&gt;
<code>Expression(lambda r: expr1(r) + expr2(r), ...)</code></li>
<li><code>expr1 == const</code> -&gt;
<code>Expression(lambda r: expr1(r) == const, ...)</code></li>
<li><code>~expr1</code> (NOT) -&gt;
<code>Expression(lambda r: z3.Not(expr1(r)), ...)</code></li>
<li><code>expr1 &amp; column_constraint2</code> (AND) -&gt;
<code>Expression(lambda r: z3.And(expr1(r), column_constraint2.as_z3_expr(r)), ...)</code></li>
</ul></li>
<li><p><strong>SQL</strong>:
<code>WHERE (salary + bonus) * 1.1 &gt; 50000 AND is_manager</code></p></li>
<li><p><strong>转换</strong>:</p>
<div class="sourceCode" id="cb15"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb15-1"><a href="#cb15-1" aria-hidden="true" tabindex="-1"></a>expr_sum <span class="op">=</span> employees.salary <span class="op">+</span> employees.bonus  <span class="co"># Expression</span></span>
<span id="cb15-2"><a href="#cb15-2" aria-hidden="true" tabindex="-1"></a>expr_total_comp <span class="op">=</span> expr_sum <span class="op">*</span> <span class="fl">1.1</span>               <span class="co"># Expression</span></span>
<span id="cb15-3"><a href="#cb15-3" aria-hidden="true" tabindex="-1"></a>cond_amount <span class="op">=</span> expr_total_comp <span class="op">&gt;</span> <span class="dv">50000</span>          <span class="co"># Expression (boolean)</span></span>
<span id="cb15-4"><a href="#cb15-4" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-5"><a href="#cb15-5" aria-hidden="true" tabindex="-1"></a><span class="co"># employees.is_manager 是 Column, 假设它是 BoolSort</span></span>
<span id="cb15-6"><a href="#cb15-6" aria-hidden="true" tabindex="-1"></a><span class="co"># 当 Expression 与 Column (boolean) 进行逻辑运算时:</span></span>
<span id="cb15-7"><a href="#cb15-7" aria-hidden="true" tabindex="-1"></a><span class="co"># cond_manager = employees.is_manager </span></span>
<span id="cb15-8"><a href="#cb15-8" aria-hidden="true" tabindex="-1"></a><span class="co"># (This might be better if employees.is_manager itself becomes an Expression via a helper,</span></span>
<span id="cb15-9"><a href="#cb15-9" aria-hidden="true" tabindex="-1"></a><span class="co"># or if Expression&#39;s __and__ can directly take a boolean Column and evaluate it with row_var)</span></span>
<span id="cb15-10"><a href="#cb15-10" aria-hidden="true" tabindex="-1"></a><span class="co"># For now, let&#39;s assume is_manager is already an Expression or ColumnConstraint</span></span>
<span id="cb15-11"><a href="#cb15-11" aria-hidden="true" tabindex="-1"></a><span class="co"># If it&#39;s a ColumnConstraint like `employees.is_manager == True`:</span></span>
<span id="cb15-12"><a href="#cb15-12" aria-hidden="true" tabindex="-1"></a>cond_manager_cc <span class="op">=</span> employees.is_manager <span class="op">==</span> <span class="va">True</span> <span class="co"># ColumnConstraint</span></span>
<span id="cb15-13"><a href="#cb15-13" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-14"><a href="#cb15-14" aria-hidden="true" tabindex="-1"></a>final_condition <span class="op">=</span> cond_amount <span class="op">&amp;</span> cond_manager_cc <span class="co"># Expression &amp; ColumnConstraint -&gt; Expression</span></span>
<span id="cb15-15"><a href="#cb15-15" aria-hidden="true" tabindex="-1"></a>s.add(final_condition)</span>
<span id="cb15-16"><a href="#cb15-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb15-17"><a href="#cb15-17" aria-hidden="true" tabindex="-1"></a><span class="co"># final_condition.expr_func (简化) 约等于:</span></span>
<span id="cb15-18"><a href="#cb15-18" aria-hidden="true" tabindex="-1"></a><span class="co"># lambda r: And(</span></span>
<span id="cb15-19"><a href="#cb15-19" aria-hidden="true" tabindex="-1"></a><span class="co">#     ((employees.salary(r) + employees.bonus(r)) * 1.1) &gt; 50000,</span></span>
<span id="cb15-20"><a href="#cb15-20" aria-hidden="true" tabindex="-1"></a><span class="co">#     employees.is_manager.function(r) == True # from cond_manager_cc.as_z3_expr(r)</span></span>
<span id="cb15-21"><a href="#cb15-21" aria-hidden="true" tabindex="-1"></a><span class="co"># )</span></span></code></pre></div></li>
</ul></li>
<li><code>__repr__(self) -&gt; str</code>: 返回表达式的可读描述。</li>
</ul></li>
</ul>
<h2 id="sql-到-z3-转换示例">4. SQL 到 Z3 转换示例</h2>
<h3 id="简单查询-单表where条件">4.1. 简单查询 (单表，WHERE条件)</h3>
<ul>
<li><p><strong>SQL</strong>:</p>
<div class="sourceCode" id="cb16"><pre
class="sourceCode sql"><code class="sourceCode sql"><span id="cb16-1"><a href="#cb16-1" aria-hidden="true" tabindex="-1"></a><span class="kw">CREATE</span> <span class="kw">TABLE</span> employees (<span class="kw">id</span> <span class="dt">INT</span>, name STRING, department STRING, salary <span class="dt">INT</span>, active BOOL, hire_date <span class="dt">DATE</span>);</span>
<span id="cb16-2"><a href="#cb16-2" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> employees <span class="kw">VALUES</span> (<span class="dv">1</span>, <span class="st">&#39;Alice&#39;</span>, <span class="st">&#39;HR&#39;</span>, <span class="dv">60000</span>, <span class="kw">True</span>, <span class="st">&#39;2020-01-15&#39;</span>);</span>
<span id="cb16-3"><a href="#cb16-3" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> employees <span class="kw">VALUES</span> (<span class="dv">2</span>, <span class="st">&#39;Bob&#39;</span>, <span class="st">&#39;Engineering&#39;</span>, <span class="dv">80000</span>, <span class="kw">True</span>, <span class="st">&#39;2018-07-01&#39;</span>);</span>
<span id="cb16-4"><a href="#cb16-4" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> employees <span class="kw">VALUES</span> (<span class="dv">3</span>, <span class="st">&#39;Charlie&#39;</span>, <span class="st">&#39;HR&#39;</span>, <span class="dv">70000</span>, <span class="kw">False</span>, <span class="st">&#39;2022-03-10&#39;</span>);</span>
<span id="cb16-5"><a href="#cb16-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb16-6"><a href="#cb16-6" aria-hidden="true" tabindex="-1"></a><span class="co">-- 我们想找到满足以下条件的员工:</span></span>
<span id="cb16-7"><a href="#cb16-7" aria-hidden="true" tabindex="-1"></a><span class="co">-- 部门是 &#39;HR&#39; 并且 工资大于 65000 并且 是活跃员工 并且 入职日期在2019年之后</span></span>
<span id="cb16-8"><a href="#cb16-8" aria-hidden="true" tabindex="-1"></a><span class="co">-- SELECT name, salary FROM employees WHERE department = &#39;HR&#39; AND salary &gt; 65000 AND active = True AND hire_date &gt; &#39;2019-01-01&#39;;</span></span></code></pre></div></li>
<li><p><strong>Python / Z3 转换</strong>:</p>
<div class="sourceCode" id="cb17"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb17-1"><a href="#cb17-1" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> new_model.sql_to_z3 <span class="im">import</span> SqlToZ3, DateType</span>
<span id="cb17-2"><a href="#cb17-2" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> z3 <span class="im">import</span> IntSort, StringSort, BoolSort, RealSort, sat, unsat</span>
<span id="cb17-3"><a href="#cb17-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-4"><a href="#cb17-4" aria-hidden="true" tabindex="-1"></a>s <span class="op">=</span> SqlToZ3()</span>
<span id="cb17-5"><a href="#cb17-5" aria-hidden="true" tabindex="-1"></a>employees <span class="op">=</span> s.create_table(<span class="st">&quot;employees&quot;</span>, {</span>
<span id="cb17-6"><a href="#cb17-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;id&quot;</span>: IntSort(), <span class="st">&quot;name&quot;</span>: StringSort(), </span>
<span id="cb17-7"><a href="#cb17-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;department&quot;</span>: StringSort(), <span class="st">&quot;salary&quot;</span>: IntSort(),</span>
<span id="cb17-8"><a href="#cb17-8" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;active&quot;</span>: BoolSort(), <span class="st">&quot;hire_date&quot;</span>: DateType()  <span class="co"># 使用DateType表示日期类型</span></span>
<span id="cb17-9"><a href="#cb17-9" aria-hidden="true" tabindex="-1"></a>})</span>
<span id="cb17-10"><a href="#cb17-10" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-11"><a href="#cb17-11" aria-hidden="true" tabindex="-1"></a>employees.insert({<span class="st">&quot;id&quot;</span>: <span class="dv">1</span>, <span class="st">&quot;name&quot;</span>: <span class="st">&quot;Alice&quot;</span>, <span class="st">&quot;department&quot;</span>: <span class="st">&quot;HR&quot;</span>, <span class="st">&quot;salary&quot;</span>: <span class="dv">60000</span>, <span class="st">&quot;active&quot;</span>: <span class="va">True</span>, <span class="st">&quot;hire_date&quot;</span>: <span class="st">&quot;2020-01-15&quot;</span>})</span>
<span id="cb17-12"><a href="#cb17-12" aria-hidden="true" tabindex="-1"></a>employees.insert({<span class="st">&quot;id&quot;</span>: <span class="dv">2</span>, <span class="st">&quot;name&quot;</span>: <span class="st">&quot;Bob&quot;</span>, <span class="st">&quot;department&quot;</span>: <span class="st">&quot;Engineering&quot;</span>, <span class="st">&quot;salary&quot;</span>: <span class="dv">80000</span>, <span class="st">&quot;active&quot;</span>: <span class="va">True</span>, <span class="st">&quot;hire_date&quot;</span>: <span class="st">&quot;2018-07-01&quot;</span>})</span>
<span id="cb17-13"><a href="#cb17-13" aria-hidden="true" tabindex="-1"></a>employees.insert({<span class="st">&quot;id&quot;</span>: <span class="dv">3</span>, <span class="st">&quot;name&quot;</span>: <span class="st">&quot;Charlie&quot;</span>, <span class="st">&quot;department&quot;</span>: <span class="st">&quot;HR&quot;</span>, <span class="st">&quot;salary&quot;</span>: <span class="dv">70000</span>, <span class="st">&quot;active&quot;</span>: <span class="va">False</span>, <span class="st">&quot;hire_date&quot;</span>: <span class="st">&quot;2022-03-10&quot;</span>})</span>
<span id="cb17-14"><a href="#cb17-14" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-15"><a href="#cb17-15" aria-hidden="true" tabindex="-1"></a><span class="co"># 构建WHERE条件</span></span>
<span id="cb17-16"><a href="#cb17-16" aria-hidden="true" tabindex="-1"></a>cond_dept <span class="op">=</span> (employees.department <span class="op">==</span> <span class="st">&quot;HR&quot;</span>)          <span class="co"># ColumnConstraint</span></span>
<span id="cb17-17"><a href="#cb17-17" aria-hidden="true" tabindex="-1"></a>cond_salary <span class="op">=</span> (employees.salary <span class="op">&gt;</span> <span class="dv">65000</span>)            <span class="co"># ColumnConstraint</span></span>
<span id="cb17-18"><a href="#cb17-18" aria-hidden="true" tabindex="-1"></a>cond_active <span class="op">=</span> (employees.active <span class="op">==</span> <span class="va">True</span>)            <span class="co"># ColumnConstraint</span></span>
<span id="cb17-19"><a href="#cb17-19" aria-hidden="true" tabindex="-1"></a>cond_hire_date <span class="op">=</span> (employees.hire_date <span class="op">&gt;</span> <span class="st">&quot;2019-01-01&quot;</span>)  <span class="co"># 日期类型可以直接使用字符串进行比较</span></span>
<span id="cb17-20"><a href="#cb17-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-21"><a href="#cb17-21" aria-hidden="true" tabindex="-1"></a><span class="co"># 组合条件</span></span>
<span id="cb17-22"><a href="#cb17-22" aria-hidden="true" tabindex="-1"></a>final_condition <span class="op">=</span> s.And(cond_dept, cond_salary, cond_active, cond_hire_date)</span>
<span id="cb17-23"><a href="#cb17-23" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-24"><a href="#cb17-24" aria-hidden="true" tabindex="-1"></a><span class="co"># 添加组合条件到求解器</span></span>
<span id="cb17-25"><a href="#cb17-25" aria-hidden="true" tabindex="-1"></a>s.add(final_condition)</span>
<span id="cb17-26"><a href="#cb17-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-27"><a href="#cb17-27" aria-hidden="true" tabindex="-1"></a><span class="co"># 检查是否有满足这些条件的员工</span></span>
<span id="cb17-28"><a href="#cb17-28" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> s.check() <span class="op">==</span> sat:</span>
<span id="cb17-29"><a href="#cb17-29" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="st">&quot;存在满足条件的员工。&quot;</span>)</span>
<span id="cb17-30"><a href="#cb17-30" aria-hidden="true" tabindex="-1"></a>    model <span class="op">=</span> s.get_model()</span>
<span id="cb17-31"><a href="#cb17-31" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-32"><a href="#cb17-32" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 声明一个特定的行变量，它必须满足条件</span></span>
<span id="cb17-33"><a href="#cb17-33" aria-hidden="true" tabindex="-1"></a>    result_row_var <span class="op">=</span> Int(<span class="st">&quot;found_employee_row&quot;</span>)</span>
<span id="cb17-34"><a href="#cb17-34" aria-hidden="true" tabindex="-1"></a>    s.add(And(<span class="dv">0</span> <span class="op">&lt;=</span> result_row_var, result_row_var <span class="op">&lt;</span> <span class="bu">len</span>(employees.rows))) <span class="co"># 确保它在有效行ID内</span></span>
<span id="cb17-35"><a href="#cb17-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-36"><a href="#cb17-36" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 断言这一行 (result_row_var) 必须满足 final_condition</span></span>
<span id="cb17-37"><a href="#cb17-37" aria-hidden="true" tabindex="-1"></a>    s.add(final_condition.as_z3_expr(result_row_var))</span>
<span id="cb17-38"><a href="#cb17-38" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-39"><a href="#cb17-39" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 再次检查，现在我们试图具体化一个满足条件的行</span></span>
<span id="cb17-40"><a href="#cb17-40" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> s.check() <span class="op">==</span> sat:</span>
<span id="cb17-41"><a href="#cb17-41" aria-hidden="true" tabindex="-1"></a>        model <span class="op">=</span> s.get_model() <span class="co"># Get updated model</span></span>
<span id="cb17-42"><a href="#cb17-42" aria-hidden="true" tabindex="-1"></a>        <span class="bu">print</span>(<span class="st">&quot;找到一个具体满足条件的员工:&quot;</span>)</span>
<span id="cb17-43"><a href="#cb17-43" aria-hidden="true" tabindex="-1"></a>        name_val <span class="op">=</span> s.<span class="bu">eval</span>(employees.name(result_row_var), model)</span>
<span id="cb17-44"><a href="#cb17-44" aria-hidden="true" tabindex="-1"></a>        salary_val <span class="op">=</span> s.<span class="bu">eval</span>(employees.salary(result_row_var), model)</span>
<span id="cb17-45"><a href="#cb17-45" aria-hidden="true" tabindex="-1"></a>        dept_val <span class="op">=</span> s.<span class="bu">eval</span>(employees.department(result_row_var), model)</span>
<span id="cb17-46"><a href="#cb17-46" aria-hidden="true" tabindex="-1"></a>        active_val <span class="op">=</span> s.<span class="bu">eval</span>(employees.active(result_row_var), model)</span>
<span id="cb17-47"><a href="#cb17-47" aria-hidden="true" tabindex="-1"></a>        hire_date_val <span class="op">=</span> s.<span class="bu">eval</span>(employees.hire_date(result_row_var), model)</span>
<span id="cb17-48"><a href="#cb17-48" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-49"><a href="#cb17-49" aria-hidden="true" tabindex="-1"></a>        <span class="co"># 注意：日期值在模型中是整数形式，可能需要转换回日期字符串形式</span></span>
<span id="cb17-50"><a href="#cb17-50" aria-hidden="true" tabindex="-1"></a>        hire_date_str <span class="op">=</span> int_to_date(hire_date_val)  <span class="co"># 假设有一个函数将整数转换为日期字符串</span></span>
<span id="cb17-51"><a href="#cb17-51" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb17-52"><a href="#cb17-52" aria-hidden="true" tabindex="-1"></a>        <span class="bu">print</span>(<span class="ss">f&quot;  Name: </span><span class="sc">{</span>name_val<span class="sc">}</span><span class="ss">, Salary: </span><span class="sc">{</span>salary_val<span class="sc">}</span><span class="ss">, Dept: </span><span class="sc">{</span>dept_val<span class="sc">}</span><span class="ss">, Active: </span><span class="sc">{</span>active_val<span class="sc">}</span><span class="ss">, Hire Date: </span><span class="sc">{</span>hire_date_str<span class="sc">}</span><span class="ss">&quot;</span>)</span>
<span id="cb17-53"><a href="#cb17-53" aria-hidden="true" tabindex="-1"></a>        <span class="co"># 预期输出: Charlie, 70000, HR, False, 2022-03-10</span></span>
<span id="cb17-54"><a href="#cb17-54" aria-hidden="true" tabindex="-1"></a><span class="cf">else</span>:</span>
<span id="cb17-55"><a href="#cb17-55" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="st">&quot;没有找到满足所有条件的员工。&quot;</span>)</span></code></pre></div></li>
</ul>
<h3 id="日期类型操作">4.4. 日期类型操作</h3>
<ul>
<li><p><strong>SQL</strong>:</p>
<div class="sourceCode" id="cb18"><pre
class="sourceCode sql"><code class="sourceCode sql"><span id="cb18-1"><a href="#cb18-1" aria-hidden="true" tabindex="-1"></a><span class="kw">CREATE</span> <span class="kw">TABLE</span> <span class="kw">events</span> (event_id <span class="dt">INT</span>, event_name STRING, event_date <span class="dt">DATE</span>, is_active BOOL);</span>
<span id="cb18-2"><a href="#cb18-2" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> <span class="kw">events</span> <span class="kw">VALUES</span> (<span class="dv">1</span>, <span class="st">&#39;Conference&#39;</span>, <span class="st">&#39;2023-05-15&#39;</span>, <span class="kw">True</span>);</span>
<span id="cb18-3"><a href="#cb18-3" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> <span class="kw">events</span> <span class="kw">VALUES</span> (<span class="dv">2</span>, <span class="st">&#39;Workshop&#39;</span>, <span class="st">&#39;2023-06-20&#39;</span>, <span class="kw">True</span>);</span>
<span id="cb18-4"><a href="#cb18-4" aria-hidden="true" tabindex="-1"></a><span class="kw">INSERT</span> <span class="kw">INTO</span> <span class="kw">events</span> <span class="kw">VALUES</span> (<span class="dv">3</span>, <span class="st">&#39;Seminar&#39;</span>, <span class="st">&#39;2023-04-10&#39;</span>, <span class="kw">False</span>);</span>
<span id="cb18-5"><a href="#cb18-5" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb18-6"><a href="#cb18-6" aria-hidden="true" tabindex="-1"></a><span class="co">-- 查询: 查找在特定日期范围内的活跃事件</span></span>
<span id="cb18-7"><a href="#cb18-7" aria-hidden="true" tabindex="-1"></a><span class="kw">SELECT</span> event_name, event_date</span>
<span id="cb18-8"><a href="#cb18-8" aria-hidden="true" tabindex="-1"></a><span class="kw">FROM</span> <span class="kw">events</span></span>
<span id="cb18-9"><a href="#cb18-9" aria-hidden="true" tabindex="-1"></a><span class="kw">WHERE</span> event_date <span class="kw">BETWEEN</span> <span class="st">&#39;2023-05-01&#39;</span> <span class="kw">AND</span> <span class="st">&#39;2023-06-30&#39;</span> <span class="kw">AND</span> is_active <span class="op">=</span> <span class="kw">True</span>;</span></code></pre></div></li>
<li><p><strong>Python / Z3 转换</strong>:</p>
<div class="sourceCode" id="cb19"><pre
class="sourceCode python"><code class="sourceCode python"><span id="cb19-1"><a href="#cb19-1" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> new_model.sql_to_z3 <span class="im">import</span> SqlToZ3, DateType</span>
<span id="cb19-2"><a href="#cb19-2" aria-hidden="true" tabindex="-1"></a><span class="im">from</span> z3 <span class="im">import</span> IntSort, StringSort, BoolSort, sat</span>
<span id="cb19-3"><a href="#cb19-3" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-4"><a href="#cb19-4" aria-hidden="true" tabindex="-1"></a>s <span class="op">=</span> SqlToZ3()</span>
<span id="cb19-5"><a href="#cb19-5" aria-hidden="true" tabindex="-1"></a>events <span class="op">=</span> s.create_table(<span class="st">&quot;events&quot;</span>, {</span>
<span id="cb19-6"><a href="#cb19-6" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;event_id&quot;</span>: IntSort(),</span>
<span id="cb19-7"><a href="#cb19-7" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;event_name&quot;</span>: StringSort(),</span>
<span id="cb19-8"><a href="#cb19-8" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;event_date&quot;</span>: DateType(),  <span class="co"># 使用DateType表示日期类型</span></span>
<span id="cb19-9"><a href="#cb19-9" aria-hidden="true" tabindex="-1"></a>    <span class="st">&quot;is_active&quot;</span>: BoolSort()</span>
<span id="cb19-10"><a href="#cb19-10" aria-hidden="true" tabindex="-1"></a>})</span>
<span id="cb19-11"><a href="#cb19-11" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-12"><a href="#cb19-12" aria-hidden="true" tabindex="-1"></a><span class="co"># 插入数据，日期直接使用字符串形式</span></span>
<span id="cb19-13"><a href="#cb19-13" aria-hidden="true" tabindex="-1"></a>events.insert({<span class="st">&quot;event_id&quot;</span>: <span class="dv">1</span>, <span class="st">&quot;event_name&quot;</span>: <span class="st">&quot;Conference&quot;</span>, <span class="st">&quot;event_date&quot;</span>: <span class="st">&quot;2023-05-15&quot;</span>, <span class="st">&quot;is_active&quot;</span>: <span class="va">True</span>})</span>
<span id="cb19-14"><a href="#cb19-14" aria-hidden="true" tabindex="-1"></a>events.insert({<span class="st">&quot;event_id&quot;</span>: <span class="dv">2</span>, <span class="st">&quot;event_name&quot;</span>: <span class="st">&quot;Workshop&quot;</span>, <span class="st">&quot;event_date&quot;</span>: <span class="st">&quot;2023-06-20&quot;</span>, <span class="st">&quot;is_active&quot;</span>: <span class="va">True</span>})</span>
<span id="cb19-15"><a href="#cb19-15" aria-hidden="true" tabindex="-1"></a>events.insert({<span class="st">&quot;event_id&quot;</span>: <span class="dv">3</span>, <span class="st">&quot;event_name&quot;</span>: <span class="st">&quot;Seminar&quot;</span>, <span class="st">&quot;event_date&quot;</span>: <span class="st">&quot;2023-04-10&quot;</span>, <span class="st">&quot;is_active&quot;</span>: <span class="va">False</span>})</span>
<span id="cb19-16"><a href="#cb19-16" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-17"><a href="#cb19-17" aria-hidden="true" tabindex="-1"></a><span class="co"># 日期范围条件</span></span>
<span id="cb19-18"><a href="#cb19-18" aria-hidden="true" tabindex="-1"></a>date_start <span class="op">=</span> <span class="st">&quot;2023-05-01&quot;</span></span>
<span id="cb19-19"><a href="#cb19-19" aria-hidden="true" tabindex="-1"></a>date_end <span class="op">=</span> <span class="st">&quot;2023-06-30&quot;</span></span>
<span id="cb19-20"><a href="#cb19-20" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-21"><a href="#cb19-21" aria-hidden="true" tabindex="-1"></a><span class="co"># 构建日期范围条件，日期可以直接使用字符串与DateType列进行比较</span></span>
<span id="cb19-22"><a href="#cb19-22" aria-hidden="true" tabindex="-1"></a>date_range_condition <span class="op">=</span> s.And(</span>
<span id="cb19-23"><a href="#cb19-23" aria-hidden="true" tabindex="-1"></a>    events.event_date <span class="op">&gt;=</span> date_start,</span>
<span id="cb19-24"><a href="#cb19-24" aria-hidden="true" tabindex="-1"></a>    events.event_date <span class="op">&lt;=</span> date_end</span>
<span id="cb19-25"><a href="#cb19-25" aria-hidden="true" tabindex="-1"></a>)</span>
<span id="cb19-26"><a href="#cb19-26" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-27"><a href="#cb19-27" aria-hidden="true" tabindex="-1"></a><span class="co"># 活跃状态条件</span></span>
<span id="cb19-28"><a href="#cb19-28" aria-hidden="true" tabindex="-1"></a>active_condition <span class="op">=</span> events.is_active <span class="op">==</span> <span class="va">True</span></span>
<span id="cb19-29"><a href="#cb19-29" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-30"><a href="#cb19-30" aria-hidden="true" tabindex="-1"></a><span class="co"># 组合条件</span></span>
<span id="cb19-31"><a href="#cb19-31" aria-hidden="true" tabindex="-1"></a>final_condition <span class="op">=</span> s.And(date_range_condition, active_condition)</span>
<span id="cb19-32"><a href="#cb19-32" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-33"><a href="#cb19-33" aria-hidden="true" tabindex="-1"></a><span class="co"># 添加条件到求解器</span></span>
<span id="cb19-34"><a href="#cb19-34" aria-hidden="true" tabindex="-1"></a>s.add(final_condition)</span>
<span id="cb19-35"><a href="#cb19-35" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-36"><a href="#cb19-36" aria-hidden="true" tabindex="-1"></a><span class="co"># 检查是否有满足条件的事件</span></span>
<span id="cb19-37"><a href="#cb19-37" aria-hidden="true" tabindex="-1"></a><span class="cf">if</span> s.check() <span class="op">==</span> sat:</span>
<span id="cb19-38"><a href="#cb19-38" aria-hidden="true" tabindex="-1"></a>    model <span class="op">=</span> s.get_model()</span>
<span id="cb19-39"><a href="#cb19-39" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-40"><a href="#cb19-40" aria-hidden="true" tabindex="-1"></a>    <span class="co"># 对于找到的特定行，我们可以这样查询</span></span>
<span id="cb19-41"><a href="#cb19-41" aria-hidden="true" tabindex="-1"></a>    result_row <span class="op">=</span> Int(<span class="st">&quot;result_row&quot;</span>)</span>
<span id="cb19-42"><a href="#cb19-42" aria-hidden="true" tabindex="-1"></a>    s.add(s.And(</span>
<span id="cb19-43"><a href="#cb19-43" aria-hidden="true" tabindex="-1"></a>        result_row <span class="op">&gt;=</span> <span class="dv">0</span>,</span>
<span id="cb19-44"><a href="#cb19-44" aria-hidden="true" tabindex="-1"></a>        result_row <span class="op">&lt;</span> <span class="bu">len</span>(events.rows),</span>
<span id="cb19-45"><a href="#cb19-45" aria-hidden="true" tabindex="-1"></a>        final_condition.as_z3_expr(result_row)</span>
<span id="cb19-46"><a href="#cb19-46" aria-hidden="true" tabindex="-1"></a>    ))</span>
<span id="cb19-47"><a href="#cb19-47" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-48"><a href="#cb19-48" aria-hidden="true" tabindex="-1"></a>    <span class="cf">if</span> s.check() <span class="op">==</span> sat:</span>
<span id="cb19-49"><a href="#cb19-49" aria-hidden="true" tabindex="-1"></a>        model <span class="op">=</span> s.get_model()</span>
<span id="cb19-50"><a href="#cb19-50" aria-hidden="true" tabindex="-1"></a>        event_id_val <span class="op">=</span> s.<span class="bu">eval</span>(events.event_id(result_row), model)</span>
<span id="cb19-51"><a href="#cb19-51" aria-hidden="true" tabindex="-1"></a>        event_name_val <span class="op">=</span> s.<span class="bu">eval</span>(events.event_name(result_row), model)</span>
<span id="cb19-52"><a href="#cb19-52" aria-hidden="true" tabindex="-1"></a>        event_date_val <span class="op">=</span> s.<span class="bu">eval</span>(events.event_date(result_row), model)</span>
<span id="cb19-53"><a href="#cb19-53" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-54"><a href="#cb19-54" aria-hidden="true" tabindex="-1"></a>        <span class="co"># 将整数形式的日期转换为可读形式</span></span>
<span id="cb19-55"><a href="#cb19-55" aria-hidden="true" tabindex="-1"></a>        <span class="kw">def</span> int_to_date(date_int):</span>
<span id="cb19-56"><a href="#cb19-56" aria-hidden="true" tabindex="-1"></a>            year <span class="op">=</span> date_int <span class="op">//</span> <span class="dv">10000</span></span>
<span id="cb19-57"><a href="#cb19-57" aria-hidden="true" tabindex="-1"></a>            month <span class="op">=</span> (date_int <span class="op">%</span> <span class="dv">10000</span>) <span class="op">//</span> <span class="dv">100</span></span>
<span id="cb19-58"><a href="#cb19-58" aria-hidden="true" tabindex="-1"></a>            day <span class="op">=</span> date_int <span class="op">%</span> <span class="dv">100</span></span>
<span id="cb19-59"><a href="#cb19-59" aria-hidden="true" tabindex="-1"></a>            <span class="cf">return</span> <span class="ss">f&quot;</span><span class="sc">{</span>year<span class="sc">}</span><span class="ss">-</span><span class="sc">{</span>month<span class="sc">:02d}</span><span class="ss">-</span><span class="sc">{</span>day<span class="sc">:02d}</span><span class="ss">&quot;</span></span>
<span id="cb19-60"><a href="#cb19-60" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-61"><a href="#cb19-61" aria-hidden="true" tabindex="-1"></a>        event_date_str <span class="op">=</span> int_to_date(event_date_val)</span>
<span id="cb19-62"><a href="#cb19-62" aria-hidden="true" tabindex="-1"></a></span>
<span id="cb19-63"><a href="#cb19-63" aria-hidden="true" tabindex="-1"></a>        <span class="bu">print</span>(<span class="ss">f&quot;找到满足条件的事件: ID: </span><span class="sc">{</span>event_id_val<span class="sc">}</span><span class="ss">, 名称: </span><span class="sc">{</span>event_name_val<span class="sc">}</span><span class="ss">, 日期: </span><span class="sc">{</span>event_date_str<span class="sc">}</span><span class="ss">&quot;</span>)</span>
<span id="cb19-64"><a href="#cb19-64" aria-hidden="true" tabindex="-1"></a><span class="cf">else</span>:</span>
<span id="cb19-65"><a href="#cb19-65" aria-hidden="true" tabindex="-1"></a>    <span class="bu">print</span>(<span class="st">&quot;没有找到满足条件的事件。&quot;</span>)</span></code></pre></div></li>
</ul>
<h2 id="日志与调试">5. 日志与调试</h2>
<ul>
<li>所有核心类 (<code>SqlToZ3</code>, <code>Table</code>,
<code>Column</code>, <code>ColumnConstraint</code>,
<code>Expression</code>) 都使用一个从 <code>SqlToZ3</code>
实例获取的共享 <code>logging.Logger</code> 实例。</li>
<li>默认日志级别为 <code>INFO</code>，但可以在创建 <code>SqlToZ3</code>
实例时指定 (例如 <code>SqlToZ3(log_level=logging.DEBUG)</code>)。</li>
<li><code>DEBUG</code>
级别的日志提供了关于对象创建、方法调用、生成的Z3变量名和表达式的详细信息，这对于理解内部工作流程和调试转换逻辑非常有帮助。</li>
<li><code>Expression</code> 和 <code>ColumnConstraint</code> 对象的
<code>description</code> 属性（例如 <code>(users.age &gt; 30)</code> 或
<code>(employees.salary * 1.1)</code>）会在日志中输出，有助于追踪表达式的来源和构成。</li>
</ul>
<h2 id="局限性与未来方向">6. 局限性与未来方向</h2>
<p>当前 <code>sql_to_z3</code>
模块提供了一个将基本SQL概念映射到Z3的基础框架，但仍有一些局限性和潜在的未来扩展方向：</p>
</body>
</html>
