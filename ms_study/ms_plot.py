"""
数据血缘图可视化工具 - 按数据库类型分类和社区检测

这个脚本处理数据血缘关系，按数据库类型(PDB, CDB, ODB, MDB, SDB等)进行分类，
并使用社区检测算法识别每个类型中的社区结构，最终生成交互式可视化图表。

主要处理流程：
1. 加载预处理的数据血缘图
2. 按数据库前缀分类节点
3. 对每个子图进行社区检测
4. 构建社区间关系图(super_G)
5. 过滤和清理孤立节点
6. 应用布局算法，计算节点位置
7. 边过滤和可视化处理
8. 最终孤立节点清理，确保图形美观
9. 生成交互式HTML图表

注意：代码中包含多次节点过滤步骤，确保最终图中不会出现孤立节点。
"""

# 标准库导入
import os
import pickle
import sys
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from collections import deque, Counter
import re
import math
from datetime import datetime  # 添加日期时间模块，用于文件名

# 第三方库导入
import numpy as np
import random
from tqdm import tqdm  # 进度条库
import networkx as nx
from infomap import Infomap
from networkx.drawing.nx_agraph import graphviz_layout
import matplotlib.pyplot as plt
import matplotlib.cm as cm
from matplotlib.backends.backend_pdf import PdfPages
import plotly.graph_objects as go
import plotly.express as px
from plotly.offline import plot
import sqlglot  # SQL解析库

# 项目内模块导入
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))

from lineage_graph.lineage_graph import build_graph, calculate_lineage_with_pruning, calculate_node_path_lengths, get_upstream_subgraph
from lineage_graph.graph_manipulate import *
from lineage_core.lineage_analyzer import LineageAnalyzer
from lineage_core.utils import build_sql_list_from_str
from lineage_graph.visualization import visualize_lineage_graph
from lineage_core.logger import logger

# ===================== 全局配置参数 =====================
# 随机种子设置，确保每次运行结果一致
RANDOM_SEED = 42
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# 节点和边的数量限制
MAX_NODES = 500  # 社区节点数量上限
MAX_EDGES = 200  # 显示边的数量上限

# 数据库类型颜色映射
PREFIX_COLORS = {
    "PDB": "#1f77b4",    # 蓝色 - 产品数据库
    "CDB": "#2ca02c",    # 绿色 - 核心数据库
    "ODB": "#ff7f0e",    # 橙色 - 运营数据库
    "MDB": "#d62728",    # 红色 - 主数据库
    "SDB": "#9467bd",    # 紫色 - 源数据库
    "SOURCEDB": "#17becf", # 青色 - 源数据库
    "OTHER": "#7f7f7f"   # 灰色 - 其他类型
}
CROSS_LINK_COLOR = "rgba(100, 100, 100, 0.6)"  # 跨类型连接的颜色

# 水平布局位置设置 - 数据流从左到右
PREFIX_POSITIONS = {
    "SDB": 0,       # 最左侧 - 源数据
    "SOURCEDB": 0,  # 最左侧 - 源数据
    "ODB": 0.3,     # 左中 - 运营数据
    "CDB": 0.6,     # 右中 - 核心数据
    "MDB": 0.9,     # 右中 - 主数据
    "PDB": 1.2,       # 最右侧 - 产品数据
    "OTHER": 0.5    # 中间 - 其他类型
}

# 社区分组配置
MIN_COMMUNITY_SIZE = 10  # SDB和ODB使用名称特征分组的最小社区大小
MIN_EDGE_WEIGHT = 5      # 社区间连接的最小权重阈值

# 输出目录配置
DEFAULT_OUTPUT_DIR = "./output"
output_dir = DEFAULT_OUTPUT_DIR

# 图中文字显示配置
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'Heiti TC', 'WenQuanYi Zen Hei']
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
plt.rcParams['font.family'] = 'sans-serif'

# ===================== 辅助函数 =====================
def get_node_prefix(node_name):
    """提取节点名称的前缀，如 PDB, CDB, ODB, MDB"""
    if isinstance(node_name, str):
        match = re.match(r'^([A-Z]+DB)', node_name)
        if match:
            return match.group(1)
    return "OTHER"  # 对于没有匹配前缀的节点

# ===================== 主程序 =====================
print("开始构建lineage图")

# 加载预处理的图
G = pickle.load(open(os.path.join(output_dir, "processed_subgraph.pkl"), "rb"))
print(f"原始图有 {G.number_of_nodes()} 个节点和 {G.number_of_edges()} 条边")

# 将节点分类到不同的组
node_groups = {}
for node in G.nodes():
    prefix = get_node_prefix(node)
    if prefix not in node_groups:
        node_groups[prefix] = []
    node_groups[prefix].append(node)

print("节点分组情况:")
for prefix, nodes in node_groups.items():
    print(f"  {prefix}: {len(nodes)} 个节点")

# 为每个组创建子图
subgraphs = {}
for prefix, nodes in node_groups.items():
    if len(nodes) > 0:
        subgraphs[prefix] = G.subgraph(nodes).copy()

# 箭头符号配置
# 可选箭头样式:
# 1. 'arrow' - 传统箭头样式，自动对准线条方向，不需要角度调整
# 2. 'arrow-bar-right-open' - 带杆的开放式箭头，需要角度调整：-90度
# 3. 'triangle-right' - 指向右侧的三角形箭头，不需要角度调整
# 4. 'triangle-up' - 向上的三角形箭头，需要角度调整：+90度
# 5. 'arrow-wide' - 宽箭头样式，自动对准线条方向，不需要角度调整
# 6. 'triangle' - 三角形箭头，需要角度调整：+90度

# 不同箭头类型所需的角度调整值
ARROW_ANGLE_ADJUSTMENTS = {
    'arrow': 0,             # 标准箭头自动对齐
    'arrow-wide': 0,        # 宽箭头自动对齐
    'triangle-right': 0,    # 右侧三角形不需调整
    'triangle-up': 90,      # 上方三角形需要+90度
    'arrow-bar-right-open': -90,  # 带杆箭头需要-90度
    'triangle': 90          # 三角形需要+90度
}

ARROW_SYMBOL = 'arrow'  # 使用标准箭头，避免角度问题
ARROW_POSITION = 0.88  # 箭头在线条上的位置，更靠近终点以明确方向

# 添加子图边和顶点属性的诊断信息
for prefix, subgraph in subgraphs.items():
    edge_count = subgraph.number_of_edges()
    node_count = subgraph.number_of_nodes()
    density = 0 if node_count <= 1 else edge_count / (node_count * (node_count - 1))
    print(f"  {prefix} 子图: {node_count}个节点, {edge_count}条边, 密度: {density:.6f}")
    
    # 检查连通分量
    if prefix in ["SDB", "ODB"]:
        # 如果是有向图，转换为无向图以检查连通性
        undirected = subgraph.to_undirected()
        connected_components = list(nx.connected_components(undirected))
        print(f"  {prefix} 子图有 {len(connected_components)} 个连通分量")
        
        # 如果连通分量太多，输出大小统计
        if len(connected_components) > 10:
            component_sizes = [len(c) for c in connected_components]
            size_counts = {}
            for size in component_sizes:
                size_counts[size] = size_counts.get(size, 0) + 1
            print(f"  {prefix} 连通分量大小分布: {size_counts}")
            
            # 过滤单节点的连通分量
            large_components = [c for c in connected_components if len(c) >= 3]
            print(f"  {prefix} 大于等于3个节点的连通分量: {len(large_components)}个")

print("开始在各个子图中进行社区检测...")
all_communities = {}  # 存储所有子图的社区信息
all_partitions = {}   # 存储所有节点的社区归属
community_counter = 0  # 为所有社区分配唯一ID

# 对于SDB和ODB这样的特殊情况，使用更细粒度的分组
for prefix, subgraph in subgraphs.items():
    print(f"处理 {prefix} 子图，包含 {subgraph.number_of_nodes()} 个节点...")
    
    # 如果是SDB或ODB并且边密度极低，使用名称特征进行分组
    if prefix in ["SDB", "ODB"] and subgraph.number_of_edges() < subgraph.number_of_nodes() * 0.5:
        print(f"  {prefix}子图边密度低，使用名称特征进行分组...")
        
        # 使用节点名称的二级特征进行分组
        feature_groups = {}
        
        for node in subgraph.nodes():
            if isinstance(node, str):
                # 提取节点名称中的更多特征
                # 例如：ODB_XXX_YYY 可以按 XXX 分组，或按第一个下划线后的整个字符串分组
                parts = node.split('_')
                if len(parts) > 1:
                    # 使用第一个下划线后的部分作为二级特征
                    feature = parts[1] if len(parts) > 1 else "OTHER"
                else:
                    # 如果没有下划线，尝试使用其他模式
                    # 例如使用第一个点后的部分
                    dot_parts = node.split('.')
                    feature = dot_parts[0] if len(dot_parts) > 1 else "OTHER"
            else:
                feature = "OTHER"
                
            # 将节点添加到相应的特征组
            if feature not in feature_groups:
                feature_groups[feature] = []
            feature_groups[feature].append(node)
        
        # 按特征组大小排序
        sorted_features = sorted(
            [(feature, nodes) for feature, nodes in feature_groups.items()],
            key=lambda x: len(x[1]),
            reverse=True
        )
        
        # 只保留足够大的特征组作为单独的社区
        feature_counter = 0
        
        for feature, nodes in sorted_features:
            if len(nodes) >= MIN_COMMUNITY_SIZE:
                all_communities[community_counter] = {
                    "nodes": nodes,
                    "prefix": prefix,
                    "local_id": feature_counter,
                    "name": f"{prefix}_{feature}"
                }
                # 更新分区信息
                for node in nodes:
                    all_partitions[node] = community_counter
                
                community_counter += 1
                feature_counter += 1
        
        # 处理太小的特征组 - 将它们合并为"其他"社区
        small_groups = []
        for feature, nodes in sorted_features:
            if len(nodes) < MIN_COMMUNITY_SIZE:
                small_groups.extend(nodes)
        
        # 只有当有足够多的节点时创建"其他"社区
        if len(small_groups) >= MIN_COMMUNITY_SIZE:
            all_communities[community_counter] = {
                "nodes": small_groups,
                "prefix": prefix,
                "local_id": -1,
                "name": f"{prefix}_其他"
            }
            for node in small_groups:
                all_partitions[node] = community_counter
            community_counter += 1
            
        continue
    
    # 对于其他前缀或边密度足够的情况，使用Infomap算法
    if subgraph.number_of_nodes() < 5:  # 节点太少跳过社区检测
        for node in subgraph.nodes():
            all_partitions[node] = community_counter
        community = {"nodes": list(subgraph.nodes()), "prefix": prefix}
        all_communities[community_counter] = community
        community_counter += 1
        continue
    
    # 使用Infomap进行社区检测
    im = Infomap(directed=True)
    
    # 为Infomap设置随机种子，确保社区检测结果的一致性
    im.reseed(RANDOM_SEED)  # 修正为reseed方法
    
    # 创建节点映射
    node_to_int = {}
    int_to_node = {}
    for i, node in enumerate(subgraph.nodes()):
        node_to_int[node] = i
        int_to_node[i] = node
    
    # 添加节点和边
    for node in subgraph.nodes():
        im.add_node(node_to_int[node])
    
    for u, v in subgraph.edges():
        im.add_link(node_to_int[u], node_to_int[v])
    
    im.run()
    
    # 从结果中提取社区信息
    partition = {}
    for node in im.tree:
        if hasattr(node, 'node_id') and node.node_id in int_to_node:
            original_node = int_to_node[node.node_id]
            module_id = node.module_id
            partition[original_node] = module_id
    
    # 收集社区信息
    communities = {}
    for node, community_id in partition.items():
        if community_id not in communities:
            communities[community_id] = []
        communities[community_id].append(node)
    
    # 筛选小社区 (减小阈值以增加社区数量)
    # 对不同前缀使用不同的社区大小阈值
    if prefix in ["SDB", "ODB"]:
        min_community_size = 1  # SDB和ODB使用最小可能的阈值，允许单节点社区
    else:
        min_community_size = 2  # 其他类型使用更小的阈值
        
    filtered_communities = {c_id: members for c_id, members in communities.items() 
                           if len(members) >= min_community_size}
    
    # 添加到全局社区字典 - 只添加过滤后的社区
    for local_id, members in filtered_communities.items():
        global_id = community_counter
        all_communities[global_id] = {
            "nodes": members,
            "prefix": prefix,
            "local_id": local_id
        }
        # 更新全局分区
        for node in members:
            all_partitions[node] = global_id
        community_counter += 1
    
    # 处理没有分配到社区的节点 - 不再单独为每个节点创建社区
    # 而是将它们合并为前缀类型的"其他"社区
    unassigned_nodes = []
    for node in subgraph.nodes():
        if node not in all_partitions or node not in partition:
            unassigned_nodes.append(node)
    
    # 只有当未分配节点数量足够多时才创建一个额外社区
    if len(unassigned_nodes) >= 20:  # 至少有20个未分配节点
        all_communities[community_counter] = {
            "nodes": unassigned_nodes,
            "prefix": prefix,
            "local_id": -1,
            "name": f"{prefix}_其他节点"
        }
        for node in unassigned_nodes:
            all_partitions[node] = community_counter
        community_counter += 1

print(f"总共检测到 {community_counter} 个社区")

# 构建社区图
super_G = nx.DiGraph()

# 添加社区节点
for community_id, community in all_communities.items():
    prefix = community["prefix"]
    nodes = community["nodes"]
    
    # 计算社区节点类型
    node_types = {"source": 0, "sink": 0, "intermediate": 0}
    for node in nodes:
        in_degree = len(list(G.predecessors(node)))
        out_degree = len(list(G.successors(node)))
        
        if in_degree == 0:
            node_types["source"] += 1
        elif out_degree == 0:
            node_types["sink"] += 1
        else:
            node_types["intermediate"] += 1
    
    # 确定主导类型
    if len(nodes) == 0:
        continue
        
    dominant_type = max(node_types.items(), key=lambda x: x[1])[0]
    
    # 对于小型社区，如果有源节点或目标节点，优先考虑这些类型
    if node_types["source"] > 0 and node_types["source"] / len(nodes) > 0.3:
        dominant_type = "source"
    if node_types["sink"] > 0 and node_types["sink"] / len(nodes) > 0.3:
        dominant_type = "sink"
    
    # 添加节点
    super_G.add_node(
        community_id, 
        size=len(nodes),
        type=dominant_type,
        nodes=nodes,
        node_types=node_types,
        prefix=prefix
    )

# 添加社区间的边
for community_id1, community1 in all_communities.items():
    for community_id2, community2 in all_communities.items():
        if community_id1 != community_id2:
            weight = 0
            # 检查两个社区之间是否有连接
            for node1 in community1["nodes"]:
                for node2 in community2["nodes"]:
                    if G.has_edge(node1, node2):
                        weight += 1
            
            if weight > 0:
                super_G.add_edge(community_id1, community_id2, weight=weight)

print(f"社区图有 {super_G.number_of_nodes()} 个社区节点和 {super_G.number_of_edges()} 条社区间连接")

# 进一步筛选社区 - 保留中等及以上规模社区
print(f"原始检测到的社区数: {len(all_communities)}")
large_communities = {}
for comm_id, comm in all_communities.items():
    prefix = comm["prefix"]
    # 对不同前缀使用不同的筛选阈值，显著降低阈值增加社区数量
    if prefix in ["SDB", "ODB"]:
        min_size = 1  # 对SDB和ODB不做筛选，保留所有社区
    else:
        min_size = 2  # 其他类型阈值也降低到最小
        
    if len(comm["nodes"]) >= min_size:
        large_communities[comm_id] = comm
        
print(f"过滤后的大型社区数: {len(large_communities)}")

# 计算每个前缀的社区统计
prefix_stats = {}
for prefix in node_groups.keys():
    prefix_communities = [c for c_id, c in large_communities.items() if c["prefix"] == prefix]
    if prefix_communities:
        prefix_stats[prefix] = {
            "count": len(prefix_communities),
            "nodes": sum(len(c["nodes"]) for c in prefix_communities)
        }
        print(f"  {prefix}: {len(prefix_communities)}个社区, 覆盖{prefix_stats[prefix]['nodes']}个节点")

# 替换原始的all_communities为过滤后的large_communities
all_communities = large_communities

# 筛选边，移除权重低的连接
edges_to_remove = [(u, v) for u, v, d in super_G.edges(data=True) if d['weight'] < MIN_EDGE_WEIGHT]
super_G.remove_edges_from(edges_to_remove)
print(f"移除低权重边后，剩余 {super_G.number_of_edges()} 条边")

# 如果边数仍然很多，进一步过滤
if super_G.number_of_edges() > MAX_EDGES:  # 如果边数仍然超过MAX_EDGES
    # 计算每条边权重的百分位数
    weights = [d['weight'] for _, _, d in super_G.edges(data=True)]
    weights.sort()
    if weights:
        # 保留权重排名前60%的边
        threshold_idx = int(len(weights) * 0.6)
        if threshold_idx < len(weights):
            threshold = weights[threshold_idx]
            # 只有当阈值比之前的MIN_EDGE_WEIGHT高时才使用
            if threshold > MIN_EDGE_WEIGHT:
                edges_to_remove = [(u, v) for u, v, d in super_G.edges(data=True) if d['weight'] < threshold]
                super_G.remove_edges_from(edges_to_remove)
                print(f"进一步过滤边，新阈值:{threshold}，剩余 {super_G.number_of_edges()} 条边")

# 移除所有独立节点（没有连接的节点）
print("检查并移除独立节点...")
isolated_nodes = [n for n in super_G.nodes() if super_G.degree(n) == 0]
if isolated_nodes:
    print(f"发现 {len(isolated_nodes)} 个独立节点，将被移除")
    super_G.remove_nodes_from(isolated_nodes)
    print(f"移除独立节点后，剩余 {super_G.number_of_nodes()} 个社区节点")

# 使用graphviz的dot布局
try:
    # 尝试使用graphviz的dot布局，体现层次结构
    # 添加确定性布局参数
    pos = graphviz_layout(super_G, prog="dot", args="-Grankdir=LR -Gstart=42")  # 添加start参数作为随机种子
    print("使用graphviz dot布局成功 (LR方向)")
    
    # 当使用graphviz布局时，增加垂直方向的分散度并避免节点重叠
    # 找到每个前缀的节点
    prefix_nodes = {}
    for prefix in node_groups.keys():
        prefix_nodes[prefix] = [n for n, d in super_G.nodes(data=True) if d['prefix'] == prefix]
    
    # 对每个前缀的节点，在垂直方向上拉伸
    for prefix, nodes in prefix_nodes.items():
        if not nodes:
            continue
        
        # 获取该前缀节点的当前y坐标
        y_coords = [pos[n][1] for n in nodes if n in pos]
        if not y_coords:
            continue
            
        # 计算当前的最小和最大y值
        y_min, y_max = min(y_coords), max(y_coords)
        if y_min == y_max:  # 如果所有节点在同一水平线上
            continue
            
        # 确定拉伸系数 - 垂直方向放大2.5倍
        STRETCH_FACTOR = 2.5  # 定义为常量
        y_mid = (y_min + y_max) / 2
        
        # 拉伸每个节点的y坐标
        for node in nodes:
            if node in pos:
                x, y = pos[node]
                # 相对于中点拉伸
                y_new = y_mid + (y - y_mid) * STRETCH_FACTOR
                pos[node] = (x, y_new)
    
    print("垂直坐标拉伸完成")
    
    # 节点排斥力算法 - 避免重叠
    # 获取所有节点的大小
    node_sizes = {}
    for node in super_G.nodes():
        if node in pos:
            # 估算节点半径
            size = 5 + min(super_G.nodes[node]['size'] * 0.3, 50)
            # 节点半径应该是节点大小的平方根，再加上一些额外间距
            radius = np.sqrt(size) * 0.05 + 0.12  # 显著增加基础半径以完全避免重叠
            node_sizes[node] = radius
except Exception as e:
    print(f"graphviz dot布局失败: {e}")
    # 如果graphviz失败，使用spring布局作为备选
    pos = nx.spring_layout(super_G, seed=RANDOM_SEED)
    print("使用spring布局作为备选")
    
    # 为备选布局创建简单的节点大小字典
    node_sizes = {}
    for node in super_G.nodes():
        # 估算节点半径
        size = 5 + min(super_G.nodes[node]['size'] * 0.3, 50)
        # 节点半径应该是节点大小的平方根，再加上一些额外间距
        radius = np.sqrt(size) * 0.05 + 0.12
        node_sizes[node] = radius

# 安全检查 - 如果节点或边太多，进一步过滤
if super_G.number_of_nodes() > MAX_NODES:
    print(f"警告: 节点数量过多({super_G.number_of_nodes()}), 将进一步过滤...")
    
    # 按每个前缀类型保留最大的几个社区
    nodes_to_keep = []
    for prefix in node_groups.keys():
        prefix_nodes = [n for n, d in super_G.nodes(data=True) if d['prefix'] == prefix]
        
        # 为不同前缀设置不同的配额，提高SDB和ODB社区的配额
        if prefix in ["SDB", "ODB"]:
            prefix_quota = min(80, len(prefix_nodes))  # SDB和ODB的配额从50增加到80
        else:
            prefix_quota = min(50, len(prefix_nodes))  # 其他类型的配额从30增加到50
        
        # 按大小排序并保留最大的几个
        if prefix_nodes:
            sorted_by_size = sorted(
                [(n, super_G.nodes[n]['size']) for n in prefix_nodes],
                key=lambda x: x[1],
                reverse=True
            )
            nodes_to_keep.extend([n for n, _ in sorted_by_size[:prefix_quota]])
    
    # 使用过滤后的节点创建子图
    super_G = super_G.subgraph(nodes_to_keep).copy()
    print(f"过滤后的最终节点数: {super_G.number_of_nodes()}")
    print(f"过滤后的最终边数: {super_G.number_of_edges()}")
    
    # 更新pos字典，只保留过滤后的节点
    pos = {n: pos[n] for n in super_G.nodes() if n in pos}

# 在准备绘图数据之前，最后一次彻底检查并移除所有孤立节点
print("最终检查并移除所有孤立节点...")
isolated_nodes = [n for n in super_G.nodes() if super_G.degree(n) == 0]
if isolated_nodes:
    print(f"最终检查发现 {len(isolated_nodes)} 个孤立节点，将被移除")
    super_G.remove_nodes_from(isolated_nodes)
    # 从pos字典中也移除这些节点
    for node in isolated_nodes:
        if node in pos:
            del pos[node]
    print(f"最终清理后的节点数: {super_G.number_of_nodes()}")
    print(f"最终清理后的边数: {super_G.number_of_edges()}")

# 确保pos和super_G同步
# 移除pos中不在图中的节点
pos_nodes_to_remove = [n for n in pos if n not in super_G]
for node in pos_nodes_to_remove:
    del pos[node]
# 确保图中所有节点都有位置信息
missing_pos_nodes = []
for node in super_G.nodes():
    if node not in pos:
        print(f"节点 {node} 缺少位置信息，将被移除")
        missing_pos_nodes.append(node)
if missing_pos_nodes:
    super_G.remove_nodes_from(missing_pos_nodes)
    print(f"移除没有位置信息的节点后，剩余 {super_G.number_of_nodes()} 个节点")

# 查找最大边权重以便缩放边宽度
edge_weights = [d['weight'] for _, _, d in super_G.edges(data=True)]
max_weight = max(edge_weights) if edge_weights else 1

# 按权重排序边以便绘制
if super_G.number_of_edges() > MAX_EDGES:
    # 按权重排序边
    filter_edges = sorted(
        [(u, v, d) for u, v, d in super_G.edges(data=True)],
        key=lambda x: x[2]['weight'],
        reverse=True
    )[:MAX_EDGES]
    print(f"绘图时将只显示权重最高的 {MAX_EDGES} 条边（共 {super_G.number_of_edges()} 条）")
else:
    filter_edges = [(u, v, d) for u, v, d in super_G.edges(data=True)]

# 创建显示用的子图，只包含我们要显示的边
display_G = nx.DiGraph()
nodes_in_edges = set()
# 添加所有边和相关节点到显示图
for source, target, data in filter_edges:
    display_G.add_edge(source, target, **data)
    nodes_in_edges.add(source)
    nodes_in_edges.add(target)
# 复制节点属性
for node in display_G.nodes():
    if node in super_G.nodes():
        display_G.nodes[node].update(super_G.nodes[node])

# 检查是否由于边过滤导致有节点变成孤立节点
print("检查由于边过滤导致的孤立节点...")
isolated_after_edge_filter = [n for n in super_G.nodes() if n not in nodes_in_edges]
if isolated_after_edge_filter:
    print(f"由于边过滤，发现 {len(isolated_after_edge_filter)} 个节点变为孤立节点，将从可视化中移除")
    # 从pos字典中移除这些节点
    for node in isolated_after_edge_filter:
        if node in pos:
            del pos[node]

# 创建HTML交互式图表
html_path = os.path.join(output_dir, "data_lineage_categorized.html")

# 准备绘图数据
edge_trace = []

# 计算箭头角度函数
def calculate_angle(x0, y0, x1, y1):
    """
    计算从(x0,y0)到(x1,y1)的方向角度（弧度）
    
    参数:
    - x0, y0: 起点坐标
    - x1, y1: 终点坐标
    
    返回:
    - 方向角度（弧度）, 范围为[-π, π]
    """
    dx = x1 - x0
    dy = y1 - y0
    
    # np.arctan2返回范围为[-π, π]的角度值
    # 该函数已考虑各象限的情况，因此不需要额外的象限判断
    angle = np.arctan2(dy, dx)
    
    # 打印调试信息，仅在开发时使用
    # print(f"线段从({x0:.2f},{y0:.2f})到({x1:.2f},{y1:.2f})，角度为{angle*180/np.pi:.2f}度")
    
    return angle

# 边的绘制 - 使用新方法绘制带箭头的线
for source, target, data in filter_edges:
    if source in pos and target in pos:  # 确保两个节点都有位置信息
        x0, y0 = pos[source]
        x1, y1 = pos[target]
        
        # 确定边的颜色
        if source in super_G.nodes() and target in super_G.nodes():
            source_prefix = super_G.nodes[source]['prefix']
            target_prefix = super_G.nodes[target]['prefix']
            
            # 使用源节点和目标节点中更重要的颜色
            if source_prefix == target_prefix:
                color = PREFIX_COLORS.get(source_prefix, 'gray')
            else:
                # 跨类型连接使用特殊颜色
                color = CROSS_LINK_COLOR
        else:
            # 如果节点不在图中，使用默认颜色
            color = 'rgba(150, 150, 150, 0.5)'
            
        # 计算权重并缩放为线宽
        weight = 0.5 + min(data['weight'] / max_weight * 3, 3.5)
        
        # 1. 添加线条
        edge_trace.append(
            go.Scatter(
                x=[x0, x1],
                y=[y0, y1],
                line=dict(
                    width=weight,
                    color=color,
                    # 使用虚线表示低权重连接
                    dash='solid' if data['weight'] > MIN_EDGE_WEIGHT * 2 else 'dot'
                ),
                opacity=0.8,
                hoverinfo='text',
                hovertext=f"从 {source} 到 {target}<br>权重: {data['weight']}",
                mode='lines',
                showlegend=False,
                # 添加自定义类名以便CSS控制
                customdata=["link-line"],
                hovertemplate="%{hovertext}<extra></extra>"
            )
        )
        
        # 2. 在线条中间位置添加箭头标记
        # 计算箭头位置
        arrow_pos = ARROW_POSITION  # 使用配置的箭头位置
        middle_x = x0 + (x1 - x0) * arrow_pos
        middle_y = y0 + (y1 - y0) * arrow_pos
        
        # 计算箭头角度（弧度转为度数）
        angle_rad = calculate_angle(x0, y0, x1, y1)
        angle_deg = angle_rad * 180 / np.pi

        # 根据箭头符号类型获取角度调整值
        angle_adjustment = ARROW_ANGLE_ADJUSTMENTS.get(ARROW_SYMBOL, 0)
        adjusted_angle = angle_deg + angle_adjustment
        
        # 添加箭头标记
        edge_trace.append(
            go.Scatter(
                x=[middle_x],
                y=[middle_y],
                mode='markers',
                marker=dict(
                    symbol=ARROW_SYMBOL,
                    size=15 + weight*3,  # 根据边权重调整箭头大小
                    color=color,
                    angle=adjusted_angle,
                    line=dict(width=1.5, color=color)  # 增强箭头轮廓
                ),
                hoverinfo='none',
                showlegend=False
            )
        )

# 根据节点数量计算最优文本位置
def get_optimal_text_positions(super_G, pos):
    """为所有节点计算最优文本位置，避免重叠"""
    # 计算节点密度和邻居方向
    text_positions = {}
    
    for node in super_G.nodes():
        if node not in pos:
            continue
            
        # 获取节点邻居
        neighbors = list(super_G.predecessors(node)) + list(super_G.successors(node))
        
        if not neighbors:
            # 如果没有邻居，默认文本在上方
            text_positions[node] = "top center"
            continue
            
        # 计算邻居的平均方向
        avg_dx, avg_dy = 0, 0
        for neighbor in neighbors:
            if neighbor in pos:
                nx, ny = pos[neighbor]
                x, y = pos[node]
                avg_dx += nx - x
                avg_dy += ny - y
                
        if avg_dx == 0 and avg_dy == 0:
            # 如果没有明确方向，默认文本在上方
            text_positions[node] = "top center"
        else:
            # 根据邻居方向决定文本位置
            if abs(avg_dx) > abs(avg_dy):
                # 水平方向更重要
                if avg_dx > 0:
                    text_positions[node] = "middle left"  # 邻居在右侧，文本放左侧
                else:
                    text_positions[node] = "middle right"  # 邻居在左侧，文本放右侧
            else:
                # 垂直方向更重要
                if avg_dy > 0:
                    text_positions[node] = "top center"  # 邻居在下方，文本放上方
                else:
                    text_positions[node] = "bottom center"  # 邻居在上方，文本放下方
    
    return text_positions

# 获取优化后的文本位置
optimized_text_positions = get_optimal_text_positions(super_G, pos)

# 创建节点轨迹
node_traces = []
for prefix in node_groups.keys():
    # 该前缀的节点 - 确保只选择仍在图中的节点
    nodes = [n for n, d in display_G.nodes(data=True) if d['prefix'] == prefix]
    if not nodes:
        continue
        
    x = []
    y = []
    sizes = []
    hover_texts = []
    text_labels = []
    text_positions = []
    
    for node in nodes:
        if node in pos:  # 确保节点在pos字典中
            attr = display_G.nodes[node]
            x.append(pos[node][0])
            y.append(pos[node][1])
            
            # 节点大小
            size = 5 + min(attr['size'] * 0.3, 50)
            sizes.append(size)
            
            # 节点标签
            if "name" in attr and attr["name"] and prefix in ["SDB", "ODB"]:
                # 显示特征名称而不是ID
                parts = attr["name"].split('_')
                if len(parts) > 1:
                    # 只显示节点数量
                    text_labels.append(f"{attr['size']}")
                else:
                    text_labels.append(f"{attr['size']}")
            else:
                # 只显示节点数量
                text_labels.append(f"{attr['size']}")
            
            # 使用优化后的文本位置
            text_positions.append(optimized_text_positions.get(node, "top center"))
            
            # 悬停文本
            hover_text = f"社区ID: {node}<br>"
            if "name" in attr and attr["name"]:
                hover_text += f"社区名称: {attr['name']}<br>"
            hover_text += f"数据库类型: {attr['prefix']}<br>"
            hover_text += f"节点数量: {attr['size']}<br>"
            hover_text += f"节点类型: {attr['type']}<br>"
            hover_text += f"源节点: {attr['node_types']['source']}<br>"
            hover_text += f"目标节点: {attr['node_types']['sink']}<br>"
            hover_text += f"中间节点: {attr['node_types']['intermediate']}<br>"
            
            hover_texts.append(hover_text)
    
    # 创建节点轨迹
    if x:  # 确保有数据
        node_trace = go.Scatter(
            x=x, y=y,
            mode='markers+text',
            text=text_labels,
            textposition=text_positions,  # 使用优化后的文本位置
            textfont=dict(
                size=12,  # 增加字体大小
                color='black',
                family='Arial, sans-serif'
            ),
            marker=dict(
                color=PREFIX_COLORS.get(prefix, 'gray'),
                size=sizes,
                line=dict(width=1.5, color='rgba(50, 50, 50, 0.8)'),  # 保持边框设置
                opacity=0.9  # 略微透明
            ),
            hoverinfo='text',
            hovertext=hover_texts,
            name=f"{prefix} 社区",
            # 添加自定义类名以便CSS控制
            customdata=[[n] for n in nodes]  # 为每个节点添加社区ID作为自定义数据
        )
        node_traces.append(node_trace)

# 创建图表，调整大小和配置以减少重叠
fig = go.Figure(
    data=edge_trace + node_traces,
    layout=go.Layout(
        title=dict(
            text=f'按数据库类型分类的数据血缘图 - 共显示 {display_G.number_of_nodes()} 个社区，{len(filter_edges)} 条连接',
            font=dict(size=18, family="Arial, sans-serif")
        ),
        showlegend=True,
        hovermode='closest',
        margin=dict(b=80, l=80, r=80, t=100),  # 增加边距为图形提供更多空间
        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
        height=3000,  # 进一步增加画布高度
        width=2400,   # 增加画布宽度
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="right",
            x=0.99,
            font=dict(size=14),
            bgcolor="rgba(255, 255, 255, 0.8)",
            bordercolor="rgba(0, 0, 0, 0.2)",
            borderwidth=1
        ),
        paper_bgcolor='rgba(240, 245, 250, 1)',  # 淡蓝色背景
        plot_bgcolor='rgba(240, 245, 250, 1)',   # 淡蓝色背景
        hoverlabel=dict(
            bgcolor="white",
            bordercolor="#666",
            font=dict(family="Arial, sans-serif", size=12)
        ),
        modebar=dict(
            orientation="v",
            bgcolor="rgba(255, 255, 255, 0.7)",
            color="#333",
            activecolor="#1f77b4"
        )
    )
)

# 配置图表交互选项
config = {
    'scrollZoom': True,  # 启用滚轮缩放
    'displayModeBar': True,  # 显示模式栏
    'editable': True,  # 允许编辑图形
    'toImageButtonOptions': {
        'format': 'png',  # 下载格式
        'filename': 'data_lineage_graph',
        'height': 2800,
        'width': 2200,
        'scale': 2  # 下载时的缩放比例
    },
    'modeBarButtonsToAdd': [
        'drawclosedpath',  # 添加绘制闭合路径功能
        'drawopenpath',    # 添加绘制开放路径功能
        'eraseshape'       # 添加擦除形状功能
    ],
    'displaylogo': False  # 不显示Plotly logo
}

# 将HTML生成函数替换为使用config和注入自定义CSS的版本
from plotly.offline import plot
# 使用plot生成HTML
plot_html = plot(fig, output_type='div', config=config)

# 创建完整的HTML文件并注入自定义CSS和JavaScript
with open(html_path, 'w', encoding='utf-8') as f:
    f.write(f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>数据血缘图可视化</title>
</head>
<body>
    <div style="width:100%; height:100%;">
        {plot_html}
    </div>
</body>
</html>
""")

print(f"交互式HTML图表已保存至: {html_path}")

# 保存社区详细信息
details_path = os.path.join(output_dir, "community_by_category_details.txt")
with open(details_path, "w") as f:
    f.write(f"按数据库类型分类的社区信息\n")
    f.write(f"社区总数: {display_G.number_of_nodes()}\n")
    f.write(f"社区间连接: {display_G.number_of_edges()}\n\n")
    
    # 按前缀分组输出
    for prefix in sorted(node_groups.keys()):
        prefix_communities = [n for n, d in display_G.nodes(data=True) if d['prefix'] == prefix]
        f.write(f"\n==== {prefix} 类型社区 ({len(prefix_communities)}个) ====\n\n")
        
        # 按大小排序
        sorted_communities = sorted(
            [(n, display_G.nodes[n]['size']) for n in prefix_communities],
            key=lambda x: x[1],
            reverse=True
        )
        
        for i, (comm_id, size) in enumerate(sorted_communities):
            attr = display_G.nodes[comm_id]
            f.write(f"社区 {comm_id} (排名 {i+1}):\n")
            f.write(f"  节点数量: {size}\n")
            f.write(f"  节点类型分布: 源节点 {attr['node_types']['source']}, "
                    f"目标节点 {attr['node_types']['sink']}, "
                    f"中间节点 {attr['node_types']['intermediate']}\n")
            
            # 连接信息
            in_edges = list(display_G.in_edges(comm_id, data=True))
            out_edges = list(display_G.out_edges(comm_id, data=True))
            
            f.write(f"  入度: {len(in_edges)}, 出度: {len(out_edges)}\n")
            
            if in_edges:
                in_str = ", ".join([f"{u}({display_G.nodes[u]['prefix']})" for u, _, _ in in_edges])
                f.write(f"  上游社区: {in_str}\n")
                
            if out_edges:
                out_str = ", ".join([f"{v}({display_G.nodes[v]['prefix']})" for _, v, _ in out_edges])
                f.write(f"  下游社区: {out_str}\n")
                
            # 样本节点
            sample_nodes = attr['nodes'][:5] if len(attr['nodes']) > 5 else attr['nodes']
            f.write(f"  样本节点: {', '.join(map(str, sample_nodes))}\n\n")

print(f"详细社区信息已保存至: {details_path}")
print("可视化和分析完成！")
