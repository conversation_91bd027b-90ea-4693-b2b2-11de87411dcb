# %%

# 标准库导入
import os
import pickle
import sys
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from collections import deque
from tqdm import tqdm  # 导入进度条库
import matplotlib.pyplot as plt
import numpy as np
from collections import Counter
import sqlglot
# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'Heiti TC', 'WenQuanYi Zen Hei']  # 中文字体，按优先级排序
plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
plt.rcParams['font.family'] = 'sans-serif'

# 添加项目根目录到Python路径
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))

# 本地模块导入
from lineage_graph.lineage_graph import build_graph, calculate_lineage_with_pruning, calculate_node_path_lengths, get_upstream_subgraph
from lineage_graph.graph_manipulate import *

from lineage_core.lineage_analyzer import LineageAnalyzer
from lineage_core.utils import build_sql_list_from_str
from lineage_graph.visualization import visualize_lineage_graph
from lineage_core.logger import logger

output_dir = "./output"

# 保存lineage_graph对象到pickle文件
pickle_path = os.path.join(output_dir, "lineage_graph.pkl")
(sql_statements, lineage_graph) = pickle.load(open(pickle_path, "rb"))
# subgraph = lineage_graph

# subgraph = extract_subgraph(subgraph, node="OBDVIEW.SF2_CSR_INDIV_WORK_IFM", direction="both", depth=1)
# visualize_lineage_graph(subgraph, output_file="output/lineage_graph_vis.pdf", open_pdf=True, show_sql=False)

# target_node = get_node_name(lineage_graph, "T03_AGT_RELA_H")
# target_node = get_node_name(lineage_graph, "SE7_GC_LOAN_CREDIT")
# print(target_node)

# %%
subgraph = extract_subgraph(lineage_graph, node="PDB.T03_AGT_RELA_H", direction="upstream", depth=4)
subgraph = filter_temp_tables(subgraph)
# visualize_lineage_graph(subgraph, output_file="output/lineage_graph_vis.pdf", open_pdf=True, show_sql=False)

subgraph = extract_subgraph_between_twonode(subgraph, target_node="PDB.T03_AGT_RELA_H", source_node="ODB.SE7_GC_LOAN_CREDIT")
subgraph = filter_temp_tables(subgraph)
# subgraph = remove_temp_tables(subgraph)
# visualize_lineage_graph(subgraph, output_file="output/lineage_graph_vis.pdf", open_pdf=True, show_sql=False)

analyzer = LineageAnalyzer()
g, _, _ = calculate_lineage_with_pruning(
        lineage_graph=subgraph,
        analyzer=analyzer,
        with_pruning=True,
        downstream_nodes=["PDB.T03_AGT_RELA_H"]
    )
# visualize_lineage_graph(subgraph, output_file="output/lineage_graph_vis.pdf", open_pdf=True, show_sql=True)

# %%












# sql_list = get_edge_sql_texts(subgraph, edge=("VT_1_1572864", "VT_1_1638400"))
# for sql in sql_list:
#     print(sqlglot.parse_one(sql).sql(pretty=True))
#     print("\n")

# subgraph = get_upstream_subgraph(lineage_graph, selected_node="VT_1_1572864")
# import networkx as nx
# import matplotlib.pyplot as plt

# 绘制图形
# plt.figure(figsize=(12, 8))  # 设置更大的图形尺寸
# nx.draw(subgraph, with_labels=False, node_color='skyblue', arrows=True)
# plt.title("有向图")
# # 保存为PDF文件
# plt.savefig(os.path.join(output_dir, "network_graph.pdf"), format="pdf", bbox_inches="tight")
# plt.show()

# analyzer = LineageAnalyzer()
# g, _, _ = calculate_lineage_with_pruning(
#         lineage_graph=subgraph,
#         analyzer=analyzer,
#         with_pruning=True,
#         downstream_nodes=["VT_1_1572864"]
#     )
# visualize_lineage_graph(subgraph, output_file="output/lineage_graph_vis.pdf", open_pdf=True, show_sql=False)
# %%
