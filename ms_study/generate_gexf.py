# 标准库导入
import os
import pickle
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import networkx as nx

# 添加项目根目录到Python路径
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))

# 本地模块导入
from lineage_graph.graph_manipulate import *

output_dir = "./output"

# 保存lineage_graph对象到pickle文件
pickle_path = os.path.join(output_dir, "lineage_graph.pkl")
(sql_statements, lineage_graph) = pickle.load(open(pickle_path, "rb"))
subgraph = lineage_graph
subgraph = remove_temp_tables(subgraph)


# 将NetworkX图对象保存为Gephi可读取的GEXF格式
def save_graph_to_gephi(graph, output_path):
    """
    将NetworkX图对象保存为Gephi可读取的GEXF格式
    
    参数:
        graph: NetworkX图对象
        output_path: 输出文件路径
    """
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 创建一个新的图，避免修改原图
    G_save = nx.DiGraph()
    
    # 为节点添加属性
    for node in graph.nodes():
        # 设置节点标签为节点名称
        attrs = {'label': str(node)}
        
        # 根据节点名称前缀设置节点类型
        if len(str(node).split('.')) > 1:
            attrs['type'] = str(node).split('.')[0].upper()
        else:
            attrs['type'] = 'OTHER'
            
        # 添加节点和属性
        G_save.add_node(node, **attrs)
    
    # 为边添加属性
    for u, v, data in graph.edges(data=True):
        edge_attrs = {}
        
        # 安全地处理SQL文本
        if 'sql_texts' in data:
            try:
                # 确保sql_texts是一个列表
                if isinstance(data['sql_texts'], list):
                    # 拼接SQL文本，限制长度避免过大
                    sql_text = '\n'.join(str(sql)[:1000] for sql in data['sql_texts'] if sql is not None)
                    edge_attrs['sql_text'] = sql_text[:5000]  # 限制总长度
                elif data['sql_texts'] is not None:
                    # 如果不是列表，转换为字符串
                    edge_attrs['sql_text'] = str(data['sql_texts'])[:5000]
            except Exception as e:
                print(f"处理边 {u} -> {v} 的SQL文本时出错: {e}")
                edge_attrs['sql_text'] = "处理错误"
        
        # 确保权重属性是数值
        if 'weight' in data:
            try:
                edge_attrs['weight'] = float(data['weight'])
            except (TypeError, ValueError):
                edge_attrs['weight'] = 1.0
        
        # 添加其他安全的属性
        for key, value in data.items():
            if key not in ['sql_texts', 'weight']:
                try:
                    # 尝试转换为简单数据类型
                    if isinstance(value, (str, int, float, bool)):
                        edge_attrs[key] = value
                    elif value is None:
                        edge_attrs[key] = ""
                    else:
                        edge_attrs[key] = str(value)[:1000]  # 限制长度
                except:
                    pass  # 忽略无法处理的属性
        
        # 添加边和属性
        G_save.add_edge(u, v, **edge_attrs)
    
    try:
        # 保存为GEXF格式
        nx.write_gexf(G_save, output_path)
        print(f"图已成功保存为Gephi格式: {output_path}")
    except Exception as e:
        print(f"保存图时发生错误: {e}")
        # 尝试更简单的保存方式
        try:
            print("尝试使用简化格式保存...")
            # 创建一个更简单的图，只保留基本结构
            simple_G = nx.DiGraph()
            for node in G_save.nodes():
                simple_G.add_node(node, label=str(node))
            for u, v in G_save.edges():
                simple_G.add_edge(u, v)
            nx.write_gexf(simple_G, output_path)
            print(f"成功使用简化格式保存到: {output_path}")
        except Exception as e2:
            print(f"使用简化格式保存也失败: {e2}")
            # 最后尝试保存为其他格式
            try:
                alt_path = output_path.replace('.gexf', '.graphml')
                nx.write_graphml(simple_G, alt_path)
                print(f"图已保存为GraphML格式: {alt_path}")
            except:
                print("无法保存图形，请检查数据结构")

# 保存子图为Gephi格式
gephi_output_path = os.path.join(output_dir, "lineage_subgraph.gexf")
save_graph_to_gephi(subgraph, gephi_output_path)

print(f"子图已保存为Gephi格式: {gephi_output_path}")
