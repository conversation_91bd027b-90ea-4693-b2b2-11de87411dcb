# 标准库导入
import os
import sys
import logging
import pickle
from pathlib import Path

# 添加项目根目录到Python路径
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))

# 本地模块导入
from lineage_graph.lineage_graph import build_graph
from lineage_core.utils import build_sql_list_from_str

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def load_sql_from_file(file_path: Path) -> str:
    """
    从文件加载SQL语句
    
    Args:
        file_path: SQL文件路径
        
    Returns:
        str: SQL语句
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            sql_content = f.read()
            logger.info(f"成功加载SQL文件: {file_path}")
            return sql_content
    except Exception as e:
        error_msg = f"加载SQL文件 {file_path} 时出错: {e}"
        logger.error(error_msg)
        print(f"错误: {error_msg}")
        return ""

def load_sql_from_directory(dir_path: Path) -> str:
    """
    从目录加载所有SQL文件并拼接
    
    Args:
        dir_path: 目录路径
        
    Returns:
        str: 拼接后的SQL语句
    """
    if not dir_path.exists() or not dir_path.is_dir():
        error_msg = f"错误：找不到目录 {dir_path}"
        logger.error(error_msg)
        print(f"错误: {error_msg}")
        return ""

    # 获取目录中所有SQL文件
    sql_files = list(dir_path.glob("**/*.sql"))
    sql_files.sort()  # 按文件名排序

    if not sql_files:
        warning_msg = f"警告：目录 {dir_path} 中没有SQL文件"
        logger.warning(warning_msg)
        print(f"警告: {warning_msg}")
        return ""

    combined_sql = ""

    for sql_file in sql_files:
        try:
            with open(sql_file, "r", encoding="utf-8") as f:
                file_content = f.read()
                # 添加文件名作为注释
                combined_sql += f"{file_content};\n\n"
                logger.info(f"成功加载文件: {sql_file}")
        except Exception as e:
            error_msg = f"加载文件 {sql_file} 时出错: {str(e)}"
            logger.error(error_msg)
            print(f"错误: {error_msg}")

    return combined_sql.strip() 

# 主程序逻辑
if __name__ == "__main__":
    # 从不同目录加载SQL内容
    sql_content = ""
    for db_type in ["CDB", "MDB", "PDB", "ODB"]:
        input_path = f"/Users/<USER>/Code/1_code_interpreter/result_expaned/{db_type}"
        if sql_content:
            sql_content += ";\n\n"
        sql_content += load_sql_from_directory(Path(input_path))
    
    # 解析SQL语句并构建血缘图
    sql_statements = build_sql_list_from_str(sql_content)
    lineage_graph = build_graph(sql_statements=sql_statements)

    # 创建输出目录
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    # 保存lineage_graph对象到pickle文件
    pickle_path = os.path.join(output_dir, "lineage_graph.pkl")
    with open(pickle_path, "wb") as f:
        pickle.dump((sql_statements, lineage_graph), f)
        logger.info(f"成功将lineage_graph保存到: {pickle_path}")

    print(f"血缘图已保存到: {pickle_path}")
