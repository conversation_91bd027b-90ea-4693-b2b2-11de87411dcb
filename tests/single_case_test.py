#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
from typing import List, Dict

# 将父目录添加到系统路径，以便可以导入 lineage_core 模块
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

from lineage_core import LineageAnalyzer, set_sql_dialect, get_sql_dialect
from lineage_core.logger import logger


def read_sql_file(file_path: str) -> List[str]:
    """
    从文件中读取SQL语句，使用 sqlglot 解析器解析多个SQL语句
    
    Args:
        file_path: SQL文件路径
        
    Returns:
        SQL语句列表
    """
    if not os.path.exists(file_path):
        logger.error(f"文件不存在: {file_path}")
        sys.exit(1)
        
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用 build_sql_list_from_str 解析 SQL 语句
    from lineage_core.utils import build_sql_list_from_str
    try:
        sql_parts = build_sql_list_from_str(content)
        return sql_parts
    except Exception as e:
        logger.error(f"解析SQL文件失败: {str(e)}")
        sys.exit(1)

def main():

    # 获取命令行参数
    # sql_file = sys.argv[1] if len(sys.argv) > 1 else "tests/test_cases/simple_insert.sql"
    sql_file = sys.argv[1] if len(sys.argv) > 1 else "/Users/<USER>/Code/7_row_data_lineage/lineage_graph/sql_examples/02_简单样例_两层血缘.sql"
    dialect = sys.argv[2] if len(sys.argv) > 2 else None
    
    # 读取SQL文件
    sql_parts = read_sql_file(sql_file)
    
    if not sql_parts:
        logger.error("SQL文件为空或格式不正确")
        sys.exit(1)
    
    logger.info(f"读取到 {len(sql_parts)} 条SQL语句")
    
    # 创建分析器并分析血缘关系
    analyzer = LineageAnalyzer()
    try:
        # schema = { "orders_amount": "INT" }
        schema = None
        result = analyzer.analyze_lineage(sql_parts, dialect=dialect, schema=schema)
        
        # 打印结果
        print("\n血缘分析结果:")
        print(result)
        
    except Exception as e:
        logger.error(f"分析过程中出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
