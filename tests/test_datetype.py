"""
测试DateType基本功能

测试DateType类的基本功能，包括：
1. 日期解析和格式化
2. 日期算术运算
3. 日期比较操作
4. 日期约束求解
"""

import unittest
import z3
from datetime import datetime, timedelta, date

from lineage_core.z3_datetype import DateType, DateExpression
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver

class TestDateType(unittest.TestCase):
    def setUp(self):
        """测试初始化"""
        self.date_type = DateType()
    
    def test_date_parsing(self):
        """测试日期解析功能"""
        # 测试字符串转日期表达式
        date_str = "2023-05-15"
        date_expr = self.date_type.from_string(date_str)
        
        # 验证是否正确创建了DateExpression对象
        self.assertIsInstance(date_expr, DateExpression)
        
        # 计算2023-05-15对应的天数
        test_date = date(2023, 5, 15)
        days_since_0 = (test_date - self.date_type.base_date).days + self.date_type.base_date_adjustment
        
        # 创建一个具体的日期值进行比较
        concrete_date = z3.IntVal(days_since_0)  # 自公元0000-01-01开始的天数
        
        # 创建求解器并添加相等约束
        solver = TypeAwareEnhancedSolver()
        solver.add(date_expr.date_expr == concrete_date)
        date_expr.apply_constraints(solver._internal_solver)
        
        # 验证约束可满足
        self.assertEqual(solver.check(), z3.sat)
    
    def test_date_arithmetic(self):
        """测试日期算术运算"""
        # 创建基准日期
        base_date = self.date_type.from_string("2023-05-15")
        
        # 测试日期加法
        next_day = base_date + 1
        self.assertIsInstance(next_day, DateExpression)
        
        # 验证加法结果
        solver = TypeAwareEnhancedSolver()
        next_day.apply_constraints(solver._internal_solver)
        base_date.apply_constraints(solver._internal_solver)
        
        self.assertEqual(solver.check(), z3.sat)
        model = solver.model()
        next_day_str = self.date_type.date_to_str(model.eval(next_day.date_expr))
        self.assertEqual(next_day_str, "2023-05-16")
        
        # 测试日期减法（减去天数）
        solver = TypeAwareEnhancedSolver()
        prev_day = base_date - 1
        self.assertIsInstance(prev_day, DateExpression)
        
        # 验证减法结果
        prev_day.apply_constraints(solver._internal_solver)
        base_date.apply_constraints(solver._internal_solver)
        
        self.assertEqual(solver.check(), z3.sat)
        model = solver.model()
        prev_day_str = self.date_type.date_to_str(model.eval(prev_day.date_expr))
        self.assertEqual(prev_day_str, "2023-05-14")
        
        # 测试日期差（两个日期相减）
        another_date = self.date_type.from_string("2023-05-10")
        date_diff = base_date - another_date
        
        # 日期差应该是5天
        solver = TypeAwareEnhancedSolver()
        solver.add(date_diff == 5)
        self.assertEqual(solver.check(), z3.sat)
    
    def test_date_comparison(self):
        """测试日期比较操作"""
        date1 = self.date_type.from_string("2023-05-15")
        date2 = self.date_type.from_string("2023-05-16")
        
        # 测试大于、小于比较
        self.assertTrue(self._evaluate_bool_expr(date2 > date1))
        self.assertTrue(self._evaluate_bool_expr(date1 < date2))
        
        # 测试大于等于、小于等于比较
        self.assertTrue(self._evaluate_bool_expr(date2 >= date1))
        self.assertTrue(self._evaluate_bool_expr(date1 <= date2))
        
        # 测试相等、不等比较
        self.assertFalse(self._evaluate_bool_expr(date1 == date2))
        
        # 使用Not操作测试不等
        solver = TypeAwareEnhancedSolver()
        not_equal = solver.Not(date1 == date2)
        solver.add(not_equal)
        date1.apply_constraints(solver._internal_solver)
        date2.apply_constraints(solver._internal_solver)
        self.assertEqual(solver.check(), z3.sat)
        
        # 测试与自身比较
        self.assertTrue(self._evaluate_bool_expr(date1 == date1))
        self.assertTrue(self._evaluate_bool_expr(date1 >= date1))
        self.assertTrue(self._evaluate_bool_expr(date1 <= date1))
    
    def test_constraint_solving(self):
        """测试日期约束求解"""
        # 创建日期变量
        date_var = self.date_type.date_var("test_date")
        
        # 添加约束：日期在2023-05-15和2023-05-20之间
        min_date = self.date_type.from_string("2023-05-15")
        max_date = self.date_type.from_string("2023-05-20")
        
        # 创建类型感知求解器并添加约束
        solver = TypeAwareEnhancedSolver()
        
        # 添加日期大小比较约束
        min_comparison = date_var >= min_date
        max_comparison = date_var <= max_date
        
        solver.add(min_comparison)
        solver.add(max_comparison)
        
        # 应用所有相关约束
        date_var.apply_constraints(solver._internal_solver)
        min_date.apply_constraints(solver._internal_solver)
        max_date.apply_constraints(solver._internal_solver)
        
        # 验证约束可满足
        self.assertEqual(solver.check(), z3.sat)
        
        # 获取模型并验证结果
        model = solver.model()
        result_date = model.eval(date_var.date_expr)
        result_str = self.date_type.date_to_str(result_date)
        
        # 验证结果在指定范围内
        self.assertGreaterEqual(result_str, "2023-05-15")
        self.assertLessEqual(result_str, "2023-05-20")
    
    def test_date_days_conversion(self):
        """测试日期和天数之间的转换"""
        # 测试日期到天数的转换
        test_date = date(2023, 5, 15)
        days = self.date_type.date_to_days(2023, 5, 15)
        
        # 测试天数到日期的转换
        converted_date = self.date_type.days_to_date(days)
        self.assertEqual(converted_date, test_date)
        
        # 测试多个日期
        test_cases = [
            (2000, 1, 1),   # 2000年1月1日
            (2023, 12, 31), # 2023年12月31日
            (1999, 2, 28),  # 1999年2月28日
            (2020, 2, 29)   # 2020年闰年2月29日
        ]
        
        for year, month, day in test_cases:
            test_date = date(year, month, day)
            days = self.date_type.date_to_days(year, month, day)
            converted_date = self.date_type.days_to_date(days)
            self.assertEqual(converted_date, test_date)
    
    def _evaluate_bool_expr(self, bool_expr):
        """辅助方法：评估布尔表达式的值"""
        solver = TypeAwareEnhancedSolver()
        
        # 如果是DateExpressionBoolRef，需要添加相关约束
        if hasattr(bool_expr, 'date_expressions'):
            for date_expr in bool_expr.date_expressions:
                date_expr.apply_constraints(solver._internal_solver)
            solver.add(bool_expr._bool_ref)
        else:
            solver.add(bool_expr)
        
        # 检查是否可满足
        result = solver.check()
        return result == z3.sat

if __name__ == "__main__":
    unittest.main() 