"""
测试类型感知求解器的能力

这个脚本专门用于测试TypeAwareEnhancedSolver的类型感知能力，
特别是变量类型的动态变化和类型传播功能。
"""

import z3
from lineage_core.z3_datetype import DateType
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver

def test_variable_type_change():
    """测试变量类型的动态变化"""
    print("\n=== 变量类型动态变化测试 ===")

    # 创建日期类型和类型感知求解器
    date_type = DateType()
    solver = TypeAwareEnhancedSolver()

    # 先定义a为整数
    a = z3.Int("a")
    solver.add(a > 0)

    # 检查求解结果
    if solver.check() == z3.sat:
        model = solver.model()
        a_val = model.eval(a)
        print(f"a初始值(整数): {a_val}")
    else:
        print("无法满足整数约束")
        return

    # 重置求解器
    solver.reset()

    # 现在将a定义为日期类型
    a = date_type.from_string("2023-05-15")

    # 添加日期相关约束
    solver.add(a.year() == 2023)
    solver.add(a.month() == 5)
    solver.add(a.day() == 15)

    # 检查求解结果
    if solver.check() == z3.sat:
        print("✓ 变量类型变化测试通过: 变量a可以从整数类型变为日期类型")
    else:
        print("✗ 变量类型变化测试失败: 无法满足约束条件")

def test_type_propagation():
    """测试类型传播功能"""
    print("\n=== 类型传播测试 ===")

    # 创建日期类型和类型感知求解器
    date_type = DateType()
    solver = TypeAwareEnhancedSolver()

    # 创建变量并添加约束
    x = z3.Int("x")
    y = z3.Int("y")

    # 添加整数约束
    solver.add(y == x + 7)

    # 将x定义为日期类型
    # 不能直接比较Z3的Int变量和DateExpression的date_expr属性
    # 我们需要使用日期的整数表示
    date_val = 20230601  # 2023-06-01的整数表示
    solver.add(x == date_val)

    # 检查求解结果
    if solver.check() == z3.sat:
        model = solver.model()

        # 获取y的值
        y_val = model.eval(y)
        y_str = date_type.date_to_str(y_val)

        print(f"x被定义为日期: 2023-06-01")
        print(f"y通过约束y == x + 7被推导为: {y_str}")

        # 验证y的值是否符合预期
        expected_y = "2023-06-08"  # x+7天
        if y_str == expected_y:
            print(f"✓ 类型传播测试通过: y = x + 7 = 2023-06-01 + 7天 = {y_str}")
        else:
            print(f"✗ 类型传播测试失败: y = {y_str}, 预期: {expected_y}")
    else:
        print("✗ 类型传播测试失败: 无法满足约束条件")

if __name__ == "__main__":
    print("开始测试类型感知求解器...")

    # 运行测试
    test_variable_type_change()
    test_type_propagation()

    print("\n所有测试完成！")
