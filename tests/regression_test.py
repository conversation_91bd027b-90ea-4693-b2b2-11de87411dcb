import unittest
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from lineage_core.lineage_analyzer import LineageAnalyzer
from lineage_core.utils import read_sql_file


class TestLineageAnalyzer(unittest.TestCase):
    """行级血缘分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = LineageAnalyzer()
        self.samples_dir = os.path.join(os.path.dirname(__file__), "samples")
        
        # 读取样例SQL文件
        self.target_sql_path = os.path.join(self.samples_dir, "target.sql")
        self.upstream_1_path = os.path.join(self.samples_dir, "upstream_1.sql")
        
        self.target_sql = read_sql_file(self.target_sql_path)
        self.upstream_1 = read_sql_file(self.upstream_1_path)
        
        # 将上游SQL文件按空行分割成多个SQL语句
        self.upstream_sqls = []
        for line in self.upstream_1.split('\n\n'):
            if line.strip():
                self.upstream_sqls.append(line.strip())
    
    def test_arithmetic_operations(self):
        """测试算术运算的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target_arithmetic.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_arithmetic_1.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
        
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls[0:1])
        print(lineage_map['collected_columns'])
        
        # 验证血缘关系
        self.assertTrue(lineage_map['result'])
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls[1:2])
        self.assertFalse(lineage_map['result'])

        
    def test_logical_operations(self):
        """测试逻辑运算的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target_logical.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_logical_1.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
        
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls)
        
        # 验证血缘关系
        self.assertTrue(lineage_map['result'])
        
        # 验证目标列集合包含预期的列
        self.assertIn("orders_status", self.analyzer.target_columns)
        self.assertIn("orders_amount", self.analyzer.target_columns)

    def test_comparison_operations(self):
        """测试比较运算的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target_comparison.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_comparison_1.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
        
        schema = {
            "orders_order_date": "STRING"
        }
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls, schema=schema)
        
        # 验证血缘关系
        self.assertFalse(lineage_map['result'])
        
    def test_null_operations(self):
        """测试NULL值操作的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target_null.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_null_1.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
       
        schema = {
        }
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls[0:1], schema=schema)
        
        # 验证血缘关系
        self.assertFalse(lineage_map['result'])
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls[1:2], schema=schema)
        self.assertTrue(lineage_map['result'])
        

    def test_multiple_step(self):
        """测试 multiple step 值操作的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target_multiple_step.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_multiple_step_1.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
        
        schema = {
            "orders_customer_id": "INT",
            "raw_orders_cid": "INT",
            "orders1_cid": "INT"
        }
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls, schema=schema)
        
        # 验证血缘关系
        self.assertIn("upstream_1", lineage_map["target"])  # 第一个SQL有血缘关系
        self.assertNotIn("upstream_2", lineage_map["target"])  # 第二个SQL无血缘关系
        
        # 验证目标列集合包含预期的列
        self.assertIn("orders_customer_id", self.analyzer.target_columns)
        self.assertIn("raw_orders_cid", self.analyzer.target_columns)

    def test_or_sql(self):
        """测试带有平行SQL的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_1.sql"))
        upstream_sql_2 = read_sql_file(os.path.join(self.samples_dir, "upstream_or_test.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
        
        or_sqls = []
        for line in upstream_sql_2.split('\n\n'):
            if line.strip():
                or_sqls.append(line.strip())

        if len(or_sqls) == 1:
            upstream_sqls.extend(or_sqls)
        else:
            upstream_sqls.append(or_sqls)

        schema = {
            "orders_customer_id": "INT",
            "raw_orders_cid": "INT",
            "orders1_cid": "INT"
        }
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls, schema=schema)
        
        # 验证血缘关系
        self.assertIn("upstream_1", lineage_map["target"])  # 第一个SQL有血缘关系
        self.assertNotIn("upstream_2", lineage_map["target"])  # 第二个SQL无血缘关系
        
    def test_regex_sql(self):
        """测试带有正则表达式的血缘分析"""
        target_sql = read_sql_file(os.path.join(self.samples_dir, "target_regex.sql"))
        upstream_sql = read_sql_file(os.path.join(self.samples_dir, "upstream_regex_1.sql"))
        
        # 将上游SQL按空行分割
        upstream_sqls = []
        for line in upstream_sql.split('\n\n'):
            if line.strip():
                upstream_sqls.append(line.strip())
        
        # 分析血缘关系
        lineage_map = self.analyzer.analyze_lineage([target_sql]+upstream_sqls)
        
        # 验证血缘关系
        self.assertTrue(lineage_map['result'])

if __name__ == "__main__":
    # 测试用例列表 - 可以通过注释控制要运行的测试
    test_cases = [
        'test_arithmetic_operations',
        'test_logical_operations',
        'test_comparison_operations',
        'test_null_operations',
        'test_multiple_step',
        'test_or_sql',
        'test_regex_sql',
    ]
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加选定的测试用例到套件中
    for test_case in test_cases:
        suite.addTest(TestLineageAnalyzer(test_case))
    
    # 运行测试并统计结果
    result = unittest.TextTestRunner().run(suite)
    
    # 统计测试结果
    total_tests = len(test_cases)
    passed = result.testsRun - len(result.failures) - len(result.errors)
    failed = len(result.failures)
    errors = len(result.errors)
    
    # 输出统计信息
    print("\n测试结果统计：")
    print(f"总测试案例数: {total_tests}")
    print(f"通过案例数: {passed}")
    print(f"失败案例数: {failed}")
    print(f"错误案例数: {errors}")
    
    # 输出失败和错误的测试案例名称
    if failed > 0 or errors > 0:
        print("\n失败的测试案例：")
        for test, _ in result.failures + result.errors:
            print(f"  - {test.id().split('.')[-1]}") 