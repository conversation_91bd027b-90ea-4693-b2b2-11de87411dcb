import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from lineage_graph import lineage_graph
from lineage_graph.lineage_graph import calculate_lineage_with_pruning
from lineage_graph.visualization import print_lineage_graph_with_sql, visualize_lineage_graph

def main_with_pruning():
    """
    带有剪枝优化的血缘分析主函数示例
    """
    from lineage_core.lineage_analyzer import LineageAnalyzer
    sqls = [
        """
        INSERT INTO PDB.T03_AGT_RELA_H
        SELECT
        *
        FROM tmp_t03_agt_rela_h_se7_20200.VT_INC_237
        WHERE
        END_DT <> CAST('0001-01-01' AS DATE)
        """,
        """
        INSERT INTO tmp_t03_agt_rela_h_se7_20200.VT_INC_237
            SELECT
            ACCT_NUM,
            AGT_MODIF_NUM,
            AGT_RELA_TYPE_CD,
            START_DT,
            AGT_TYPE_CD,
            RELA_ACCT_NUM,
            RELA_AGT_MODIF_NUM,
            AGT_RELA_ADD_FEAT_TYPE_CD1,
            AGT_RELA_ADD_FEAT1,
            AGT_RELA_ADD_FEAT_TYPE_CD2,
            AGT_RELA_ADD_FEAT2,
            AGT_RELA_ADD_FEAT_TYPE_CD3,
            AGT_RELA_ADD_FEAT3,
            AGT_RELA_ADD_FEAT_TYPE_CD4,
            AGT_RELA_ADD_FEAT4,
            AGT_RELA_ADD_FEAT_TYPE_CD5,
            AGT_RELA_ADD_FEAT5,
            CAST('0001-01-01' AS DATE),
            DATA_SRC_TABLE_NAME,
            ETL_JOB_NUM
            FROM PDBVIEW.T03_AGT_RELA_H
            WHERE
            END_DT = CAST('3000-12-31' AS DATE)
            AND NOT (
            RELA_ACCT_NUM,
            RELA_AGT_MODIF_NUM
            ) IN (
            SELECT
            RELA_ACCT_NUM,
            RELA_AGT_MODIF_NUM
            FROM tmp_t03_agt_rela_h_se7_20200.VT_NEW_237
            )
        """
    ]

    print("开始构建带有剪枝优化的血缘图...")
    
    # 创建血缘分析器
    analyzer = LineageAnalyzer()
   
    result = analyzer.analyze_lineage(sqls, dialect=None, schema=None)
    # 输出结果
    print(f"\n=== 血缘分析结果 ===")
    print(result)
    
if __name__ == "__main__":
    # main()  # 运行原有主函数
    main_with_pruning()  # 运行带剪枝优化的主函数
   