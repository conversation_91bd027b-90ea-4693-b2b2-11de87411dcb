-- 上游SQL：包含四则运算的数据处理
INSERT INTO orders
SELECT 
  customer_id,
  order_id,
  base_price + tax as price,
  qty as quantity,
  base_price * 0.1 as discount,
  order_date
FROM 
  raw_orders
WHERE 
  base_price > 0
  AND qty >= 1
HAVING
  customer_id > 100
; 

-- 上游SQL：包含四则运算的数据处理
INSERT INTO orders
SELECT 
  customer_id,
  order_id,
  base_price + tax as price,
  qty as quantity,
  base_price * 0.1 as discount,
  order_date
FROM 
  raw_orders
WHERE 
  base_price <= 100 AND tax = 0 
  AND qty <= 10 AND qty > 0; 