"""
测试TypeAwareEnhancedSolver对非日期表达式的处理

此测试用例旨在验证TypeAwareEnhancedSolver对于非日期相关表达式的处理
与原始Z3求解器的行为一致，包括：

1. 基本的布尔逻辑表达式 (And, Or, Not, Implies)
2. 算术表达式 (加减乘除、比较)
3. 复杂组合表达式
4. 不可满足情况
5. 求解器API行为一致性
"""

import unittest
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import z3
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver


class TestNonDateExpressions(unittest.TestCase):
    """测试TypeAwareEnhancedSolver与原始Z3求解器行为一致性"""

    def setUp(self):
        """测试初始化，创建两个求解器实例"""
        # 原始Z3求解器
        self.original_solver = z3.Solver()
        # 增强型类型感知求解器
        self.enhanced_solver = TypeAwareEnhancedSolver()

    def test_boolean_logic(self):
        """测试基本布尔逻辑表达式"""
        # 创建基本布尔变量
        a = z3.Bool('a')
        b = z3.Bool('b')
        c = z3.Bool('c')

        # 测试And操作
        expr1 = z3.And(a, b)
        # 向两个求解器添加相同的表达式
        self.original_solver.add(expr1)
        self.enhanced_solver.add(expr1)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试Or操作
        expr2 = z3.Or(a, b, c)
        self.original_solver.add(expr2)
        self.enhanced_solver.add(expr2)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试Not操作
        expr3 = z3.Not(z3.And(a, b))
        self.original_solver.add(expr3)
        self.enhanced_solver.add(expr3)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试Implies操作
        expr4 = z3.Implies(a, b)
        self.original_solver.add(expr4)
        self.enhanced_solver.add(expr4)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试增强型求解器特定的布尔API
        expr5 = self.enhanced_solver.And(a, b)
        expr6 = self.enhanced_solver.Or(a, c)
        expr7 = self.enhanced_solver.Not(b)
        expr8 = self.enhanced_solver.Implies(a, c)
        self.enhanced_solver.add(expr5, expr6, expr7, expr8)
        # 对应的Z3原始表达式
        self.original_solver.add(z3.And(a, b), z3.Or(a, c), z3.Not(b), z3.Implies(a, c))
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())

    def test_arithmetic_operations(self):
        """测试算术表达式的处理"""
        # 创建整数变量
        x = z3.Int('x')
        y = z3.Int('y')
        z = z3.Int('z')

        # 测试基本算术操作
        expr1 = x + y == z
        self.original_solver.add(expr1)
        self.enhanced_solver.add(expr1)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试复杂算术表达式
        expr2 = z3.And(x > 0, y > 0, x + y < 10, z == x * 2)
        self.original_solver.add(expr2)
        self.enhanced_solver.add(expr2)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试除法和取模
        expr3 = z3.And(x > 0, y > 0, x % y == 0, x / y == 2)
        self.original_solver.add(expr3)
        self.enhanced_solver.add(expr3)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())

    def test_array_operations(self):
        """测试数组和索引操作"""
        # 创建数组类型和变量
        int_array = z3.ArraySort(z3.IntSort(), z3.IntSort())
        arr = z3.Const('arr', int_array)
        i = z3.Int('i')
        j = z3.Int('j')
        v = z3.Int('v')

        # 测试数组存储和选择操作
        expr1 = z3.And(z3.Select(arr, i) == v, i >= 0, i < 10, v > 0)
        self.original_solver.add(expr1)
        self.enhanced_solver.add(expr1)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试数组存储后选择
        expr2 = z3.And(
            z3.Select(z3.Store(arr, i, v), i) == v,
            z3.Select(z3.Store(arr, i, v), j) == z3.Select(arr, j)
        )
        self.original_solver.add(expr2, i != j)
        self.enhanced_solver.add(expr2, i != j)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())

    def test_real_and_bitvector(self):
        """测试实数和位向量类型"""
        # 实数变量
        x = z3.Real('x')
        y = z3.Real('y')
        
        # 位向量变量
        a = z3.BitVec('a', 8)
        b = z3.BitVec('b', 8)

        # 测试实数约束
        expr1 = z3.And(x > 0, y > 0, x + y < 1.5, x * y > 0.1)
        self.original_solver.add(expr1)
        self.enhanced_solver.add(expr1)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        self.original_solver.reset()
        self.enhanced_solver.reset()

        # 测试位向量约束
        expr2 = z3.And(a & b == 0, a | b == 255, a > 0, b > 0)
        self.original_solver.add(expr2)
        self.enhanced_solver.add(expr2)
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())

    def test_unsatisfiable_case(self):
        """测试不可满足情况的处理"""
        x = z3.Int('x')
        
        # 创建明显矛盾的约束
        expr = z3.And(x > 0, x < 0)
        
        self.original_solver.add(expr)
        self.enhanced_solver.add(expr)
        
        # 验证两个求解器都返回unsat
        self.assertEqual(self.original_solver.check(), z3.unsat)
        self.assertEqual(self.enhanced_solver.check(), z3.unsat)

    def test_complex_mixed_expressions(self):
        """测试复杂混合表达式"""
        # 创建不同类型的变量
        x = z3.Int('x')
        y = z3.Int('y')
        b1 = z3.Bool('b1')
        b2 = z3.Bool('b2')
        r = z3.Real('r')

        # 创建一个混合各种操作的复杂表达式
        complex_expr = z3.And(
            z3.Or(x > 0, y < 0),
            z3.Implies(b1, x + y > 10),
            z3.Implies(b2, r * 2.0 > z3.ToReal(x)),
            z3.If(x > y, b1, b2),
            z3.Not(z3.And(b1, b2))
        )
        
        self.original_solver.add(complex_expr)
        self.enhanced_solver.add(complex_expr)
        
        # 验证两个求解器返回相同结果
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())

    def test_model_consistency(self):
        """测试模型获取与变量求值一致性"""
        x = z3.Int('x')
        y = z3.Int('y')
        
        # 添加简单约束
        expr = z3.And(x > 0, x < 10, y == x * 2)
        
        self.original_solver.add(expr)
        self.enhanced_solver.add(expr)
        
        # 验证两个求解器都是可满足的
        self.assertEqual(self.original_solver.check(), z3.sat)
        self.assertEqual(self.enhanced_solver.check(), z3.sat)
        
        # 获取模型
        original_model = self.original_solver.model()
        enhanced_model = self.enhanced_solver.model()
        
        # 从原始求解器模型中获取x值
        x_val_original = original_model.eval(x)
        y_val_original = original_model.eval(y)
        
        # 在增强求解器中添加额外约束，固定x值为原始求解器中的值
        self.enhanced_solver.reset()
        self.enhanced_solver.add(expr, x == x_val_original)
        
        # 验证增强型求解器仍然可满足
        self.assertEqual(self.enhanced_solver.check(), z3.sat)
        
        # 获取新模型
        new_enhanced_model = self.enhanced_solver.model()
        
        # 验证y的值与原始模型中的计算一致
        y_val_enhanced = new_enhanced_model.eval(y)
        self.assertEqual(y_val_enhanced, y_val_original)
        
    def test_solver_api_consistency(self):
        """测试求解器API的一致性"""
        x = z3.Int('x')
        y = z3.Int('y')
        
        # 测试push/pop行为
        self.original_solver.push()
        self.enhanced_solver.push()
        
        self.original_solver.add(x > 0)
        self.enhanced_solver.add(x > 0)
        
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        
        self.original_solver.push()
        self.enhanced_solver.push()
        
        self.original_solver.add(y < 0)
        self.enhanced_solver.add(y < 0)
        
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        
        self.original_solver.pop()
        self.enhanced_solver.pop()
        
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        
        self.original_solver.pop()
        self.enhanced_solver.pop()
        
        # 测试重置行为
        self.original_solver.reset()
        self.enhanced_solver.reset()
        
        # 添加新约束
        self.original_solver.add(x == y)
        self.enhanced_solver.add(x == y)
        
        self.assertEqual(self.original_solver.check(), self.enhanced_solver.check())
        
if __name__ == "__main__":
    unittest.main() 