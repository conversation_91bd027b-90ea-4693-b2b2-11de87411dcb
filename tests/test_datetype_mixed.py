"""
测试DateType与Int类型混合使用

测试DateType与Int类型混合使用的情况，包括：
1. 使用TypeAwareEnhancedSolver处理混合类型
2. 测试复杂的布尔表达式，包括Not/And/Or操作
3. 测试日期类型与整数类型的交互
"""

import unittest
import z3
from datetime import datetime, timedelta, date

from lineage_core.z3_datetype import DateType, DateExpression
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver

class TestDateTypeMixed(unittest.TestCase):
    def setUp(self):
        """测试初始化"""
        self.date_type = DateType()
        self.solver = TypeAwareEnhancedSolver()
    
    def test_type_aware_solver_capabilities(self):
        """测试TypeAwareEnhancedSolver的基本功能"""
        # 创建整数变量
        a = z3.Int('a')
        # 创建日期变量
        b = self.date_type.date_var('b')
        # 创建目标日期
        target = self.date_type.from_string("2023-05-15")
        
        # 添加约束
        self.solver.add(a > 10)
        self.solver.add(b == target)
        
        # 检查求解器状态
        result = self.solver.check()
        self.assertEqual(result, z3.sat)
        
        # 获取模型并验证结果
        model = self.solver.model()
        a_val = model.eval(a).as_long()
        b_val = model.eval(b.date_expr)
        
        self.assertGreater(a_val, 10)
        self.assertEqual(self.date_type.date_to_str(b_val), "2023-05-15")
    
    def test_boolean_operations(self):
        """测试复杂的布尔表达式，包括Not/And/Or操作"""
        # 创建日期变量和常量
        date_var = self.date_type.date_var('date_var')
        date1 = self.date_type.from_string("2023-05-15")
        date2 = self.date_type.from_string("2023-05-20")
        
        # 创建整数变量和常量
        int_var = z3.Int('int_var')
        
        # 测试Not操作
        not_expr = self.solver.Not(date_var == date1)
        
        self.solver.add(not_expr)
        self.solver.add(date_var == date2)  # 确保date_var有其他可行值
        
        # 应用日期约束
        date_var.apply_constraints(self.solver._internal_solver)
        date1.apply_constraints(self.solver._internal_solver)
        date2.apply_constraints(self.solver._internal_solver)
        
        # 检查求解器状态
        result = self.solver.check()
        self.assertEqual(result, z3.sat)
        
        # 获取模型并验证结果
        model = self.solver.model()
        date_val = model.eval(date_var.date_expr)
        
        # 验证date_var不等于date1
        self.assertNotEqual(self.date_type.date_to_str(date_val), "2023-05-15")
        # 验证date_var确实等于date2
        self.assertEqual(self.date_type.date_to_str(date_val), "2023-05-20")
        
        # 重置求解器
        self.solver = TypeAwareEnhancedSolver()
        
        # 测试And操作
        and_expr = self.solver.And(date_var > date1, date_var < date2, int_var > 5)
        
        self.solver.add(and_expr)
        
        # 应用日期约束
        date_var.apply_constraints(self.solver._internal_solver)
        date1.apply_constraints(self.solver._internal_solver)
        date2.apply_constraints(self.solver._internal_solver)
        
        # 检查求解器状态
        result = self.solver.check()
        self.assertEqual(result, z3.sat)
        
        # 获取模型并验证结果
        model = self.solver.model()
        date_val = model.eval(date_var.date_expr)
        int_val = model.eval(int_var).as_long()
        
        # 验证date_var在date1和date2之间
        date_str = self.date_type.date_to_str(date_val)
        self.assertGreater(date_str, "2023-05-15")
        self.assertLess(date_str, "2023-05-20")
        # 验证int_var大于5
        self.assertGreater(int_val, 5)
        
        # 重置求解器
        self.solver = TypeAwareEnhancedSolver()
        
        # 测试Or操作
        or_expr = self.solver.Or(date_var < date1, date_var > date2, int_var < 0)
        
        self.solver.add(or_expr)
        
        # 应用日期约束
        date_var.apply_constraints(self.solver._internal_solver)
        date1.apply_constraints(self.solver._internal_solver)
        date2.apply_constraints(self.solver._internal_solver)
        
        # 检查求解器状态
        result = self.solver.check()
        self.assertEqual(result, z3.sat)
        
        # 获取模型并验证结果
        model = self.solver.model()
        date_val = model.eval(date_var.date_expr)
        int_val = model.eval(int_var).as_long()
        
        # 验证至少有一个条件被满足
        date_str = self.date_type.date_to_str(date_val)
        self.assertTrue(
            date_str < "2023-05-15" or 
            date_str > "2023-05-20" or 
            int_val < 0
        )
    
    def test_mixed_type_constraints(self):
        """测试日期类型与整数类型的混合约束"""
        # 创建日期变量
        start_date = self.date_type.date_var('start_date')
        base_date = self.date_type.from_string("2023-01-01")
        
        # 创建整数变量，表示天数
        days_offset = z3.Int('days_offset')
        
        # 使用整数变量计算目标日期
        # 基准日期 + 天数偏移 = 目标日期
        target_date = base_date + days_offset
        
        # 添加约束
        self.solver.add(days_offset > 0)
        self.solver.add(days_offset < 10)
        
        # 使用下面方式添加日期比较约束
        eq_constraint = start_date == target_date
        self.solver.add(eq_constraint._bool_ref)  # 使用内部的布尔表达式
        
        # 确保应用所有日期表达式的约束
        start_date.apply_constraints(self.solver._internal_solver)
        base_date.apply_constraints(self.solver._internal_solver)
        target_date.apply_constraints(self.solver._internal_solver)
        
        # 检查求解器状态
        result = self.solver.check()
        self.assertEqual(result, z3.sat)
        
        # 获取模型并验证结果
        model = self.solver.model()
        days_val = model.eval(days_offset).as_long()
        start_val = model.eval(start_date.date_expr)
        target_val = model.eval(target_date.date_expr)
        
        # 验证天数偏移在规定范围内
        self.assertGreater(days_val, 0)
        self.assertLess(days_val, 10)
        
        # 验证start_date确实等于base_date + days_offset
        expected_date = (datetime.strptime("2023-01-01", "%Y-%m-%d") + timedelta(days=days_val)).strftime("%Y-%m-%d")
        self.assertEqual(self.date_type.date_to_str(start_val), expected_date)
        
        # 直接验证天数计算是否正确
        self.assertEqual(start_val.as_long(), target_val.as_long())

def test_type_aware_solver_capabilities():
    """用于快速测试的辅助函数"""
    test = TestDateTypeMixed()
    test.setUp()
    test.test_type_aware_solver_capabilities()
    print("基本功能测试成功")
    
    test.test_boolean_operations()
    print("布尔操作测试成功")
    
    test.test_mixed_type_constraints()
    print("混合类型约束测试成功")
    
    print("所有测试通过！")

if __name__ == "__main__":
    test_type_aware_solver_capabilities()
    # 或者运行所有测试
    # unittest.main() 