# Python-generated files
__pycache__/
*.py[oc]
build/
dist/
wheels/
*.egg-info

# Virtual environments
.venv
.DS_Store

# 忽略所有.pyc文件
*.pyc

# 忽略所有.pyo文件
*.pyo
lineage_graph/sql_examples/CDB

*.pdf
*_vis
*.pkl
output/lineage_graph_vis.pdf
new_model_backup/data_lineage_system_design.html
new_model_backup/data_lineage_system_design.md
new_model_backup/dependency_analyzer.py
new_model_backup/relevance_analyzer.py
new_model_backup/sql_parser.py
new_model_backup/type_analyzer.py
new_model_backup/z3_converter.py
new_model_backup/sql_to_z3/__init__.py
new_model_backup/sql_to_z3/column.py
new_model_backup/sql_to_z3/DESIGN_AND_USAGE.md
new_model_backup/sql_to_z3/expression.py
new_model_backup/sql_to_z3/solver.py
new_model_backup/sql_to_z3/table.py
new_model_backup/tests/regression.sh
new_model_backup/tests/test_case_when_type_inference.py
new_model_backup/tests/test_complex_aggregations.py
new_model_backup/tests/test_data_lineage.py
new_model_backup/tests/test_join_operations.py
new_model_backup/tests/test_nested_alias_resolution.py
new_model_backup/tests/test_relevance_analyzer_multi_sql.py
new_model_backup/tests/test_subquery_lineage.py
new_model_backup/tests/test_type_analyzer.py
new_model_backup/tests/test_type_conversions.py
new_model_backup/tests/test_window_and_set_ops.py
new_model_backup/tests/sql_to_z3/basic_example.py
new_model_backup/tests/sql_to_z3/complex_example.py
new_model_backup/tests/sql_to_z3/expression_example.py
new_model_backup/tests/sql_to_z3/field_lineage_example.py
new_model_backup/tests/sql_to_z3/lineage_example.py
new_model_backup/tests/sql_to_z3/multi_table_join_example.py
new_model_backup/tests/sql_to_z3/simplified_syntax_example.py
new_model_backup/tests/sql_to_z3/SQL_TO_Z3_README.md
new_model_backup/tests/sql_to_z3/test_advanced_scenarios.py
new_model_backup/tests/sql_to_z3/test_date_support.py
new_model_backup/tests/sql_to_z3/test_solver_coverage.py
new_model_backup/tests/sql_to_z3/test_table_column_expression.py
