{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: 当前文件",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal"
        },
        {
            "name": "Python Pytest: 当前文件",
            "type": "debugpy",
            "request": "launch",
            "module": "pytest",
            "args": [
                "${file}",
                "-v"
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "Python Unittest: 当前文件",
            "type": "debugpy",
            "request": "launch",
            "module": "unittest",
            "args": [
                "${file}"
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "Python:Streamlit 启动APP",
            "type": "debugpy",
            "request": "launch",
            "module": "streamlit",
            "args": [
                "run",
                "lineage_graph/lineage_app.py"
            ]
        },
        {
            "name": "Python Debugger: 单次运行",
            "type": "debugpy",
            "request": "launch",
            "program": "main.py",
            "console": "integratedTerminal",
            "args": "--target tests/samples/target.sql --upstream tests/samples/upstream_1.sql"
        }
    ]
}