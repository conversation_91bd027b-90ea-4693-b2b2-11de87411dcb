// 表级血缘图
digraph {
	rankdir=LR
	node [fontname=SimHei]
	edge [fontname=SimHei]
	node [fixedsize=true height=0.5 width=1.8]
	nodesep=0.4
	ranksep=0.8
	all_orders [label=all_orders fillcolor="#FF9999" fontcolor=black penwidth=1.5 shape=box style="filled,rounded"]
	target_table [label=target_table fillcolor="#99FF99" fontcolor=black penwidth=1.5 shape=box style="filled,rounded"]
	raw_orders [label=raw_orders fillcolor="#9999FF" fontcolor=black penwidth=1.5 shape=box style="filled,rounded"]
	raw_order4 [label=raw_order4 fillcolor="#666666" fontcolor=white penwidth=1 shape=box style="filled,rounded"]
	raw_orders1 [label=raw_orders1 fillcolor="#666666" fontcolor=white penwidth=1 shape=box style="filled,rounded"]
	raw_orders3 [label=raw_orders3 fillcolor="#666666" fontcolor=white penwidth=1 shape=box style="filled,rounded"]
	raw_orders2 [label=raw_orders2 fillcolor="#666666" fontcolor=white penwidth=1 shape=box style="filled,rounded"]
	source_table [label=source_table fillcolor="#99FF99" fontcolor=black penwidth=1.5 shape=box style="filled,rounded"]
	source_raw_orders [label=source_raw_orders fillcolor="#DDDDDD" fontcolor="#666666" penwidth=1 shape=box style="filled,rounded"]
	all_orders -> target_table [label="SQL 1: \lINSERT INTO target_table\l　SELECT\l　　　order_date\l　FROM all_orders\l　WHERE\l　　　price \> 100 AND order_date \> \'2024-01-01\' AND code = \'S002\'\l------------------------------\l" align=left color="#0066CC" fontsize=10 penwidth=1.5]
	raw_orders -> all_orders [label="SQL 2: \lINSERT INTO all_orders\l　SELECT\l　　　order_date,\l　　　price\l　FROM raw_orders\l　WHERE\l　　　price \> 100\l------------------------------\l" align=left color="#0066CC" fontsize=10 penwidth=1.5]
	raw_order4 -> all_orders [label="SQL 3: \lINSERT INTO all_orders\l　SELECT\l　　　order_date,\l　　　code,\l　　　price AS price\l　FROM raw_order4\l　WHERE\l　　　code = \'S001\'\l------------------------------\l\lSQL 4: \lINSERT INTO all_orders\l　SELECT\l　　　order_date,\l　　　code,\l　　　0 AS price\l　FROM raw_order4\l　WHERE\l　　　NOT code = \'S001\'\l------------------------------\l" align=left color="#666666" fontsize=10 penwidth=1 style=dashed]
	raw_orders1 -> all_orders [label="SQL 5: \lINSERT INTO all_orders\l　SELECT\l　　　order_date,\l　　　price\l　FROM raw_orders1\l　WHERE\l　　　price \< 50\l------------------------------\l" align=left color="#666666" fontsize=10 penwidth=1 style=dashed]
	raw_orders3 -> all_orders [label="SQL 6: \lINSERT INTO all_orders\l　SELECT\l　　　order_date,\l　　　raw_price * rate AS price\l　FROM raw_orders3\l　WHERE\l　　　raw_price \< 50 AND rate \< 1 AND raw_price \> 0 AND rate \> 0\l------------------------------\l" align=left color="#666666" fontsize=10 penwidth=1 style=dashed]
	raw_orders2 -> all_orders [label="SQL 7: \lINSERT INTO all_orders\l　SELECT\l　　　order_date,\l　　　\'S001\' AS code,\l　　　price\l　FROM raw_orders2\l------------------------------\l" align=left color="#666666" fontsize=10 penwidth=1 style=dashed]
	source_table -> raw_orders2 [label="SQL 8: \lINSERT INTO raw_orders2\l　SELECT\l　　　\'2024-01-02\' AS order_date,\l　　　\'S001\' AS code,\l　　　price\l　FROM source_table\l------------------------------\l" align=left color="#666666" fontsize=10 penwidth=1 style=dashed]
	source_table -> raw_orders [label="SQL 9: \lINSERT INTO raw_orders\l　SELECT\l　　　order_date,\l　　　price\l　FROM source_table\l　WHERE\l　　　price \> 100\l------------------------------\l" align=left color="#0066CC" fontsize=10 penwidth=1.5]
	source_raw_orders -> raw_orders1 [label="SQL 10: \lINSERT INTO raw_orders1\l　SELECT\l　　　order_date,\l　　　price\l　FROM source_raw_orders\l　WHERE\l　　　price \> 100\l------------------------------\l" align=left color="#CCCCCC" fontsize=10 penwidth=1 style=dashed]
	bgcolor="#FAFAFA"
}
