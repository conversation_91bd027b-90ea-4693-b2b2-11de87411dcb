"""
SQL行级血缘分析工具 - 基于Neo4j的血缘图构建模块

该模块提供了构建SQL语句血缘关系图的核心功能，包括：
- 构建血缘关系图
- 应用剪枝优化
- 处理SQL语句组
- 图操作功能（计算路径长度、获取上游子图等）

作者: 数据血缘分析团队
版本: 1.0.0
"""

from typing import List, Set, Dict, Tuple, Union, Optional, Any
import sys
from pathlib import Path
from collections import deque
import json  # 添加json导入用于序列化
import argparse
import traceback

# 第三方库导入
from lineage_core.lineage_analyzer import LineageAnalyzer
from neo4j import GraphDatabase

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 本地模块导入
from lineage_core.utils import build_sql_list_from_str, extract_tables, convert_create_table_to_insert
from lineage_core.split_sql import split_union
from lineage_core.logger import logger

class Neo4jLineageGraph:
    """基于Neo4j的血缘图构建和操作类"""
    
    def __init__(self, uri="bolt://localhost:7687", user="neo4j", password="12345678", namespace=None):
        """
        初始化Neo4j连接
        
        Args:
            uri: Neo4j服务器URI
            user: 用户名
            password: 密码
            namespace: 命名空间，用于区分不同来源的数据
        """
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self.namespace = namespace or "default"
        logger.info(f"初始化Neo4j血缘图，命名空间: {self.namespace}")
        self._initialize_database()
    
    def close(self):
        """关闭Neo4j连接"""
        self.driver.close()
    
    def _initialize_database(self):
        """初始化数据库，创建约束和索引"""
        with self.driver.session() as session:
            # 修改表名唯一约束，确保表名在命名空间内唯一而不是全局唯一
            # 先删除可能存在的旧约束
            try:
                session.run("DROP CONSTRAINT table_name_unique IF EXISTS")
            except Exception as e:
                logger.warning(f"删除旧约束时出错: {e}")
            
            # 根据Neo4j版本创建合适的复合约束
            # 对于Neo4j 4.x及以上版本
            try:
                session.run(
                    "CREATE CONSTRAINT table_name_ns_unique IF NOT EXISTS "
                    "FOR (t:Table) REQUIRE (t.name, t.namespace) IS UNIQUE"
                )
            except Exception as e:
                logger.warning(f"创建复合约束时出错: {e}")
                # 如果复合约束失败，尝试使用标签+属性的方式
                try:
                    session.run(
                        "CREATE CONSTRAINT table_name_ns_unique IF NOT EXISTS "
                        "FOR (t:Table) REQUIRE t.name IS UNIQUE"
                    )
                    logger.warning("已创建基本约束，但命名空间隔离可能不完全")
                except Exception as e2:
                    logger.error(f"创建基本约束也失败: {e2}")
            
            # 创建索引以提高查询性能
            try:
                session.run("CREATE INDEX table_name_index IF NOT EXISTS FOR (t:Table) ON (t.name)")
                session.run("CREATE INDEX table_namespace_index IF NOT EXISTS FOR (t:Table) ON (t.namespace)")
            except Exception as e:
                logger.warning(f"创建索引时出错: {e}")
    
    def clear_graph(self):
        """清空数据库中的所有节点和关系"""
        with self.driver.session() as session:
            session.run("MATCH (n {namespace: $namespace}) DETACH DELETE n", namespace=self.namespace)
    
    def add_node(self, node_name, **attributes):
        """添加表节点，带有命名空间标签"""
        # 检查节点名是否有效
        if not node_name or node_name.strip() == "":
            logger.warning("尝试添加空名称的节点，已跳过")
            return
            
        with self.driver.session() as session:
            # 添加命名空间属性和节点名属性
            attributes['namespace'] = self.namespace
            attributes['name'] = node_name  # 确保name属性存在于属性集中
            
            # 创建节点，使用命名空间标签并包含节点属性
            try:
                session.run(
                    """
                    CREATE (t:Table:`""" + self.namespace + """`)
                    SET t = $attributes
                    """,
                    attributes=attributes
                )
                logger.debug(f"创建节点: {node_name} 在命名空间: {self.namespace}")
            except Exception as e:
                # 节点可能已存在，尝试更新操作
                logger.debug(f"创建节点异常: {e}, 尝试MERGE操作")
                session.run(
                    """
                    MERGE (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                    ON CREATE SET t = $attributes
                    ON MATCH SET t += $attributes
                    """,
                    name=node_name, namespace=self.namespace, attributes=attributes
                )
    
    def add_edge(self, source, target, **attributes):
        """
        添加表之间的血缘关系边
        
        Args:
            source: 源表名
            target: 目标表名
            attributes: 边属性
        """
        # 检查节点名是否有效
        if not source or source.strip() == "" or not target or target.strip() == "":
            logger.warning(f"尝试添加边时发现无效的节点名: source={source}, target={target}, 已跳过")
            return
            
        with self.driver.session() as session:
            # 处理复杂属性，将其序列化为JSON字符串
            processed_attributes = {}
            for key, value in attributes.items():
                if isinstance(value, (list, dict)):
                    processed_attributes[key] = json.dumps(value)
                else:
                    processed_attributes[key] = value
            
            # 首先确保节点存在
            source_attrs = {'name': source, 'namespace': self.namespace}
            target_attrs = {'name': target, 'namespace': self.namespace}
            
            # 创建源节点
            session.run(
                """
                MERGE (s:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                """,
                name=source, namespace=self.namespace
            )
            
            # 创建目标节点
            session.run(
                """
                MERGE (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                """,
                name=target, namespace=self.namespace
            )
            
            # 然后创建关系
            session.run(
                """
                MATCH (s:Table:`""" + self.namespace + """` {name: $source, namespace: $namespace})
                MATCH (t:Table:`""" + self.namespace + """` {name: $target, namespace: $namespace})
                MERGE (s)-[r:FLOWS_TO]->(t)
                SET r += $attributes
                """,
                source=source, target=target, namespace=self.namespace, attributes=processed_attributes
            )
            
            logger.debug(f"添加边: {source} -> {target} 在命名空间: {self.namespace}")
    
    def has_node(self, node_name):
        """
        检查节点是否存在
        
        Args:
            node_name: 要检查的节点名
            
        Returns:
            bool: 节点是否存在
        """
        with self.driver.session() as session:
            result = session.run(
                "MATCH (t:Table:`" + self.namespace + "` {name: $name, namespace: $namespace}) RETURN count(t) AS count",
                name=node_name, namespace=self.namespace
            )
            return result.single()["count"] > 0
    
    def has_edge(self, source, target):
        """
        检查两节点间是否存在边
        
        Args:
            source: 源表名
            target: 目标表名
            
        Returns:
            bool: 边是否存在
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (s:Table:`""" + self.namespace + """` {name: $source, namespace: $namespace})-[r:FLOWS_TO]->
                      (t:Table:`""" + self.namespace + """` {name: $target, namespace: $namespace})
                RETURN count(r) AS count
                """,
                source=source, target=target, namespace=self.namespace
            )
            return result.single()["count"] > 0
    
    def get_edge_attributes(self, source, target):
        """
        获取边的属性
        
        Args:
            source: 源表名
            target: 目标表名
            
        Returns:
            Dict: 边的所有属性
        """
        with self.driver.session() as session:
            try:
                result = session.run(
                    """
                    MATCH (s:Table:`""" + self.namespace + """` {name: $source, namespace: $namespace})-[r:FLOWS_TO]->
                          (t:Table:`""" + self.namespace + """` {name: $target, namespace: $namespace})
                    RETURN properties(r) AS props
                    """,
                    source=source, target=target, namespace=self.namespace
                )
                record = result.single()
                if not record:
                    return {}
                
                props = record["props"]
                
                # 反序列化JSON字符串
                processed_props = {}
                for key, value in props.items():
                    if isinstance(value, str) and (value.startswith('[') or value.startswith('{')):
                        try:
                            processed_props[key] = json.loads(value)
                        except json.JSONDecodeError:
                            # 如果不是有效的JSON，保持不变
                            processed_props[key] = value
                    else:
                        processed_props[key] = value
                
                # 确保sql_statements是有效的格式
                if 'sql_statements' in processed_props:
                    if isinstance(processed_props['sql_statements'], str):
                        try:
                            # 尝试再次解析，可能是嵌套JSON字符串
                            processed_props['sql_statements'] = json.loads(processed_props['sql_statements'])
                        except json.JSONDecodeError:
                            # 如果不是有效的JSON，使用字符串作为单个SQL语句
                            processed_props['sql_statements'] = [processed_props['sql_statements']]
                
                return processed_props
            except Exception as e:
                logger.error(f"获取边属性时出错: {e}")
                return {}
    
    def update_edge_attributes(self, source, target, **attributes):
        """
        更新边的属性
        
        Args:
            source: 源表名
            target: 目标表名
            attributes: 要更新的属性
        """
        with self.driver.session() as session:
            # 处理复杂属性，将其序列化为JSON字符串
            processed_attributes = {}
            for key, value in attributes.items():
                if isinstance(value, (list, dict)):
                    processed_attributes[key] = json.dumps(value)
                else:
                    processed_attributes[key] = value
                
            session.run(
                """
                MATCH (s:Table:`""" + self.namespace + """` {name: $source, namespace: $namespace})-[r:FLOWS_TO]->
                      (t:Table:`""" + self.namespace + """` {name: $target, namespace: $namespace})
                SET r += $attributes
                """,
                source=source, target=target, namespace=self.namespace, attributes=processed_attributes
            )
    
    def get_node_attributes(self, node_name):
        """
        获取节点属性
        
        Args:
            node_name: 节点名
            
        Returns:
            Dict: 节点属性
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                RETURN properties(t) AS props
                """,
                name=node_name, namespace=self.namespace
            )
            record = result.single()
            return record["props"] if record else {}
    
    def update_node_attributes(self, node_name, **attributes):
        """
        更新节点属性
        
        Args:
            node_name: 节点名
            attributes: 要更新的属性
        """
        # 确保不覆盖命名空间属性
        attributes['namespace'] = self.namespace
        
        with self.driver.session() as session:
            session.run(
                """
                MATCH (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                SET t += $attributes
                """,
                name=node_name, namespace=self.namespace, attributes=attributes
            )
    
    def get_all_nodes(self):
        """
        获取所有节点
        
        Returns:
            List[str]: 所有节点名称
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Table:`""" + self.namespace + """`)
                WHERE t.namespace = $namespace
                RETURN t.name AS name
                """,
                namespace=self.namespace
            )
            nodes = [record["name"] for record in result if record["name"] is not None]
            logger.debug(f"获取到 {len(nodes)} 个节点，命名空间: {self.namespace}")
            return nodes
    
    def get_all_edges(self):
        """
        获取所有边
        
        Returns:
            List[Tuple[str, str]]: 所有边（源节点，目标节点）
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (s:Table:`""" + self.namespace + """` {namespace: $namespace})-[r:FLOWS_TO]->
                      (t:Table:`""" + self.namespace + """` {namespace: $namespace})
                RETURN s.name AS source, t.name AS target
                """,
                namespace=self.namespace
            )
            return [(record["source"], record["target"]) for record in result]
    
    def predecessors(self, node_name):
        """
        获取节点的所有前驱节点
        
        Args:
            node_name: 节点名
            
        Returns:
            List[str]: 前驱节点列表
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (s:Table:`""" + self.namespace + """` {namespace: $namespace})-[:FLOWS_TO]->
                      (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                RETURN s.name AS name
                """,
                name=node_name, namespace=self.namespace
            )
            return [record["name"] for record in result]
    
    def successors(self, node_name):
        """
        获取节点的所有后继节点
        
        Args:
            node_name: 节点名
            
        Returns:
            List[str]: 后继节点列表
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (s:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})-[:FLOWS_TO]->
                      (t:Table:`""" + self.namespace + """` {namespace: $namespace})
                RETURN t.name AS name
                """,
                name=node_name, namespace=self.namespace
            )
            return [record["name"] for record in result]
    
    def in_edges(self, node_name, data=False):
        """
        获取节点的所有入边
        
        Args:
            node_name: 节点名
            data: 是否包含边属性
            
        Returns:
            List: 入边列表，如果data=True则包含边属性
        """
        with self.driver.session() as session:
            if data:
                result = session.run(
                    """
                    MATCH (s:Table:`""" + self.namespace + """` {namespace: $namespace})-[r:FLOWS_TO]->
                          (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                    RETURN s.name AS source, t.name AS target, properties(r) AS data
                    """,
                    name=node_name, namespace=self.namespace
                )
                return [(record["source"], record["target"], record["data"]) for record in result]
            else:
                result = session.run(
                    """
                    MATCH (s:Table:`""" + self.namespace + """` {namespace: $namespace})-[:FLOWS_TO]->
                          (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})
                    RETURN s.name AS source, t.name AS target
                    """,
                    name=node_name, namespace=self.namespace
                )
                return [(record["source"], record["target"]) for record in result]
    
    def out_edges(self, node_name, data=False):
        """
        获取节点的所有出边
        
        Args:
            node_name: 节点名
            data: 是否包含边属性
            
        Returns:
            List: 出边列表，如果data=True则包含边属性
        """
        with self.driver.session() as session:
            if data:
                result = session.run(
                    """
                    MATCH (s:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})-[r:FLOWS_TO]->
                          (t:Table:`""" + self.namespace + """` {namespace: $namespace})
                    RETURN s.name AS source, t.name AS target, properties(r) AS data
                    """,
                    name=node_name, namespace=self.namespace
                )
                return [(record["source"], record["target"], record["data"]) for record in result]
            else:
                result = session.run(
                    """
                    MATCH (s:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})-[:FLOWS_TO]->
                          (t:Table:`""" + self.namespace + """` {namespace: $namespace})
                    RETURN s.name AS source, t.name AS target
                    """,
                    name=node_name, namespace=self.namespace
                )
                return [(record["source"], record["target"]) for record in result]
    
    def out_degree(self, node_name):
        """
        获取节点的出度
        
        Args:
            node_name: 节点名
            
        Returns:
            int: 出度
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Table:`""" + self.namespace + """` {name: $name, namespace: $namespace})-[:FLOWS_TO]->()
                RETURN count(*) AS count
                """,
                name=node_name, namespace=self.namespace
            )
            return result.single()["count"]
    
    def get_downstream_nodes(self):
        """
        获取所有下游节点（没有出边的节点）
        
        Returns:
            List[str]: 下游节点列表
        """
        with self.driver.session() as session:
            result = session.run(
                """
                MATCH (t:Table:`""" + self.namespace + """` {namespace: $namespace})
                WHERE NOT (t)-[:FLOWS_TO]->()
                RETURN t.name AS name
                """,
                namespace=self.namespace
            )
            return [record["name"] for record in result]
    
    def calculate_downstream_distance(self, force_recalculate: bool = False):
        """
        计算每个节点到最下游节点的距离
        
        该方法使用BFS算法计算每个节点到最下游节点的距离：
        1. 找到所有出度为0的节点（终点）
        2. 将这些节点的距离设为0
        3. 对这些节点的上游节点，将距离设为1
        4. 对距离为1的节点的上游节点，将距离设为2
        5. 依此类推，直到所有可达节点都有了距离
        
        距离作为节点属性(downstream_distance)保存到节点中
        
        Args:
            force_recalculate: 是否强制重新计算，即使节点已有distance属性。默认为False
        
        Returns:
            Dict[str, int]: 节点名称到距离的映射
        """
        # 如果不是强制重新计算，检查是否已经计算过
        if not force_recalculate:
            # 获取所有节点
            all_nodes = self.get_all_nodes()
            if not all_nodes:
                logger.warning("图中没有节点")
                return {}
                
            # 随机抽取一个节点检查是否有downstream_distance属性
            sample_node = all_nodes[0]
            node_attrs = self.get_node_attributes(sample_node)
            
            # 如果已经计算过，直接从节点属性中获取
            if 'downstream_distance' in node_attrs:
                logger.info("下游距离已计算，从节点属性中获取")
                distance_map = {}
                
                for node in all_nodes:
                    attrs = self.get_node_attributes(node)
                    if 'downstream_distance' in attrs:
                        distance_map[node] = attrs['downstream_distance']
                
                logger.info(f"从节点属性中获取了 {len(distance_map)} 个节点的下游距离")
                return distance_map
        
        # 找到所有出度为0的节点
        downstream_nodes = self.get_downstream_nodes()
        
        if not downstream_nodes:
            logger.warning("图中没有最下游节点（出度为0的节点）")
            return {}
        
        # 初始化距离映射
        distance_map = {}
        
        # 初始化BFS队列，所有出度为0的节点距离为0
        queue = deque()
        for node in downstream_nodes:
            distance_map[node] = 0
            # 设置节点属性
            self.update_node_attributes(node, downstream_distance=0)
            queue.append(node)
        
        # 使用BFS计算每个节点到最下游节点的距离
        while queue:
            current_node = queue.popleft()
            current_distance = distance_map[current_node]
            
            # 获取当前节点的所有上游节点
            predecessors = self.predecessors(current_node)
            
            for pred in predecessors:
                # 如果前驱节点还没有被访问（没有计算距离）
                if pred not in distance_map:
                    # 设置距离为当前节点距离+1
                    new_distance = current_distance + 1
                    distance_map[pred] = new_distance
                    # 设置节点属性
                    self.update_node_attributes(pred, downstream_distance=new_distance)
                    # 将前驱节点加入队列
                    queue.append(pred)
        
        logger.info(f"已重新计算 {len(distance_map)} 个节点的下游距离")
        
        # 检查是否有节点未被访问（不可达最下游节点）
        all_nodes = self.get_all_nodes()
        unreachable = [node for node in all_nodes if node not in distance_map]
        
        if unreachable:
            logger.warning(f"发现 {len(unreachable)} 个节点不可达最下游节点")
            # 为不可达节点设置一个特殊值
            for node in unreachable:
                self.update_node_attributes(node, downstream_distance=-1)
        
        return distance_map

# ====================== 血缘图构建函数 ======================

def get_lineage_graph_by_namespace(namespace: str, uri: str = "bolt://localhost:7687", 
                                  user: str = "neo4j", password: str = "12345678") -> Neo4jLineageGraph:
    """
    通过命名空间获取Neo4jLineageGraph对象
    
    Args:
        namespace: 命名空间，用于区分不同来源的数据
        uri: Neo4j服务器URI，默认为"bolt://localhost:7687"
        user: Neo4j用户名，默认为"neo4j"
        password: Neo4j密码，默认为"12345678"
        
    Returns:
        Neo4jLineageGraph: 指定命名空间的血缘图对象
    """
    return Neo4jLineageGraph(uri=uri, user=user, password=password, namespace=namespace)

def build_lineage_graph_with_sql_mapping(sql_statements: List[str], graph=None) -> Neo4jLineageGraph:
    """
    使用 extract_tables 函数构建血缘图
    
    Args:
        sql_statements: SQL语句列表
        graph: 现有的Neo4jLineageGraph实例，如果为None则创建新实例
        
    Returns:
        Neo4jLineageGraph: 构建好的血缘图，其中：
            - 节点表示数据表
            - 边表示表之间的血缘关系
            - 边属性包含关联的SQL语句信息
    """
    # 创建或使用现有图
    if graph is None:
        lineage_graph = Neo4jLineageGraph()
    else:
        lineage_graph = graph
    
    # 遍历每个SQL语句，提取输入表和输出表
    for i, stmt in enumerate(sql_statements):
        try:
            # 使用 extract_tables 函数提取输入表和输出表
            input_tables, output_tables = extract_tables(stmt)
            
            # 过滤无效的表名（空字符串或None）
            input_tables = [t for t in input_tables if t and t.strip()]
            output_tables = [t for t in output_tables if t and t.strip()]
            
            # 如果没有输出表，跳过此语句
            if not output_tables:
                logger.debug(f"SQL语句 {i} 没有输出表，跳过")
                continue
                
            # 为每对输入表和输出表添加边
            for source_table in input_tables:
                for target_table in output_tables:
                    # 添加节点
                    if not lineage_graph.has_node(source_table):
                        lineage_graph.add_node(source_table)
                    if not lineage_graph.has_node(target_table):
                        lineage_graph.add_node(target_table)
                    
                    # 添加或更新边
                    if lineage_graph.has_edge(source_table, target_table):
                        # 如果边已存在，检查是否已存在相同的SQL语句
                        edge_attrs = lineage_graph.get_edge_attributes(source_table, target_table)
                        
                        sql_statements_attr = edge_attrs.get('sql_statements', [])
                        
                        sql_already_exists = False
                        for existing_sql in sql_statements_attr:
                            if existing_sql['sql_text'] == stmt:
                                sql_already_exists = True
                                break
                        
                        # 只有当SQL语句不存在时才添加
                        if not sql_already_exists:
                            sql_statements_attr.append({
                                'sql_id': i,
                                'sql_text': stmt
                            })
                            
                            lineage_graph.update_edge_attributes(
                                source_table, 
                                target_table, 
                                sql_statements=sql_statements_attr
                            )
                    else:
                        # 如果边不存在，创建新边并添加SQL语句
                        lineage_graph.add_edge(
                            source_table, 
                            target_table, 
                            sql_statements=[{
                                'sql_id': i,
                                'sql_text': stmt
                            }]
                        )
        except Exception as e:
            logger.error(f"处理SQL语句 {i} 时出错: {e}")
            # 继续处理下一条SQL语句，不中断整个流程
    
    nodes_count = len(lineage_graph.get_all_nodes())
    edges_count = len(lineage_graph.get_all_edges())
    logger.info(f"血缘图构建完成，节点数: {nodes_count}, 边数: {edges_count}")
    return lineage_graph

def build_graph(sql_statements: List[str], namespace: str = None) -> Neo4jLineageGraph:
    """
    根据输入的SQL语句列表构建血缘关系图

    Args:
        sql_statements: 包含SQL语句的列表
        namespace: 命名空间，用于区分不同来源的数据
        
    Returns:
        Neo4jLineageGraph: 构建好的有向图
    """
    logger.debug(f"输入SQL语句数量: {len(sql_statements)}")
    
    try:
        # 创建Neo4jLineageGraph实例，使用指定的命名空间
        lineage_graph = Neo4jLineageGraph(namespace=namespace)
        
        # 预处理SQL语句，拆分包含UNION的INSERT语句
        processed_sqls = []
        # convert create table to insert
        for i, sql_item in enumerate(sql_statements):
            try:
                if isinstance(sql_item, str):
                    # 如果是单个SQL字符串
                    sql_parts = convert_create_table_to_insert(sql_item)
                    if sql_parts:
                        processed_sqls.append(sql_parts)
                elif isinstance(sql_item, list):
                    # 如果是SQL列表（OR关系）
                    processed_sql_list = []
                    for sub_sql in sql_item:
                        sql_parts = convert_create_table_to_insert(sub_sql)
                        if sql_parts:
                            processed_sql_list.append(sql_parts)
                    
                    if processed_sql_list:
                        processed_sqls.append(processed_sql_list)
                else:
                    logger.warning(f"跳过无效的SQL项类型: {type(sql_item)}")
            except Exception as e:
                logger.error(f"处理SQL项 {i} 时出错: {e}")
                # 继续处理下一项，不中断整个流程

        # 预处理SQL语句，拆分包含UNION的INSERT语句
        processed_sql_statements = []
        for sql in processed_sqls:
            try:
                # 尝试拆分每个SQL语句
                split_results = split_union(sql)
                # 如果拆分成功（返回多个语句），则添加所有拆分后的语句
                # 否则保留原始语句
                processed_sql_statements.extend(split_results)
            except Exception as e:
                logger.error(f"拆分SQL语句时出错: {e}")
                # 如果拆分失败，保留原始SQL
                processed_sql_statements.append(sql)
        
        logger.debug(f"UNION拆分后SQL语句数量: {len(processed_sql_statements)}")
        
        # 构建血缘图
        logger.info("开始构建血缘图并映射SQL语句")
        lineage_graph = build_lineage_graph_with_sql_mapping(processed_sql_statements, graph=lineage_graph)
        return lineage_graph
    except Exception as e:
        logger.error(f"构建血缘图时出错: {e}")
        # 返回空图，确保调用方能够继续执行
        return Neo4jLineageGraph(namespace=namespace)

# ====================== 剪枝优化函数 ======================
    
def calculate_lineage_with_pruning(
    lineage_graph: Neo4jLineageGraph, 
    analyzer: Optional[Any] = None, 
    dialect: str = None, 
    schema: Optional[Dict] = None,
    with_pruning: bool = True,
    force_update: bool = False
) -> Tuple[Neo4jLineageGraph, Set[str], List[str]]:
    """
    计算SQL语句之间的血缘关系，并应用剪枝优化
    
    Args:
        lineage_graph: 已经构建完成的Neo4j血缘图
        analyzer: 血缘分析器实例，如果为None则创建新实例
        dialect: SQL方言，如果为None则使用全局配置
        schema: 字段名称和对应的字段类型的字典
        with_pruning: 是否应用剪枝优化
        force_update: 是否强制重新计算剪枝，即使图中已存在剪枝信息。默认为False
        
    Returns:
        元组(血缘图, 剪枝节点集合, 处理后的SQL列表)
    """
    
    if not with_pruning:
        nodes_count = len(lineage_graph.get_all_nodes())
        edges_count = len(lineage_graph.get_all_edges())
        logger.debug(f"血缘图构建完成，节点数: {nodes_count}, 边数: {edges_count}")
        logger.info("剪枝优化关闭，返回原始血缘图")
        return lineage_graph, set(), []
    
    try:
        # 如果不强制更新，检查图中是否已经计算过pruning
        if not force_update:
            # 获取所有节点，检查是否有pruned属性
            all_nodes = lineage_graph.get_all_nodes()
            if all_nodes:
                # 随机抽取一个节点检查
                sample_node = all_nodes[0]
                attrs = lineage_graph.get_node_attributes(sample_node)
                if 'pruned' in attrs or 'visited' in attrs:
                    logger.info("检测到图中已经计算过剪枝信息，直接返回现有图")
                    
                    # 构建现有剪枝节点集合
                    pruned_nodes = set()
                    for node in all_nodes:
                        node_attrs = lineage_graph.get_node_attributes(node)
                        if node_attrs.get('pruned', False):
                            pruned_nodes.add(node)
                    
                    # 获取下游节点
                    downstream_nodes = lineage_graph.get_downstream_nodes()
                    logger.info(f"从现有图中获取到 {len(pruned_nodes)} 个剪枝节点和 {len(downstream_nodes)} 个下游节点")
                    
                    return lineage_graph, pruned_nodes, downstream_nodes
        
        # 没有剪枝信息或需要强制更新，进行剪枝计算
        logger.info("开始计算剪枝信息" + ("（强制更新）" if force_update else ""))
        
        # 找到最下游节点（没有出边的节点）
        downstream_nodes = lineage_graph.get_downstream_nodes()
        
        if not downstream_nodes:
            logger.warning("未找到明确的下游节点（没有出边的节点），无法进行剪枝分析")
            return lineage_graph, set(), []
        
        logger.info(f"识别到 {len(downstream_nodes)} 个下游节点: {downstream_nodes}")
        
        # 记录可剪枝的节点
        pruned_nodes = set()
        # 记录所有被访问过的节点
        all_visited_nodes = set()
        
        # 为每个下游节点进行广度优先遍历
        for target_node in downstream_nodes:
            logger.info(f"开始从目标节点 {target_node} 进行广度优先遍历...")
            
            # 初始化BFS
            visited = set([target_node])
            queue = deque([(target_node, [])])  # (节点, 收集的SQL路径列表)
            
            while queue:
                current_node, path_sql_groups = queue.popleft()
                # 将当前节点添加到全局访问集合中
                all_visited_nodes.add(current_node)
                
                # 获取当前节点的所有上游节点
                predecessors = lineage_graph.predecessors(current_node)
                logger.debug(f"节点 {current_node} 的上游节点: {predecessors}")
                
                for predecessor in predecessors:
                    # 如果节点已访问或已被标记为可剪枝，则跳过
                    if predecessor in visited or predecessor in pruned_nodes:
                        logger.debug(f"节点 {predecessor} 已被访问或已标记为可剪枝，跳过")
                        continue
                    
                    # 收集当前边上的SQL语句（作为一个整体）
                    edge_sqls = []
                    edge_attrs = lineage_graph.get_edge_attributes(predecessor, current_node)
                    
                    if 'sql_statements' in edge_attrs:
                        for sql_info in edge_attrs['sql_statements']:
                            # 检查sql_info是否是字典，如果是字符串则直接使用
                            if isinstance(sql_info, dict) and 'sql_text' in sql_info:
                                edge_sqls.append(sql_info['sql_text'])
                            elif isinstance(sql_info, str):
                                # 直接使用字符串作为SQL
                                edge_sqls.append(sql_info)
                            else:
                                logger.warning(f"忽略无效的SQL信息: {sql_info}")
                    
                    # 构建新的SQL路径，将当前边上的SQL作为一个整体添加到路径中
                    new_path_sql_groups = path_sql_groups.copy()
                    if edge_sqls:
                        new_path_sql_groups.insert(0, edge_sqls)  # 将新的边SQL组插入到路径最前面
                    
                    # 检查predecessor是否有入度边
                    predecessor_in_edges = lineage_graph.in_edges(predecessor, data=True)
                    
                    if not predecessor_in_edges:
                        # 如果没有入度边，直接处理当前路径
                        logger.debug(f"节点 {predecessor} 没有入度边，开始评估是否可剪枝")
                        if analyzer and new_path_sql_groups:
                            # 处理SQL路径组，为分析准备数据
                            analysis_sqls = prepare_analysis_sqls(new_path_sql_groups)
                            
                            # 如果有有效的SQL，调用分析器进行分析
                            if analysis_sqls:
                                logger.debug(f"分析节点 {predecessor} 的血缘关系，SQL数量: {len(analysis_sqls)}")
                                try:
                                    lineage_result = analyzer.analyze_lineage(
                                        analysis_sqls, 
                                        dialect=dialect, 
                                        schema=schema
                                    )

                                    if lineage_result is None: # 只有一个sql被处理
                                        can_prune = False
                                    else:
                                        # 根据返回的血缘关系判断是否可剪枝
                                        can_prune = not lineage_result.get("result", True)
                                    
                                    if can_prune:
                                        logger.info(f"节点 {predecessor} 可以剪枝")
                                        pruned_nodes.add(predecessor)
                                        # 将剪枝节点也添加到访问集合中
                                        all_visited_nodes.add(predecessor)
                                        continue
                                except Exception as e:
                                    logger.error(f"分析节点 {predecessor} 的血缘关系时出错: {e}")
                                    # 出错时默认不可剪枝
                                    can_prune = False
                        
                        # 节点不可剪枝，继续BFS
                        logger.debug(f"节点 {predecessor} 不可剪枝，继续BFS")
                        visited.add(predecessor)
                        queue.append((predecessor, new_path_sql_groups))
                    else:
                        # 如果有入度边，需要对每个入度边分别进行剪枝计算
                        logger.debug(f"节点 {predecessor} 有 {len(predecessor_in_edges)} 个入度边，需分别评估")
                        all_in_edges_prunable = True
                        
                        for in_edge in predecessor_in_edges:
                            in_source, in_target, in_data = in_edge
                            
                            # 收集入度边上的SQL语句（作为一个整体）
                            in_edge_sqls = []
                            if 'sql_statements' in in_data:
                                for sql_info in in_data['sql_statements']:
                                    # 检查sql_info是否是字典，如果是字符串则尝试解析
                                    if isinstance(sql_info, dict) and 'sql_text' in sql_info:
                                        in_edge_sqls.append(sql_info['sql_text'])
                                    elif isinstance(sql_info, str):
                                        # 直接使用字符串作为SQL
                                        in_edge_sqls.append(sql_info)
                                    else:
                                        logger.warning(f"忽略无效的SQL信息: {sql_info}")
                            
                            # 构建包含入度边SQL的完整路径，同样保持SQL组的完整性
                            complete_path_sql_groups = new_path_sql_groups.copy()
                            if in_edge_sqls:
                                complete_path_sql_groups.insert(0, in_edge_sqls)
                            
                            if analyzer and complete_path_sql_groups:
                                # 处理SQL路径组，为分析准备数据
                                analysis_sqls = prepare_analysis_sqls(complete_path_sql_groups)
                                
                                # 如果有有效的SQL，调用分析器进行分析
                                if analysis_sqls:
                                    logger.debug(f"分析入度边 {in_source} -> {in_target} 的血缘关系")
                                    try:
                                        lineage_result = analyzer.analyze_lineage(
                                            analysis_sqls, 
                                            dialect=dialect, 
                                            schema=schema
                                        )
                                        
                                        # 根据返回的血缘关系判断是否可剪枝
                                        can_prune_this_edge = not lineage_result.get("result", True)
                                        
                                        if not can_prune_this_edge:
                                            logger.debug(f"入度边 {in_source} -> {in_target} 不可剪枝")
                                            all_in_edges_prunable = False
                                            break
                                    except Exception as e:
                                        logger.error(f"分析入度边 {in_source} -> {in_target} 的血缘关系时出错: {e}")
                                        # 出错时默认不可剪枝
                                        all_in_edges_prunable = False
                                        break
                        
                        if all_in_edges_prunable and predecessor_in_edges:
                            logger.info(f"节点 {predecessor} 的所有入度边都可以剪枝，该节点可以剪枝")
                            pruned_nodes.add(predecessor)
                            # 将剪枝节点也添加到访问集合中
                            all_visited_nodes.add(predecessor)
                            continue
                        
                        # 节点不可剪枝，继续BFS
                        logger.debug(f"节点 {predecessor} 不可剪枝，继续BFS")
                        visited.add(predecessor)
                        queue.append((predecessor, new_path_sql_groups))
        
        # 在图中标记可剪枝节点和访问过的节点
        for node in lineage_graph.get_all_nodes():
            if node in pruned_nodes:
                lineage_graph.update_node_attributes(node, pruned=True)
            if node in all_visited_nodes:
                lineage_graph.update_node_attributes(node, visited=True)
        
        logger.info(f"血缘分析完成，共有 {len(pruned_nodes)} 个节点可剪枝，{len(all_visited_nodes)} 个节点被访问")
        
        # 进一步优化剪枝：如果一个节点的所有上游节点都被剪枝，则该节点也可以被剪枝
        logger.info("开始进行剪枝优化：检查节点的上游节点是否全部可剪枝")
        
        # 记录初始剪枝节点数量
        initial_pruned_count = len(pruned_nodes)
        changes_made = True
        iteration = 0
        
        while changes_made:
            iteration += 1
            changes_made = False
            nodes_to_prune = set()
            
            # 检查每个被访问但未被剪枝的节点
            for node in all_visited_nodes:
                if node not in pruned_nodes:
                    # 获取该节点的所有上游节点
                    predecessors = lineage_graph.predecessors(node)
                    
                    # 如果节点没有上游节点，跳过
                    if not predecessors:
                        continue
                    
                    # 检查是否所有上游节点都已被剪枝
                    all_predecessors_pruned = all(pred in pruned_nodes for pred in predecessors)
                    
                    if all_predecessors_pruned:
                        logger.debug(f"节点 {node} 的所有上游节点都已被剪枝，该节点也可以被剪枝")
                        nodes_to_prune.add(node)
            
            # 更新剪枝节点集合
            if nodes_to_prune:
                pruned_nodes.update(nodes_to_prune)
                changes_made = True
                logger.info(f"第 {iteration} 轮优化：新增 {len(nodes_to_prune)} 个可剪枝节点")
                
                # 在图中标记新的可剪枝节点
                for node in nodes_to_prune:
                    lineage_graph.update_node_attributes(node, pruned=True)
        
        # 输出优化后的剪枝结果
        additional_pruned = len(pruned_nodes) - initial_pruned_count
        if additional_pruned > 0:
            logger.info(f"剪枝优化完成，额外识别出 {additional_pruned} 个可剪枝节点，总计 {len(pruned_nodes)} 个可剪枝节点")
        else:
            logger.info("剪枝优化完成，没有发现额外的可剪枝节点")
        
        return lineage_graph, pruned_nodes, downstream_nodes
    except Exception as e:
        logger.error(f"计算血缘关系并应用剪枝优化时出错: {e}")
        # 记录错误信息
        error_traceback = traceback.format_exc()
        logger.error(f"详细错误信息: {error_traceback}")
        # 出错时返回原始图和空集合
        return lineage_graph, set(), []

# ====================== 图操作函数 ======================

def calculate_node_path_lengths(graph: Neo4jLineageGraph) -> Dict[str, int]:
    """
    计算图中每个节点到最下游节点的路径长度

    Args:
        graph: Neo4j血缘图对象

    Returns:
        Dict[str, int]: 节点名称到路径长度的映射
    """
    # 找到最下游节点（没有出边的节点）
    downstream_nodes = graph.get_downstream_nodes()

    if not downstream_nodes:
        logger.warning("图中没有下游节点")
        return {node: 0 for node in graph.get_all_nodes()}

    # 创建查询
    with graph.driver.session() as session:
        # 初始化结果字典
        path_lengths = {node: 0 for node in graph.get_all_nodes()}
        
        for target in downstream_nodes:
            # 使用Cypher查询计算路径长度
            result = session.run(
                """
                MATCH path = (source:Table:`""" + graph.namespace + """` )-[:FLOWS_TO*]->(target:Table:`""" + graph.namespace + """` {name: $target})
                WITH source, length(path) AS path_length
                RETURN source.name AS node, max(path_length) AS length
                """,
                target=target
            )
            
            # 更新路径长度，取最大值
            for record in result:
                node = record["node"]
                length = record["length"]
                path_lengths[node] = max(path_lengths[node], length)

    return path_lengths

def get_upstream_subgraph(graph: Neo4jLineageGraph, selected_node: str, force_update: bool = False) -> Neo4jLineageGraph:
    """
    计算与选定节点相连通的所有上游节点，构建子图
    
    如果数据库中已经存在该子图的命名空间，则直接返回已有子图，除非指定强制更新

    Args:
        graph: Neo4j血缘图对象
        selected_node: 选定的节点
        force_update: 是否强制更新子图，即使已存在。默认为False

    Returns:
        Neo4jLineageGraph: 包含选定节点及其所有上游节点的子图
    """
    # 确保选定的节点存在且有效
    if not selected_node or selected_node.strip() == "":
        logger.error("选定的节点名无效")
        return Neo4jLineageGraph(namespace=graph.namespace+"_invalid")
    
    if not graph.has_node(selected_node):
        logger.error(f"选定的节点 '{selected_node}' 在原图中不存在")
        return Neo4jLineageGraph(namespace=graph.namespace+"_notfound")
    
    # 创建子图命名空间
    subgraph_namespace = graph.namespace+f"_{selected_node}"
    
    # 尝试创建已有命名空间的图实例
    existing_subgraph = Neo4jLineageGraph(namespace=subgraph_namespace)
            
    if not force_update:
        try:
            # 检查该命名空间下是否有节点
            with existing_subgraph.driver.session() as session:
                result = session.run(
                    """
                    MATCH (t:Table:`""" + subgraph_namespace + """` {namespace: $namespace})
                    RETURN count(t) AS count
                    """,
                    namespace=subgraph_namespace
                )
                count = result.single()["count"]
                
                if count > 0:
                    logger.info(f"发现已存在的子图命名空间 '{subgraph_namespace}'，直接返回")
                    return existing_subgraph
        except Exception as e:
            logger.debug(f"检查已有子图时出错: {e}")
            # 如果出错，继续创建新的子图
    
    # 如果需要强制更新或子图不存在，则创建新的子图
    if force_update and existing_subgraph:
        logger.info(f"强制更新子图命名空间 '{subgraph_namespace}'")
        existing_subgraph.clear_graph()
        subgraph = existing_subgraph
    else:
        subgraph = Neo4jLineageGraph(namespace=subgraph_namespace)
    
    # 使用Cypher查询获取上游子图
    with graph.driver.session() as session:
        # 首先确认选定的节点存在且有正确的命名空间属性
        node_exists = session.run(
            """
            MATCH (t:Table:`""" + graph.namespace + """` {name: $name, namespace: $namespace})
            RETURN count(t) AS count
            """,
            name=selected_node, namespace=graph.namespace
        ).single()["count"] > 0
        
        if not node_exists:
            logger.error(f"选定的节点 '{selected_node}' 在原图的命名空间 '{graph.namespace}' 中不存在")
            return subgraph
        
        # 获取所有与选定节点相连的上游节点
        result = session.run(
            """
            MATCH path = (source:Table:`""" + graph.namespace + """` {namespace: $orig_namespace})-[:FLOWS_TO*]->
                          (target:Table:`""" + graph.namespace + """` {name: $selected_node, namespace: $orig_namespace})
            UNWIND nodes(path) AS node
            RETURN DISTINCT node.name AS name
            """,
            selected_node=selected_node, orig_namespace=graph.namespace
        )
        
        # 添加所有节点
        for record in result:
            node_name = record["name"]
            if not node_name or node_name.strip() == "":
                continue  # 跳过空名称节点
                
            node_attrs = graph.get_node_attributes(node_name)
            # 修改命名空间属性以匹配新的子图
            node_attrs['namespace'] = subgraph_namespace
            subgraph.add_node(node_name, **node_attrs)
        
        # 获取子图中节点之间的所有边
        node_names = subgraph.get_all_nodes()
        if node_names:  # 确保有节点可查询
            result = session.run(
                """
                MATCH (source:Table:`""" + graph.namespace + """` {namespace: $orig_namespace})-[r:FLOWS_TO]->
                      (target:Table:`""" + graph.namespace + """` {namespace: $orig_namespace})
                WHERE source.name IN $node_names AND target.name IN $node_names
                RETURN source.name AS source, target.name AS target, properties(r) AS props
                """,
                node_names=node_names, orig_namespace=graph.namespace
            )
            
            # 添加所有边
            for record in result:
                source = record["source"]
                target = record["target"]
                if not source or not target or source.strip() == "" or target.strip() == "":
                    continue  # 跳过空名称节点的边
                    
                props = record["props"]
                subgraph.add_edge(source, target, **props)
    
    nodes_count = len(subgraph.get_all_nodes())
    edges_count = len(subgraph.get_all_edges())
    logger.info(f"子图 '{subgraph_namespace}' 构建完成，节点数: {nodes_count}, 边数: {edges_count}")

    return subgraph

def get_downstream_nodes(graph: Neo4jLineageGraph) -> List[str]:
    """
    获取图中所有下游节点（没有出边的节点）
    
    Args:
        graph: Neo4j血缘图对象
        
    Returns:
        List[str]: 下游节点列表
    """
    return graph.get_downstream_nodes()

# ====================== 辅助函数 ======================

def prepare_analysis_sqls(sql_groups: List[List[str]]) -> List[Union[str, List[str]]]:
    """
    从SQL组列表中准备用于分析的SQL列表。
    
    该函数接收一个SQL语句组列表，每组代表一条边上的全部SQL语句。
    函数将这些SQL语句组处理成分析器需要的格式，按照从目标到源的顺序排列。
    
    处理逻辑：
    1. 对于只有一条SQL语句的组，直接使用该语句
    2. 对于包含多条SQL语句的组，将整个组作为一个列表保留
    3. 最终将所有语句按从目标到源的顺序排列（翻转原列表）
    
    Args:
        sql_groups: SQL组列表，每个组是一个SQL语句列表，代表一条边上的全部SQL
        
    Returns:
        用于分析的SQL列表，顺序为从目标到源。列表中的元素可能是字符串（单条SQL）
        或列表（包含多条SQL的组）
    """
    try:
        # 从每组SQL中选取代表性SQL，如果组长度为1则直接使用元素，否则保持列表形式
        flat_sqls = []
        for sql_group in sql_groups:
            if sql_group:  # 确保SQL组不为空
                if len(sql_group) == 1:  # 如果组只有一个元素，直接添加该元素
                    flat_sqls.append(sql_group[0])
                else:  # 否则保持列表形式
                    flat_sqls.append(sql_group)
        
        # 返回格式化为分析器需要的顺序：[目标SQL, 上游SQL...]
        output = flat_sqls[::-1]
        return output
    except Exception as e:
        logger.error(f"准备分析SQL时出错: {e}")
        # 出错时返回空列表
        return []

# 扩展命令行参数
def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="SQL行级血缘分析工具 - 命令行版本")
    
    # ... 现有参数 ...
    
    parser.add_argument(
        "--namespace",
        help="指定数据命名空间，默认使用输入文件/目录名"
    )
    
    parser.add_argument(
        "--clear-namespace",
        action="store_true",
        help="清除指定命名空间下的现有数据"
    )
    
    parser.add_argument(
        "--list-namespaces",
        action="store_true",
        help="列出所有可用的命名空间并退出"
    )
    
    return parser.parse_args()

# 添加命名空间管理功能
def clear_namespace(uri, user, password, namespace):
    """清除特定命名空间下的所有数据"""
    with GraphDatabase.driver(uri, auth=(user, password)) as driver:
        with driver.session() as session:
            session.run(
                f"MATCH (n:`{namespace}`) DETACH DELETE n"
            )
            print(f"已清除命名空间 '{namespace}' 下的所有数据")

def list_namespaces(uri, user, password):
    """列出所有可用的命名空间"""
    with GraphDatabase.driver(uri, auth=(user, password)) as driver:
        with driver.session() as session:
            result = session.run(
                "CALL db.labels() YIELD label "
                "WHERE label <> 'Table' "
                "RETURN label AS namespace"
            )
            namespaces = [record["namespace"] for record in result]
            return namespaces

# 修改 analyze_sql 函数，添加命名空间支持
def analyze_sql(sql_content: str, do_pruning: bool = False, namespace: str = None, force_update: bool = False) -> Tuple[Neo4jLineageGraph, Dict[str, int], List[str]]:
    """
    分析SQL内容并构建血缘图
    
    Args:
        sql_content: SQL语句
        do_pruning: 是否应用剪枝优化
        namespace: 命名空间，用于区分不同来源的数据
        force_update: 是否强制重新计算剪枝，即使图中已存在剪枝信息
        
    Returns:
        Tuple: (血缘图, 节点路径长度, 下游节点)
    """
    try:
        # 解析SQL语句
        sql_statements = build_sql_list_from_str(sql_content)
        
        if not sql_statements:
            logger.warning("未能解析出有效的SQL语句")
            return None, {}, []
        
        # 构建血缘图，使用命名空间
        lineage_graph = build_graph(sql_statements=sql_statements, namespace=namespace)
        
        # 应用剪枝优化
        if do_pruning:
            logger.info("启用剪枝优化...")
            analyzer = LineageAnalyzer()
            lineage_graph, pruned_nodes, downstream_nodes = calculate_lineage_with_pruning(
                lineage_graph=lineage_graph,
                analyzer=analyzer,
                with_pruning=True,
                force_update=force_update
            )
            logger.info(f"剪枝完成，共剪枝 {len(pruned_nodes)} 个节点")
        else:
            downstream_nodes = lineage_graph.get_downstream_nodes()
        
        # 计算节点到最下游节点的路径长度
        node_path_lengths = calculate_node_path_lengths(lineage_graph)
        
        return lineage_graph, node_path_lengths, downstream_nodes
    except Exception as e:
        logger.exception(f"分析SQL时出错: {e}")
        print(f"错误: 分析SQL时出错: {e}")
        return None, {}, []

def remove_isolated_nodes(graph: Neo4jLineageGraph):
    """
    移除图中的孤立节点（没有任何边连接的节点）
    
    Args:
        graph: Neo4j血缘图对象
    """
    try:
        with graph.driver.session() as session:
            # 查询孤立节点（没有入边也没有出边的节点）
            result = session.run(
                """
                MATCH (n:Table:`""" + graph.namespace + """`)
                WHERE n.namespace = $namespace AND NOT (n)--()  // 节点没有任何连接的边
                RETURN n.name AS name
                """,
                namespace=graph.namespace
            )
            
            isolated_nodes = [record["name"] for record in result if record["name"] is not None]
            
            if isolated_nodes:
                logger.info(f"发现 {len(isolated_nodes)} 个孤立节点，将被移除: {isolated_nodes[:10]}{'...' if len(isolated_nodes) > 10 else ''}")
                
                # 删除所有孤立节点
                delete_result = session.run(
                    """
                    MATCH (n:Table:`""" + graph.namespace + """`)
                    WHERE n.namespace = $namespace AND NOT (n)--()
                    WITH n, n.name AS name
                    DELETE n
                    RETURN count(*) as deleted_count
                    """,
                    namespace=graph.namespace
                )
                
                deleted_count = delete_result.single()["deleted_count"]
                logger.info(f"成功删除 {deleted_count} 个孤立节点")
            else:
                logger.debug("未发现孤立节点")
    except Exception as e:
        logger.error(f"移除孤立节点时出错: {e}")
    
    return
