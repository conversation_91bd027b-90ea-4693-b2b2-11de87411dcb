"""
SQL行级血缘分析工具 - 血缘图构建模块

该模块提供了构建SQL语句血缘关系图的核心功能，包括：
- 构建血缘关系图
- 应用剪枝优化
- 处理SQL语句组
- 图操作功能（计算路径长度、获取上游子图等）

作者: 数据血缘分析团队
版本: 1.0.0
"""

from typing import List, Set, Dict, Tuple, Union, Optional, Any
import sys
from pathlib import Path
from collections import deque

# 第三方库导入
import networkx as nx
import sqlglot
from sqlglot import exp

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

# 本地模块导入
from lineage_core.utils import extract_tables
from lineage_core.split_sql import split_union
from lineage_core.logger import logger
from lineage_core.lineage_analyzer import LineageAnalyzer
# from lineage_core.lineage_dynamic_analyzer import LineageDynamicAnalyzer as LineageAnalyzer
from lineage_core.config import get_sql_dialect

# ====================== 血缘图构建函数 ======================

def build_lineage_graph_with_sql_mapping(sql_statements: List[str]) -> nx.DiGraph:
    """
    使用 extract_tables 函数构建血缘图
    
    Args:
        sql_statements: SQL语句列表
        
    Returns:
        nx.DiGraph: 构建好的血缘图，其中：
            - 节点表示数据表
            - 边表示表之间的血缘关系
            - 边属性包含关联的SQL语句信息
    """
    # 创建有向图
    lineage_graph = nx.DiGraph()
    
    # 遍历每个SQL语句，提取输入表和输出表
    for i, stmt in enumerate(sql_statements):
        try:
            # 使用 extract_tables 函数提取输入表和输出表
            input_tables, output_tables = extract_tables(stmt)
            
            # 如果没有输出表，跳过此语句
            if not output_tables:
                logger.debug(f"SQL语句 {i} 没有输出表，跳过")
                continue
                
            # 为每对输入表和输出表添加边
            for source_table in input_tables:
                for target_table in output_tables:
                    # 添加节点
                    if source_table not in lineage_graph:
                        lineage_graph.add_node(source_table)
                    if target_table not in lineage_graph:
                        lineage_graph.add_node(target_table)
                    
                    # 添加或更新边
                    if lineage_graph.has_edge(source_table, target_table):
                        # 如果边已存在，检查是否已存在相同的SQL语句
                        if 'sql_statements' not in lineage_graph[source_table][target_table]:
                            lineage_graph[source_table][target_table]['sql_statements'] = []
                        
                        sql_already_exists = False
                        for existing_sql in lineage_graph[source_table][target_table]['sql_statements']:
                            if existing_sql['sql_text'] == stmt:
                                sql_already_exists = True
                                break
                        
                        # 只有当SQL语句不存在时才添加
                        if not sql_already_exists:
                            lineage_graph[source_table][target_table]['sql_statements'].append({
                                'sql_id': i,
                                'sql_text': stmt
                            })
                    else:
                        # 如果边不存在，创建新边并添加SQL语句
                        lineage_graph.add_edge(source_table, target_table, sql_statements=[{
                            'sql_id': i,
                            'sql_text': stmt
                        }])
        except Exception as e:
            logger.error(f"处理SQL语句 {i} 时出错: {e}")
            # 继续处理下一条SQL语句，不中断整个流程
    
    logger.info(f"血缘图构建完成，节点数: {len(lineage_graph.nodes())}, 边数: {len(lineage_graph.edges())}")
    return lineage_graph

def build_graph(sql_statements: List[str]) -> nx.DiGraph:
    """
    根据输入的SQL语句列表构建血缘关系图，只处理INSERT语句

    Args:
        sql_statements: 包含SQL语句的列表，每个元素可以是一个SQL字符串或SQL列表（OR关系）

    Returns:
        nx.DiGraph: 构建好的有向图，其中：
            - 节点表示数据表
            - 边表示表之间的血缘关系
            - 边属性包含关联的SQL语句信息

    处理流程：
        1. 检查SQL语句类型，只保留INSERT语句
        2. 拆分包含UNION的复杂SQL语句
        3. 构建血缘图并映射SQL语句到对应的边上
    """
    logger.debug(f"输入SQL语句数量: {len(sql_statements)}")
    
    try:
        # 预处理SQL语句，只保留INSERT语句
        processed_sqls = []
        for i, sql_item in enumerate(sql_statements):
            try:
                if isinstance(sql_item, str):
                    # 如果是单个SQL字符串
                    parsed = sqlglot.parse_one(sql_item, dialect=get_sql_dialect())
                    if isinstance(parsed, exp.Insert):
                        processed_sqls.append(sql_item)
                        logger.debug(f"处理第{i+1}条SQL语句：确认是INSERT语句")
                    else:
                        logger.debug(f"跳过第{i+1}条SQL语句：非INSERT语句")
                elif isinstance(sql_item, list):
                    # 如果是SQL列表（OR关系）
                    processed_sql_list = []
                    for j, sub_sql in enumerate(sql_item):
                        parsed = sqlglot.parse_one(sub_sql, dialect=get_sql_dialect())
                        if isinstance(parsed, exp.Insert):
                            processed_sql_list.append(sub_sql)
                            logger.debug(f"处理第{i+1}条SQL语句的第{j+1}个子语句：确认是INSERT语句")
                        else:
                            logger.debug(f"跳过第{i+1}条SQL语句的第{j+1}个子语句：非INSERT语句")
                    
                    if processed_sql_list:
                        processed_sqls.append(processed_sql_list)
                else:
                    logger.warning(f"跳过无效的SQL项类型: {type(sql_item)}")
            except Exception as e:
                logger.error(f"处理SQL项 {i} 时出错: {e}")
                # 继续处理下一项，不中断整个流程

        if not processed_sqls:
            logger.info("没有找到有效的INSERT语句，返回空图")
            return nx.DiGraph()

        # 预处理SQL语句，拆分包含UNION的INSERT语句
        processed_sql_statements = []
        for sql in processed_sqls:
            try:
                # 尝试拆分每个SQL语句
                split_results = split_union(sql)
                # 如果拆分成功（返回多个语句），则添加所有拆分后的语句
                # 否则保留原始语句
                processed_sql_statements.extend(split_results)
            except Exception as e:
                logger.error(f"拆分SQL语句时出错: {e}")
                # 如果拆分失败，保留原始SQL
                processed_sql_statements.append(sql)
        
        logger.debug(f"UNION拆分后SQL语句数量: {len(processed_sql_statements)}")
        
        # 构建血缘图
        logger.info("开始构建血缘图并映射SQL语句")
        lineage_graph = build_lineage_graph_with_sql_mapping(processed_sql_statements)
        return lineage_graph
    except Exception as e:
        logger.error(f"构建血缘图时出错: {e}")
        # 返回空图，确保调用方能够继续执行
        return nx.DiGraph()

# ====================== 剪枝优化函数 ======================
    
def calculate_lineage_with_pruning(
    lineage_graph: nx.DiGraph, 
    analyzer: LineageAnalyzer = None, 
    dialect: str = None, 
    schema: Optional[Dict] = None,
    with_pruning: bool = True,
    downstream_nodes: Optional[List[str]] = None
) -> Tuple[nx.DiGraph, Set[str], List[str]]:
    """
    计算SQL语句之间的血缘关系，并应用剪枝优化
    
    Args:
        lineage_graph: 已经构建完成的 networkx graph
        analyzer: 血缘分析器实例，如果为None则创建新实例
        dialect: SQL方言，如果为None则使用全局配置
        schema: 字段名称和对应的字段类型的字典
        with_pruning: 是否应用剪枝优化
        
    Returns:
        元组(血缘图, 剪枝节点集合, 处理后的SQL列表)
    """
    
    if not with_pruning:
        logger.debug(f"血缘图构建完成，节点数: {len(lineage_graph.nodes())}, 边数: {len(lineage_graph.edges())}")
        logger.info("剪枝优化关闭，返回原始血缘图")
        return lineage_graph, set(), []
    
    try:
        if downstream_nodes is None:
            # 找到最下游节点（没有出边的节点）
            downstream_nodes = [node for node in lineage_graph.nodes() if lineage_graph.out_degree(node) == 0]
        
        if not downstream_nodes:
            logger.warning("未找到明确的下游节点（没有出边的节点），无法进行剪枝分析")
            return lineage_graph, set(), []
        
        logger.info(f"识别到 {len(downstream_nodes)} 个下游节点: {downstream_nodes}")
        
        # 记录可剪枝的节点
        pruned_nodes = set()
        # 记录所有被访问过的节点
        all_visited_nodes = set()
        
        # 为每个下游节点进行广度优先遍历
        for target_node in downstream_nodes:
            logger.info(f"开始从目标节点 {target_node} 进行广度优先遍历...")
            
            # 初始化BFS
            visited = set([target_node])
            queue = deque([(target_node, [])])  # (节点, 收集的SQL路径列表)
            
            while queue:
                current_node, path_sql_groups = queue.popleft()
                # 将当前节点添加到全局访问集合中
                all_visited_nodes.add(current_node)
                
                # 获取当前节点的所有上游节点
                predecessors = list(lineage_graph.predecessors(current_node))
                logger.debug(f"节点 {current_node} 的上游节点: {predecessors}")
                
                for predecessor in predecessors:
                    # 如果节点已访问或已被标记为可剪枝，则跳过
                    if predecessor in visited or predecessor in pruned_nodes:
                        logger.debug(f"节点 {predecessor} 已被访问或已标记为可剪枝，跳过")
                        continue
                    
                    # 收集当前边上的SQL语句（作为一个整体）
                    edge_sqls = []
                    if 'sql_statements' in lineage_graph[predecessor][current_node]:
                        for sql_info in lineage_graph[predecessor][current_node]['sql_statements']:
                            edge_sqls.append(sql_info['sql_text'])
                    
                    # 构建新的SQL路径，将当前边上的SQL作为一个整体添加到路径中
                    new_path_sql_groups = path_sql_groups.copy()
                    if edge_sqls:
                        new_path_sql_groups.insert(0, edge_sqls)  # 将新的边SQL组插入到路径最前面
                    
                    # 检查predecessor是否有入度边
                    predecessor_in_edges = list(lineage_graph.in_edges(predecessor, data=True))
                    
                    if not predecessor_in_edges:
                        # 如果没有入度边，直接处理当前路径
                        logger.debug(f"节点 {predecessor} 没有入度边，开始评估是否可剪枝")
                        if analyzer and new_path_sql_groups:
                            # 处理SQL路径组，为分析准备数据
                            analysis_sqls = prepare_analysis_sqls(new_path_sql_groups)
                            
                            # 如果有有效的SQL，调用分析器进行分析
                            if analysis_sqls:
                                logger.debug(f"分析节点 {predecessor} 的血缘关系，SQL数量: {len(analysis_sqls)}")
                                try:
                                    if current_node.startswith("tmp_") and predecessor.startswith("tmp_"):
                                        # 不计算临时表的血缘连通性
                                        lineage_result = {"result": True}
                                    else:
                                        lineage_result = analyzer.analyze_lineage(
                                            analysis_sqls, 
                                            dialect=dialect, 
                                            schema=schema
                                        )
                                    if lineage_result is None: # 只有一个sql被处理
                                        can_prune = False
                                    else:
                                        # 根据返回的血缘关系判断是否可剪枝
                                        can_prune = not lineage_result.get("result", True)
                                    
                                    if can_prune:
                                        logger.info(f"节点 {predecessor} 可以剪枝")
                                        pruned_nodes.add(predecessor)
                                        # 将剪枝节点也添加到访问集合中
                                        all_visited_nodes.add(predecessor)
                                        continue
                                except Exception as e:
                                    logger.error(f"分析节点 {predecessor} 的血缘关系时出错: {e}")
                                    # 出错时默认不可剪枝
                                    can_prune = False
                        
                        # 节点不可剪枝，继续BFS
                        logger.debug(f"节点 {predecessor} 不可剪枝，继续BFS")
                        visited.add(predecessor)
                        queue.append((predecessor, new_path_sql_groups))
                    else:
                        # 如果有入度边，需要对每个入度边分别进行剪枝计算
                        logger.debug(f"节点 {predecessor} 有 {len(predecessor_in_edges)} 个入度边，需分别评估")
                        all_in_edges_prunable = True
                        
                        for in_edge in predecessor_in_edges:
                            in_source, in_target, in_data = in_edge
                            
                            # 收集入度边上的SQL语句（作为一个整体）
                            in_edge_sqls = []
                            if 'sql_statements' in in_data:
                                for sql_info in in_data['sql_statements']:
                                    in_edge_sqls.append(sql_info['sql_text'])
                            
                            # 构建包含入度边SQL的完整路径，同样保持SQL组的完整性
                            complete_path_sql_groups = new_path_sql_groups.copy()
                            if in_edge_sqls:
                                complete_path_sql_groups.insert(0, in_edge_sqls)
                            
                            if analyzer and complete_path_sql_groups:
                                # 处理SQL路径组，为分析准备数据
                                analysis_sqls = prepare_analysis_sqls(complete_path_sql_groups)
                                
                                # 如果有有效的SQL，调用分析器进行分析
                                if analysis_sqls:
                                    logger.debug(f"分析入度边 {in_source} -> {in_target} 的血缘关系")
                                    try:
                                        if current_node.startswith("tmp_") and predecessor.startswith("tmp_"):
                                            lineage_result = {"result": True}
                                        else:
                                            lineage_result = analyzer.analyze_lineage(
                                                analysis_sqls, 
                                                dialect=dialect, 
                                                schema=schema
                                            )
                                        # 根据返回的血缘关系判断是否可剪枝
                                        can_prune_this_edge = not lineage_result.get("result", True)
                                        
                                        if not can_prune_this_edge:
                                            logger.debug(f"入度边 {in_source} -> {in_target} 不可剪枝")
                                            all_in_edges_prunable = False
                                            break
                                    except Exception as e:
                                        logger.error(f"分析入度边 {in_source} -> {in_target} 的血缘关系时出错: {e}")
                                        # 出错时默认不可剪枝
                                        all_in_edges_prunable = False
                                        break
                        
                        if all_in_edges_prunable and predecessor_in_edges:
                            logger.info(f"节点 {predecessor} 的所有入度边都可以剪枝，该节点可以剪枝")
                            pruned_nodes.add(predecessor)
                            # 将剪枝节点也添加到访问集合中
                            all_visited_nodes.add(predecessor)
                            continue
                        
                        # 节点不可剪枝，继续BFS
                        logger.debug(f"节点 {predecessor} 不可剪枝，继续BFS")
                        visited.add(predecessor)
                        queue.append((predecessor, new_path_sql_groups))
        
        # 在图中标记可剪枝节点和访问过的节点
        for node in lineage_graph.nodes():
            if node in pruned_nodes:
                lineage_graph.nodes[node]['pruned'] = True
            if node in all_visited_nodes:
                lineage_graph.nodes[node]['visited'] = True
        
        logger.info(f"血缘分析完成，共有 {len(pruned_nodes)} 个节点可剪枝，{len(all_visited_nodes)} 个节点被访问")
        
        # 进一步优化剪枝：如果一个节点的所有上游节点都被剪枝，则该节点也可以被剪枝
        logger.info("开始进行剪枝优化：检查节点的上游节点是否全部可剪枝")
        
        # 记录初始剪枝节点数量
        initial_pruned_count = len(pruned_nodes)
        changes_made = True
        iteration = 0
        
        while changes_made:
            iteration += 1
            changes_made = False
            nodes_to_prune = set()
            
            # 检查每个被访问但未被剪枝的节点
            for node in all_visited_nodes:
                if node not in pruned_nodes:
                    # 获取该节点的所有上游节点
                    predecessors = list(lineage_graph.predecessors(node))
                    
                    # 如果节点没有上游节点，跳过
                    if not predecessors:
                        continue
                    
                    # 检查是否所有上游节点都已被剪枝
                    all_predecessors_pruned = all(pred in pruned_nodes for pred in predecessors)
                    
                    if all_predecessors_pruned:
                        logger.debug(f"节点 {node} 的所有上游节点都已被剪枝，该节点也可以被剪枝")
                        nodes_to_prune.add(node)
            
            # 更新剪枝节点集合
            if nodes_to_prune:
                pruned_nodes.update(nodes_to_prune)
                changes_made = True
                logger.info(f"第 {iteration} 轮优化：新增 {len(nodes_to_prune)} 个可剪枝节点")
                
                # 在图中标记新的可剪枝节点
                for node in nodes_to_prune:
                    lineage_graph.nodes[node]['pruned'] = True
        
        # 输出优化后的剪枝结果
        additional_pruned = len(pruned_nodes) - initial_pruned_count
        if additional_pruned > 0:
            logger.info(f"剪枝优化完成，额外识别出 {additional_pruned} 个可剪枝节点，总计 {len(pruned_nodes)} 个可剪枝节点")
        else:
            logger.info("剪枝优化完成，没有发现额外的可剪枝节点")
        
        return lineage_graph, pruned_nodes, downstream_nodes
    except Exception as e:
        logger.error(f"计算血缘关系并应用剪枝优化时出错: {e}")
        # 出错时返回原始图和空集合
        return lineage_graph, set(), []

# ====================== 图操作函数（从lineage_app.py迁移过来的功能） ======================

def calculate_node_path_lengths(graph: nx.DiGraph) -> Dict[str, int]:
    """
    计算图中每个节点到最下游节点的路径长度

    Args:
        graph: NetworkX有向图对象

    Returns:
        Dict[str, int]: 节点名称到路径长度的映射
    """
    # 找到最下游节点（没有出边的节点）
    downstream_nodes = [node for node in graph.nodes() if graph.out_degree(node) == 0]

    if not downstream_nodes:
        logger.warning("图中没有下游节点")
        return {node: 0 for node in graph.nodes()}

    # 初始化结果字典
    path_lengths = {node: 0 for node in graph.nodes()}

    # 为每个下游节点计算到其他节点的最短路径长度
    for target in downstream_nodes:
        # 使用单源最短路径算法计算从目标节点到所有其他节点的路径长度
        # 注意：我们反转图的方向，从下游节点向上游节点计算
        reversed_graph = graph.reverse()
        path_lengths_from_target = nx.single_source_shortest_path_length(reversed_graph, target)

        # 更新结果字典，取最大值
        for node, length in path_lengths_from_target.items():
            path_lengths[node] = max(path_lengths[node], length)

    return path_lengths

def get_upstream_subgraph(graph: nx.DiGraph, selected_node: str) -> nx.DiGraph:
    """
    计算与选定节点相连通的所有上游节点，构建子图

    Args:
        graph: NetworkX有向图对象
        selected_node: 选定的节点

    Returns:
        nx.DiGraph: 包含选定节点及其所有上游节点的子图
    """
    # 创建一个新的有向图作为子图
    subgraph = nx.DiGraph()

    # 使用BFS从选定节点向上游遍历
    visited = set([selected_node])
    queue = [selected_node]

    # 将选定节点添加到子图
    subgraph.add_node(selected_node)

    while queue:
        current_node = queue.pop(0)

        # 获取当前节点的所有上游节点
        predecessors = list(graph.predecessors(current_node))

        for predecessor in predecessors:
            # 将节点和边添加到子图
            subgraph.add_node(predecessor)
            
            # 检查边是否已存在，如果不存在则添加
            if not subgraph.has_edge(predecessor, current_node):
                subgraph.add_edge(predecessor, current_node)
                
                # 复制边的属性
                if 'sql_statements' in graph[predecessor][current_node]:
                    subgraph[predecessor][current_node]['sql_statements'] = graph[predecessor][current_node]['sql_statements']
            
            # 如果节点未访问过，则添加到访问集合和队列
            if predecessor not in visited:
                visited.add(predecessor)
                queue.append(predecessor)

    return subgraph

def get_downstream_nodes(graph: nx.DiGraph) -> List[str]:
    """
    获取图中所有下游节点（没有出边的节点）
    
    Args:
        graph: NetworkX有向图对象
        
    Returns:
        List[str]: 下游节点列表
    """
    return [node for node in graph.nodes() if graph.out_degree(node) == 0]

# ====================== 辅助函数 ======================

def prepare_analysis_sqls(sql_groups: List[List[str]]) -> List[Union[str, List[str]]]:
    """
    从SQL组列表中准备用于分析的SQL列表。
    
    该函数接收一个SQL语句组列表，每组代表一条边上的全部SQL语句。
    函数将这些SQL语句组处理成分析器需要的格式，按照从目标到源的顺序排列。
    
    处理逻辑：
    1. 对于只有一条SQL语句的组，直接使用该语句
    2. 对于包含多条SQL语句的组，将整个组作为一个列表保留
    3. 最终将所有语句按从目标到源的顺序排列（翻转原列表）
    
    Args:
        sql_groups: SQL组列表，每个组是一个SQL语句列表，代表一条边上的全部SQL
        
    Returns:
        用于分析的SQL列表，顺序为从目标到源。列表中的元素可能是字符串（单条SQL）
        或列表（包含多条SQL的组）
    """
    try:
        # 从每组SQL中选取代表性SQL，如果组长度为1则直接使用元素，否则保持列表形式
        flat_sqls = []
        for sql_group in sql_groups:
            if sql_group:  # 确保SQL组不为空
                if len(sql_group) == 1:  # 如果组只有一个元素，直接添加该元素
                    flat_sqls.append(sql_group[0])
                else:  # 否则保持列表形式
                    flat_sqls.append(sql_group)
        
        # 返回格式化为分析器需要的顺序：[目标SQL, 上游SQL...]
        output = flat_sqls[::-1]
        return output
    except Exception as e:
        logger.error(f"准备分析SQL时出错: {e}")
        # 出错时返回空列表
        return []
