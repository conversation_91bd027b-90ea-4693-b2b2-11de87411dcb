"""
SQL行级血缘分析工具 - 可视化模块

该模块提供了血缘图的可视化功能，包括：
- 打印血缘图中的边及其关联的SQL语句
- 使用graphviz生成血缘图的可视化图像
- 支持节点状态（已访问、可剪枝）的样式渲染
- 支持在边上显示SQL语句

作者: 数据血缘分析团队
版本: 1.0.0
"""

import networkx as nx
from typing import Optional, Any, Dict, List, Tuple
from lineage_core.logger import logger

# ====================== 血缘图打印函数 ======================

def print_lineage_graph_with_sql(graph: nx.DiGraph) -> None:
    """
    打印血缘图中的边及其关联的SQL语句。
    
    该函数遍历血缘图中的所有边，输出每条边的源节点和目标节点，以及关联的SQL语句。
    SQL语句过长时会被截断，以便于显示。
    
    Args:
        graph: 血缘图对象，NetworkX有向图，包含关联的SQL语句信息
    
    Returns:
        无返回值，直接通过logger输出信息
    """
    try:
        logger.info("=== 血缘图边与SQL语句映射 ===")
        for edge in graph.edges(data=True):
            source, target, data = edge
            logger.info(f"边: {source} -> {target}")
            
            if 'sql_statements' in data and data['sql_statements']:
                logger.info(f"关联的SQL语句数量: {len(data['sql_statements'])}")
                for sql_info in data['sql_statements']:
                    sql_id = sql_info['sql_id']
                    sql_text = sql_info['sql_text']
                    # 截断过长的SQL以便于显示
                    short_sql = sql_text[:100] + "..." if len(sql_text) > 100 else sql_text
                    logger.debug(f"  SQL ID: {sql_id+1}, 语句: {short_sql}")
            else:
                logger.info("  没有关联的SQL语句")
    except Exception as e:
        logger.error(f"打印血缘图时出错: {e}")

# ====================== 血缘图可视化函数 ======================

def visualize_lineage_graph(graph: nx.DiGraph, output_file: str = "output/lineage_graph_vis.pdf", open_pdf: bool = True, show_sql: bool = True) -> Optional[Any]:
    """
    使用 graphviz 可视化血缘图，并保存为指定格式。
    
    该函数将血缘图转换为graphviz格式，并根据节点状态(是否访问、是否可剪枝)进行不同的样式渲染。
    图中的节点和边会根据状态使用不同的颜色和样式，边上还会显示对应的SQL语句。
    
    Args:
        graph: 血缘图对象，NetworkX有向图
        output_file: 输出文件路径，默认为 "output/lineage_graph_vis.pdf"
        open_pdf: 是否在生成后自动打开PDF文件，默认为True。如果为False，则只生成DOT文件而不生成PDF
        show_sql: 是否在边上显示SQL语句，默认为True
    
    Returns:
        graphviz.Digraph对象，如果graphviz库未安装则返回None
    
    Raises:
        Exception: 当图像保存失败时可能引发异常
    """
    try:
        import graphviz
        import sqlglot
    except ImportError:
        logger.error("请先安装 graphviz 包: pip install graphviz")
        return None
    
    try:
        # 创建有向图
        dot = graphviz.Digraph(comment='表级血缘图', format='pdf', engine='dot')
        dot.attr(rankdir='LR')  # 从左到右布局
        dot.attr('node', fontname='SimHei')  # 设置中文字体
        dot.attr('edge', fontname='SimHei')  # 设置中文字体
        # 设置节点的样式，不再设置固定高度，以便自动适应换行内容
        dot.attr('node', fontsize='10')  # 设置较小的字体大小
        # 增加节点之间的横向间距
        dot.attr(nodesep='0.4')  # 节点之间的最小间距
        dot.attr(ranksep='0.8')  # 层级之间的最小间距
        
        # 定义明亮的颜色方案
        node_colors = ['#FF9999', '#99FF99', '#9999FF', '#FFFF99', '#FF99FF', '#99FFFF']
        # 定义可剪枝节点的颜色
        pruned_node_color = '#666666'  # 深灰色
        # 定义未访问节点的颜色
        unvisited_node_color = '#DDDDDD'  # 浅灰色
        
        logger.debug(f"开始创建血缘图可视化，总节点数: {len(graph.nodes())}, 总边数: {len(graph.edges())}")
        
        # 添加节点
        for i, node in enumerate(graph.nodes()):
            try:
                # 获取表名
                table_name = str(node)
                
                # 处理标签文本，每10个字符添加一个换行符
                formatted_label = ""
                for j in range(0, len(table_name), 20):
                    chunk = table_name[j:j+20]
                    formatted_label += chunk + "\n" if j+20 < len(table_name) else chunk
                
                # 根据标签长度计算节点宽度
                # 基础宽度为0.8，比原来的1.0小
                width = 0.8
                height = max(0.5, 0.2 + (formatted_label.count('\n') + 1) * 0.2)  # 根据行数设置高度
                
                # 检查节点状态
                is_pruned = graph.nodes[node].get('pruned', False)
                is_visited = graph.nodes[node].get('visited', False)
                
                if not is_visited:
                    # 未访问节点使用浅灰色
                    dot.node(str(node), formatted_label, shape='box', style='filled,rounded', 
                             fillcolor=unvisited_node_color, fontcolor='#666666', penwidth='1',
                             width=str(width), height=str(height), margin='0.05,0.05')
                elif is_pruned:
                    # 可剪枝节点使用深灰色
                    dot.node(str(node), formatted_label, shape='box', style='filled,rounded', 
                             fillcolor=pruned_node_color, fontcolor='white', penwidth='1',
                             width=str(width), height=str(height), margin='0.05,0.05')
                else:
                    # 正常访问过的节点使用彩色
                    color = node_colors[i % len(node_colors)]
                    dot.node(str(node), formatted_label, shape='box', style='filled,rounded', 
                             fillcolor=color, fontcolor='black', penwidth='1.5',
                             width=str(width), height=str(height), margin='0.05,0.05')
            except Exception as e:
                logger.error(f"添加节点 {node} 时出错: {e}")
                # 继续处理下一个节点
        
        # 添加边
        for source, target, data in graph.edges(data=True):
            try:
                # 检查源节点和目标节点状态
                source_pruned = graph.nodes[source].get('pruned', False)
                target_pruned = graph.nodes[target].get('pruned', False)
                source_visited = graph.nodes[source].get('visited', False)
                target_visited = graph.nodes[target].get('visited', False)
                
                edge_label = ""
                if show_sql and 'sql_statements' in data and data['sql_statements']:
                    # 为每条SQL语句创建标签，添加明显的分隔符
                    sql_labels = []
                    for sql_info in data['sql_statements']:
                        sql_id = sql_info['sql_id']
                        sql_text = sql_info['sql_text']
                        
                        try:
                            # 使用sqlglot从语法层面美化SQL
                            formatted_sql = sqlglot.transpile(sql_text, read='teradata', pretty=True)[0]
                        except Exception as format_error:
                            logger.warning(f"SQL美化失败: {format_error}")
                            formatted_sql = sql_text  # 如果美化失败，使用原始SQL
                        
                        # 移除分号，它在DOT语言中是语句终止符
                        formatted_sql = formatted_sql.replace(';', '')
                        
                        # 转义其他可能导致问题的字符
                        formatted_sql = formatted_sql.replace('\\', '\\\\')  # 转义反斜杠
                        formatted_sql = formatted_sql.replace('"', '\\"')    # 转义双引号
                        formatted_sql = formatted_sql.replace("'", "\\'")    # 转义单引号
                        formatted_sql = formatted_sql.replace('<', '\\<')    # 转义小于号
                        formatted_sql = formatted_sql.replace('>', '\\>')    # 转义大于号
                        
                        # 处理SQL格式以确保左对齐
                        lines = formatted_sql.split('\n')
                        if lines:
                            # 找出所有非空行的最小缩进
                            non_empty_lines = [line for line in lines if line.strip()]
                            if non_empty_lines:
                                min_indent = min(len(line) - len(line.lstrip()) for line in non_empty_lines)
                                # 移除共同的前导空格以实现左对齐
                                aligned_lines = []
                                for i, line in enumerate(lines):
                                    if line.strip():  # 非空行
                                        # 移除最小共同缩进
                                        base_line = line[min_indent:] if len(line) > min_indent else line
                                        # 第一行不缩进，后续行添加缩进
                                        if i > 0:
                                            # 使用中文全角空格来代替普通空格，这在大多数环境下不会被压缩
                                            aligned_line = "　" + base_line  # 两个全角空格
                                        else:
                                            aligned_line = base_line
                                        aligned_line = aligned_line.replace('  ', '　　')
                                        aligned_lines.append(aligned_line)
                                    else:
                                        aligned_lines.append('')  # 保留空行
                                
                                formatted_sql = '\n'.join(aligned_lines)
                        
                        # 创建SQL标签，确保每行都左对齐，并添加明显的分隔和边框
                        sql_label = f"SQL {sql_id+1}: \\l" + formatted_sql.replace('\n', '\\l') + '\\l' + "-" * 30 + "\\l"
                        sql_labels.append(sql_label)
                    
                    # 将所有SQL语句连接成一个标签，用分隔符分开
                    edge_label = "\\l".join(sql_labels)
                
                # 根据节点的状态设置边的样式
                if not source_visited or not target_visited:
                    # 如果边的任一端未被访问，使用浅灰色虚线
                    if edge_label:
                        dot.edge(str(source), str(target), label=edge_label, 
                                color='#CCCCCC', penwidth='1', fontsize='10', 
                                align='left', style='dashed')
                    else:
                        dot.edge(str(source), str(target), color='#CCCCCC', 
                                penwidth='1', style='dashed')
                elif source_pruned or target_pruned:
                    # 如果边的任一端是可剪枝节点，使用深灰色虚线
                    if edge_label:
                        dot.edge(str(source), str(target), label=edge_label, 
                                color='#666666', penwidth='1', fontsize='10', 
                                align='left', style='dashed')
                    else:
                        dot.edge(str(source), str(target), color='#666666', 
                                penwidth='1', style='dashed')
                else:
                    # 正常边使用蓝色实线
                    if edge_label:
                        dot.edge(str(source), str(target), label=edge_label, 
                                color='#0066CC', penwidth='1.5', fontsize='10', align='left')
                    else:
                        dot.edge(str(source), str(target), color='#0066CC', penwidth='1.5')
            except Exception as e:
                logger.error(f"添加边 {source} -> {target} 时出错: {e}")
                # 继续处理下一条边
        
        # 设置全局图形属性
        dot.attr(bgcolor='#FAFAFA')  # 设置背景色为浅灰色
        
        # 保存图像
        try:
            if open_pdf:
                # 生成并可能打开PDF文件
                dot.render(output_file.split('.')[0], view=open_pdf)
                logger.info(f"血缘图已保存为 {output_file}")
        except Exception as e:
            logger.error(f"保存图像时出错: {e}")
        
        return dot
    except Exception as e:
        logger.error(f"可视化血缘图时出错: {e}")
        return None 