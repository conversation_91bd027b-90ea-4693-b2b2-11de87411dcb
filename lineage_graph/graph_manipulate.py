"""
SQL行级血缘分析工具 - 图操作模块

该模块提供了对血缘图进行各种操作的函数，包括：
- 从指定节点提取子图
- 支持向上游或下游方向遍历
- 支持限制遍历深度
- 移除临时表并重新连接其上下游节点

作者: 数据血缘分析团队
版本: 1.0.0
"""

from typing import List, Dict, Set, Optional, Literal, Tuple, Union
import networkx as nx
from collections import defaultdict

from lineage_core.logger import logger

def extract_subgraph(
    graph: nx.DiGraph,
    node: str,
    depth: int = -1,
    direction: Literal['upstream', 'downstream', 'both'] = 'both'
) -> nx.DiGraph:
    """
    从指定节点开始，根据给定的深度和方向提取子图

    Args:
        graph: NetworkX有向图对象
        node: 起始节点名称
        depth: 遍历深度，-1表示不限制深度（默认），0只包含起始节点本身，大于0表示遍历的层数
        direction: 遍历方向
            - 'upstream'：仅向上游方向遍历（源节点）
            - 'downstream'：仅向下游方向遍历（目标节点）
            - 'both'：同时向上游和下游方向遍历（默认）

    Returns:
        nx.DiGraph: 提取的子图，包含起始节点及指定方向和深度内的相关节点

    Raises:
        ValueError: 如果起始节点不在图中或方向参数无效
    """
    # 检查节点是否存在
    if node not in graph:
        raise ValueError(f"节点 '{node}' 不在图中")

    # 检查方向参数是否有效
    valid_directions = ['upstream', 'downstream', 'both']
    if direction not in valid_directions:
        raise ValueError(f"无效的方向参数: '{direction}'，必须是 {valid_directions} 中的一个")

    # 创建一个新的有向图作为子图
    subgraph = nx.DiGraph()

    # 添加起始节点到子图
    subgraph.add_node(node)

    # 使用广度优先搜索(BFS)遍历图
    visited = {node}
    # 队列中的元素是 (节点, 当前深度) 的元组
    queue = [(node, 0)]

    while queue:
        current_node, current_depth = queue.pop(0)

        # 如果设置了深度限制并且当前深度已经达到限制，则不再继续遍历
        if depth != -1 and current_depth >= depth:
            continue

        # 根据方向参数决定遍历的邻居节点
        if direction in ['upstream', 'both']:
            # 向上游方向遍历（获取前驱节点）
            predecessors = list(graph.predecessors(current_node))
            for predecessor in predecessors:
                # 将边和节点添加到子图
                subgraph.add_node(predecessor)

                # 复制边的属性
                if not subgraph.has_edge(predecessor, current_node):
                    subgraph.add_edge(predecessor, current_node)

                    # 如果原图中有边属性，则复制到子图
                    for key, value in graph[predecessor][current_node].items():
                        subgraph[predecessor][current_node][key] = value

                # 如果节点未访问过，则添加到访问集合和队列
                if predecessor not in visited:
                    visited.add(predecessor)
                    queue.append((predecessor, current_depth + 1))

        if direction in ['downstream', 'both']:
            # 向下游方向遍历（获取后继节点）
            successors = list(graph.successors(current_node))
            for successor in successors:
                # 将边和节点添加到子图
                subgraph.add_node(successor)

                # 复制边的属性
                if not subgraph.has_edge(current_node, successor):
                    subgraph.add_edge(current_node, successor)

                    # 如果原图中有边属性，则复制到子图
                    for key, value in graph[current_node][successor].items():
                        subgraph[current_node][successor][key] = value

                # 如果节点未访问过，则添加到访问集合和队列
                if successor not in visited:
                    visited.add(successor)
                    queue.append((successor, current_depth + 1))

    logger.info(f"从节点 '{node}' 提取了一个子图, 方向: {direction}, 深度: {depth if depth != -1 else '不限'}")
    logger.debug(f"子图包含 {len(subgraph.nodes())} 个节点, {len(subgraph.edges())} 条边")

    return subgraph

def get_edge_sqls(graph: nx.DiGraph, edge: Tuple[str, str]) -> List[Dict[str, str]]:
    """
    获取指定边上的SQL语句列表

    Args:
        graph: NetworkX有向图对象
        edge: 表示边的元组 (source_node, target_node)

    Returns:
        List[Dict[str, str]]: SQL语句信息列表，每项包含 'sql_id' 和 'sql_text'

    Raises:
        ValueError: 如果边不存在
        KeyError: 如果边上没有SQL语句信息
    """
    source, target = edge

    # 检查边是否存在
    if not graph.has_edge(source, target):
        raise ValueError(f"边 ({source}, {target}) 不存在于图中")

    # 获取边的属性
    edge_attrs = graph[source][target]

    # 检查边是否有SQL语句信息
    if 'sql_statements' not in edge_attrs:
        raise KeyError(f"边 ({source}, {target}) 没有SQL语句信息")

    return edge_attrs['sql_statements']

def get_edge_sql_texts(graph: nx.DiGraph, edge: Tuple[str, str]) -> List[str]:
    """
    获取指定边上所有SQL语句的文本列表

    Args:
        graph: NetworkX有向图对象
        edge: 表示边的元组 (source_node, target_node)

    Returns:
        List[str]: 该边上所有SQL语句的文本列表
    """
    try:
        # 获取边上的SQL语句信息
        sql_statements = get_edge_sqls(graph, edge)

        # 提取所有SQL文本
        return [stmt['sql_text'] for stmt in sql_statements]
    except (ValueError, KeyError) as e:
        logger.warning(f"获取边 {edge} 的SQL文本时出错: {e}")
        return []

def get_node_neighbors(
    graph: nx.DiGraph,
    node: str,
    direction: Literal['upstream', 'downstream', 'both'] = 'both'
) -> Dict[str, List[str]]:
    """
    获取指定节点的邻居节点

    Args:
        graph: NetworkX有向图对象
        node: 节点名称
        direction: 遍历方向
            - 'upstream'：仅获取上游节点（前驱节点）
            - 'downstream'：仅获取下游节点（后继节点）
            - 'both'：同时获取上游和下游节点（默认）

    Returns:
        Dict[str, List[str]]: 包含上游和下游节点的字典
        {
            'upstream': [上游节点列表],
            'downstream': [下游节点列表]
        }
    """
    # 检查节点是否存在
    if node not in graph:
        raise ValueError(f"节点 '{node}' 不在图中")

    result = {'upstream': [], 'downstream': []}

    # 根据方向参数获取邻居节点
    if direction in ['upstream', 'both']:
        result['upstream'] = list(graph.predecessors(node))

    if direction in ['downstream', 'both']:
        result['downstream'] = list(graph.successors(node))

    return result

def filter_temp_tables(graph: nx.DiGraph, direction: Literal['upstream', 'downstream', 'both'] = 'both') -> nx.DiGraph:
    """
    过滤掉图中没有下游节点的临时表

    临时表的定义：节点名称中包含 "VT" 的表
    过滤条件：如果一个临时表没有下游节点（出度为0），则将其删除
    注意：会迭代删除，因为删除一批临时表后可能导致新的临时表变成无下游节点

    Args:
        graph: NetworkX有向图对象

    Returns:
        nx.DiGraph: 过滤临时表后的新图
    """
    # 创建原图的拷贝，避免修改原图
    filtered_graph = graph.copy()

    # 记录总共删除的节点数
    total_removed = 0
    # 迭代次数
    iteration = 0

    while True:
        iteration += 1
        # 找出所有临时表节点
        temp_tables = [node for node in filtered_graph.nodes() if (str(node).startswith("tmp_") or str(node).startswith("VT_"))]

        # 记录要删除的节点
        nodes_to_remove = []

        # 遍历所有临时表，检查是否有下游节点
        for temp_node in temp_tables:
            # 检查出度（下游节点数量）
            if direction == 'upstream' or direction == 'both':
                if filtered_graph.out_degree(temp_node) == 0:
                    # 如果没有下游节点，则标记为需要删除
                    nodes_to_remove.append(temp_node)
            if direction == 'downstream' or direction == 'both':
                if filtered_graph.in_degree(temp_node) == 0:
                    # 如果没有上游节点，则标记为需要删除
                    nodes_to_remove.append(temp_node)

            # 如果节点是临时表，节点仅有一个下游节点，同时该下游节点又存在一条边，连接回当前节点，则删除该节点
            if filtered_graph.out_degree(temp_node) == 1 and filtered_graph.in_degree(temp_node) == 1:
                successor = list(filtered_graph.successors(temp_node))[0]
                if filtered_graph.has_edge(successor, temp_node):
                    nodes_to_remove.append(temp_node)


        # 如果没有需要删除的节点，结束循环
        if not nodes_to_remove:
            break

        # 从图中删除标记的节点
        for node in nodes_to_remove:
            try:
                filtered_graph.remove_node(node)
            except Exception as e:
                logger.error(f"删除节点 {node} 时出错: {e}")

        # 更新总删除计数
        total_removed += len(nodes_to_remove)

        logger.info(f"第 {iteration} 轮过滤：删除 {len(nodes_to_remove)} 个无下游节点的临时表")
        logger.debug(f"本轮删除的临时表: {nodes_to_remove}")

    logger.info(f"过滤临时表完成，共进行 {iteration-1} 轮过滤，总共删除 {total_removed} 个无下游节点的临时表")

    output_graph = filtered_graph.copy()
    # 删除所有孤立节点
    filtered_graph.remove_nodes_from(nx.isolates(output_graph))
    return filtered_graph

def get_node_depth_layers(
    graph: nx.DiGraph,
    node: str,
    max_depth: int = -1,
    direction: Literal['upstream', 'downstream', 'both'] = 'both'
) -> Dict[int, Dict[str, List[str]]]:
    """
    获取从指定节点开始的不同深度层级的节点

    Args:
        graph: NetworkX有向图对象
        node: 起始节点名称
        max_depth: 最大深度，-1表示不限制深度
        direction: 遍历方向
            - 'upstream'：仅向上游方向遍历
            - 'downstream'：仅向下游方向遍历
            - 'both'：同时向上游和下游方向遍历（默认）

    Returns:
        Dict[int, Dict[str, List[str]]]: 深度层级到节点列表的映射
        {
            0: {
                'center': [中心节点],
                'upstream': [],
                'downstream': []
            },
            1: {
                'upstream': [深度1的上游节点列表],
                'downstream': [深度1的下游节点列表]
            },
            2: {
                'upstream': [深度2的上游节点列表],
                'downstream': [深度2的下游节点列表]
            },
            ...
        }
    """
    # 检查节点是否存在
    if node not in graph:
        raise ValueError(f"节点 '{node}' 不在图中")

    # 检查方向参数是否有效
    valid_directions = ['upstream', 'downstream', 'both']
    if direction not in valid_directions:
        raise ValueError(f"无效的方向参数: '{direction}'，必须是 {valid_directions} 中的一个")

    # 结果字典，用于存储每个深度的节点
    layers = defaultdict(lambda: {'upstream': [], 'downstream': []})

    # 初始化第0层（中心节点）
    layers[0] = {'center': [node], 'upstream': [], 'downstream': []}

    # 使用BFS遍历图，分别处理上游和下游
    if direction in ['upstream', 'both']:
        # 上游方向BFS
        visited = {node}
        queue = [(node, 0)]  # (节点, 深度)

        while queue:
            current_node, current_depth = queue.pop(0)

            # 如果设置了深度限制并且当前深度已经达到限制，则不再继续遍历
            if max_depth != -1 and current_depth >= max_depth:
                continue

            # 获取上游节点
            predecessors = list(graph.predecessors(current_node))
            next_depth = current_depth + 1

            for predecessor in predecessors:
                # 将节点添加到对应深度的上游列表
                if predecessor not in visited:
                    layers[next_depth]['upstream'].append(predecessor)
                    visited.add(predecessor)
                    queue.append((predecessor, next_depth))

    if direction in ['downstream', 'both']:
        # 下游方向BFS
        visited = {node}
        queue = [(node, 0)]  # (节点, 深度)

        while queue:
            current_node, current_depth = queue.pop(0)

            # 如果设置了深度限制并且当前深度已经达到限制，则不再继续遍历
            if max_depth != -1 and current_depth >= max_depth:
                continue

            # 获取下游节点
            successors = list(graph.successors(current_node))
            next_depth = current_depth + 1

            for successor in successors:
                # 将节点添加到对应深度的下游列表
                if successor not in visited:
                    layers[next_depth]['downstream'].append(successor)
                    visited.add(successor)
                    queue.append((successor, next_depth))

    # 移除没有节点的深度层
    result = {}
    for depth, nodes in layers.items():
        if any(len(node_list) > 0 for node_list in nodes.values()):
            result[depth] = nodes

    return result

def get_node_name(graph: nx.DiGraph, keyword: str) -> List[str]:
    """
    查找图中包含指定关键词的所有节点

    Args:
        graph: NetworkX有向图对象
        keyword: 要搜索的关键词

    Returns:
        List[str]: 包含关键词的节点名称列表
    """
    matched_nodes = [node for node in graph.nodes() if keyword.lower() in str(node).split('.')[-1].lower()]

    logger.debug(f"在图中找到 {len(matched_nodes)} 个包含关键词 '{keyword}' 的节点")
    return matched_nodes

def extract_subgraph_between_twonode(
    graph: nx.DiGraph,
    source_node: Union[str, List[str]],
    target_node: str,
    max_paths: int = 10
) -> nx.DiGraph:
    """
    提取从源节点到目标节点之间的子图

    该函数查找从源节点到目标节点的所有简单路径（不包含环路），
    然后提取这些路径所涉及的所有节点和边，构成一个新的子图。

    Args:
        graph: NetworkX有向图对象
        source_node: 起始节点名称或起始节点列表
        target_node: 目标节点名称
        max_paths: 每对源节点和目标节点之间的最大路径数量，默认为10。
                   如果路径太多，可能会导致性能问题。

    Returns:
        nx.DiGraph: 包含源节点到目标节点之间所有路径的子图

    Raises:
        ValueError: 如果节点不存在于图中或无法找到从源到目标的路径
    """
    # 创建一个新的有向图作为子图
    subgraph = nx.DiGraph()

    # 将单个源节点转换为列表以统一处理
    source_nodes = [source_node] if isinstance(source_node, str) else source_node

    # 检查节点是否存在
    for src in source_nodes:
        if src not in graph:
            raise ValueError(f"源节点 '{src}' 不在图中")

    if target_node not in graph:
        raise ValueError(f"目标节点 '{target_node}' 不在图中")

    # 记录所有找到的路径
    all_found_paths = []

    # 为每个源节点分别找路径
    for src in source_nodes:
        try:
            # 查找所有从当前源节点到目标的简单路径
            paths = list(nx.all_simple_paths(graph, src, target_node, cutoff=None))

            # 如果找到路径，添加到总路径列表中
            if paths:
                all_found_paths.extend(paths)
                logger.debug(f"从 '{src}' 到 '{target_node}' 找到 {len(paths)} 条路径")
            else:
                logger.warning(f"无法找到从 '{src}' 到 '{target_node}' 的路径")

        except nx.NetworkXNoPath:
            logger.warning(f"无法找到从 '{src}' 到 '{target_node}' 的路径")
        except Exception as e:
            logger.error(f"查找从 '{src}' 到 '{target_node}' 的路径时出错: {e}")

    # 如果没有找到任何路径，抛出异常
    if not all_found_paths:
        raise ValueError(f"无法找到从任何提供的源节点到 '{target_node}' 的路径")

    # 如果路径数量超过限制，只取前max_paths条路径
    if len(all_found_paths) > max_paths:
        logger.warning(f"找到的总路径数 ({len(all_found_paths)}) 超过限制 ({max_paths})，只使用前 {max_paths} 条路径")
        all_found_paths = all_found_paths[:max_paths]

    # 将所有路径中的节点和边添加到子图中
    for path in all_found_paths:
        # 添加路径中的所有节点
        for node in path:
            if node not in subgraph:
                subgraph.add_node(node)

        # 添加路径中的所有边
        for i in range(len(path) - 1):
            source = path[i]
            target = path[i + 1]

            # 如果边还没有添加到子图中，则添加它
            if not subgraph.has_edge(source, target):
                subgraph.add_edge(source, target)

                # 复制边的属性
                if 'sql_statements' in graph[source][target]:
                    subgraph[source][target]['sql_statements'] = graph[source][target]['sql_statements']

    logger.info(f"成功提取了从 {len(source_nodes)} 个源节点到 '{target_node}' 的子图")
    logger.debug(f"子图包含 {len(subgraph.nodes())} 个节点, {len(subgraph.edges())} 条边, 基于 {len(all_found_paths)} 条路径")

    return subgraph

def remove_temp_tables(graph: nx.DiGraph) -> nx.DiGraph:
    """
    移除图中的临时表，并将临时表的上游和下游节点直接连接起来

    临时表的定义：节点名称以 "tmp_" 或 "VT_" 开头的表
    处理逻辑：
    1. 识别所有临时表
    2. 迭代处理临时表，直到所有临时表都被移除
    3. 每次迭代中，只处理那些没有临时表作为上游或下游的临时表
    4. 为每对上游和下游节点创建新的边（不复制原有属性）
    5. 删除处理过的临时表节点

    Args:
        graph: NetworkX有向图对象

    Returns:
        nx.DiGraph: 处理后的新图，其中临时表被移除，上下游节点直接连接
    """
    # 创建原图的拷贝，避免修改原图
    result_graph = graph.copy()

    # 找出所有临时表节点
    is_temp_table = lambda node: str(node).startswith("tmp_") or str(node).startswith("VT_")
    temp_tables = [node for node in result_graph.nodes() if is_temp_table(node)]

    logger.info(f"在图中找到 {len(temp_tables)} 个临时表节点")

    # 记录已处理的临时表数量
    processed_count = 0
    # 记录添加的新边数量
    edge_count = 0

    # 迭代处理临时表，直到所有临时表都被处理
    while temp_tables:
        # 本轮可以处理的临时表（没有临时表作为上游或下游）
        processable_tables = []

        for temp_table in temp_tables:
            # 获取临时表的上游节点
            predecessors = list(result_graph.predecessors(temp_table))
            # 获取临时表的下游节点
            successors = list(result_graph.successors(temp_table))

            # 检查上游和下游是否有临时表
            has_temp_predecessor = any(is_temp_table(pred) for pred in predecessors)
            has_temp_successor = any(is_temp_table(succ) for succ in successors)

            # 如果上游和下游都没有临时表，则可以处理该临时表
            if not has_temp_predecessor or not has_temp_successor:
                processable_tables.append(temp_table)

        # 如果没有可处理的临时表，但仍有临时表未处理，说明存在临时表循环
        if not processable_tables and temp_tables:
            logger.warning(f"检测到临时表循环，将随机选择一个临时表进行处理")
            processable_tables = [temp_tables[0]]

        # 处理本轮可处理的临时表
        for temp_table in processable_tables:
            # 获取临时表的上游节点
            predecessors = list(result_graph.predecessors(temp_table))
            # 获取临时表的下游节点
            successors = list(result_graph.successors(temp_table))

            logger.debug(f"处理临时表 '{temp_table}' - 有 {len(predecessors)} 个上游节点和 {len(successors)} 个下游节点")

            # 为每对上游和下游节点创建新的边
            for predecessor in predecessors:
                for successor in successors:
                    # 检查边是否已存在
                    if not result_graph.has_edge(predecessor, successor):
                        # 添加新边（不复制属性）
                        result_graph.add_edge(predecessor, successor)
                        edge_count += 1

            # 从图中删除该临时表
            result_graph.remove_node(temp_table)
            processed_count += 1

        # 更新待处理的临时表列表
        temp_tables = [node for node in result_graph.nodes() if is_temp_table(node)]

    logger.info(f"临时表处理完成：移除了 {processed_count} 个临时表，添加了 {edge_count} 条新边")

    return result_graph