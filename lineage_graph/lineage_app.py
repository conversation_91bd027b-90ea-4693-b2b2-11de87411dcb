"""
SQL行级血缘分析工具 - Streamlit应用

该模块提供了一个基于Streamlit的Web应用，用于分析SQL语句之间的血缘关系，
并通过可视化图形展示表与表之间的数据流动关系。

主要功能:
- 加载和分析SQL语句
- 构建和可视化血缘关系图
- 支持选择特定节点查看其上游血缘
- 提供SQL样例选择和管理
- 支持显示边上SQL语句和智能剪枝功能

作者: 数据血缘分析团队
版本: 1.0.0
"""

# 标准库导入
import os
import sys
import tempfile
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

# 第三方库导入
import streamlit as st

# 添加项目根目录到Python路径
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))

# 本地模块导入
from lineage_graph.lineage_graph import build_graph, calculate_lineage_with_pruning, calculate_node_path_lengths, get_upstream_subgraph
from lineage_core.lineage_analyzer import LineageAnalyzer
from lineage_graph.visualization import visualize_lineage_graph
from lineage_core.utils import build_sql_list_from_str
from lineage_core.logger import logger

# 设置页面配置
st.set_page_config(
    page_title="SQL行级血缘分析工具",
    page_icon="🔍",
    layout="wide"
)

# 添加标题和描述
st.title("SQL行级血缘分析工具")
st.markdown("输入SQL语句，分析并可视化其血缘关系图。")

# ====================== 工具函数 ======================

def load_sql_examples() -> Dict[str, str]:
    """
    加载SQL样例文件
    
    从sql_examples目录中加载所有.sql文件和文件夹，构建样例字典
    
    Returns:
        Dict[str, str]: 样例名称到SQL内容的映射
    """
    examples = {"请选择SQL样例": ""}

    # 样例目录路径
    examples_dir = Path(__file__).parent / "sql_examples"

    # 确保目录存在
    if not examples_dir.exists():
        logger.warning(f"样例目录不存在: {examples_dir}")
        examples_dir.mkdir(parents=True, exist_ok=True)
        return examples

    # 读取所有.sql文件和文件夹
    sql_files = list(examples_dir.glob("*.sql"))
    folders = [f for f in examples_dir.iterdir() if f.is_dir()]

    # 对文件名和文件夹名进行排序
    sql_files.sort()
    folders.sort()

    # 添加文件
    for sql_file in sql_files:
        try:
            with open(sql_file, "r", encoding="utf-8") as f:
                # 使用文件名(不含扩展名)作为样例名称
                example_name = sql_file.stem
                examples[example_name] = f.read()
                logger.info(f"成功加载样例文件: {sql_file}")
        except Exception as e:
            logger.error(f"加载样例文件 {sql_file} 时出错: {e}")
            st.error(f"加载样例文件 {sql_file} 时出错: {e}")

    # 添加文件夹（使用特殊前缀标识）
    for folder in folders:
        folder_name = f"📁 {folder.name}"
        examples[folder_name] = folder.name  # 存储文件夹名称，用于后续加载
        logger.info(f"添加样例文件夹: {folder_name}")

    return examples

def load_sql_from_folder(folder_name: str) -> str:
    """
    从文件夹加载所有SQL文件并拼接
    
    Args:
        folder_name: 文件夹名称
        
    Returns:
        str: 拼接后的SQL语句
    """
    folder_path = Path(__file__).parent / "sql_examples" / folder_name

    if not folder_path.exists() or not folder_path.is_dir():
        error_msg = f"错误：找不到文件夹 {folder_name}"
        logger.error(error_msg)
        return error_msg

    # 获取文件夹中所有SQL文件
    sql_files = list(folder_path.glob("*.sql"))
    sql_files.sort()  # 按文件名排序

    if not sql_files:
        logger.warning(f"文件夹 {folder_name} 中没有SQL文件")
        return f"警告：文件夹 {folder_name} 中没有SQL文件"

    combined_sql = ""

    for sql_file in sql_files:
        try:
            with open(sql_file, "r", encoding="utf-8") as f:
                file_content = f.read()
                # 添加文件名作为注释
                combined_sql += f"-- 文件: {sql_file.name}\n{file_content}\n\n"
                logger.info(f"成功加载文件: {sql_file}")
        except Exception as e:
            error_msg = f"加载文件 {sql_file} 时出错: {str(e)}"
            logger.error(error_msg)
            combined_sql += f"-- {error_msg}\n\n"

    return combined_sql.strip()

# ====================== 回调函数 ======================

def update_sql_input() -> None:
    """下拉框选择回调函数，更新SQL输入区域"""
    selected = st.session_state.sql_example_selector
    # 仅当选择的不是默认选项时更新文本区域
    if selected != "请选择SQL样例":
        if selected.startswith("📁 "):  # 如果是文件夹
            folder_name = sql_examples[selected]  # 获取存储的文件夹名
            st.session_state.sql_input = load_sql_from_folder(folder_name)
        else:  # 如果是文件
            st.session_state.sql_input = sql_examples[selected]
        st.session_state.sql_input_updated = True
        logger.info(f"已更新SQL输入，选择: {selected}")
    st.session_state.previous_example = selected

def load_selected_sql() -> None:
    """加载按钮回调函数，加载选中的SQL样例"""
    selected = st.session_state.sql_example_selector
    if selected != "请选择SQL样例":
        if selected.startswith("📁 "):  # 如果是文件夹
            folder_name = sql_examples[selected]  # 获取存储的文件夹名
            st.session_state.sql_input = load_sql_from_folder(folder_name)
        else:  # 如果是文件
            st.session_state.sql_input = sql_examples[selected]
        st.session_state.sql_input_updated = True
        logger.info(f"已加载SQL样例: {selected}")

def clear_input() -> None:
    """清空输入的回调函数"""
    # 清空文本输入
    st.session_state.sql_input = ""
    # 不直接修改selectbox的值，而是设置一个标志，表示需要重置
    st.session_state.reset_selector = True
    # 更新上一个选择的记录
    st.session_state.previous_example = "请选择SQL样例"
    logger.info("已清空SQL输入")

# ====================== 初始化 ======================

# 加载SQL样例
sql_examples = load_sql_examples()

# 初始化session state
def init_session_state() -> None:
    """初始化所有session state变量"""
    # 初始化SQL相关状态
    if 'previous_example' not in st.session_state:
        st.session_state.previous_example = None
    if 'is_first_load' not in st.session_state:
        st.session_state.is_first_load = True
    if 'sql_input_updated' not in st.session_state:
        st.session_state.sql_input_updated = False
    if 'reset_selector' not in st.session_state:
        st.session_state.reset_selector = False
    
    # 初始化图形相关状态
    if 'dot_source' not in st.session_state:
        st.session_state.dot_source = None
    if 'analysis_completed' not in st.session_state:
        st.session_state.analysis_completed = False
    if 'node_path_lengths' not in st.session_state:
        st.session_state.node_path_lengths = {}
    if 'selected_node' not in st.session_state:
        st.session_state.selected_node = None
    if 'subgraph' not in st.session_state:
        st.session_state.subgraph = None
    if 'subgraph_updated' not in st.session_state:
        st.session_state.subgraph_updated = False
    
    # 初始化显示选项状态
    if 'show_edge_sql' not in st.session_state:
        st.session_state.show_edge_sql = False
    if 'do_pruning' not in st.session_state:
        st.session_state.do_pruning = False
    if 'prev_show_edge_sql' not in st.session_state:
        st.session_state.prev_show_edge_sql = False
    if 'prev_do_pruning' not in st.session_state:
        st.session_state.prev_do_pruning = False
    if 'show_sql_changed' not in st.session_state:
        st.session_state.show_sql_changed = False
    if 'do_pruning_changed' not in st.session_state:
        st.session_state.do_pruning_changed = False

# 初始化session state
init_session_state()

# ====================== 主界面 ======================

# 在侧边栏添加SQL输入区域
with st.sidebar:
    st.header("配置选项")

    cola, colb = st.columns(2)
    with cola:
        # 添加显示边上SQL的开关，并将值存储在session_state中
        st.session_state.show_edge_sql = st.toggle("Show SQL", help="开启后将在血缘图上显示相关的SQL")
    with colb:
        # 添加剪枝功能开关，并将值存储在session_state中
        st.session_state.do_pruning = st.toggle("Do Pruning", help="开启后将启用智能剪枝功能")

    # 检查选项是否发生变化
    st.session_state.show_sql_changed = st.session_state.show_edge_sql != st.session_state.prev_show_edge_sql
    st.session_state.do_pruning_changed = st.session_state.do_pruning != st.session_state.prev_do_pruning

    # 更新前一个状态
    st.session_state.prev_show_edge_sql = st.session_state.show_edge_sql
    st.session_state.prev_do_pruning = st.session_state.do_pruning

    # 如果选项变化且有选中的节点
    if st.session_state.selected_node and st.session_state.subgraph:
        # 如果"Do Pruning"选项变化，需要重新计算子图
        if st.session_state.do_pruning_changed:
            # 重新计算子图，使用当前选中的节点
            st.session_state.subgraph = get_upstream_subgraph(st.session_state.lineage_graph, st.session_state.selected_node)
            st.session_state.subgraph_updated = True
            logger.info(f"选项变化，重新计算子图: {st.session_state.selected_node}")

    st.header("SQL输入")

    # 确定默认索引
    default_index = 0 if st.session_state.reset_selector else 1
    
    # 如果需要重置，则在显示selectbox后重置标志
    if st.session_state.reset_selector:
        st.session_state.reset_selector = False

    # 添加SQL样例下拉菜单，使用on_change来监听变化
    selected_example = st.selectbox(
        "选择预置SQL样例",
        options=list(sql_examples.keys()),
        index=default_index,  # 根据重置标志确定默认索引
        key="sql_example_selector",
        on_change=update_sql_input
    )

    # 首次加载时自动填充SQL
    if st.session_state.is_first_load:
        # 获取index=1对应的SQL样例（假设列表至少有两个元素）
        if len(list(sql_examples.keys())) > 1:
            default_example = list(sql_examples.keys())[1]
            st.session_state.sql_input = sql_examples[default_example]
            st.session_state.is_first_load = False
            logger.info(f"首次加载，自动填充SQL样例: {default_example}")

    # 添加按钮行
    col2, col1 = st.columns(2)
    with col1:
        analyze_button = st.button("构建血缘", key="analyze", use_container_width=True)
    with col2:
        clear_button = st.button("清空输入", key="clear", on_click=clear_input, use_container_width=True)

    # 使用文本区域输入SQL，带有语法高亮
    sql_input = st.text_area(
        "请输入SQL语句:",
        height=300,
        key="sql_input",
        help="输入一个或多个SQL语句进行血缘分析，用空行分隔多个语句"
    )

    # 添加节点路径长度下拉列表
    st.header("选择数据表查看血缘")

    # 显示节点路径长度下拉列表
    if st.session_state.node_path_lengths:
        # 将节点路径长度数据转换为选项列表
        node_options = [f"{node} (路径长度: {length})" for node, length in sorted(st.session_state.node_path_lengths.items(), key=lambda x: x[1])]
        # 添加下拉列表
        selected_node_option = st.selectbox(
            "选择数据表查看其上游血缘关系",
            options=node_options,
            index=0,
            key="node_path_length_selector"
        )

        # 从选定的选项中提取节点名称
        selected_node = selected_node_option.split(" (路径长度:")[0]

        # 如果选定的节点发生变化，重新计算子图
        if selected_node != st.session_state.selected_node:
            st.session_state.selected_node = selected_node
            # 计算子图
            st.session_state.subgraph = get_upstream_subgraph(st.session_state.lineage_graph, selected_node)
            st.session_state.subgraph_updated = True
            logger.info(f"选择新节点，重新计算子图: {selected_node}")
    else:
        st.info("请先构建血缘关系以查看数据表")

    # 如果用户输入了SQL，显示带语法高亮的版本
    if sql_input:
        with st.expander("查看输入的SQL语句", expanded=False):
            st.code(sql_input, language="sql")

    # 添加使用说明
    st.header("使用说明")
    st.markdown("""
    1. 从下拉菜单选择预置SQL样例或在文本框中输入自定义SQL
    2. 点击"构建血缘"按钮
    3. 主页面将显示分析结果和血缘图

    **支持的功能:**
    - 表级血缘分析
    - 智能剪枝优化
    - 可视化显示血缘关系

    **注意事项:**
    - 多个SQL语句请用空行分隔
    - 复杂SQL可能需要更长处理时间
    - 确保SQL语法正确
    """)

    # 添加样例文件管理说明
    st.markdown("""
    **样例文件:**
    样例存储在 `lineage_graph/sql_examples/` 目录下的 `.sql` 文件中。
    您可以添加自己的样例文件到该目录。

    **文件夹功能:**
    选择带有📁前缀的选项将加载该文件夹中的所有SQL文件。
    """)

# ====================== 主页面逻辑 ======================

# 当用户点击分析按钮时
if analyze_button or st.session_state.sql_input_updated:
    with st.spinner("正在分析SQL血缘关系..."):
        try:
            # 重置状态
            st.session_state.subgraph = None
            st.session_state.selected_node = None

            # 检查SQL输入是否为空
            if not sql_input.strip():
                st.warning("请输入SQL语句")
                logger.warning("用户尝试分析空SQL")
            else:
                # 使用 sqlglot 解析 SQL 语句
                sql_statements = build_sql_list_from_str(sql_input)
                
                if not sql_statements:
                    st.warning("未能解析出有效的SQL语句")
                    logger.warning("SQL解析失败")
                else:
                    # 调用血缘分析函数
                    st.session_state.lineage_graph = build_graph(sql_statements=sql_statements)
                    
                    # 计算节点到最下游节点的路径长度
                    node_path_lengths = calculate_node_path_lengths(st.session_state.lineage_graph)
                    # 将结果保存到session state中
                    st.session_state.node_path_lengths = node_path_lengths

                    st.session_state.sql_input_updated = False

                    # 设置一个标志，表示分析已完成
                    st.session_state.analysis_completed = True

                    # 生成血缘图并保存
                    temp_dir = tempfile.mkdtemp()
                    output_file = os.path.join(temp_dir, "lineage_graph.pdf")

                    # 使用visualization模块可视化血缘图
                    dot = visualize_lineage_graph(
                        graph=st.session_state.lineage_graph,
                        output_file=output_file,
                        open_pdf=False,
                        show_sql=False  # 传递是否显示边上SQL的参数
                    )

                    # 在主页面显示结果
                    if dot:
                        # 保存图形源代码到session state
                        st.session_state.dot_source = dot.source
                        logger.info(f"成功生成血缘图，节点数: {len(st.session_state.node_path_lengths)}")
                    else:
                        st.error("生成血缘图失败")
                        logger.error("生成血缘图失败")

        except Exception as e:
            error_msg = f"分析过程中发生错误: {str(e)}"
            st.error(error_msg)
            logger.exception(error_msg)
            st.exception(e)

# 如果分析已完成，触发页面刷新
if st.session_state.analysis_completed:
    st.session_state.analysis_completed = False
    st.rerun()

# 如果有保存的图形数据，显示图形
if st.session_state.dot_source:
    st.success("血缘分析完成！")
    st.info(f"共发现 {len(st.session_state.node_path_lengths)} 个节点")
    try:
        st.subheader("表级血缘关系全图")
        st.graphviz_chart(st.session_state.dot_source, use_container_width=False)
    except Exception as e:
        error_msg = f"显示图像时出错: {e}"
        st.error(error_msg)
        logger.exception(error_msg)

# 如果有子图数据，显示子图
if st.session_state.subgraph is not None and (st.session_state.subgraph_updated or st.session_state.show_sql_changed or st.session_state.do_pruning_changed):
    st.subheader(f"数据表 '{st.session_state.selected_node}' 的上游节点")
    
    try:
        if st.session_state.do_pruning and not st.session_state.show_sql_changed:
            # 创建血缘分析器
            analyzer = LineageAnalyzer()
            lineage_graph, pruned_nodes, downstream_nodes = calculate_lineage_with_pruning(
                    lineage_graph=st.session_state.subgraph,
                    analyzer=analyzer,
                    with_pruning=st.session_state.do_pruning
                )
            logger.info(f"应用剪枝功能，剪枝节点数: {len(pruned_nodes) if pruned_nodes else 0}")
        
        # 生成子图并保存
        temp_dir = tempfile.mkdtemp()
        subgraph_output_file = os.path.join(temp_dir, "subgraph.pdf")

        # 使用visualization模块可视化子图
        subgraph_dot = visualize_lineage_graph(
            graph=st.session_state.subgraph,
            output_file=subgraph_output_file,
            open_pdf=False,
            show_sql=st.session_state.show_edge_sql  # 传递是否显示边上SQL的参数
        )

        if subgraph_dot:
            # 使用graphviz源代码显示子图
            st.graphviz_chart(subgraph_dot.source, use_container_width=False)
            st.session_state.subgraph_updated = False
            logger.info(f"成功显示子图: {st.session_state.selected_node}")
        else:
            st.error("生成子图失败")
            logger.error("生成子图失败")
    except Exception as e:
        error_msg = f"显示子图时出错: {e}"
        st.error(error_msg)
        logger.exception(error_msg)

