INSERT INTO target_table
SELECT 
    order_date
FROM 
    all_orders
WHERE 
    price > 100
    and order_date > '2024-01-01'
    and code = 'S002';

INSERT INTO all_orders
SELECT 
    order_date,
    price
FROM 
    raw_orders
WHERE 
    price < 100;

INSERT INTO all_orders
SELECT 
    order_date,
    code,
    case when code = 'S001' then price 
        else 0 end as price
FROM 
    raw_order4;

INSERT INTO all_orders
SELECT 
    order_date,
    price
FROM 
    raw_orders1
WHERE 
    price > 110
UNION ALL
SELECT 
    order_date,
    raw_price * rate as price
FROM 
    raw_orders3
WHERE 
    raw_price < 50 and rate < 1 and raw_price > 0 and rate > 0;

INSERT INTO all_orders
SELECT 
    order_date,
    'S001' as code,
    price
FROM 
    raw_orders2
    ;

INSERT INTO raw_orders2
SELECT 
    '2024-01-02' as order_date,
    'S001' as code,
    price
FROM 
    source_table
    ;

INSERT INTO raw_orders
SELECT 
    order_date,
    price
FROM 
    source_table
WHERE 
    price > 100;

INSERT INTO raw_orders1
SELECT 
    order_date,
    price
FROM 
    source_raw_orders
WHERE 
    price < 100; 