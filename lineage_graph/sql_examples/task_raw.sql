-- 资本净额
DROP TABLE IF EXISTS tmp.tmp_a;
CREATE TABLE IF not EXISTS tmp.tmp_a  AS(
SELECT t1.SUBJECT_CODE,
sum(case when t1.subject_code LIKE '1001%'          --现金
           OR t1.subject_code LIKE '1003%'          --存放中央银行款项
           OR t1.subject_code LIKE '1011%'          --存放同业款项(银行等)
           OR t1.subject_code LIKE '1012%'          --存放同业款项(证券)
           OR t1.subject_code LIKE '1132%'          --应收利息
           OR t1.subject_code LIKE '1303%'          --贷款
           OR t1.subject_code LIKE '1301%'          --贴现及买断式转贴现
           OR t1.subject_code LIKE '1302%'          --拆放同业
           OR t1.subject_code LIKE '1122%'          --其他应收款\应收账款
           OR t1.subject_code LIKE '1123%'          --其他应收款\预付账款
           OR t1.subject_code LIKE '1031%'          --其他应收款\存出保证金
           OR t1.subject_code LIKE '1221%'          --其他应收款\其他应收款
           OR t1.subject_code LIKE '15010102%'          --持有至到期投资\成本\债券投资
           OR t1.subject_code LIKE '15010302%'          --持有至到期投资\应计利息\债券投资
           OR t1.subject_code LIKE '15030201%'          --可供出售金融资产\债券\成本
           OR t1.subject_code LIKE '15030202%'          --可供出售金融资产\债券\公允价值变动
           OR t1.subject_code LIKE '110102%'          --交易性金融资产\债券投资
           OR t1.subject_code LIKE '110101%'          --交易性金融资产\股票投资
           OR t1.subject_code LIKE '150301%'          --可供出售金融资产\股票
           OR t1.subject_code LIKE '110103%'          --交易性金融资产\基金投资
           OR t1.subject_code LIKE '110107%'          --交易性金融资产\理财产品
           OR t1.subject_code LIKE '111101%'          --买入返售金融资产\买入返售债券
           OR t1.subject_code LIKE '1801%'          --长期待摊费用
           OR t1.subject_code LIKE '1601%'          --固定资产原值
           OR t1.subject_code LIKE '1604%'          --在建工程
           OR t1.subject_code LIKE '1701%'          --无形资产原值
           OR t1.subject_code LIKE '1811%'          --递延所得税资产
           OR t1.subject_code LIKE '1521%'          --投资性房地产
           OR t1.subject_code LIKE '1711%'          --商誉
           OR t1.subject_code LIKE '110108%'          --投资同业存单
           then t1.end_balance * t2.cny_exchange else 0 end )
+sum(case when  t1.subject_code LIKE '1602%'                              --固定资产摊销
             OR t1.subject_code LIKE '1702%'                              --无形资产摊销
             OR t1.subject_code LIKE '1304%'                              --各项资产减值损失准备
             then t1.end_balance * t2.cny_exchange else 0 end )
+sum(case when t1.subject_code LIKE '2012%'                               --单位存款
            OR t1.subject_code LIKE '2011%'                               --同业存放款项
            OR t1.subject_code LIKE '200301%'                               --同业拆入(银行等)
            OR t1.subject_code LIKE '200302%'                               --同业拆入(证券)
            OR t1.subject_code LIKE '211101%'                               --卖出回购款项(银行等)
            OR t1.subject_code LIKE '211102%'                               --卖出回购款项(证券)
            OR t1.subject_code LIKE '2004%'                               --卖出回购款项(中央银行)
            OR t1.subject_code LIKE '2002%'                               --存入保证金
            OR t1.subject_code LIKE '2232%'                               --应付利息
            OR t1.subject_code LIKE '2221%'                               --应交税费（没对上）
            OR t1.subject_code LIKE '2211%'                               --应付职工薪酬
            OR t1.subject_code LIKE '2231%'                               --应付股利息
            OR t1.subject_code LIKE '2241%'                               --其他应付款
            OR t1.subject_code LIKE '2801%'                               --预计负债
            OR t1.subject_code LIKE '2502%'                               --应付债券
             then t1.end_balance * t2.cny_exchange else 0 end ) as a1
from dwd.dwd_fc_alm_occurbalance t1
LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
on t1.currency_code = t2.currency_code
and t2.dt = '********'
WHERE t1.dt = '********'
and t1.subject_name not like '%结转%'
and t1.SUBJECT_CODE = '1011'
);

DROP TABLE IF EXISTS tmp.tmp_b;
CREATE TABLE IF not EXISTS tmp.tmp_b  AS(
 -- 2022-09-30 : 32251934271.946335 0
-- 资产总额,同业拆出比例
    SELECT
        MAX(CASE WHEN t1.INDICATOR_CODE = 'GDT0000120' THEN nvl(t0.D_VALUE,0) END) AS b1,
        MAX(CASE WHEN t1.INDICATOR_CODE = 'GDT0000185' THEN nvl(t0.D_VALUE,0) END) AS b2
FROM odssk.ods_gdtcwdm_gp_indicator_valuecw t0    --每日全量覆盖
LEFT JOIN odssk.ods_gdtcwdm_gp_indicator_infocw t1  --每日全量覆盖
    ON t0.INDICATOR_ID =t1.ID
    WHERE 1=1
      AND VDATE =  FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')
      AND t1.INDICATOR_CODE IN (
                                'GDT0000120'  -- 资产总额
        ,'GDT0000185' -- 同业拆出比例
        )
      AND t0.CURR_CODE ='ZRMB' );

DROP TABLE IF EXISTS tmp.tmp_c;
CREATE TABLE IF not EXISTS tmp.tmp_c  AS(
   -- 2023-09-30 : 401380335.41
-- 承兑汇票余额
    SELECT
        nvl(sum(nvl(AMOUNT,0)),0) AS c1 -- 余额
FROM odscw.ods_cpisms_fb_business 
    WHERE 1=1
	  and dt = '********'
      AND BIZ_TYPE='118' -- 承兑
AND to_date(START_DATE)<= FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')
AND to_date(MATURITY) > FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')
);
DROP TABLE IF EXISTS tmp.tmp_d;
CREATE TABLE IF not EXISTS tmp.tmp_d  AS(
-- --财务公司存款含存入保证金余额(折人民币,境内),承兑保证金存款余额
    select 
    d2,d3
    from (
        (SELECT nvl(SUM(abs(t1.end_balance) * t2.cny_exchange),0)           AS d2 -- 财务公司存款含存入保证金余额(折人民币,境内)
        from dwd.dwd_fc_alm_occurbalance t1
        LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
        on t1.currency_code = t2.currency_code
        and t2.dt = '********'
        WHERE t1.subject_name not like '%结转%'
		    and t1.dt = '********'
        AND (t1.subject_code like '2012%' or t1.subject_code like '2002%'))t1
    left join 
        (SELECT
            nvl(sum(CASE when t1.PROD_TYPE in('********','********') THEN abs(t2.TOTAL_AMOUNT*t3.cny_exchange) END ),0) AS d3 -- 承兑保证金存款余额
        FROM odscw.ods_enscbank_mb_acct                   t1  -- 神码账户主表
                LEFT JOIN odscw.ODS_ENSCBANK_MB_ACCT_BALANCE_HIST  t2  -- 余额表
                        ON t1.internal_key = t2.internal_key
						and t2.dt = '********'
                LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t3
                        ON t1.ccy = t3.currency_code
                            and t3.dt = '********'
        WHERE
                t2.AMT_TYPE = 'BAL' and t1.dt = '********') t2
    on 1=1
)
);
-- 同业拆入
DROP TABLE IF EXISTS tmp.tmp_e;
CREATE TABLE IF not EXISTS tmp.tmp_e  AS(
    SELECT
        nvl(sum(amount),0) e1
    FROM odscw.ods_cpisms_stl_withholding
    WHERE 1=1
	  and dt = '********'
      AND STARTDATE = FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')
);
DROP TABLE IF EXISTS tmp.tmp_i;
CREATE TABLE IF not EXISTS tmp.tmp_i  AS(
    SELECT SUM(t1.end_balance * t2.cny_exchange)           AS i1
        from dwd.dwd_fc_alm_occurbalance t1
        LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
        on t1.currency_code = t2.currency_code
        AND t2.dt = '********'
    WHERE subject_name not like  '%结转%'
      and (subject_code like '1011%'
      or subject_code like '1012%')
	  and t1.SUBJECT_CODE <> '1011'
      AND t1.dt = '********'
);
DROP TABLE IF EXISTS tmp.tmp_f;
CREATE TABLE IF not EXISTS tmp.tmp_f AS ( -- 2023-09-30 : 0,387584284.08,7491782250
-- 卖出回购,固定资产净额,实收资本
    SELECT
        nvl(sum(CASE WHEN (t1.subject_code like '211101%' or t1.subject_code like '211102%' or t1.subject_code like '2004%') THEN abs(end_balance) * t2.cny_exchange END),0) AS f1 -- 卖出回购
         ,nvl(sum(CASE WHEN t1.subject_code like '1601%' THEN  abs(end_balance) * t2.cny_exchange
                          WHEN t1.subject_code like '1602%' THEN -abs(end_balance) * t2.cny_exchange
        END),0)                                                                                     AS f2 -- 固定资产净额
         ,7500000000                     AS f3 -- 实收资本
        from dwd.dwd_fc_alm_occurbalance t1
        LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
        on t1.currency_code = t2.currency_code
        AND t2.dt = '********'
    where t1.dt = '********'
    and subject_name not like  '%结转%')
	;

-- 转贴现总额
DROP TABLE IF EXISTS tmp.tmp_g;
CREATE TABLE IF not EXISTS tmp.tmp_g AS ( -- 0
    SELECT 0 AS g1
    FROM DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT
	limit 1
);
-- 2023-09-30 投资总余额 1045002.142021
DROP TABLE IF EXISTS tmp.tmp_h;
CREATE TABLE IF not EXISTS tmp.tmp_h as (
SELECT
SUM(PRINCIPAL) AS h1
FROM (SELECT
BIZ_DATE
,BUSINESS_CODE --大类
,BUSINESS_NAME --大类
,PROD_CLASS_CODE --类型
,PROD_CLASS --类型
,PRINCIPAL    --成本
,MARKET_VAL --市值
FROM odscw.ODS_CPISMS_BIFS_INVEST_PRO
WHERE 1=1
    and dt = '********'
    AND BIZ_DATE = FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')
	  )t
	 );

-- a  资本净额
-- b  资产总额,同业拆出比例,投资总额（成本）
-- c  承兑汇票余额
-- d  财务公司存放同业款项（业务口径）,--财务公司存款含存入保证金余额(折人民币,境内),承兑保证金存款余额
-- e  同业拆入
-- f  卖出回购,固定资产净额,实收资本
-- g  转贴现总额


-- 3.1承兑汇票占存放同业款项比例=承兑汇票余额/财务公司存放同业款项（业务口径）×100%

CREATE TABLE IF NOT EXISTS dws.dws_dis_risk_control_index_hist(
data_date   date COMMENT '日期',
zb_value    DECIMAL(18,6) COMMENT '指标值',
zb_name     VARCHAR(64)   COMMENT '指标名称'
)comment '风险监控指标——历史数据'
PARTITIONED BY (dt string)
ROW FORMAT DELIMITED FIELDS TERMINATED BY '\027'
STORED AS ORC;

INSERT OVERWRITE TABLE  dws.dws_dis_risk_control_index_hist PARTITION (dt = '********')
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
a1,
'资本净额' as zb_name
from tmp.tmp_a
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
b1,
'资产总额' as zb_name
from tmp.tmp_b
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
b2,
'同业拆出比例' as zb_name
from tmp.tmp_b
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
c1,
'承兑汇票余额' as zb_name
from tmp.tmp_c
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
d2,
'财务公司存款含存入保证金余额' as zb_name
from tmp.tmp_d
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
d3,
'承兑保证金存款余额' as zb_name
from tmp.tmp_d
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
e1,
'同业拆入' as zb_name
from tmp.tmp_e
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
i1,
'存放同业' as zb_name
from tmp.tmp_i
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
f1,
'卖出回购' as zb_name
from tmp.tmp_f
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
f2,
'固定资产净额' as zb_name
from tmp.tmp_f
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
f3,
'实收资本' as zb_name
from tmp.tmp_f
union all
select
FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd')             AS data_date,
h1,
'投资总余额' as zb_name
from tmp.tmp_h
;




--CREATE TABLE IF NOT EXISTS dm.dm_dis_risk_control_index_hist(
--data_date   date COMMENT '日期',
--zb_name     VARCHAR(64) COMMENT '指标名称',
--zb_value    DECIMAL(18,6) COMMENT '指标值',
--threshold  VARCHAR(64) COMMENT '范围',
--yuzhi       VARCHAR(64) COMMENT '阈值',
--)comment '风险监控指标——历史数据'
--PARTITIONED BY (dt string)
--ROW FORMAT DELIMITED FIELDS TERMINATED BY '\027'
--STORED AS ORC;

INSERT OVERWRITE TABLE  dm.dm_dis_risk_control_index_hist PARTITION (dt = '********')
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'承兑汇票占存放同业款项比例' as indicator_name
     ,CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end as indicator_value
     ,'≤3' as threshold
     ,case
          when (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end)>=0   and  (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end) <=2.7 then '1'
          when (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end)>2.7  and  (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end) <=2.8 then '2'
          when (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end)>2.8  and  (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end) <=3   then '3'
          when (CASE WHEN i1 <> 0 THEN round(100*c1/i1,2) else 0 end)>3                                                                      then '4'
    end as gear
FROM tmp.tmp_c tc
         LEFT JOIN
     tmp.tmp_i ti
     ON 1=1
UNION ALL

-- 3.2集团外负债比例=集团外负债总额/资本净额×100%
-- 集团外负债总额=同业拆入+卖出回购
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'集团外负债比例' as indicator_name
     ,CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end    as indicator_value
     ,'≤100%' as threshold
     ,case
          when (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end)>=0   and  (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end) <=60 then '1'
          when (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end)>60  and  (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end) <=85  then '2'
          when (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end)>85 and  (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end) <=100  then '3'
          when (CASE WHEN a1 <> 0 THEN round(100*(e1+f1)/a1,2) else 0 end)>100                                                                        then '4'
    end as gear
FROM tmp.tmp_e te
         LEFT JOIN tmp.tmp_f tf
                   ON 1=1
         LEFT JOIN
     tmp.tmp_a ta
     ON 1=1
UNION ALL

-- 3.3承兑汇票占资产比例=承兑汇票余额/资产总额×100%
SELECT
     FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'承兑汇票占资产比例' as indicator_name
     ,CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end    as indicator_value
     ,'≤15%' as threshold
     ,case
          when (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end)>=0   and  (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end) <=11 then '1'
          when (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end)>11 and  (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end) <=13   then '2'
          when (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end)>13 and  (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end) <=15   then '3'
          when (CASE WHEN b1 <> 0 THEN round(100*c1/b1,2) else 0 end)>15                                                                    then '4'
    end as gear
FROM tmp.tmp_c tc
         LEFT JOIN
     tmp.tmp_b tb
     ON 1=1
UNION ALL

-- 3.4票据承兑和转贴现总额占资本净额比例=（承兑汇票余额+转贴现总额）/资本净额×100%
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'票据承兑和转贴现总额占资本净额比例' as indicator_name
     ,CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end   as indicator_value
     ,'≤100%' as threshold
     ,case
          when (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end)>=0   and  (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end) <=80 then '1'
          when (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end)>80  and  (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end) <=90  then '2'
          when (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end)>90  and  (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end) <=100 then '3'
          when (CASE WHEN a1 <> 0 THEN round(100*(c1+g1)/a1,2) else 0 end)>100                                                                        then '4'
    end as gear
FROM tmp.tmp_c tc
         LEFT JOIN tmp.tmp_g tg
                   ON 1=1
         LEFT JOIN tmp.tmp_a ta
                   ON 1=1
where ta.subject_code <> '1011'
UNION ALL

-- 3.5承兑保证金存款占吸收存款比例=承兑保证金存款余额/财务公司存款含存入保证金余额(折人民币,境内)×100%
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'承兑保证金存款占吸收存款比例' as indicator_name
     ,CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end    as indicator_value
     ,'≤10%' as threshold
     ,case
          when (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end)>=0   and  (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end) <=8 then '1'
          when (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end)>8  and  (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end) <=9   then '2'
          when (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end)>9  and  (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end) <=10  then '3'
          when (CASE WHEN d2 <> 0 THEN round(100*d3/d2,2) else 0 end)>10                                                                   then '4'
    end as gear
FROM tmp.tmp_d td
UNION ALL

-- 3.6固定资产比例=固定资产净额／资本净额×100%
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'固定资产比例' as indicator_name
     ,CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end    as indicator_value
     ,'≤20%' as threshold
     ,case
          when (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end)>=0   and  (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end) <=15 then '1'
          when (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end)>15  and  (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end) <=16  then '2'
          when (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end)>16  and  (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end) <=20  then '3'
          when (CASE WHEN a1 <> 0 THEN round(100*f2/a1,2) else 0 end)>20                                                                    then '4'
    end as gear
FROM tmp.tmp_f tf
         LEFT JOIN tmp.tmp_a ta
                   ON 1=1
UNION ALL

-- 3.7投资比例=投资总额（成本）/资本净额×100%  【投资总额（成本） s3d 的品种取业务明细。不要同业存单】
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'投资比例' as indicator_name
     ,CASE WHEN a1 <> 0 THEN round(100*h1/(a1+700000000),2) else 0 end    as indicator_value
     ,'≤70%' as threshold
     ,case
          when (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end)>=0   and  (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end) <=68 then '1'
          when (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end)>68 and  (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end) <=69   then '2'
          when (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end)>69  and  (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end) <=70  then '3'
          when (CASE WHEN a1 <> 0 THEN round(100*h1/a1,2) else 0 end)>70                                                                    then '4'
    end as gear
FROM tmp.tmp_h tb
         LEFT JOIN tmp.tmp_a ta
                   ON 1=1
UNION ALL

-- 3.8同业拆入比例(人民币)=同业拆入/实收资本×100%
SELECT
     FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'同业拆入比例(人民币)' as indicator_name
     ,CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end    as indicator_value
     ,'≤100%' as threshold
     ,case
          when (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end)>=0   and  (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end) <=90  then '1'
          when (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end)>90 and  (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end) <=95    then '2'
          when (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end)>95  and  (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end) <=100  then '3'
          when (CASE WHEN f3 <> 0 THEN round(100*e1/f3,2) else 0 end)>100                                                                    then '4'
    end as gear
FROM tmp.tmp_e te
         LEFT JOIN tmp.tmp_f tf
                   ON 1=1
UNION ALL

-- 3.9同业拆出比例(人民币)=同业拆出期末余额/实收资本×100%
SELECT
     FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'同业拆出比例(人民币)'     as indicator_name
     ,nvl(round(b2*100,2),0)     as indicator_value
     ,'≤100%' as threshold
     ,case
          when (round(b2*100,2))>=0   and  (round(b2*100,2)) <=60 then '1'
          when (round(b2*100,2))>60 and  (round(b2*100,2)) <=85   then '2'
          when (round(b2*100,2))>85  and  (round(b2*100,2)) <=100 then '3'
          when (round(b2*100,2))>100                              then '4'
    end as gear
FROM tmp.tmp_b tb
UNION ALL

-- 3.10担保比例=承兑汇票余额/资本净额
SELECT
    FROM_UNIXTIME(UNIX_TIMESTAMP('********','yyyymmdd'),'yyyy-mm-dd') as data_date
     ,'担保比例' as indicator_name
     ,CASE WHEN a1 <> 0 THEN round(100*c1/(a1+700000000),2) else 0 end    as indicator_value
     ,'≤100%' as threshold
     ,'1' as gear
FROM tmp.tmp_c tc
         LEFT JOIN tmp.tmp_a ta
                   ON 1=1
;