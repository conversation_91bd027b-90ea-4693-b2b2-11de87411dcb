INSERT INTO ads_order_kpi_report
SELECT 
    order_creation_date
FROM 
    dws_order_summary_daily
WHERE 
    order_amount between 100 and 200
    and order_creation_date > '2024-01-01'
    and shipment_date LIKE '2024-03-%'
    and product_sku = 'S001'
    and shipment_status IS NOT NULL 
    and is_deleted IS NULL
    ;

INSERT INTO dws_order_summary_daily
WITH all_orders as (
    SELECT 
        order_creation_date,
        order_amount,
        shipment_date,
        shipment_status,
        is_deleted,
        product_sku
    FROM 
        dwd_order_item_detail
    WHERE 
        sales_tax_amount > 10 
) 
SELECT 
    order_creation_date,
        order_amount,
        shipment_date,
        shipment_status,
        is_deleted,
        product_sku
FROM all_orders;

INSERT INTO dwd_order_item_detail (
    order_creation_date,
    order_amount,
    shipment_date,
    shipment_status,
    is_deleted,
    product_sku,
    sales_tax_amount
)
SELECT 
    order_date,
    order_amt,
    s_date,
    s_status,
    is_del,
    prd_sku,
    tax_amount
FROM 
    ods_order_details_incr
WHERE 
    order_amt > 100
    and tax_amount > 10;

INSERT INTO dwd_order_item_detail
SELECT 
    order_creation_date,
    product_sku,
    case when product_sku = 'S002' then order_amount 
        else 102 end as order_amount
FROM 
    ods_order_promotion_info;

INSERT INTO dwd_order_item_detail
SELECT 
    order_creation_date,
    original_price as order_amount,
sales_tax_amount
FROM 
    ods_order_tax_info
WHERE 
    sales_tax_amount < 5
UNION
SELECT 
    order_creation_date,
    original_price * exchange_rate as order_amount
FROM 
    ods_order_exchange_rate
WHERE 
    original_price < 50 and exchange_rate < 1 and original_price > 0 and exchange_rate > 0;

INSERT INTO dwd_order_item_detail
SELECT 
    order_creation_date,
    'YES' as is_deleted,
    shipment_date,
    order_amount
FROM 
    ods_order_return_records
    ;

INSERT INTO ods_order_details_incr
SELECT 
    '2024-01-02' as order_date,
    s_date,
    order_amt
FROM 
    src_ecommerce_order_raw
WHERE 
    s_date < '2024-02-20'
    ;

INSERT INTO ods_order_details_incr
SELECT 
    order_date,
    s_status,
    prd_sku,
    order_amt
FROM 
    src_ecommerce_order_raw
HAVING s_status IS NULL;
