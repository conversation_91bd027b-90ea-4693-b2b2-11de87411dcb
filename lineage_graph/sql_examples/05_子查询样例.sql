INSERT INTO ADS_SCHEMA.FINANCIAL_SUMMARY 
SELECT 
    t1.D2 AS BALANCE_CNY,
    t1.D3 AS SPECIAL_PROD_AMOUNT,
    ROUND(t1.D2 / NULLIF(t1.D3, 0) * 100, 2) AS RATIO
FROM DWD_SCHEMA.TARGET_TABLE t1
WHERE t1.D2 > 0 
    AND t1.D3 LIKE 's001%';

INSERT INTO DWD_SCHEMA.TARGET_TABLE 
SELECT 
    t1.D2,
    t2.D3
FROM (
    SELECT NVL(SUM(ABS(t1.END_BALANCE) * t2.CNY_EXCHANGE), 0) AS D2
    FROM DWD.DWD_FC_ALM_OCCURBALANCE t1
    LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t2
        ON t1.CURRENCY_CODE = t2.CURRENCY_CODE
        AND t2.DT = '********'
    WHERE t1.SUBJECT_NAME NOT LIKE '%结转%'
        AND t1.DT = '********'
        AND (t1.SUBJECT_CODE LIKE '2012%' OR t1.SUBJECT_CODE LIKE '2002%')
    HAVING D2 > 0
) t1
LEFT JOIN (
    SELECT NVL(SUM(
        CASE 
            WHEN t1.PROD_TYPE IN ('********','********') 
            THEN ABS(t2.TOTAL_AMOUNT * t3.CNY_EXCHANGE) 
        END), 0) AS D3
    FROM ODSCW.ODS_ENSCBANK_MB_ACCT t1
    LEFT JOIN ODSCW.ODS_ENSCBANK_MB_ACCT_BALANCE_HIST t2
        ON t1.INTERNAL_KEY = t2.INTERNAL_KEY
        AND t2.DT = '********'
    LEFT JOIN DWD.DWD_PI_MKI_CURRENCY_EXCHANGE_STAT t3
        ON t1.CCY = t3.CURRENCY_CODE
        AND t3.DT = '********'
    WHERE t2.AMT_TYPE = 'BAL' 
        AND t1.DT = '********'
    HAVING D3 = 's0011'
) t2 ON 1=1;
