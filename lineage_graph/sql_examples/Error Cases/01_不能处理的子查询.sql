INSERT INTO ads_order_kpi_report
SELECT 
    order_creation_date
FROM 
    dws_order_summary_daily
WHERE 
    order_amount between 100 and 200
    and order_creation_date > '2024-01-01'
    and shipment_date LIKE '2024-03-%'
    and product_sku = 'S001'
    and shipment_status IS NOT NULL 
    and is_deleted IS NULL
    ;

INSERT INTO dws_order_summary_daily
WITH all_orders as (
    SELECT 
        order_creation_date,
        order_amount,
        shipment_date,
        shipment_status,
        is_deleted,
        product_sku
    FROM 
        dwd_order_item_detail
    WHERE 
        sales_tax_amount > 10 
) 
SELECT 
    order_creation_date,
        order_amount,
        shipment_date,
        shipment_status,
        is_deleted,
        product_sku
FROM all_orders;

-- CASE 中包含子查询，无法处理, 当前返回 True， 可能造成非预期结果
INSERT INTO dwd_order_item_detail
SELECT 
    order_creation_date,
    product_sku,
    case when product_sku = 'S002' then order_amount 
        when product_sku = 'S001' then (select a from table_a)
        else 102 end as order_amount
FROM 
    ods_order_promotion_info;
