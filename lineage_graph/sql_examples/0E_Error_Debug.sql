-- 3. 投资组合分析 (DWS_ASSET_SUMMARY → ADS_INVESTMENT_PORTFOLIO)
INSERT INTO ads_investment_portfolio (
    analysis_date, portfolio_allocation, performance_metrics, recommendation
)
WITH asset_daily_data AS (
    SELECT 
        summary_date,
        asset_category,
        total_value,
        COALESCE(daily_change, 0) AS daily_change,
        COALESCE(daily_change_pct, 0) AS daily_change_pct
    FROM 
        dws_asset_summary
    WHERE 
        summary_date >= CURRENT_DATE - INTERVAL '30 days'
),
asset_max_date AS (
    SELECT 
        asset_category,
        MAX(summary_date) AS latest_date
    FROM 
        asset_daily_data
    GROUP BY 
        asset_category
),
asset_latest_data AS (
    SELECT DISTINCT ON (add.asset_category)
        add.asset_category,
        add.total_value,
        add.daily_change,
        add.daily_change_pct,
        add.summary_date
    FROM 
        asset_daily_data add
    JOIN 
        asset_max_date amd ON add.asset_category = amd.asset_category 
        AND add.summary_date = amd.latest_date
    ORDER BY 
        add.asset_category, add.summary_date DESC
),
portfolio_total AS (
    SELECT 
        summary_date,
        SUM(total_value) AS total_value,
        SUM(daily_change) AS daily_change,
        (SUM(daily_change) * 100.0 / NULLIF(SUM(total_value) - SUM(daily_change), 0))::NUMERIC(5,2) AS daily_change_pct
    FROM 
        asset_daily_data
    GROUP BY 
        summary_date
),
asset_allocation_data AS (
    SELECT 
        add.summary_date,
        add.asset_category,
        add.total_value,
        pt.total_value,
        (add.total_value * 100.0 / pt.total_value)::NUMERIC(5,2) AS percentage
    FROM 
        asset_daily_data add
    JOIN 
        portfolio_total pt ON add.summary_date = pt.summary_date
),
allocation_json AS (
    SELECT 
        summary_date AS analysis_date,
        jsonb_object_agg(
            asset_category,
            percentage
        ) AS allocation_data
    FROM 
        asset_allocation_data
    GROUP BY 
        summary_date
),
performance_summary AS (
    SELECT 
        summary_date,
        SUM(total_value) AS total_value,
        SUM(daily_change) AS daily_change,
        (SUM(daily_change) * 100.0 / NULLIF(SUM(total_value) - SUM(daily_change), 0))::NUMERIC(5,2) AS daily_change_pct
    FROM 
        asset_daily_data
    GROUP BY 
        summary_date
),
best_asset AS (
    SELECT DISTINCT
        asset_category,
        daily_change_pct
    FROM 
        asset_daily_data
    ORDER BY 
        daily_change_pct DESC
    LIMIT 1
),
worst_asset AS (
    SELECT DISTINCT
        asset_category,
        daily_change_pct
    FROM 
        asset_daily_data
    ORDER BY 
        daily_change_pct ASC
    LIMIT 1
),
performance_json AS (
    SELECT 
        ps.summary_date,
        jsonb_build_object(
            'total_value', ps.total_value,
            'daily_change', ps.daily_change,
            'daily_change_pct', ps.daily_change_pct,
            'best_performing', (SELECT asset_category FROM best_asset),
            'worst_performing', (SELECT asset_category FROM worst_asset)
        ) AS performance_data
    FROM 
        performance_summary ps
),
high_concentration AS (
    SELECT 
        COUNT(*) > 0 AS exists
    FROM 
        asset_allocation_data
    WHERE 
        percentage > 40
),
high_risk_allocation AS (
    SELECT 
        SUM(percentage) > 60 AS exists
    FROM 
        asset_allocation_data
    WHERE 
        asset_category IN ('股票', '基金')
),
low_risk_allocation AS (
    SELECT 
        SUM(percentage) > 80 AS exists
    FROM 
        asset_allocation_data
    WHERE 
        asset_category IN ('存款', '债券')
),
opportunity_asset AS (
    SELECT 
        asset_category
    FROM 
        asset_daily_data
    WHERE 
        daily_change_pct < -2
    ORDER BY 
        daily_change_pct ASC
    LIMIT 1
),
recommendation_json AS (
    SELECT 
        jsonb_build_object(
            'rebalance_suggestion', CASE 
                WHEN (SELECT exists FROM high_concentration) THEN '建议分散投资，降低单一资产类别占比'
                ELSE '当前资产配置较为均衡'
            END,
            'risk_assessment', CASE 
                WHEN (SELECT exists FROM high_risk_allocation) THEN '当前风险较高，建议适当增加低风险资产'
                WHEN (SELECT exists FROM low_risk_allocation) THEN '当前风险较低，可考虑适当增加收益型资产'
                ELSE '当前风险适中'
            END,
            'opportunity', (SELECT asset_category FROM opportunity_asset)
        ) AS recommendation_data
    FROM 
        asset_daily_data
    LIMIT 1
)
SELECT 
    aj.analysis_date,
    aj.allocation_data AS portfolio_allocation,
    pj.performance_data AS performance_metrics,
    (SELECT recommendation_data FROM recommendation_json) AS recommendation
FROM 
    allocation_json aj
JOIN 
    performance_json pj ON aj.analysis_date = pj.summary_date;

-- 3. 资产汇总表 (DWD_ACCOUNT_ASSET → DWS_ASSET_SUMMARY)
INSERT INTO dws_asset_summary (
    summary_date, asset_category, total_value, 
    daily_change, daily_change_pct, customer_count
)
WITH current_assets AS (
    SELECT 
        CURRENT_DATE AS report_date,
        daa.account_type AS asset_category,
        SUM(daa.total_asset_value) AS total_value,
        COUNT(DISTINCT daa.cust_id) AS customer_count
    FROM 
        dwd_account_asset daa
    GROUP BY 
        daa.account_type
),
previous_assets AS (
    SELECT 
        das.asset_category,
        das.total_value AS previous_value
    FROM 
        dws_asset_summary das
    WHERE 
        das.summary_date = CURRENT_DATE - INTERVAL '1 day'
)
SELECT 
    ca.report_date AS summary_date,
    ca.asset_category,
    ca.total_value,
    COALESCE(ca.total_value - pa.previous_value, 0) AS daily_change,
    001 AS daily_change_pct,
    ca.customer_count
FROM 
    current_assets ca
LEFT JOIN 
    previous_assets pa ON ca.asset_category = pa.asset_category
WHERE 
    NOT EXISTS (
        SELECT 1 FROM dws_asset_summary das 
        WHERE das.summary_date = ca.report_date
        AND das.asset_category = ca.asset_category
    );