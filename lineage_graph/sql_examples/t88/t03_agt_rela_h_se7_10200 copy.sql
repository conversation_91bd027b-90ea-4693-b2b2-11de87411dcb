INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  CODE,
  'SE3',
  '001',
  CAST('2026-01-01' AS DATE),
  '104',
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  'SE7',
  '0021',
  R<PERSON><PERSON>PP<PERSON>YRA<PERSON>,
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_LOAN_CREDIT',
  202
FROM ODB.SE7_GC_LOAN_CREDIT
;

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  CODE,
  'SE7',
  '001',
  CAST('2026-01-01' AS DATE),
  '104',
  LIMITCODE,
  'SE001',
  '0021',
  REALAPPLYRATE,
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_LOAN_CREDIT',
  202
FROM ODB.SE7_GC_LOAN_CREDIT_02
;

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_INC_202
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
FROM tmp_t03_agt_rela_h_se7_10200.VT_NEW_202
;

INSERT INTO PDB.T03_AGT_RELA_H
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
FROM tmp_t03_agt_rela_h_se7_10200.VT_INC_202
WHERE
  END_DT <> CAST('0001-01-01' AS DATE);
