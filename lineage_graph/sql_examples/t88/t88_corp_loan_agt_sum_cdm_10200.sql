INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP5 (
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  OVDUE_BEGIN_DT, /* 逾期起始日期 */
  PRIN_OVDUE_BEGIN_DT, /* 本金逾期起始日期 */
  OWE_INT_BEGIN_DT /* 欠息起始日期  */
)
SELECT
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  CASE
    WHEN OVDUE_BEGIN_DT = '********'
    THEN CAST('3000-12-31' AS DATE)
    ELSE OVDUE_BEGIN_DT
  END,
  CASE
    WHEN PRIN_OVDUE_BEGIN_DT = '********'
    THEN CAST('3000-12-31' AS DATE)
    ELSE PRIN_OVDUE_BEGIN_DT
  END,
  CASE
    WHEN OWE_INT_BEGIN_DT = '********'
    THEN CAST('3000-12-31' AS DATE)
    ELSE OWE_INT_BEGIN_DT
  END
FROM PDB.T03_CORP_LOAN_DUBIL
WHERE
  START_DT <= '********' AND END_DT > '********';

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_ACTV_PRE (
  ACCT_NUM, /* 协议号 */
  CONTRACT_NO /* 合同号 */
)
SELECT
  T1.ACCT_NUM,
  T2.CNTR_NO
FROM PDB.T03_CORP_LOAN_DUBIL AS T1
INNER JOIN ODB.SDI_ACCOUNT_MAIN AS T2 /* 借据信息表 */
  ON T1.ACCT_NUM = T2.LOAN_NO AND T2.START_DT <= '********' AND T2.END_DT > '********'
WHERE
  T1.CORP_LOAN_BREED_CD = '5901'
  AND T1.START_DT <= '********'
  AND T1.END_DT > '********';


INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP2 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  CURRENCY_CD,
  REMAIN_PRIN,
  AGENT_CAP,
  NORMAL_PRIN,
  OVDUE_PRIN,
  IDLE_PRIN,
  BAD_DEBT_PRIN,
  AGENT_NORMAL_PRIN,
  AGENT_OVDUE_PRIN,
  AGENT_IDLE_PRIN,
  AGENT_BAD_DEBT_PRIN
)
SELECT
  T1.ACCT_NUM, /* 协议号 */
  T1.AGT_MODIF_NUM, /* 协议修饰符 */
  MAX(CASE WHEN AMT_TYPE_CD = '001' THEN T1.CURRENCY_CD ELSE '' END),
  SUM(CASE WHEN AMT_TYPE_CD = '001' THEN AGT_AMT ELSE 0 END), /* 剩余本金 */
  SUM(CASE WHEN AMT_TYPE_CD = '007' THEN AGT_AMT ELSE 0 END), /* 代理资金 */
  SUM(CASE WHEN AMT_TYPE_CD = '003' THEN AGT_AMT ELSE 0 END), /* 正常本金 */
  SUM(CASE WHEN AMT_TYPE_CD = '004' THEN AGT_AMT ELSE 0 END), /* 逾期本金 */
  SUM(CASE WHEN AMT_TYPE_CD = '005' THEN AGT_AMT ELSE 0 END), /* 呆滞本金 */
  SUM(CASE WHEN AMT_TYPE_CD = '006' THEN AGT_AMT ELSE 0 END), /* 呆账本金 */
  SUM(CASE WHEN AMT_TYPE_CD = '008' THEN AGT_AMT ELSE 0 END), /* 正常本金（代理资金） */
  SUM(CASE WHEN AMT_TYPE_CD = '009' THEN AGT_AMT ELSE 0 END), /* 逾期本金（代理资金） */
  SUM(CASE WHEN AMT_TYPE_CD = '010' THEN AGT_AMT ELSE 0 END), /* 呆滞本金（代理资金） */
  SUM(CASE WHEN AMT_TYPE_CD = '011' THEN AGT_AMT ELSE 0 END) /* 呆账本金（代理资金） */
FROM PDB.T03_AGT_AMT_H AS T1
WHERE
  AMT_TYPE_CD IN ('001', '007', '003', '004', '005', '006', '008', '009', '010', '011')
  AND AGT_TYPE_CD = '104'
  AND START_DT <= '********'
  AND END_DT > '********'
GROUP BY
  1,
  2;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP3 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  IN_BS_PUNISH_INT,
  OFF_BS_PUNISH_INT,
  IN_BS_OWE_INT,
  OFF_BS_OWE_INT,
  AGENT_IN_BS_PUNISH_INT,
  AGENT_OFF_BS_PUNISH_INT,
  AGENT_IN_BS_OWE_INT,
  AGENT_OFF_BS_OWE_INT
)
SELECT
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '904' THEN INT_AMT ELSE 0 END), /* 表内罚息 */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '906' THEN INT_AMT ELSE 0 END), /* 表外罚息 */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '903' THEN INT_AMT ELSE 0 END), /* 表内欠息 */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '905' THEN INT_AMT ELSE 0 END), /* 表外欠息              已还利息总额 */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '909' THEN INT_AMT ELSE 0 END), /* 表内罚息（代理资金） */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '911' THEN INT_AMT ELSE 0 END), /* 表外罚息（代理资金） */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '908' THEN INT_AMT ELSE 0 END), /* 表内欠息（代理资金） */
  SUM(CASE WHEN INT_AMT_TYPE_CD = '910' THEN INT_AMT ELSE 0 END) /* 表外欠息（代理资金） */
FROM PDB.T03_AGT_INT_H
WHERE
  INT_AMT_TYPE_CD IN ('904', '906', '903', '905', '909', '911', '908', '910')
  AND AGT_TYPE_CD = '104'
  AND START_DT <= '********'
  AND END_DT > '********'
GROUP BY
  1,
  2;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_LOAN_REPAY_PLAN_H (
  ACCT_NUM,
  AGT_MODIF_NUM,
  PAID_INT_TOTAL_AMT
)
SELECT
  LOAN_NO,
  'SDI104',
  SUM(FLOW_AMT)
FROM ODB.SDI_PAYM_SCHED
WHERE
  START_DT <= '********'
  AND END_DT > '********'
  AND FLOW_TYP IN ('3210', '3220', '3230')
  AND REVSE_IND <> 'Y' /* 排除冲销类交易 */
GROUP BY
  1,
  2;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_PUNISH_INT_BOOK (
  ACCT_NUM,
  AGT_MODIF_NUM,
  PAID_PUNISH_INT_TOTAL_AMT
)
SELECT
  LOAN_NO,
  'SDI104',
  SUM(FLOW_AMT)
FROM ODB.SDI_PAYM_SCHED
WHERE
  START_DT <= '********'
  AND END_DT > '********'
  AND REVSE_IND <> 'Y' /* 排除冲销类交易 */
  AND FLOW_TYP IN ('3280', '3281', '3282', '4280', '4281', '4282', '3288')
GROUP BY
  1,
  2;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP6 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  LOAN_MONTH_DAY_AVG,
  LOAN_QUAR_DAY_AVG,
  LOAN_YEAR_DAY_AVG,
  LOAN_LATE_3MONTH_DAY_AVG,
  LOAN_LATE_YEAR_DAY_AVG,
  NORMAL_PRIN_M_DAY_AVG, /* 正常本金月日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_M_DAY_AVG, /* 逾期本金月日均 ADD JIANGLEI ******** */
  IDLE_PRIN_M_DAY_AVG, /* 呆滞本金月日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_M_DAY_AVG, /* 呆账本金月日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Q_DAY_AVG, /* 正常本金季日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Q_DAY_AVG, /* 逾期本金季日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Q_DAY_AVG, /* 呆滞本金季日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Q_DAY_AVG, /* 呆账本金季日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Y_DAY_AVG, /* 正常本金年日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Y_DAY_AVG, /* 逾期本金年日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Y_DAY_AVG, /* 呆滞本金年日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Y_DAY_AVG /* 呆账本金年日均 ADD JIANGLEI ******** */
)
SELECT
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '001'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 贷款月日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '001'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 贷款季日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '001'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 贷款年日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '001'
      THEN (
        CASE
          WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
          AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3) + 1
          )
          WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
          AND END_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
          AND END_DT <= '********'
          THEN AGT_AMT * (
            END_DT - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
          )
          WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
          AND START_DT <= '********'
          AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
          AND START_DT <= '********'
          AND END_DT <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3) + 1
  ) /* 贷款近三个月日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '001'
      THEN (
        CASE
          WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
          AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12) + 1
          )
          WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
          AND END_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
          AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
          )
          WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
          AND START_DT <= '********'
          AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
          AND START_DT <= '********'
          AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12) + 1
  ) /* 贷款近一年日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '003'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 正常本金月日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '004'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 逾期本金月日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '005'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 呆滞本金月日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '006'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 呆账本金月日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '003'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 正常本金季日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '004'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 逾期本金季日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '005'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 呆滞本金季日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '006'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 呆账本金季日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '003'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 正常本金年日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '004'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 逾期本金年日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '005'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 呆滞本金年日均 */,
  SUM(
    CASE
      WHEN AMT_TYPE_CD = '006'
      THEN (
        CASE
          WHEN START_DT <= '********' AND END_DT > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - CAST('********' AS DATE) + 1
          )
          WHEN START_DT <= '********' AND END_DT > '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - CAST('********' AS DATE)
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt > '********'
          THEN AGT_AMT * (
            CAST('********' AS DATE) - START_DT + 1
          )
          WHEN START_DT > '********' AND START_DT <= '********' AND End_Dt <= '********'
          THEN AGT_AMT * (
            END_DT - START_DT
          )
          ELSE 0
        END
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ) /* 呆账本金年日均 */
FROM PDB.T03_AGT_AMT_H AS T1
WHERE
  AMT_TYPE_CD IN ('001', '003', '004', '005', '006')
  AND (
    End_Dt >= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
    OR End_Dt >= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
    OR END_DT >= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
  )
  AND AGT_TYPE_CD IN ('104', '106') /* 对公贷款借据 存款户透支明细账户(法人透支/个人借贷合一卡) */
GROUP BY
  1,
  2;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP8 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  BASE_INT_RATE,
  EXEC_INT_RATE
)
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  SUM(CASE WHEN AGT_INT_RATE_TYPE_CD = '002' THEN INT_RATE ELSE 0.000000 END), /* '基准利率' */
  SUM(CASE WHEN AGT_INT_RATE_TYPE_CD = '001' THEN INT_RATE ELSE 0.000000 END) /* '执行利率' */
FROM PDB.T03_AGT_INT_RATE_H
WHERE
  AGT_INT_RATE_TYPE_CD IN ('001', '002')
  AND INT_RATE_FREQ_CD = 'A' /* '年' */
  AND START_DT <= STR_TO_DATE('********', '%Y%m%d')
  AND END_DT > STR_TO_DATE('********', '%Y%m%d')
  AND AGT_MODIF_NUM IN ('SDI104', 'SC0106')
GROUP BY
  1,
  2;

/* 核销   2014年10月15日添加 */
INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP10 (
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  WRTOFF_PRIN, /* 核销本金 */
  WRTOFF_INT, /* 核销利息 */
  WRTOFF_DT /* 核销日期 */
)
SELECT
  LOAN_NO, /* 对公贷款借据协议号 */
  'SDI104', /* 对公贷款借据协议修饰符 */
  CHRG_PRCP_AMT, /* 核销本金金额 */
  CHRG_INT_AMT + CHRG_OD_AMT /* 核销利息金额+核销罚息金额 */,
  CHRG_DT /* 核销日期 */
FROM ODB.SDI_CHRGO_LOG /* 贷款核销记录表 */
WHERE
  START_DT <= '********'
  AND END_DT > '********'
  AND REVSE_IND = 'N' /* 冲销标识 */
  AND HOST_IND = 'Y'
  AND CHRG_DT <= '********' /* 核销日期 */;

/* 计提  2014年10月15日添加 20170329调整增加开始日期和结束日期，因为存在借据变更，数据重复问题，增加该限制条件脚本才支持重跑 */ /* 202111 修改 CLS系统迁移 */
INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP11 (
  ACCT_NUM, /* 协议号 */
  ACCRU_INT /* 计提利息 */
)
SELECT
  LOAN_NO, /* 借据号 */
  SUM(INT_AMT + OD_INT_AMT) /* 金额 */
FROM ODB.SDI_ACC_LOG /* 利息计提表 */
WHERE
  ACC_DT = '********' /* 计提日期 */ AND START_DT <= '********' AND END_DT > '********'
GROUP BY
  1;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP13 (
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  NEXT_TERM_REPAY_DT, /* 下期还本日期 */
  NEXT_TERM_REPAY_AMT /* 下期还本金额 */
)
SELECT
  LOAN_NO, /* 协议号 */
  'SDI104', /* 协议修饰符 */
  DUE_DT, /* 下期还本日期 */
  FLOW_AMT /* 下期还本金额 */
FROM ODB.SDI_PAYM_SCHED_PRE /* 未来还款计划表 */
WHERE
  DUE_DT > '********'
  AND START_DT <= '********'
  AND END_DT > '********'
  AND DEL_IND <> 'D'
  AND FLOW_TYP = '0115'
  AND FLOW_AMT <> 0
  AND REVSE_IND <> 'Y' /* 排除冲销类交易 */
QUALIFY
  ROW_NUMBER() OVER (PARTITION BY LOAN_NO ORDER BY PERD_NO) = 1;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP14 (
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  NEXT_TERM_REPAY_INT_DT, /* 下期还息日期 */
  NEXT_TERM_REPAY_INT_AMT /* 下期还息金额 */
)
SELECT
  LOAN_NO, /* 协议号 */
  'SDI104', /* 协议修饰符 */
  DUE_DT, /* 下期还息日期 */
  FLOW_AMT /* 下期还息金额 */
FROM ODB.SDI_PAYM_SCHED_PRE /* 未来还款计划表 */
WHERE
  DUE_DT > '********'
  AND START_DT <= '********'
  AND END_DT > '********'
  AND DEL_IND <> 'D'
  AND FLOW_TYP = '0110'
  AND FLOW_AMT <> 0
  AND REVSE_IND <> 'Y' /* 排除冲销类交易 */
QUALIFY
  ROW_NUMBER() OVER (PARTITION BY LOAN_NO ORDER BY PERD_NO) = 1;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_T03_AGT_AMT_H_1 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_AMT,
  START_DT,
  END_DT
)
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  CASE WHEN DEBIT_CRDT_IND = '0' THEN AGT_AMT ELSE -AGT_AMT END,
  START_DT,
  END_DT
FROM PDB.T03_AGT_AMT_H
WHERE
  AMT_TYPE_CD = '001'
  AND AGT_TYPE_CD = '109' /* 109 同业资金业务内部账户(来源于ARAP/KTP/EUSP的存放拆借债券等) */
  AND START_DT <= '********'
  AND END_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12) - 1;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_T03_AGT_AMT_H (
  ACCT_NUM,
  AGT_MODIF_NUM,
  BAL,
  BAL_YEAR_DAY_AVG,
  BAL_MONTH_DAY_AVG,
  BAL_QUAR_DAY_AVG,
  BAL_LATE_3MONTH_DAY_AVG,
  BAL_LATE_YEAR_DAY_AVG
)
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  SUM(CASE WHEN START_DT <= '********' AND END_DT > '********' THEN AGT_AMT ELSE 0 END),
  SUM(
    CASE
      WHEN START_DT <= CAST('********' AS DATE) AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - CAST('********' AS DATE) + 1
      )
      WHEN START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - CAST('********' AS DATE)
      )
      WHEN START_DT > CAST('********' AS DATE)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - START_DT + 1
      )
      WHEN START_DT > CAST('********' AS DATE)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - START_DT
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ),
  SUM(
    CASE
      WHEN START_DT <= CAST('********' AS DATE) AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - CAST('********' AS DATE) + 1
      )
      WHEN START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - CAST('********' AS DATE)
      )
      WHEN START_DT > CAST('********' AS DATE)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - START_DT + 1
      )
      WHEN START_DT > CAST('********' AS DATE)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - START_DT
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ),
  SUM(
    CASE
      WHEN START_DT <= CAST('********' AS DATE) AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - CAST('********' AS DATE) + 1
      )
      WHEN START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - CAST('********' AS DATE)
      )
      WHEN START_DT > CAST('********' AS DATE)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - START_DT + 1
      )
      WHEN START_DT > CAST('********' AS DATE)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - START_DT
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - CAST('********' AS DATE) + 1
  ),
  SUM(
    CASE
      WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3) + 1
      )
      WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
      AND END_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
      )
      WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - START_DT + 1
      )
      WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - START_DT
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -3) + 1
  ),
  SUM(
    CASE
      WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12) + 1
      )
      WHEN START_DT <= ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
      AND END_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
      )
      WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT > CAST('********' AS DATE)
      THEN AGT_AMT * (
        CAST('********' AS DATE) - START_DT + 1
      )
      WHEN START_DT > ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12)
      AND START_DT <= CAST('********' AS DATE)
      AND END_DT <= CAST('********' AS DATE)
      THEN AGT_AMT * (
        END_DT - START_DT
      )
      ELSE 0
    END
  ) / (
    CAST('********' AS DATE) - ADD_MONTHS(STR_TO_DATE('********', '%Y%m%d') + 1, -12) + 1
  )
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.VT_T03_AGT_AMT_H_1
GROUP BY
  1,
  2;

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_LOAN_REPAY_KAU (
  ACCT_NUM,
  AGT_MODIF_NUM,
  CURRENCY_CD,
  IN_BS_PUNISH_INT_KAU,
  IN_BS_PUNISH_INT_USD,
  IN_BS_PUNISH_INT_RMB,
  OFF_BS_PUNISH_INT_KAU,
  OFF_BS_PUNISH_INT_USD,
  OFF_BS_PUNISH_INT_RMB,
  IN_BS_OWE_INT_KAU,
  IN_BS_OWE_INT_USD,
  IN_BS_OWE_INT_RMB,
  OFF_BS_OWE_INT_KAU,
  OFF_BS_OWE_INT_USD,
  OFF_BS_OWE_INT_RMB,
  AGENT_IN_BS_PUNISH_INT_KAU,
  AGENT_IN_BS_PUNISH_INT_USD,
  AGENT_IN_BS_PUNISH_INT_RMB,
  AGENT_OFF_BS_PUNISH_INT_KAU,
  AGENT_OFF_BS_PUNISH_INT_USD,
  AGENT_OFF_BS_PUNISH_INT_RMB,
  AGENT_IN_BS_OWE_INT_KAU,
  AGENT_IN_BS_OWE_INT_USD,
  AGENT_IN_BS_OWE_INT_RMB,
  AGENT_OFF_BS_OWE_INT_KAU,
  AGENT_OFF_BS_OWE_INT_USD,
  AGENT_OFF_BS_OWE_INT_RMB,
  PAID_INT_TOTAL_AMT_KAU,
  PAID_INT_TOTAL_AMT_USD,
  PAID_INT_TOTAL_AMT_RMB,
  PAID_PUNISH_INT_TOTAL_AMT_KAU,
  PAID_PUNISH_INT_TOTAL_AMT_USD,
  PAID_PUNISH_INT_TOTAL_AMT_RMB,
  NEXT_TERM_REPAY_INT_AMT_KAU,
  NEXT_TERM_REPAY_INT_AMT_USD,
  NEXT_TERM_REPAY_INT_AMT_RMB,
  ACCRU_INT_KAU,
  ACCRU_INT_USD,
  ACCRU_INT_RMB
)
SELECT
  T1.ACCT_NUM,
  T1.AGT_MODIF_NUM,
  T1.CURRENCY_CD,
  COALESCE(T3.IN_BS_PUNISH_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.IN_BS_PUNISH_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.IN_BS_PUNISH_INT, 0),
  COALESCE(T3.OFF_BS_PUNISH_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.OFF_BS_PUNISH_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.OFF_BS_PUNISH_INT, 0),
  COALESCE(T3.IN_BS_OWE_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.IN_BS_OWE_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.IN_BS_OWE_INT, 0),
  COALESCE(T3.OFF_BS_OWE_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.OFF_BS_OWE_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.OFF_BS_OWE_INT, 0),
  COALESCE(T3.AGENT_IN_BS_PUNISH_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_IN_BS_PUNISH_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_IN_BS_PUNISH_INT, 0),
  COALESCE(T3.AGENT_OFF_BS_PUNISH_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_OFF_BS_PUNISH_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_OFF_BS_PUNISH_INT, 0),
  COALESCE(T3.AGENT_IN_BS_OWE_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_IN_BS_OWE_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_IN_BS_OWE_INT, 0),
  COALESCE(T3.AGENT_OFF_BS_OWE_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_OFF_BS_OWE_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T3.AGENT_OFF_BS_OWE_INT, 0),
  COALESCE(T4.PAID_INT_TOTAL_AMT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T4.PAID_INT_TOTAL_AMT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T4.PAID_INT_TOTAL_AMT, 0),
  COALESCE(T5.PAID_PUNISH_INT_TOTAL_AMT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T5.PAID_PUNISH_INT_TOTAL_AMT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T5.PAID_PUNISH_INT_TOTAL_AMT, 0),
  COALESCE(T6.NEXT_TERM_REPAY_INT_AMT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T6.NEXT_TERM_REPAY_INT_AMT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T6.NEXT_TERM_REPAY_INT_AMT, 0),
  COALESCE(T7.ACCRU_INT / T8.RMB_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T7.ACCRU_INT / T8.USD_CONVT_RATE * T8.CURR_UNIT, 0),
  COALESCE(T7.ACCRU_INT, 0)
FROM PDB.T03_CORP_LOAN_DUBIL AS T1
INNER JOIN ODB.SDI_GOLD AS T2
  ON T1.ACCT_NUM = T2.LOAN_NO
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP3 AS T3
  ON T1.ACCT_NUM = T3.ACCT_NUM AND T1.AGT_MODIF_NUM = T3.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_LOAN_REPAY_PLAN_H AS T4
  ON T1.ACCT_NUM = T4.ACCT_NUM AND T1.AGT_MODIF_NUM = T4.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_PUNISH_INT_BOOK AS T5
  ON T1.ACCT_NUM = T5.ACCT_NUM AND T1.AGT_MODIF_NUM = T5.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP14 AS T6
  ON T1.ACCT_NUM = T6.ACCT_NUM AND T1.AGT_MODIF_NUM = T6.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP11 AS T7
  ON T1.ACCT_NUM = T7.ACCT_NUM
INNER JOIN PDB.T00_CONVT_EXCH_RATE_H AS T8
  ON T1.CURRENCY_CD = T8.CURRENCY_CD
  AND T8.START_DT <= STR_TO_DATE('********', '%Y%m%d')
  AND T8.END_DT > STR_TO_DATE('********', '%Y%m%d')
  AND T8.EXCH_RATE_TYPE_CD = '001'
WHERE
  T1.START_DT <= STR_TO_DATE('********', '%Y%m%d')
  AND T1.END_DT > STR_TO_DATE('********', '%Y%m%d')
  AND T2.START_DT <= STR_TO_DATE('********', '%Y%m%d')
  AND T2.END_DT > STR_TO_DATE('********', '%Y%m%d')
  AND T1.CURRENCY_CD = '69'
  AND T2.FEE_CCY = 'RMB';

/* 对公贷款部分 */
INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP7 (
  STAT_DT, /* 统计日期 */
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  VAL_TYPE_CD, /* 值类型代码 */
  CURRENCY_CD, /* 币种 */
  ACCT_ORG_NUM, /* 账务机构号 */
  BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  CORP_LOAN_BREED_CD, /* 对公贷款品种代码 */
  LOAN_PROD_ID, /* 贷款产品编号                    zzr added */
  CORP_LOAN_STRU_AGT_TYPE_CD, /* 对公贷款结构产品协议种类代码    zzr added */
  MAIN_GUAR_MODE_CD, /* 主担保方式代码 */
  GUAR_MODE_COMB_DESC, /* 担保方式组合描述 */
  HOST_CUST_ID, /* 核心客户号 */
  CRDT_SYS_CUST_NAME, /* 信贷系统客户名称 */
  CORP_LOAN_CONTR_ACCT_NUM, /* 对公贷款合同协议号 */
  CORP_LOAN_CONTR_AGT_MODIF_NUM, /* 对公贷款合同协议修饰符 */
  CORP_LOAN_CRDT_ACCT_NUM, /* 对公贷款授信协议号 */
  CORP_LOAN_CRDT_AGT_MODIF_NUM, /* 对公贷款授信协议修饰符 */
  HOST_PROD_CD, /* 主机产品代码 */
  LOAN_INIT_BIZ_TYPE_CD, /* 贷款发起业务类型代码 */
  SPON_CRDT_TELLER_NUM, /* 主办信贷柜员号 */
  OUT_ACCT_FLW_INIT_CRDT_ORG_NUM, /* 出账流程发起信贷机构号 */
  RISK_MGMT_REC_PARTY_ID, /* 风控采录当事人编号               zzr added */
  MARGIN_EQ_AMT, /* 保证金等价物金额 */
  MARGIN_RATIO, /* 保证金比例 */
  MARGIN_AMT, /* 保证金金额 */
  MARGIN_CURRENCY_CD, /* 保证金币种 */
  MARGIN_ACCT_NUM, /* 保证金账号 */
  DIR_INDUS_TYPE_CD, /* 投向行业种类代码 */
  LOAN_USAGE_CD, /* 贷款用途代码 */
  REPAY_MODE_CD, /* 还款方式代码 */
  LOW_RISK_BIZ_IND, /* 低风险业务标志 */
  MS_IND, /* 中小标志 */
  CTY_LIMIT_DIR_INDUS_IND, /* 是否国家限制投向行业标志 */
  DISTR_DT, /* 放款日期 */
  DISTR_AMT_CURRENCY_CD, /* 放款金额币种 */
  DISTR_AMT, /* 放款金额 */
  BASE_INT_RATE, /* 基准利率 */
  EXEC_INT_RATE, /* 执行利率 */
  INT_RATE_FLOAT_RATIO, /* 利率浮动比率 */
  INT_RATE_FLOAT_VAL, /* 利率浮动值 */
  INT_RATE_PRICE_STD_CD, /* 利率定价标准代码 */
  FOLLOW_PBC_INT_RATE_CHANGE_IND, /* 随央行利率变动标志 */
  START_INT_DT, /* 起息日期 */
  MATURE_DT, /* 到期日期 */
  LOAN_TERM, /* 贷款期限 */
  LOAN_TERM_FREQ_CD, /* 贷款期限周期代码 */
  LOAN_REMAIN_TERM, /* 贷款剩余期限 */
  REPAY_INTRV, /* 还款间隔 */
  REPAY_INTRV_FREQ_CD, /* 还款间隔周期代码 */
  LOAN_DISP_MODE_CD, /* 贷款处置方式代码 */
  ENTR_LOAN_SYNDIC_LOAN_IND, /* 委托贷款银团贷款标志 */
  INTER_SYNDIC_LOAN_IND, /* 国际银团贷款标志 */
  CALC_INT_PERIOD_CD, /* 计息周期代码 */
  ADJ_INT_TMPNT_MODE_CD, /* 调息时点模式代码 */
  PUNISH_INT_RATE_FLOAT_RATIO, /* 罚息利率浮动比例 */
  DEBT_RATING_LEVEL_CD, /* 债项评级级别代码 */
  OVDUE_BAD_LOAN_REVW_RESULT_CD, /* 一逾两呆贷款评审结果代码 */
  GRADE5_CLASS_REVW_RESULT_CD, /* 五级分类贷款评审结果代码 */
  GRADE10_CLASS_REVW_RESULT_CD, /* 十级分类贷款评审结果代码 */
  RENEW_IND, /* 展期标志 */
  RENEW_AMT, /* 展期金额                        zzr added */
  LOAN_DUBIL_STAT_CD, /* 贷款借据状态代码 */
  WRTOFF_IND, /* 核销标志 */
  PAYOFF_IND, /* 结清标志 */
  CLEAR_DT, /* 结清日期 */
  REMAIN_PRIN, /* 剩余本金 */
  AGENT_CAP, /* 代理资金 */
  NORMAL_PRIN, /* 正常本金 */
  OVDUE_PRIN, /* 逾期本金 */
  IDLE_PRIN, /* 呆滞本金 */
  BAD_DEBT_PRIN, /* 呆账本金 */
  IN_BS_PUNISH_INT, /* 表内罚息 */
  OFF_BS_PUNISH_INT, /* 表外罚息 */
  IN_BS_OWE_INT, /* 表内欠息 */
  OFF_BS_OWE_INT, /* 表外欠息 */
  WRTOFF_PRIN, /* 核销本金                        zzr added */
  WRTOFF_INT, /* 核销利息                        zzr added */
  ACCRU_INT, /* 计提利息                        zzr added */
  PAID_INT_TOTAL_AMT, /* 已还利息总额 */
  PAID_PUNISH_INT_TOTAL_AMT, /* 已还罚息总额 */
  OVDUE_BEGIN_DT, /* 逾期起始日期 */
  LOAN_OVDUE_DAYS, /* 贷款逾期天数 */
  PRIN_OVDUE_BEGIN_DT, /* 本金逾期起始日期 */
  OWE_INT_BEGIN_DT, /* 欠息起始日期 */
  NEXT_TERM_REPAY_DT, /* 下期还本日期 */
  NEXT_TERM_REPAY_AMT, /* 下期还本金额 */
  NEXT_TERM_REPAY_INT_DT, /* 下期还息日期 */
  NEXT_TERM_REPAY_INT_AMT, /* 下期还息金额 */
  LOAN_MONTH_DAY_AVG, /* 贷款月日均 */
  LOAN_QUAR_DAY_AVG, /* 贷款季日均 */
  LOAN_YEAR_DAY_AVG, /* 贷款年日均 */
  LOAN_LATE_3MONTH_DAY_AVG, /* 贷款近三个月日均 */
  LOAN_LATE_YEAR_DAY_AVG, /* 贷款近一年日均 */
  NORMAL_PRIN_M_DAY_AVG, /* 正常本金月日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_M_DAY_AVG, /* 逾期本金月日均 ADD JIANGLEI ******** */
  IDLE_PRIN_M_DAY_AVG, /* 呆滞本金月日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_M_DAY_AVG, /* 呆账本金月日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Q_DAY_AVG, /* 正常本金季日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Q_DAY_AVG, /* 逾期本金季日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Q_DAY_AVG, /* 呆滞本金季日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Q_DAY_AVG, /* 呆账本金季日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Y_DAY_AVG, /* 正常本金年日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Y_DAY_AVG, /* 逾期本金年日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Y_DAY_AVG, /* 呆滞本金年日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Y_DAY_AVG /* 呆账本金年日均 ADD JIANGLEI ******** */
)
SELECT
  '********', /* 统计日期 */
  T1.ACCT_NUM, /* 协议号 */
  T1.AGT_MODIF_NUM, /* 协议修饰符 */
  '1', /* 值类型代码 */
  CASE WHEN T2.CURRENCY_CD <> '' THEN T2.CURRENCY_CD ELSE T1.CURRENCY_CD END AS CURRENCY_CD1, /* 币种[贵金属贷款的币种从协议级金额历史中取] */
  T1.ACCT_ORG_NUM, /* 账务机构号 */
  T1.BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  T1.CORP_LOAN_BREED_CD, /* 对公贷款品种代码 */
  T1.LOAN_PROD_ID, /* 贷款产品编号                     zzr added */
  T1.CORP_LOAN_STRU_AGT_TYPE_CD, /* 对公贷款结构产品协议种类代码      zzr added */
  T1.MAIN_GUAR_MODE_CD, /* 主担保方式代码 */
  T1.GUAR_MODE_COMB_DESC, /* 担保方式组合描述 */
  T1.HOST_CUST_ID, /* 核心客户号 */
  T1.CRDT_SYS_CUST_NAME, /* 信贷系统客户名称 */
  T1.CORP_LOAN_CONTR_ACCT_NUM, /* 对公贷款合同协议号 */
  T1.CORP_LOAN_CONTR_AGT_MODIF_NUM, /* 对公贷款合同协议修饰符 */
  T1.CORP_LOAN_CRDT_ACCT_NUM, /* 对公贷款授信协议号 */
  T1.CORP_LOAN_CRDT_AGT_MODIF_NUM, /* 对公贷款授信协议修饰符 */
  T1.HOST_PROD_CD, /* 主机产品代码 */
  T1.LOAN_INIT_BIZ_TYPE_CD, /* 贷款发起业务类型代码 */
  T1.SPON_CRDT_TELLER_NUM, /* 主办信贷柜员号 */
  T1.OUT_ACCT_FLW_INIT_CRDT_ORG_NUM, /* 出账流程发起信贷机构号 */
  T1.RISK_MGMT_REC_PARTY_ID, /* 风控 当事人编号  2014年10月15日 添加 */
  T1.MARGIN_EQ_AMT, /* 保证金等价物金额 */
  T1.MARGIN_RATIO, /* 保证金比例 */
  T1.MARGIN_AMT, /* 保证金金额 */
  T1.MARGIN_CURRENCY_CD, /* 保证金币种 */
  T1.MARGIN_ACCT_NUM, /* 保证金账号 */
  T1.DIR_INDUS_TYPE_CD, /* 投向行业种类代码 */
  T1.LOAN_USAGE_CD, /* 贷款用途代码 */
  T1.REPAY_MODE_CD, /* 还款方式代码 */
  T1.LOW_RISK_BIZ_IND, /* 低风险业务标志 */
  T1.MS_IND, /* 中小标志 */
  T1.CTY_LIMIT_DIR_INDUS_IND, /* 是否国家限制投向行业标志 */
  T1.DISTR_DT, /* 放款日期 */
  T1.CURRENCY_CD, /* 放款金额币种 */
  T1.DISTR_AMT, /* 放款金额 */
  COALESCE(T6.BASE_INT_RATE, 0), /* 基准利率 */
  COALESCE(T6.EXEC_INT_RATE, 0), /* 执行利率 */
  T1.INT_RATE_FLOAT_RATIO, /* 利率浮动比率 */
  T1.INT_RATE_FLOAT_VAL, /* 利率浮动值 */
  T1.INT_RATE_PRICE_STD_CD, /* 利率定价标准代码 */
  T1.FOLLOW_PBC_INT_RATE_CHANGE_IND, /* 随央行利率变动标志 */
  T1.START_INT_DT, /* 起息日期 */
  T1.MATURE_DT, /* 到期日期 */
  CASE
    WHEN T1.DISTR_DT >= T1.MATURE_DT OR T1.DISTR_DT = '********'
    THEN 0
    ELSE COALESCE(T7.MONTH_OF_CALN, 0) - COALESCE(T9.MONTH_OF_CALN, 0)
  END, /* 贷款期限 */
  T1.LOAN_TERM_FREQ_CD, /* 贷款期限周期代码 */
  CASE
    WHEN T1.MATURE_DT >= CAST('********' AS DATE)
    AND T1.MATURE_DT <> CAST('30001231' AS DATE)
    THEN COALESCE(T7.MONTH_OF_CALN, 0) - COALESCE(T8.MONTH_OF_CALN, 0)
    ELSE 0
  END, /* 贷款剩余期限 */
  T1.REPAY_INTRV, /* 还款间隔 */
  T1.REPAY_INTRV_FREQ_CD, /* 还款间隔周期代码 */
  T1.LOAN_DISP_MODE_CD, /* 贷款处置方式代码 */
  T1.ENTR_LOAN_SYNDIC_LOAN_IND, /* 委托贷款银团贷款标志 */
  T1.INTER_SYNDIC_LOAN_IND, /* 国际银团贷款标志 */
  T1.CALC_INT_PERIOD_CD, /* 计息周期代码 */
  T1.ADJ_INT_TMPNT_MODE_CD, /* 调息时点模式代码 */
  T1.PUNISH_INT_RATE_FLOAT_RATIO, /* 罚息利率浮动比例 */
  T1.DEBT_RATING_LEVEL_CD, /* 债项评级级别代码 */
  CASE
    WHEN T1.CORP_LOAN_BREED_CD IN ('5734', '5B91', '5G90', '5203')
    THEN (
      CASE
        WHEN T2.BAD_DEBT_PRIN + T2.AGENT_BAD_DEBT_PRIN > 0
        THEN '140'
        WHEN T2.IDLE_PRIN + T2.AGENT_IDLE_PRIN > 0
        THEN '130'
        WHEN T2.OVDUE_PRIN + T2.AGENT_OVDUE_PRIN > 0
        THEN '120'
        WHEN T2.NORMAL_PRIN + T2.AGENT_NORMAL_PRIN > 0
        THEN '110'
        ELSE ''
      END
    )
    ELSE (
      CASE
        WHEN T2.BAD_DEBT_PRIN > 0
        THEN '140'
        WHEN T2.IDLE_PRIN > 0
        THEN '130'
        WHEN T2.OVDUE_PRIN > 0
        THEN '120'
        WHEN T2.NORMAL_PRIN > 0
        THEN '110'
        ELSE ''
      END
    )
  END, /* 一逾两呆贷款评审结果代码 */
  T1.GRADE5_CLASS_REVW_RESULT_CD, /* 五级分类贷款评审结果代码 */
  T1.GRADE10_CLASS_REVW_RESULT_CD, /* 十级分类贷款评审结果代码 */
  T1.RENEW_IND, /* 展期标志 */
  T1.RENEW_AMT, /* 展期金额 */
  T1.LOAN_DUBIL_STAT_CD, /* 贷款借据状态代码 */
  T1.WRTOFF_IND, /* 核销标志 */
  CASE
    WHEN T1.WRTOFF_IND <> '0' AND T1.LOAN_DUBIL_STAT_CD = 'ACTV'
    THEN '1'
    ELSE '0'
  END AS PAYOFF_IND_TMP, /* 结清标志 */
  CASE
    WHEN PAYOFF_IND_TMP = '0' AND T1.CLEAR_DT = CAST('3000-12-31' AS DATE)
    THEN CAST('0001-01-01' AS DATE)
    WHEN PAYOFF_IND_TMP = '1' AND T1.CLEAR_DT = CAST('0001-01-01' AS DATE)
    THEN CAST('3000-12-31' AS DATE)
    ELSE T1.CLEAR_DT
  END, /* 结清日期 */
  COALESCE(T2.REMAIN_PRIN, 0), /* 剩余本金 */
  COALESCE(T2.AGENT_CAP, 0), /* 代理资金 */
  COALESCE(T2.NORMAL_PRIN, 0), /* 正常本金 */
  COALESCE(T2.OVDUE_PRIN, 0), /* 逾期本金 */
  COALESCE(T2.IDLE_PRIN, 0), /* 呆滞本金 */
  COALESCE(T2.BAD_DEBT_PRIN, 0), /* 呆账本金 */ /* ,COALESCE(T3.IN_BS_PUNISH_INT,0)                  表内罚息 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.IN_BS_PUNISH_INT_KAU
    ELSE COALESCE(T3.IN_BS_PUNISH_INT, 0)
  END, /* ,COALESCE(T3.OFF_BS_PUNISH_INT,0)                 表外罚息 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.OFF_BS_PUNISH_INT_KAU
    ELSE COALESCE(T3.OFF_BS_PUNISH_INT, 0)
  END, /* ,COALESCE(T3.IN_BS_OWE_INT,0)                     表内欠息 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.IN_BS_OWE_INT_KAU
    ELSE COALESCE(T3.IN_BS_OWE_INT, 0)
  END, /* ,COALESCE(T3.OFF_BS_OWE_INT,0)                    表外欠息 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.OFF_BS_OWE_INT_KAU
    ELSE COALESCE(T3.OFF_BS_OWE_INT, 0)
  END,
  COALESCE(T11.WRTOFF_PRIN, 0), /* 核销本金     2014年10月15日添加 */
  COALESCE(T11.WRTOFF_INT, 0), /* 核销利息     2014年10月15日添加 */ /* ,COALESCE(CASE WHEN T18.ACCT_NUM IS NOT NULL THEN T18.ACCRU_INT_KAU ELSE T12.ACCRU_INT END,0)                        计提利息 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.ACCRU_INT_KAU
    ELSE COALESCE(T12.ACCRU_INT, 0)
  END, /* 计提利息        */ /* ,COALESCE(T16.PAID_INT_TOTAL_AMT,0)               已还利息总额 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.PAID_INT_TOTAL_AMT_KAU
    ELSE COALESCE(T16.PAID_INT_TOTAL_AMT, 0)
  END, /* ,COALESCE(T17.PAID_PUNISH_INT_TOTAL_AMT,0)        已还罚息总额 */
  CASE
    WHEN NOT T18.ACCT_NUM IS NULL
    THEN T18.PAID_PUNISH_INT_TOTAL_AMT_KAU
    ELSE COALESCE(T17.PAID_PUNISH_INT_TOTAL_AMT, 0)
  END,
  COALESCE(T4.OVDUE_BEGIN_DT, CAST('30001231' AS DATE)), /* 逾期开始日期 */
  CASE
    WHEN T4.OVDUE_BEGIN_DT IS NULL
    THEN 0
    ELSE STR_TO_DATE('********', '%Y%m%d') - T4.OVDUE_BEGIN_DT
  END, /* 贷款逾期天数 */
  COALESCE(T4.PRIN_OVDUE_BEGIN_DT, CAST('30001231' AS DATE)), /* 本金逾期起始日期 */
  COALESCE(T4.OWE_INT_BEGIN_DT, CAST('30001231' AS DATE)), /* 欠息起始日期 */
  COALESCE(
    CASE
      WHEN PAYOFF_IND_TMP = 0
      THEN CAST('30001231' AS DATE)
      ELSE T14.NEXT_TERM_REPAY_DT
    END,
    CAST('30001231' AS DATE)
  ), /* 下期还本日期 */
  COALESCE(CASE WHEN PAYOFF_IND_TMP = 0 THEN 0 ELSE T14.NEXT_TERM_REPAY_AMT END, 0), /* 下期还本金额 */
  COALESCE(
    CASE
      WHEN PAYOFF_IND_TMP = 0
      THEN CAST('30001231' AS DATE)
      ELSE T15.NEXT_TERM_REPAY_INT_DT
    END,
    CAST('30001231' AS DATE)
  ), /* 下期还息日期 */
  COALESCE(
    CASE
      WHEN PAYOFF_IND_TMP = 0
      THEN 0
      ELSE COALESCE(T18.NEXT_TERM_REPAY_INT_AMT_KAU, T15.NEXT_TERM_REPAY_INT_AMT)
    END,
    0
  ), /* 下期还息金额 */
  COALESCE(T5.LOAN_MONTH_DAY_AVG, 0), /* 贷款月日均 */
  COALESCE(T5.LOAN_QUAR_DAY_AVG, 0), /* 贷款季日均 */
  COALESCE(T5.LOAN_YEAR_DAY_AVG, 0), /* 贷款年日均 */
  COALESCE(T5.LOAN_LATE_3MONTH_DAY_AVG, 0), /* 贷款近三个月日均 */
  COALESCE(T5.LOAN_LATE_YEAR_DAY_AVG, 0), /* 贷款近一年日均 */
  COALESCE(T5.NORMAL_PRIN_M_DAY_AVG, 0), /* 正常本金月日均 ADD JIANGLEI ******** */
  COALESCE(T5.OVDUE_PRIN_M_DAY_AVG, 0), /* 逾期本金月日均 ADD JIANGLEI ******** */
  COALESCE(T5.IDLE_PRIN_M_DAY_AVG, 0), /* 呆滞本金月日均 ADD JIANGLEI ******** */
  COALESCE(T5.BAD_DEBT_PRIN_M_DAY_AVG, 0), /* 呆账本金月日均 ADD JIANGLEI ******** */
  COALESCE(T5.NORMAL_PRIN_Q_DAY_AVG, 0), /* 正常本金季日均 ADD JIANGLEI ******** */
  COALESCE(T5.OVDUE_PRIN_Q_DAY_AVG, 0), /* 逾期本金季日均 ADD JIANGLEI ******** */
  COALESCE(T5.IDLE_PRIN_Q_DAY_AVG, 0), /* 呆滞本金季日均 ADD JIANGLEI ******** */
  COALESCE(T5.BAD_DEBT_PRIN_Q_DAY_AVG, 0), /* 呆账本金季日均 ADD JIANGLEI ******** */
  COALESCE(T5.NORMAL_PRIN_Y_DAY_AVG, 0), /* 正常本金年日均 ADD JIANGLEI ******** */
  COALESCE(T5.OVDUE_PRIN_Y_DAY_AVG, 0), /* 逾期本金年日均 ADD JIANGLEI ******** */
  COALESCE(T5.IDLE_PRIN_Y_DAY_AVG, 0), /* 呆滞本金年日均 ADD JIANGLEI ******** */
  COALESCE(T5.BAD_DEBT_PRIN_Y_DAY_AVG, 0) /* 呆账本金年日均 ADD JIANGLEI ******** */
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP1 AS T1
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP2 AS T2
  ON T1.ACCT_NUM = T2.ACCT_NUM AND T1.AGT_MODIF_NUM = T2.AGT_MODIF_NUM
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP3 AS T3
  ON T1.ACCT_NUM = T3.ACCT_NUM AND T1.AGT_MODIF_NUM = T3.AGT_MODIF_NUM
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP5 AS T4
  ON T1.ACCT_NUM = T4.ACCT_NUM AND T1.AGT_MODIF_NUM = T4.AGT_MODIF_NUM
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP6 AS T5
  ON T1.ACCT_NUM = T5.ACCT_NUM AND T1.AGT_MODIF_NUM = T5.AGT_MODIF_NUM
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP8 AS T6
  ON T1.ACCT_NUM = T6.ACCT_NUM AND T1.AGT_MODIF_NUM = T6.AGT_MODIF_NUM
LEFT OUTER JOIN ODB.MAN_CALN AS T7
  ON T1.MATURE_DT = T7.CALN_DT
LEFT OUTER JOIN ODB.MAN_CALN AS T8
  ON STR_TO_DATE('********', '%Y%m%d') = T8.CALN_DT
LEFT OUTER JOIN ODB.MAN_CALN AS T9
  ON T1.DISTR_DT = T9.CALN_DT
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP10 AS T11 /* 核销 */
  ON T1.ACCT_NUM = T11.ACCT_NUM AND T1.AGT_MODIF_NUM = T11.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP11 AS T12 /* 计提 */
  ON T1.ACCT_NUM = T12.ACCT_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP13 AS T14
  ON T1.ACCT_NUM = T14.ACCT_NUM AND T1.AGT_MODIF_NUM = T14.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP14 AS T15
  ON T1.ACCT_NUM = T15.ACCT_NUM AND T1.AGT_MODIF_NUM = T15.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_LOAN_REPAY_PLAN_H AS T16
  ON T1.ACCT_NUM = T16.ACCT_NUM AND T1.AGT_MODIF_NUM = T16.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_PUNISH_INT_BOOK AS T17
  ON T1.ACCT_NUM = T17.ACCT_NUM AND T1.AGT_MODIF_NUM = T17.AGT_MODIF_NUM
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_LOAN_REPAY_KAU AS T18
  ON T1.ACCT_NUM = T18.ACCT_NUM AND T1.AGT_MODIF_NUM = T18.AGT_MODIF_NUM;


/* 同业主机产品为S-**********的部分 */
INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_LOAN_AGT_SUM_TMP7 (
  STAT_DT, /* 统计日期 */
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  VAL_TYPE_CD, /* 值类型代码 */
  CURRENCY_CD, /* 币种 */
  ACCT_ORG_NUM, /* 账务机构号 */
  BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  CORP_LOAN_BREED_CD, /* 对公贷款品种代码 */
  LOAN_PROD_ID, /* 贷款产品编号                    zzr added */
  CORP_LOAN_STRU_AGT_TYPE_CD, /* 对公贷款结构产品协议种类代码    zzr added */
  MAIN_GUAR_MODE_CD, /* 主担保方式代码 */
  GUAR_MODE_COMB_DESC, /* 担保方式组合描述 */
  HOST_CUST_ID, /* 核心客户号 */
  CRDT_SYS_CUST_NAME, /* 信贷系统客户名称 */
  CORP_LOAN_CONTR_ACCT_NUM, /* 对公贷款合同协议号 */
  CORP_LOAN_CONTR_AGT_MODIF_NUM, /* 对公贷款合同协议修饰符 */
  CORP_LOAN_CRDT_ACCT_NUM, /* 对公贷款授信协议号 */
  CORP_LOAN_CRDT_AGT_MODIF_NUM, /* 对公贷款授信协议修饰符 */
  HOST_PROD_CD, /* 主机产品代码 */
  LOAN_INIT_BIZ_TYPE_CD, /* 贷款发起业务类型代码 */
  SPON_CRDT_TELLER_NUM, /* 主办信贷柜员号 */
  OUT_ACCT_FLW_INIT_CRDT_ORG_NUM, /* 出账流程发起信贷机构号 */
  RISK_MGMT_REC_PARTY_ID, /* 风控采录当事人编号               zzr added */
  MARGIN_EQ_AMT, /* 保证金等价物金额 */
  MARGIN_RATIO, /* 保证金比例 */
  MARGIN_AMT, /* 保证金金额 */
  MARGIN_CURRENCY_CD, /* 保证金币种 */
  MARGIN_ACCT_NUM, /* 保证金账号 */
  DIR_INDUS_TYPE_CD, /* 投向行业种类代码 */
  LOAN_USAGE_CD, /* 贷款用途代码 */
  REPAY_MODE_CD, /* 还款方式代码 */
  LOW_RISK_BIZ_IND, /* 低风险业务标志 */
  MS_IND, /* 中小标志 */
  CTY_LIMIT_DIR_INDUS_IND, /* 是否国家限制投向行业标志 */
  DISTR_DT, /* 放款日期 */
  DISTR_AMT_CURRENCY_CD, /* 放款金额币种 */
  DISTR_AMT, /* 放款金额 */
  BASE_INT_RATE, /* 基准利率 */
  EXEC_INT_RATE, /* 执行利率 */
  INT_RATE_FLOAT_RATIO, /* 利率浮动比率 */
  INT_RATE_FLOAT_VAL, /* 利率浮动值 */
  INT_RATE_PRICE_STD_CD, /* 利率定价标准代码 */
  FOLLOW_PBC_INT_RATE_CHANGE_IND, /* 随央行利率变动标志 */
  START_INT_DT, /* 起息日期 */
  MATURE_DT, /* 到期日期 */
  LOAN_TERM, /* 贷款期限 */
  LOAN_TERM_FREQ_CD, /* 贷款期限周期代码 */
  LOAN_REMAIN_TERM, /* 贷款剩余期限 */
  REPAY_INTRV, /* 还款间隔 */
  REPAY_INTRV_FREQ_CD, /* 还款间隔周期代码 */
  LOAN_DISP_MODE_CD, /* 贷款处置方式代码 */
  ENTR_LOAN_SYNDIC_LOAN_IND, /* 委托贷款银团贷款标志 */
  INTER_SYNDIC_LOAN_IND, /* 国际银团贷款标志 */
  CALC_INT_PERIOD_CD, /* 计息周期代码 */
  ADJ_INT_TMPNT_MODE_CD, /* 调息时点模式代码 */
  PUNISH_INT_RATE_FLOAT_RATIO, /* 罚息利率浮动比例 */
  DEBT_RATING_LEVEL_CD, /* 债项评级级别代码 */
  OVDUE_BAD_LOAN_REVW_RESULT_CD, /* 一逾两呆贷款评审结果代码 */
  GRADE5_CLASS_REVW_RESULT_CD, /* 五级分类贷款评审结果代码 */
  GRADE10_CLASS_REVW_RESULT_CD, /* 十级分类贷款评审结果代码 */
  RENEW_IND, /* 展期标志 */
  RENEW_AMT, /* 展期金额                        zzr added */
  LOAN_DUBIL_STAT_CD, /* 贷款借据状态代码 */
  WRTOFF_IND, /* 核销标志 */
  PAYOFF_IND, /* 结清标志 */
  CLEAR_DT, /* 结清日期 */
  REMAIN_PRIN, /* 剩余本金 */
  AGENT_CAP, /* 代理资金 */
  NORMAL_PRIN, /* 正常本金 */
  OVDUE_PRIN, /* 逾期本金 */
  IDLE_PRIN, /* 呆滞本金 */
  BAD_DEBT_PRIN, /* 呆账本金 */
  IN_BS_PUNISH_INT, /* 表内罚息 */
  OFF_BS_PUNISH_INT, /* 表外罚息 */
  IN_BS_OWE_INT, /* 表内欠息 */
  OFF_BS_OWE_INT, /* 表外欠息 */
  WRTOFF_PRIN, /* 核销本金                        zzr added */
  WRTOFF_INT, /* 核销利息                        zzr added */
  ACCRU_INT, /* 计提利息                        zzr added */
  PAID_INT_TOTAL_AMT, /* 已还利息总额 */
  PAID_PUNISH_INT_TOTAL_AMT, /* 已还罚息总额 */
  OVDUE_BEGIN_DT, /* 逾期起始日期 */
  LOAN_OVDUE_DAYS, /* 贷款逾期天数 */
  PRIN_OVDUE_BEGIN_DT, /* 本金逾期起始日期 */
  OWE_INT_BEGIN_DT, /* 欠息起始日期 */
  NEXT_TERM_REPAY_DT, /* 下期还本日期 */
  NEXT_TERM_REPAY_AMT, /* 下期还本金额 */
  NEXT_TERM_REPAY_INT_DT, /* 下期还息日期 */
  NEXT_TERM_REPAY_INT_AMT, /* 下期还息金额 */
  LOAN_MONTH_DAY_AVG, /* 贷款月日均 */
  LOAN_QUAR_DAY_AVG, /* 贷款季日均 */
  LOAN_YEAR_DAY_AVG, /* 贷款年日均 */
  LOAN_LATE_3MONTH_DAY_AVG, /* 贷款近三个月日均 */
  LOAN_LATE_YEAR_DAY_AVG, /* 贷款近一年日均 */
  NORMAL_PRIN_M_DAY_AVG, /* 正常本金月日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_M_DAY_AVG, /* 逾期本金月日均 ADD JIANGLEI ******** */
  IDLE_PRIN_M_DAY_AVG, /* 呆滞本金月日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_M_DAY_AVG, /* 呆账本金月日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Q_DAY_AVG, /* 正常本金季日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Q_DAY_AVG, /* 逾期本金季日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Q_DAY_AVG, /* 呆滞本金季日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Q_DAY_AVG, /* 呆账本金季日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Y_DAY_AVG, /* 正常本金年日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Y_DAY_AVG, /* 逾期本金年日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Y_DAY_AVG, /* 呆滞本金年日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Y_DAY_AVG /* 呆账本金年日均 ADD JIANGLEI ******** */
)
SELECT
  '********', /* 统计日期 */
  T1.ACCT_NUM, /* 协议号 */
  T1.AGT_MODIF_NUM, /* 协议修饰符 */
  '1', /* 值类型代码 */
  T1.CURRENCY_CD, /* 币种 */
  T1.OPEN_ORG_NUM, /* 账务机构号 */
  'MS' || T1.OPEN_ORG_NUM /* 业务归属机构号 */,
  'FFTAC', /* 对公贷款品种代码 */
  '', /* 贷款产品编号 */
  '', /* 对公贷款结构产品协议种类代码 */
  CASE
    WHEN COALESCE(T4.VOUCH_TYPE, '') IN ('00', '')
    THEN ''
    WHEN COALESCE(T4.VOUCH_TYPE, '') = '01'
    THEN 'CREDIT'
    ELSE ''
  END AS VOUCH_TYPE1, /* 主担保方式代码 */
  CASE WHEN VOUCH_TYPE1 = '' THEN '' ELSE COALESCE(T7.CD_DESC, '') END, /* 担保方式组合描述 */
  T1.TX_CNTPTY_PARTY_ID, /* 核心客户号 */
  T1.ACCT_NAME, /* 信贷系统客户名称 */
  COALESCE(T3.DOWNLOAD_KEY, ''), /* 对公贷款合同协议号 */
  '', /* 对公贷款合同协议修饰符 */
  '', /* 对公贷款授信协议号 */
  '', /* 对公贷款授信协议修饰符 */
  T1.HOST_PROD_CD, /* 主机产品代码 */
  '', /* 贷款发起业务类型代码 */
  '', /* 主办信贷柜员号 */
  '', /* 风控采录当事人编号 */
  '', /* 出账流程发起信贷机构号 */
  0, /* 保证金等价物金额 */
  0, /* 保证金比例 */
  0, /* 保证金金额 */
  '', /* 保证金币种 */
  '', /* 保证金账号 */
  '', /* 投向行业种类代码 */
  '', /* 贷款用途代码 */
  '1', /* 还款方式代码 */
  '', /* 低风险业务标志 */
  '', /* 中小标志 */
  '', /* 是否国家限制投向行业标志 */
  T1.START_INT_DT, /* 放款日期 */
  T1.CURRENCY_CD, /* 放款金额币种 */
  COALESCE(T3.ORIGINAL_AMT, 0), /* 放款金额 */
  T1.EXEC_INT_RATE, /* 基准利率 */
  T1.EXEC_INT_RATE, /* 执行利率 */
  0, /* 利率浮动比率 */
  0, /* 利率浮动值 */
  '', /* 利率定价标准代码 */
  '', /* 随央行利率变动标志 */
  T1.START_INT_DT, /* 起息日期 */
  T1.MATURE_DT, /* 到期日期 */
  CASE
    WHEN T1.START_INT_DT >= T1.MATURE_DT OR T1.START_INT_DT = CAST('********' AS DATE)
    THEN 0
    ELSE COALESCE(T5.MONTH_OF_CALN, 0) - COALESCE(T6.MONTH_OF_CALN, 0)
  END, /* 贷款期限 */
  'M', /* 贷款期限周期代码 */
  CASE
    WHEN T1.MATURE_DT >= CAST('********' AS DATE)
    AND T1.MATURE_DT <> CAST('30001231' AS DATE)
    THEN COALESCE(T5.MONTH_OF_CALN, 0) - COALESCE(T8.MONTH_OF_CALN, 0)
    ELSE 0
  END, /* 贷款剩余期限 */
  CASE
    WHEN T1.START_INT_DT >= T1.MATURE_DT OR T1.START_INT_DT = CAST('********' AS DATE)
    THEN 0
    ELSE COALESCE(T5.MONTH_OF_CALN, 0) - COALESCE(T6.MONTH_OF_CALN, 0)
  END, /* 还款间隔 */
  'M', /* 还款间隔周期代码 */
  'NORM', /* 贷款处置方式代码 */
  '', /* 委托贷款银团贷款标志 */
  '', /* 国际银团贷款标志 */
  '', /* 计息周期代码 */
  'FXX', /* 调息时点模式代码 */
  0, /* 罚息利率浮动比例 */
  CASE
    WHEN COALESCE(T4.FINANCE_TRADER_LEVEL_TPYE, '') = '03'
    THEN COALESCE(T4.FINANCE_TRADER_LEVEL, '')
    ELSE ''
  END, /* 债项评级级别代码 */
  '110', /* 一逾两呆贷款评审结果代码 */
  CASE
    WHEN COALESCE(T4.INVEST_RISK_TYPE, '') IN ('01', '')
    THEN '201'
    WHEN COALESCE(T4.INVEST_RISK_TYPE, '') = '02'
    THEN '202'
    WHEN COALESCE(T4.INVEST_RISK_TYPE, '') = '03'
    THEN '203'
    WHEN COALESCE(T4.INVEST_RISK_TYPE, '') = '04'
    THEN '204'
    WHEN COALESCE(T4.INVEST_RISK_TYPE, '') = '05'
    THEN '205'
    ELSE ''
  END, /* 五级分类贷款评审结果代码 */
  '', /* 十级分类贷款评审结果代码 */
  '1', /* 展期标志 */
  0, /* 展期金额 */
  CASE WHEN COALESCE(T2.BAL, 0) > 0 THEN 'ACTV' ELSE 'SETL' END, /* 贷款借据状态代码 */
  '', /* 核销标志 */
  CASE WHEN COALESCE(T2.BAL, 0) > 0 THEN '1' ELSE '0' END, /* 结清标志 */
  CASE WHEN COALESCE(T2.BAL, 0) > 0 THEN CAST('30001231' AS DATE) ELSE T1.START_DT END, /* 结清日期 */
  COALESCE(T2.BAL, 0), /* 剩余本金 */
  0, /* 代理资金 */
  COALESCE(T2.BAL, 0), /* 正常本金 */
  0, /* 逾期本金 */
  0, /* 呆滞本金 */
  0, /* 呆账本金 */
  0, /* 表内罚息 */
  0, /* 表外罚息 */
  0, /* 表内欠息 */
  0, /* 表外欠息 */
  0, /* 核销本金 */
  0, /* 核销利息 */
  0, /* 计提利息 */
  COALESCE(T4.INTEREST_INCOME, 0), /* 已还利息总额 */
  0, /* 已还罚息总额 */
  CAST('30001231' AS DATE), /* 逾期起始日期 */
  0, /* 贷款逾期天数 */
  CAST('30001231' AS DATE), /* 本金逾期起始日期 */
  CAST('30001231' AS DATE), /* 欠息起始日期 */
  CASE WHEN BAL1 = 0 THEN CAST('30001231' AS DATE) ELSE T1.MATURE_DT END, /* 下期还本日期 */
  COALESCE(T2.BAL, 0) AS BAL1, /* 下期还本金额 */
  CAST('30001231' AS DATE), /* 下期还息日期 */
  0, /* 下期还息金额 */
  COALESCE(T2.BAL_MONTH_DAY_AVG, 0), /* 贷款月日均 */
  COALESCE(T2.BAL_QUAR_DAY_AVG, 0), /* 贷款季日均 */
  COALESCE(T2.BAL_YEAR_DAY_AVG, 0), /* 贷款年日均 */
  COALESCE(T2.BAL_LATE_3MONTH_DAY_AVG, 0), /* 贷款近三个月日均 */
  COALESCE(T2.BAL_LATE_YEAR_DAY_AVG, 0), /* 贷款近一年日均 */
  0, /* 正常本金月日均 ADD JIANGLEI ******** */
  0, /* 逾期本金月日均 ADD JIANGLEI ******** */
  0, /* 呆滞本金月日均 ADD JIANGLEI ******** */
  0, /* 呆账本金月日均 ADD JIANGLEI ******** */
  0, /* 正常本金季日均 ADD JIANGLEI ******** */
  0, /* 逾期本金季日均 ADD JIANGLEI ******** */
  0, /* 呆滞本金季日均 ADD JIANGLEI ******** */
  0, /* 呆账本金季日均 ADD JIANGLEI ******** */
  0, /* 正常本金年日均 ADD JIANGLEI ******** */
  0, /* 逾期本金年日均 ADD JIANGLEI ******** */
  0, /* 呆滞本金年日均 ADD JIANGLEI ******** */
  0 /* 呆账本金年日均 ADD JIANGLEI ******** */
FROM PDB.T03_IBANK_CAP_BIZ_INT_ACCT AS T1
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_T03_AGT_AMT_H AS T2
  ON T1.ACCT_NUM = T2.ACCT_NUM AND T1.AGT_MODIF_NUM = T2.AGT_MODIF_NUM
LEFT JOIN ODB.SQ4_MM_DEAL AS T3
  ON SUBSTRING(
    SUBSTRING(T1.ACCT_NUM, 8),
    1,
    INDEX(SUBSTRING(T1.ACCT_NUM, 8), SUBSTRING(T1.ACCT_NUM, LENGTH(T1.ACCT_NUM) - 12)) - 1
  ) = T3.TRADE_ID
LEFT JOIN ODB.SR3_TYS_ASSET AS T4
  ON T3.DOWNLOAD_KEY = T4.ASSET_ID
  AND T4.START_DT <= '********'
  AND T4.END_DT > '********'
LEFT JOIN ODB.MAN_CALN AS T5
  ON T1.MATURE_DT = T5.CALN_DT
LEFT JOIN ODB.MAN_CALN AS T6
  ON T1.START_INT_DT = T6.CALN_DT
LEFT JOIN ODB.MAN_QSL_SRC_CD AS T7
  ON T7.BDP_SRC_TABLE_NAME = 'SR3_TYS_ASSET'
  AND T7.BDP_SRC_FIELD_NAME = 'VOUCH_TYPE'
  AND T4.VOUCH_TYPE = T7.CD_VALUE
LEFT JOIN ODB.MAN_CALN AS T8
  ON STR_TO_DATE('********', '%Y%m%d') = T8.CALN_DT
WHERE
  T1.START_DT <= '********'
  AND T1.END_DT > '********'
  AND T1.HOST_PROD_CD = 'S-**********';



INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_GROUP_DETAILS_REAL
SELECT
  CUST_NO,
  GROUP_ID
FROM ODB.ST3_CORP_GROUP_DETAILS_REAL
WHERE
  EFFECT_DT <= '********'
  AND END_DATE > '********'
  AND START_DT <= '********'
  AND END_DT > '********'
  AND DEL_IND <> 'D'
  AND GROUP_ID LIKE ANY ('0940%', '0903%')
  AND CUST_NO <> ''
QUALIFY
  ROW_NUMBER() OVER (PARTITION BY CUST_NO, SUBSTRING(GROUP_ID, 1, 4) ORDER BY EFFECT_DT DESC, GROUP_ID DESC) = 1;


CREATE MULTISET TABLE tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT DATE FORMAT 'YYYYMMDD' NOT NULL
)
VOLATILE ON COMMIT PRESERVE ROWS PRIMARY INDEX (STAT_DT);

CREATE MULTISET TABLE tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP (
  STAT_DT DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT1 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT2 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT3 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT4 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT5 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT6 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT7 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT8 DATE FORMAT 'YYYYMMDD' NOT NULL,
  HOLI_DT9 DATE FORMAT 'YYYYMMDD' NOT NULL
)
VOLATILE ON COMMIT PRESERVE ROWS PRIMARY INDEX (STAT_DT);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP (
  STAT_DT,
  HOLI_DT1,
  HOLI_DT2,
  HOLI_DT3,
  HOLI_DT4,
  HOLI_DT5,
  HOLI_DT6,
  HOLI_DT7,
  HOLI_DT8,
  HOLI_DT9
)
SELECT
  '********',
  CASE WHEN HOLID_IND = 'Y' THEN CALN_DT ELSE CAST('0001-01-01' AS DATE) END AS HOLI_DT1,
  CASE
    WHEN HOLI_DT1 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 1 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT1 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT2,
  CASE
    WHEN HOLI_DT2 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 2 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT2 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT3,
  CASE
    WHEN HOLI_DT3 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 3 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT3 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT4,
  CASE
    WHEN HOLI_DT4 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 4 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT4 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT5,
  CASE
    WHEN HOLI_DT4 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 5 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT5 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT6,
  CASE
    WHEN HOLI_DT4 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 6 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT6 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT7,
  CASE
    WHEN HOLI_DT4 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 7 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT7 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT8,
  CASE
    WHEN HOLI_DT4 <> CAST('0001-01-01' AS DATE)
    AND HOLID_IND = (
      SELECT
        HOLID_IND
      FROM ODB.MAN_CALN
      WHERE
        CALN_DT = STR_TO_DATE('********', '%Y%m%d') - 8 AND HOLID_IND = 'Y'
    )
    THEN HOLI_DT8 - 1
    ELSE CAST('0001-01-01' AS DATE)
  END AS HOLI_DT9
FROM ODB.MAN_CALN
WHERE
  CALN_DT = '********';

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT1
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT1 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT2
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT2 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT3
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT3 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT4
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT4 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT5
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT5 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT6
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT6 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT7
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT7 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT8
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT8 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 (
  STAT_DT,
  HOLI_DT
)
SELECT
  STAT_DT,
  HOLI_DT9
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP
WHERE
  HOLI_DT9 <> CAST('0001-01-01' AS DATE);

INSERT INTO tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM1 (
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  VAL_TYPE_CD, /* 值类型代码 */
  CURRENCY_CD, /* 币种 */
  ACCT_ORG_NUM, /* 账务机构号 */
  BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  HOST_CUST_ID, /* 核心客户号 */
  OVDUE_PRIN, /* 逾期本金 */
  OVDUE_BEGIN_DT, /* 逾期起始日期 */
  OWE_INT_BEGIN_DT, /* 欠息起始日期 */
  REFIN_OLD_DUBIL_MATURE_DT, /* 借新还旧到期日 */
  REFIN_OLD_DUBIL_OVDUE_DT, /* 借新还旧逾期起始日 */
  LOAN_OVDUE_DAYS, /* 逾期天数 */
  ENTR_LOAN_SYNDIC_LOAN_IND /* 委托贷款银团贷款标志 */
)
SELECT
  T1.ACCT_NUM, /* 协议号 */
  T1.AGT_MODIF_NUM, /* 协议修饰符 */
  T1.VAL_TYPE_CD, /* 值类型代码 */
  T1.CURRENCY_CD, /* 币种 */
  T1.ACCT_ORG_NUM, /* 账务机构号 */
  T1.BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  T1.HOST_CUST_ID, /* 核心客户号                                    逾期本金 */
  CASE
    WHEN T1.MATURE_DT <= T1.STAT_DT
    AND T1.REMAIN_PRIN <> 0
    AND T1.OVDUE_PRIN + T1.IDLE_PRIN + T1.BAD_DEBT_PRIN + T1.IN_BS_PUNISH_INT + T1.OFF_BS_PUNISH_INT + T1.IN_BS_OWE_INT + T1.OFF_BS_OWE_INT = 0
    AND T16.HOLI_DT IS NULL
    THEN T1.REMAIN_PRIN
    ELSE T1.OVDUE_PRIN
  END AS OVDUE_PRINT, /* 逾期本金 仅用于计算逾期天数 */ /* ,CASE WHEN T1.MATURE_DT <= T1.STAT_DT AND T1.REMAIN_PRIN <> 0 AND T1.OVDUE_PRIN + T1.IDLE_PRIN + T1.BAD_DEBT_PRIN + T1.IN_BS_PUNISH_INT + T1.OFF_BS_PUNISH_INT + T1.IN_BS_OWE_INT + T1.OFF_BS_OWE_INT = 0  AND T16.HOLI_DT IS NULL */ /*      THEN T1.MATURE_DT */ /*      ELSE T1.OVDUE_BEGIN_DT */ /* END      OVDUE_BEGIN_DTT                                逾期起始日期 */
  T1.OVDUE_BEGIN_DT AS OVDUE_BEGIN_DTT, /* 逾期起始日期 */
  T1.OWE_INT_BEGIN_DT, /* 欠息起始日期 */
  COALESCE(T4.MATURE_DT, STR_TO_DATE('30001231', '%Y%m%d')) AS REFIN_OLD_DUBIL_MATURE_DT, /* 借新还旧到期日 */
  COALESCE(T4.MATURE_DT, STR_TO_DATE('30001231', '%Y%m%d')) AS REFIN_OLD_DUBIL_OVDUE_DT, /* 借新还旧逾期起始日 */ /* -yuhongwei 20160331修改本金逾期条件，因为当OVDUE_BEGIN_DTT=T1.OWE_INT_BEGIN_DT='30001231'时，条件也成立，这时候就出现逾期天数是一个负很大的数 */
  CASE
    WHEN (
      OVDUE_PRINT + T1.IDLE_PRIN + T1.BAD_DEBT_PRIN + T1.IN_BS_PUNISH_INT + T1.OFF_BS_PUNISH_INT + T1.IN_BS_OWE_INT + T1.OFF_BS_OWE_INT = 0
    )
    THEN 0
    WHEN CAST(REFIN_OLD_DUBIL_OVDUE_DT AS DATE) < OVDUE_BEGIN_DTT
    AND CAST(REFIN_OLD_DUBIL_OVDUE_DT AS DATE) < T1.OWE_INT_BEGIN_DT
    THEN T1.STAT_DT - CAST(REFIN_OLD_DUBIL_OVDUE_DT AS DATE) + 1
    WHEN OVDUE_BEGIN_DTT <= T1.OWE_INT_BEGIN_DT
    AND (
      OVDUE_BEGIN_DTT <> '30001231' OR T1.OWE_INT_BEGIN_DT <> '30001231'
    )
    THEN T1.STAT_DT - OVDUE_BEGIN_DTT + 1
    WHEN OVDUE_BEGIN_DTT > T1.OWE_INT_BEGIN_DT
    THEN T1.STAT_DT - T1.OWE_INT_BEGIN_DT + 1
    ELSE 0
  END AS LOAN_OVDUE_DAYS, /* 逾期天数 */
  T1.ENTR_LOAN_SYNDIC_LOAN_IND /* 委托贷款银团贷款标志 */
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM AS T1
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM AS T2
  ON T1.ACCT_NUM = T2.ACCT_NUM
  AND T1.AGT_MODIF_NUM = T2.AGT_MODIF_NUM
  AND T2.STAT_DT = '********'
  AND (
    T2.VAL_TYPE_CD = '3' OR T2.CURRENCY_CD = '01'
  )
  AND T2.LOAN_INIT_BIZ_TYPE_CD = '0106'
  AND (
    T2.OVDUE_PRIN + T2.IDLE_PRIN + T2.BAD_DEBT_PRIN + T2.IN_BS_PUNISH_INT + T2.OFF_BS_PUNISH_INT + T2.IN_BS_OWE_INT + T2.OFF_BS_OWE_INT <> 0
  )
LEFT OUTER JOIN PDB.T03_AGT_RELA_H AS T3
  ON T2.ACCT_NUM = T3.ACCT_NUM
  AND T2.AGT_MODIF_NUM = T3.AGT_MODIF_NUM
  AND T3.AGT_RELA_TYPE_CD = '001'
  AND T3.START_DT <= '********'
  AND T3.END_DT > '********'
LEFT OUTER JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM AS T4
  ON T4.ACCT_NUM = T3.RELA_ACCT_NUM
  AND T4.AGT_MODIF_NUM = T3.RELA_AGT_MODIF_NUM
  AND (
    T4.VAL_TYPE_CD = '3' OR T4.CURRENCY_CD = '01'
  )
  AND T4.STAT_DT = '********'
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.MAN_CALN_TMP1 AS T16
  ON T1.MATURE_DT = T16.HOLI_DT
WHERE
  T1.STAT_DT = '********';

/* 把处理好的数据插入最终目标表 */
INSERT INTO CDB.T88_CORP_LOAN_AGT_SUM (
  STAT_DT, /* 统计日期 */
  ACCT_NUM, /* 协议号 */
  AGT_MODIF_NUM, /* 协议修饰符 */
  VAL_TYPE_CD, /* 值类型代码 */
  CURRENCY_CD, /* 币种 */
  ACCT_ORG_NUM, /* 账务机构号 */
  BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  CORP_LOAN_BREED_CD, /* 对公贷款品种代码 */
  LOAN_PROD_ID, /* 贷款产品编号                   zzr added */
  CORP_LOAN_STRU_AGT_TYPE_CD, /* 对公贷款结构产品协议种类代码    zzr added */
  MAIN_GUAR_MODE_CD, /* 主担保方式代码 */
  GUAR_MODE_COMB_DESC, /* 担保方式组合描述 */
  HOST_CUST_ID, /* 核心客户号 */
  CRDT_SYS_CUST_NAME, /* 信贷系统客户名称 */
  CORP_LOAN_CONTR_ACCT_NUM, /* 对公贷款合同协议号 */
  CORP_LOAN_CONTR_AGT_MODIF_NUM, /* 对公贷款合同协议修饰符 */
  CORP_LOAN_CRDT_ACCT_NUM, /* 对公贷款授信协议号 */
  CORP_LOAN_CRDT_AGT_MODIF_NUM, /* 对公贷款授信协议修饰符 */
  HOST_PROD_CD, /* 主机产品代码 */
  LOAN_INIT_BIZ_TYPE_CD, /* 贷款发起业务类型代码 */
  SPON_CRDT_TELLER_NUM, /* 主办信贷柜员号 */
  OUT_ACCT_FLW_INIT_CRDT_ORG_NUM, /* 出账流程发起信贷机构号 */
  RISK_MGMT_REC_PARTY_ID, /* 风控采录当事人编号              zzr added */
  MARGIN_EQ_AMT, /* 保证金等价物金额 */
  MARGIN_RATIO, /* 保证金比例 */
  MARGIN_AMT, /* 保证金金额 */
  MARGIN_CURRENCY_CD, /* 保证金币种 */
  MARGIN_ACCT_NUM, /* 保证金账号 */
  DIR_INDUS_TYPE_CD, /* 投向行业种类代码 */
  LOAN_USAGE_CD, /* 贷款用途代码 */
  REPAY_MODE_CD, /* 还款方式代码 */
  LOW_RISK_BIZ_IND, /* 低风险业务标志 */
  MS_IND, /* 中小标志 */
  CTY_LIMIT_DIR_INDUS_IND, /* 是否国家限制投向行业标志 */
  DISTR_DT, /* 放款日期 */
  DISTR_AMT_CURRENCY_CD, /* 放款金额币种 */
  DISTR_AMT, /* 放款金额 */
  BASE_INT_RATE, /* 基准利率 */
  EXEC_INT_RATE, /* 执行利率 */
  INT_RATE_FLOAT_RATIO, /* 利率浮动比率 */
  INT_RATE_FLOAT_VAL, /* 利率浮动值 */
  INT_RATE_PRICE_STD_CD, /* 利率定价标准代码 */
  FOLLOW_PBC_INT_RATE_CHANGE_IND, /* 随央行利率变动标志 */
  START_INT_DT, /* 起息日期 */
  MATURE_DT, /* 到期日期 */
  LOAN_TERM, /* 贷款期限 */
  LOAN_TERM_FREQ_CD, /* 贷款期限周期代码 */
  LOAN_REMAIN_TERM, /* 贷款剩余期限 */
  REPAY_INTRV, /* 还款间隔 */
  REPAY_INTRV_FREQ_CD, /* 还款间隔周期代码 */
  LOAN_DISP_MODE_CD, /* 贷款处置方式代码 */
  ENTR_LOAN_SYNDIC_LOAN_IND, /* 委托贷款银团贷款标志 */
  INTER_SYNDIC_LOAN_IND, /* 国际银团贷款标志 */
  CALC_INT_PERIOD_CD, /* 计息周期代码 */
  ADJ_INT_TMPNT_MODE_CD, /* 调息时点模式代码 */
  PUNISH_INT_RATE_FLOAT_RATIO, /* 罚息利率浮动比例 */
  DEBT_RATING_LEVEL_CD, /* 债项评级级别代码 */
  OVDUE_BAD_LOAN_REVW_RESULT_CD, /* 一逾两呆贷款评审结果代码 */
  GRADE5_CLASS_REVW_RESULT_CD, /* 五级分类贷款评审结果代码 */
  GRADE10_CLASS_REVW_RESULT_CD, /* 十级分类贷款评审结果代码 */
  RENEW_IND, /* 展期标志 */
  RENEW_AMT, /* 展期金额   zzr added */
  LOAN_DUBIL_STAT_CD, /* 贷款借据状态代码 */
  WRTOFF_IND, /* 核销标志 */
  PAYOFF_IND, /* 结清标志 */
  CLEAR_DT, /* 结清日期 */
  REMAIN_PRIN, /* 剩余本金 */
  AGENT_CAP, /* 代理资金 */
  NORMAL_PRIN, /* 正常本金 */
  OVDUE_PRIN, /* 逾期本金 */
  IDLE_PRIN, /* 呆滞本金 */
  BAD_DEBT_PRIN, /* 呆账本金 */
  IN_BS_PUNISH_INT, /* 表内罚息 */
  OFF_BS_PUNISH_INT, /* 表外罚息 */
  IN_BS_OWE_INT, /* 表内欠息 */
  OFF_BS_OWE_INT, /* 表外欠息 */
  WRTOFF_PRIN, /* 核销本金  zzr added */
  WRTOFF_INT, /* 核销利息  zzr added */
  ACCRU_INT, /* 计提利息  zzr added */
  PAID_INT_TOTAL_AMT, /* 已还利息总额 */
  PAID_PUNISH_INT_TOTAL_AMT, /* 已还罚息总额 */
  OVDUE_BEGIN_DT, /* 逾期起始日期 */
  LOAN_OVDUE_DAYS, /* 贷款逾期天数 */
  PRIN_OVDUE_BEGIN_DT, /* 本金逾期起始日期 */
  OWE_INT_BEGIN_DT, /* 欠息起始日期 */
  NEXT_TERM_REPAY_DT, /* 下期还本日期 */
  NEXT_TERM_REPAY_AMT, /* 下期还本金额 */
  NEXT_TERM_REPAY_INT_DT, /* 下期还息日期 */
  NEXT_TERM_REPAY_INT_AMT, /* 下期还息金额 */
  LOAN_MONTH_DAY_AVG, /* 贷款月日均 */
  LOAN_QUAR_DAY_AVG, /* 贷款季日均 */
  LOAN_YEAR_DAY_AVG, /* 贷款年日均 */
  LOAN_LATE_3MONTH_DAY_AVG, /* 贷款近三个月日均 */
  LOAN_LATE_YEAR_DAY_AVG, /* 贷款近一年日均 */
  NORMAL_PRIN_M_DAY_AVG, /* 正常本金月日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_M_DAY_AVG, /* 逾期本金月日均 ADD JIANGLEI ******** */
  IDLE_PRIN_M_DAY_AVG, /* 呆滞本金月日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_M_DAY_AVG, /* 呆账本金月日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Q_DAY_AVG, /* 正常本金季日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Q_DAY_AVG, /* 逾期本金季日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Q_DAY_AVG, /* 呆滞本金季日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Q_DAY_AVG, /* 呆账本金季日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_Y_DAY_AVG, /* 正常本金年日均 ADD JIANGLEI ******** */
  OVDUE_PRIN_Y_DAY_AVG, /* 逾期本金年日均 ADD JIANGLEI ******** */
  IDLE_PRIN_Y_DAY_AVG, /* 呆滞本金年日均 ADD JIANGLEI ******** */
  BAD_DEBT_PRIN_Y_DAY_AVG, /* 呆账本金年日均 ADD JIANGLEI ******** */
  NORMAL_PRIN_GL_NUM, /* 正常本金科目号 */
  OVDUE_PRIN_GL_NUM, /* 逾期本金科目号 */
  IDLE_PRIN_GL_NUM, /* 呆滞本金科目号 */
  BAD_DEBT_PRIN_GL_NUM, /* 呆账本金科目号 */
  ACCRU_INT_GL_NUM, /* 应计利息科目号 */
  COLLBL_INT_GL_NUM, /* 应收利息科目号 */
  COLLBL_INT, /* 应收利息 */
  CONCN_CUST_GROUP1_NUM, /* 关注客群1编号 */
  CONCN_CUST_GROUP1_BCLASS_DESC, /* 关注客群1大类描述 */
  CONCN_CUST_GROUP2_NUM, /* 关注客群2编号 */
  CONCN_CUST_GROUP2_BCLASS_DESC /* 关注客群2大类描述 */
)
SELECT
  T1.STAT_DT, /* 统计日期 */
  T1.ACCT_NUM, /* 协议号 */
  T1.AGT_MODIF_NUM, /* 协议修饰符 */
  T1.VAL_TYPE_CD, /* 值类型代码 */
  T1.CURRENCY_CD, /* 币种 */
  T1.ACCT_ORG_NUM, /* 账务机构号 */
  T1.BIZ_BELONG_ORG_NUM, /* 业务归属机构号 */
  T1.CORP_LOAN_BREED_CD, /* 对公贷款品种代码 */
  T1.LOAN_PROD_ID, /* 贷款产品编号                   zzr added */
  T1.CORP_LOAN_STRU_AGT_TYPE_CD, /* 对公贷款结构产品协议种类代码    zzr added */
  T1.MAIN_GUAR_MODE_CD, /* 主担保方式代码 */
  T1.GUAR_MODE_COMB_DESC, /* 担保方式组合描述 */
  T1.HOST_CUST_ID, /* 核心客户号 */
  T1.CRDT_SYS_CUST_NAME, /* 信贷系统客户名称 */
  T1.CORP_LOAN_CONTR_ACCT_NUM, /* 对公贷款合同协议号 */
  T1.CORP_LOAN_CONTR_AGT_MODIF_NUM, /* 对公贷款合同协议修饰符 */
  T1.CORP_LOAN_CRDT_ACCT_NUM, /* 对公贷款授信协议号 */
  T1.CORP_LOAN_CRDT_AGT_MODIF_NUM, /* 对公贷款授信协议修饰符 */
  T1.HOST_PROD_CD, /* 主机产品代码 */
  T1.LOAN_INIT_BIZ_TYPE_CD, /* 贷款发起业务类型代码 */
  T1.SPON_CRDT_TELLER_NUM, /* 主办信贷柜员号 */
  T1.OUT_ACCT_FLW_INIT_CRDT_ORG_NUM, /* 出账流程发起信贷机构号 */
  T1.RISK_MGMT_REC_PARTY_ID, /* 风控采录当事人编号              zzr added */
  T1.MARGIN_EQ_AMT, /* 保证金等价物金额 */
  T1.MARGIN_RATIO, /* 保证金比例 */
  T1.MARGIN_AMT, /* 保证金金额 */
  T1.MARGIN_CURRENCY_CD, /* 保证金币种 */
  T1.MARGIN_ACCT_NUM, /* 保证金账号 */
  T1.DIR_INDUS_TYPE_CD, /* 投向行业种类代码 */
  T1.LOAN_USAGE_CD, /* 贷款用途代码 */
  T1.REPAY_MODE_CD, /* 还款方式代码 */
  T1.LOW_RISK_BIZ_IND, /* 低风险业务标志 */
  T1.MS_IND, /* 中小标志 */
  T1.CTY_LIMIT_DIR_INDUS_IND, /* 是否国家限制投向行业标志 */
  T1.DISTR_DT, /* 放款日期 */
  T1.DISTR_AMT_CURRENCY_CD, /* 放款金额币种 */
  T1.DISTR_AMT, /* 放款金额 */
  T1.BASE_INT_RATE, /* 基准利率 */
  T1.EXEC_INT_RATE, /* 执行利率 */
  T1.INT_RATE_FLOAT_RATIO, /* 利率浮动比率 */
  T1.INT_RATE_FLOAT_VAL, /* 利率浮动值 */
  T1.INT_RATE_PRICE_STD_CD, /* 利率定价标准代码 */
  T1.FOLLOW_PBC_INT_RATE_CHANGE_IND, /* 随央行利率变动标志 */
  T1.START_INT_DT, /* 起息日期 */
  T1.MATURE_DT, /* 到期日期 */
  T1.LOAN_TERM, /* 贷款期限 */
  T1.LOAN_TERM_FREQ_CD, /* 贷款期限周期代码 */
  T1.LOAN_REMAIN_TERM, /* 贷款剩余期限 */
  T1.REPAY_INTRV, /* 还款间隔 */
  T1.REPAY_INTRV_FREQ_CD, /* 还款间隔周期代码 */
  T1.LOAN_DISP_MODE_CD, /* 贷款处置方式代码 */
  T1.ENTR_LOAN_SYNDIC_LOAN_IND, /* 委托贷款银团贷款标志 */
  T1.INTER_SYNDIC_LOAN_IND, /* 国际银团贷款标志 */
  T1.CALC_INT_PERIOD_CD, /* 计息周期代码 */
  T1.ADJ_INT_TMPNT_MODE_CD, /* 调息时点模式代码 */
  T1.PUNISH_INT_RATE_FLOAT_RATIO, /* 罚息利率浮动比例 */
  T1.DEBT_RATING_LEVEL_CD, /* 债项评级级别代码 */
  T1.OVDUE_BAD_LOAN_REVW_RESULT_CD, /* 一逾两呆贷款评审结果代码 */
  T1.GRADE5_CLASS_REVW_RESULT_CD, /* 五级分类贷款评审结果代码 */
  T1.GRADE10_CLASS_REVW_RESULT_CD, /* 十级分类贷款评审结果代码 */
  T1.RENEW_IND, /* 展期标志 */
  T1.RENEW_AMT, /* 展期金额   zzr added */
  T1.LOAN_DUBIL_STAT_CD, /* 贷款借据状态代码 */
  T1.WRTOFF_IND, /* 核销标志 */
  T1.PAYOFF_IND, /* 结清标志 */
  T1.CLEAR_DT, /* 结清日期 */
  T1.REMAIN_PRIN, /* 剩余本金 */
  T1.AGENT_CAP, /* 代理资金 */
  T1.NORMAL_PRIN, /* 正常本金 */
  T1.OVDUE_PRIN, /* 逾期本金 */
  T1.IDLE_PRIN, /* 呆滞本金 */
  T1.BAD_DEBT_PRIN, /* 呆账本金 */
  T1.IN_BS_PUNISH_INT, /* 表内罚息 */
  T1.OFF_BS_PUNISH_INT, /* 表外罚息 */
  T1.IN_BS_OWE_INT, /* 表内欠息 */
  T1.OFF_BS_OWE_INT, /* 表外欠息 */
  T1.WRTOFF_PRIN, /* 核销本金  zzr added */
  T1.WRTOFF_INT, /* 核销利息  zzr added */
  T1.ACCRU_INT, /* 计提利息  zzr added */
  T1.PAID_INT_TOTAL_AMT, /* 已还利息总额 */
  T1.PAID_PUNISH_INT_TOTAL_AMT, /* 已还罚息总额 */
  CASE
    WHEN T2.OVDUE_BEGIN_DT IS NULL
    THEN CAST('3000-12-31' AS DATE)
    ELSE T2.OVDUE_BEGIN_DT
  END, /* 逾期起始日期 */
  COALESCE(CASE WHEN T2.ENTR_LOAN_SYNDIC_LOAN_IND = '0' THEN 0 ELSE T2.LOAN_OVDUE_DAYS END, 0) AS LOAN_OVDUE_DAYS, /* 贷款逾期天数 */
  T1.PRIN_OVDUE_BEGIN_DT, /* 本金逾期起始日期 */
  CASE
    WHEN T2.OWE_INT_BEGIN_DT IS NULL
    THEN CAST('3000-12-31' AS DATE)
    ELSE T2.OWE_INT_BEGIN_DT
  END, /* 欠息起始日期 */
  T1.NEXT_TERM_REPAY_DT, /* 下期还本日期 */
  T1.NEXT_TERM_REPAY_AMT, /* 下期还本金额 */
  T1.NEXT_TERM_REPAY_INT_DT, /* 下期还息日期 */
  T1.NEXT_TERM_REPAY_INT_AMT, /* 下期还息金额 */
  T1.LOAN_MONTH_DAY_AVG, /* 贷款月日均 */
  T1.LOAN_QUAR_DAY_AVG, /* 贷款季日均 */
  T1.LOAN_YEAR_DAY_AVG, /* 贷款年日均 */
  T1.LOAN_LATE_3MONTH_DAY_AVG, /* 贷款近三个月日均 */
  T1.LOAN_LATE_YEAR_DAY_AVG, /* 贷款近一年日均 */
  T1.NORMAL_PRIN_M_DAY_AVG, /* 正常本金月日均 ADD JIANGLEI ******** */
  T1.OVDUE_PRIN_M_DAY_AVG, /* 逾期本金月日均 ADD JIANGLEI ******** */
  T1.IDLE_PRIN_M_DAY_AVG, /* 呆滞本金月日均 ADD JIANGLEI ******** */
  T1.BAD_DEBT_PRIN_M_DAY_AVG, /* 呆账本金月日均 ADD JIANGLEI ******** */
  T1.NORMAL_PRIN_Q_DAY_AVG, /* 正常本金季日均 ADD JIANGLEI ******** */
  T1.OVDUE_PRIN_Q_DAY_AVG, /* 逾期本金季日均 ADD JIANGLEI ******** */
  T1.IDLE_PRIN_Q_DAY_AVG, /* 呆滞本金季日均 ADD JIANGLEI ******** */
  T1.BAD_DEBT_PRIN_Q_DAY_AVG, /* 呆账本金季日均 ADD JIANGLEI ******** */
  T1.NORMAL_PRIN_Y_DAY_AVG, /* 正常本金年日均 ADD JIANGLEI ******** */
  T1.OVDUE_PRIN_Y_DAY_AVG, /* 逾期本金年日均 ADD JIANGLEI ******** */
  T1.IDLE_PRIN_Y_DAY_AVG, /* 呆滞本金年日均 ADD JIANGLEI ******** */
  T1.BAD_DEBT_PRIN_Y_DAY_AVG, /* 呆账本金年日均 ADD JIANGLEI ******** */ /* ,T1.NORMAL_PRIN_GL_NUM                                        正常本金科目号 */
  CASE
    WHEN NOT T5.EVNO IS NULL AND T1.REMAIN_PRIN + T1.AGENT_CAP > 0
    THEN T5.GL_NUM
    ELSE T1.NORMAL_PRIN_GL_NUM
  END, /* 正常本金科目号 */
  T1.OVDUE_PRIN_GL_NUM, /* 逾期本金科目号 */
  T1.IDLE_PRIN_GL_NUM, /* 呆滞本金科目号 */
  T1.BAD_DEBT_PRIN_GL_NUM, /* 呆账本金科目号 */
  T1.ACCRU_INT_GL_NUM, /* 应计利息科目号 */
  T1.COLLBL_INT_GL_NUM, /* 应收利息科目号 */
  T1.COLLBL_INT, /* 应收利息 */
  COALESCE(T3.GROUP_ID, '') AS CONCN_CUST_GROUP1_NUM, /* 关注客群1编号 */
  CASE WHEN CONCN_CUST_GROUP1_NUM <> '' THEN '地产' ELSE '' END AS CONCN_CUST_GROUP1_BCLASS_DESC, /* 关注客群1大类描述 */
  COALESCE(T4.GROUP_ID, '') AS CONCN_CUST_GROUP2_NUM, /* 关注客群2编号 */
  CASE WHEN CONCN_CUST_GROUP2_NUM <> '' THEN '战略' ELSE '' END AS CONCN_CUST_GROUP2_BCLASS_DESC /* 关注客群2大类描述 */
FROM tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM AS T1
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM1 AS T2
  ON T1.ACCT_NUM = T2.ACCT_NUM
  AND T1.AGT_MODIF_NUM = T2.AGT_MODIF_NUM
  AND T1.VAL_TYPE_CD = T2.VAL_TYPE_CD
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_GROUP_DETAILS_REAL AS T3
  ON T1.HOST_CUST_ID = T3.CUST_NO AND T3.GROUP_ID LIKE '0940%'
LEFT JOIN tmp_t88_corp_loan_agt_sum_cdm_10200.VT_CORP_GROUP_DETAILS_REAL AS T4
  ON T1.HOST_CUST_ID = T4.CUST_NO AND T4.GROUP_ID LIKE '0903%'
LEFT JOIN (
  SELECT
    T1.EVNO,
    T2.GL_NUM
  FROM ODB.SA0_LAMVA AS T1
  INNER JOIN CDB.T98_GL_ACCT AS T2
    ON SUBSTRING(T2.GL_NUM, 5, 5) = T1.ITEM AND T2.GL_NUM LIKE '%9'
  WHERE
    T1.START_DT <= '********'
    AND T1.END_DT > '********'
    AND NOT T1.EVNO IN (
      SELECT
        LOAN_NO
      FROM ODB.SDI_ACCOUNT_MAIN
      WHERE
        START_DT <= '********' AND END_DT > '********'
    )
    AND T1.EVNO IN (
      SELECT
        ACCT_NUM
      FROM tmp_t88_corp_loan_agt_sum_cdm_10200.VT88_CORP_LOAN_AGT_SUM
      WHERE
        REMAIN_PRIN + AGENT_CAP > 0
    )
  GROUP BY
    1,
    2
) AS T5
  ON T1.ACCT_NUM = T5.EVNO;
