-- ODS 到 DWD 层的 ETL 处理
-- 作者：数据工程团队
-- 创建日期：2023-06-01
-- 最后修改：2023-06-15

-- 1. 客户全量信息整合 (ODS_CUSTOMER_CLEAN + EXTERNAL_CREDIT_SCORES → DWD_CUSTOMER_FULL)
INSERT INTO dwd_customer_full (unified_id, identity_info, contact_info, credit_score, create_time, update_time, valid_from)
SELECT 
    'U' || oc.id_card AS unified_id,
    jsonb_build_object(
        'name', oc.legal_name,
        'id_card', oc.id_card,
        'gender', CASE 
            WHEN SUBSTRING(oc.id_card, 17, 1) ~ '^[0-9]+$' THEN 
                CASE WHEN SUBSTRING(oc.id_card, 17, 1)::int % 2 = 1 THEN '男' ELSE '女' END
            ELSE '未知'
        END,
        'birth_date', CASE 
            WHEN SUBSTRING(oc.id_card, 7, 8) ~ '^[0-9]{8}$' AND 
                 SUBSTRING(oc.id_card, 7, 2)::int BETWEEN 19 AND 20 AND
                 SUBSTRING(oc.id_card, 9, 2)::int BETWEEN 1 AND 12 AND
                 SUBSTRING(oc.id_card, 11, 2)::int BETWEEN 1 AND 31
            THEN 
                -- 使用 CAST 并捕获可能的错误
                CAST(SUBSTRING(oc.id_card, 7, 4) || '-' || 
                     SUBSTRING(oc.id_card, 11, 2) || '-' || 
                     SUBSTRING(oc.id_card, 13, 2) AS date)
            ELSE NULL
        END
    ) AS identity_info,
    jsonb_build_object(
        'phone', oc.contact_phone,
        'email', sc.raw_data->>'email',
        'address', sc.raw_data->>'address'
    ) AS contact_info,
    COALESCE(ecs.risk_score, 0) AS credit_score,
    oc.etl_time AS create_time,
    CURRENT_TIMESTAMP AS update_time,
    CURRENT_DATE AS valid_from
FROM 
    ods_customer_clean oc
JOIN 
    src_customer sc ON oc.src_cust_id = sc.cust_id
LEFT JOIN 
    external_credit_scores ecs ON oc.id_card = ecs.id_number
WHERE 
    'U' || oc.id_card NOT IN (SELECT unified_id FROM dwd_customer_full);

-- 2. 交易合并表 (ODS_TRADE_STD + ODS_EXTERNAL_TRADE_STD → DWD_TRADE_COMBINED)
INSERT INTO dwd_trade_combined (trade_source, trade_id, cust_id, amount, trade_time, channel_or_system)
-- 内部交易
SELECT 
    '内部' AS trade_source,
    ots.trade_id::VARCHAR(50) AS trade_id,
    ots.cust_id,
    ots.amount,
    ots.trade_time,
    ots.channel AS channel_or_system
FROM 
    ods_trade_std ots
WHERE 
    ots.trade_id::VARCHAR(50) NOT IN (
        SELECT trade_id FROM dwd_trade_combined WHERE trade_source = '内部'
    )
UNION ALL
-- 外部交易
SELECT 
    '外部' AS trade_source,
    oets.ext_trade_id AS trade_id,
    oets.cust_id,
    oets.amount,
    oets.trade_time,
    oets.source_system AS channel_or_system
FROM 
    ods_external_trade_std oets
WHERE 
    oets.ext_trade_id NOT IN (
        SELECT trade_id FROM dwd_trade_combined WHERE trade_source = '外部'
    );

-- 2. 交易合并表 (ODS_TRADE_STD + ODS_EXTERNAL_TRADE_STD → DWD_TRADE_COMBINED)
INSERT INTO dwd_trade_combined (trade_source, trade_id, cust_id, amount, trade_time, channel_or_system)
-- 内部交易
SELECT 
    '内部' AS trade_source,
    ots.trade_id::VARCHAR(50) AS trade_id,
    ots.cust_id,
    ots.amount,
    ots.trade_time,
    ots.channel AS channel_or_system
FROM 
    ods_trade_std ots
WHERE 
    ots.trade_id::VARCHAR(50) NOT IN (
        SELECT trade_id FROM dwd_trade_combined WHERE trade_source = '内部'
    )
UNION ALL
-- 外部交易
SELECT 
    '外部' AS trade_source,
    oets.ext_trade_id AS trade_id,
    oets.cust_id,
    oets.amount,
    oets.trade_time,
    oets.source_system AS channel_or_system
FROM 
    ods_external_trade_std oets
WHERE 
    oets.ext_trade_id NOT IN (
        SELECT trade_id FROM dwd_trade_combined WHERE trade_source = '外部'
    );
    
-- 3. 交易分析表 (ODS_TRADE_STD + ODS_EXTERNAL_TRADE_STD → DWD_TRADE_ANALYSIS)
INSERT INTO dwd_trade_analysis (
    analysis_id, trade_id, cust_id, trade_amount, 
    trade_type_group, risk_score, trade_time, etl_batch_id
)
-- 内部交易分析
SELECT 
    nextval('dwd_trade_analysis_analysis_id_seq'::regclass) AS analysis_id,
    ots.trade_id::VARCHAR(50) AS trade_id,
    ots.cust_id,
    ots.amount AS trade_amount,
    ots.trade_category AS trade_type_group,
    ots.risk_level::NUMERIC(5,2) AS risk_score,
    ots.trade_time,
    20230601 AS etl_batch_id
FROM 
    ods_trade_std ots
WHERE 
    ots.trade_id::VARCHAR(50) NOT IN (SELECT trade_id FROM dwd_trade_analysis)
UNION ALL
-- 外部交易分析
SELECT 
    nextval('dwd_trade_analysis_analysis_id_seq'::regclass) AS analysis_id,
    oets.ext_trade_id AS trade_id,
    oets.cust_id,
    oets.amount AS trade_amount,
    oets.trade_category AS trade_type_group,
    oets.risk_level::NUMERIC(5,2) AS risk_score,
    oets.trade_time,
    20230601 AS etl_batch_id
FROM 
    ods_external_trade_std oets
WHERE 
    oets.ext_trade_id NOT IN (SELECT trade_id FROM dwd_trade_analysis);

-- 4. 产品详情表 (ODS_PRODUCT_STD + ODS_MARKET_DATA_STD + EXTERNAL_MARKET_INDEX → DWD_PRODUCT_DETAIL)
INSERT INTO dwd_product_detail (
    product_id, product_name, product_category, 
    risk_rating, expected_return, term_days, 
    is_active, detail_info, update_time
)
SELECT 
    ops.product_id,
    ops.product_name,
    ops.product_category,
    ops.risk_rating,
    ops.yield_rate AS expected_return,
    COALESCE(ops.duration_days, 0) AS term_days,
    CASE 
        WHEN ops.status = '在售' THEN TRUE
        ELSE FALSE
    END AS is_active,
    jsonb_build_object(
        'category', ops.product_category,
        'risk_rating', ops.risk_rating,
        'expected_return', ops.yield_rate,
        'term_days', COALESCE(ops.duration_days, 0),
        'status', ops.status,
        -- 市场数据
        'market_data', jsonb_build_object(
            'latest_price', COALESCE(omds.price, 0),
            'price_type', omds.price_type,
            'trading_datetime', omds.trading_datetime,
            'data_source', omds.source
        ),
        -- 市场指数数据
        'market_index', jsonb_build_object(
            'related_index', emi.index_name,
            'index_value', emi.index_value,
            'index_change_pct', emi.change_pct,
            'index_date', emi.index_date
        )
    ) AS detail_info,
    CURRENT_TIMESTAMP AS update_time
FROM 
    ods_product_std ops
LEFT JOIN 
    ods_market_data_std omds ON ops.product_id = omds.instrument_code 
    AND omds.trading_datetime = (
        SELECT MAX(trading_datetime) 
        FROM ods_market_data_std 
        WHERE instrument_code = ops.product_id
    )
LEFT JOIN 
    external_market_index emi ON ops.product_category = emi.index_code 
    AND emi.index_date = (
        SELECT MAX(index_date) 
        FROM external_market_index 
        WHERE index_code = ops.product_category
    )
WHERE 
    ops.product_id NOT IN (SELECT product_id FROM dwd_product_detail);

-- 5. 账户资产表 (ODS_ACCOUNT_CLEAN + DWD_PRODUCT_DETAIL → DWD_ACCOUNT_ASSET)
INSERT INTO dwd_account_asset (
    account_id, cust_id, account_type,
    current_balance, available_balance,
    product_holdings, total_asset_value,
    update_time
)
SELECT 
    oac.account_id,
    oac.cust_id,
    oac.account_type,
    oac.balance AS current_balance,
    oac.balance AS available_balance,
    COALESCE(
        jsonb_build_object(
            'products', (
                SELECT jsonb_agg(
                    jsonb_build_object(
                        'product_id', dpd.product_id,
                        'product_name', dpd.product_name,
                        'category', dpd.product_category,
                        'risk_rating', dpd.risk_rating,
                        'expected_return', dpd.expected_return,
                        'is_active', dpd.is_active
                    )
                )
                FROM dwd_product_detail dpd
            )
        ),
        '{}'::jsonb
    ) AS product_holdings,
    oac.balance AS total_asset_value,  -- 由于没有持仓市值信息，暂时用余额表示
    CURRENT_TIMESTAMP AS update_time
FROM 
    ods_account_clean oac
WHERE 
    oac.account_id NOT IN (SELECT account_id FROM dwd_account_asset);

-- 6. 客户行为表 (DWD_TRADE_COMBINED + DWD_CUSTOMER_FULL + DWD_TRADE_ANALYSIS → DWD_CUSTOMER_BEHAVIOR)
INSERT INTO dwd_customer_behavior (
    cust_id, trading_preference, risk_indicators, 
    behavior_pattern, behavior_period, start_date, end_date
)
WITH customer_trades AS (
    SELECT 
        dtc.cust_id,
        COUNT(*) AS trade_count,
        COUNT(CASE WHEN dtc.channel_or_system IN ('网银', '手机银行', '微信', '支付宝') THEN 1 END) AS online_count,
        COUNT(CASE WHEN dtc.channel_or_system IN ('柜台', 'ATM', '电话银行') THEN 1 END) AS offline_count,
        AVG(dtc.amount) AS avg_amount,
        MAX(dtc.amount) AS max_amount,
        COUNT(DISTINCT DATE_TRUNC('day', dtc.trade_time)) AS active_days,
        MAX(dtc.trade_time) AS last_trade_time,
        MIN(dtc.trade_time) AS first_trade_time
    FROM 
        dwd_trade_combined dtc
    GROUP BY 
        dtc.cust_id
),
customer_risk AS (
    SELECT 
        dta.cust_id,
        AVG(dta.risk_score) AS avg_risk_score,
        MAX(dta.risk_score) AS max_risk_score,
        COUNT(CASE WHEN dta.risk_score > 3 THEN 1 END) AS high_risk_count,
        STRING_AGG(DISTINCT dta.trade_type_group, ',') AS trade_types
    FROM 
        dwd_trade_analysis dta
    GROUP BY 
        dta.cust_id
),
customer_info AS (
    SELECT 
        'U' || (dcf.identity_info->>'id_card') AS cust_id,
        dcf.identity_info->>'gender' AS gender,
        (dcf.identity_info->>'birth_date')::date AS birth_date,
        dcf.valid_from AS info_valid_from
    FROM 
        dwd_customer_full dcf
)
SELECT 
    ct.cust_id,
    jsonb_build_object(
        'preferred_channel', CASE WHEN COALESCE(ct.online_count, 0) > COALESCE(ct.offline_count, 0) THEN '线上' ELSE '线下' END,
        'avg_transaction', COALESCE(ct.avg_amount, 0),
        'transaction_frequency', CASE 
            WHEN COALESCE(ct.trade_count, 0) = 0 OR ct.first_trade_time IS NULL THEN 0
            ELSE ct.trade_count::FLOAT / GREATEST(1, EXTRACT(DAY FROM (ct.last_trade_time - ct.first_trade_time)))
        END,
        'preferred_time', (
            SELECT TO_CHAR(MODE() WITHIN GROUP (ORDER BY DATE_PART('hour', dtc.trade_time)), 'FM00') || ':00'
            FROM dwd_trade_combined dtc
            WHERE dtc.cust_id = ct.cust_id
        ),
        'customer_gender', COALESCE(ci.gender, '未知'),
        'trade_types', COALESCE(cr.trade_types, '无交易')
    ) AS trading_preference,
    jsonb_build_object(
        'risk_score', COALESCE(cr.avg_risk_score, 0),
        'high_risk_transactions', COALESCE(cr.high_risk_count, 0),
        'risk_level', CASE 
            WHEN COALESCE(cr.avg_risk_score, 0) >= 4 THEN '高风险'
            WHEN COALESCE(cr.avg_risk_score, 0) >= 2.5 THEN '中风险'
            ELSE '低风险'
        END
    ) AS risk_indicators,
    jsonb_build_object(
        'activity_level', CASE 
            WHEN COALESCE(ct.trade_count, 0) > 50 THEN '高活跃'
            WHEN COALESCE(ct.trade_count, 0) > 20 THEN '中活跃'
            ELSE '低活跃'
        END,
        'transaction_pattern', CASE 
            WHEN COALESCE(ct.max_amount, 0) > 10 * COALESCE(ct.avg_amount, 1) THEN '大额不定期'
            WHEN COALESCE(ct.active_days, 0) > 20 THEN '高频小额'
            ELSE '一般'
        END,
        'last_active', ct.last_trade_time
    ) AS behavior_pattern,
    '月度' AS behavior_period,
    DATE_TRUNC('month', CURRENT_DATE)::DATE AS start_date,
    (DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day')::DATE AS end_date
FROM 
    customer_trades ct
LEFT JOIN 
    customer_risk cr ON ct.cust_id = cr.cust_id
LEFT JOIN 
    customer_info ci ON ct.cust_id = ci.cust_id
WHERE 
    ct.cust_id NOT IN (SELECT cust_id FROM dwd_customer_behavior); 