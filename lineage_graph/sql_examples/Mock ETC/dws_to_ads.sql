-- DWS 到 ADS 层的 ETL 处理
-- 作者：数据工程团队
-- 创建日期：2023-06-01
-- 最后修改：2023-06-15

-- 1. 监管报告 (DWS_DAILY_REPORT + ODS_TRADE_STD + ODS_EXTERNAL_TRADE_STD → ADS_REGULATORY_REPORT)
-- 该报表整合了:
-- - DWS_DAILY_REPORT: 提供总交易量等基础指标
-- - ODS_TRADE_STD: 用于统计大额交易
-- - ODS_EXTERNAL_TRADE_STD: 用于统计跨境交易金额
INSERT INTO ads_regulatory_report (
    report_date, total_transactions, large_transactions, cross_border_amount
)
WITH large_txns AS (
    SELECT 
        DATE_TRUNC('day', ots.trade_time)::DATE AS trade_date,
        COUNT(*) AS large_count
    FROM 
        ods_trade_std ots
    WHERE 
        ots.amount > 50000
    GROUP BY 
        DATE_TRUNC('day', ots.trade_time)::DATE
),
cross_border AS (
    SELECT 
        DATE_TRUNC('day', oets.trade_time)::DATE AS trade_date,
        COALESCE(SUM(oets.amount), 0) AS cross_amount
    FROM 
        ods_external_trade_std oets
    WHERE 
        oets.source_system = '外汇系统'
    GROUP BY 
        DATE_TRUNC('day', oets.trade_time)::DATE
)
SELECT 
    ddr.report_date,
    ddr.total_transactions,
    COALESCE(lt.large_count, 0) AS large_transactions,
    COALESCE(cb.cross_amount, 0) AS cross_border_amount
FROM 
    dws_daily_report ddr
LEFT JOIN 
    large_txns lt ON ddr.report_date = lt.trade_date
LEFT JOIN 
    cross_border cb ON ddr.report_date = cb.trade_date
WHERE 
    ddr.report_date NOT IN (SELECT report_date FROM ads_regulatory_report);

-- 2. 风险报告 (DWS_RISK_ALERT + DWD_CUSTOMER_BEHAVIOR + SRC_CUSTOMER + ODS_CUSTOMER_CLEAN + DWD_CUSTOMER_FULL → ADS_RISK_REPORT)
-- 该报表整合了:
-- - DWS_RISK_ALERT: 提供风险预警信息
-- - DWD_CUSTOMER_BEHAVIOR: 提供客户风险等级分布
-- - SRC_CUSTOMER + ODS_CUSTOMER_CLEAN + DWD_CUSTOMER_FULL: 提供客户基础信息
INSERT INTO ads_risk_report (
    report_period, risk_distribution, top_risky_customers
)
WITH alert_types_count AS (
    SELECT 
        alert_type,
        COUNT(*) AS type_count
    FROM 
        dws_risk_alert
    WHERE 
        DATE_TRUNC('month', alert_time) = DATE_TRUNC('month', CURRENT_DATE)
    GROUP BY 
        alert_type
),
alert_types_json AS (
    SELECT 
        jsonb_object_agg(
            alert_type,
            type_count
        ) AS json_data
    FROM 
        alert_types_count
),
monthly_stats AS (
    SELECT 
        DATE_TRUNC('month', alert_time)::DATE AS report_month,
        COUNT(*) AS total_alerts,
        COUNT(DISTINCT cust_id) AS affected_customers
    FROM 
        dws_risk_alert
    WHERE 
        DATE_TRUNC('month', alert_time) = DATE_TRUNC('month', CURRENT_DATE)
    GROUP BY 
        DATE_TRUNC('month', alert_time)::DATE
),
top_alert_customers AS (
    SELECT 
        cust_id,
        COUNT(*) AS alert_count
    FROM 
        dws_risk_alert
    WHERE 
        DATE_TRUNC('month', alert_time) = DATE_TRUNC('month', CURRENT_DATE)
    GROUP BY 
        cust_id
    ORDER BY 
        COUNT(*) DESC
    LIMIT 10
),
customer_alerts AS (
    SELECT 
        cust_id,
        jsonb_build_object(
            'alert_type', alert_type,
            'alert_time', alert_time,
            'details', details
        ) AS alert_json
    FROM 
        dws_risk_alert
    WHERE 
        cust_id IN (SELECT cust_id FROM top_alert_customers)
        AND DATE_TRUNC('month', alert_time) = DATE_TRUNC('month', CURRENT_DATE)
),
customer_alerts_agg AS (
    SELECT 
        cust_id,
        jsonb_agg(alert_json) AS alerts
    FROM 
        customer_alerts
    GROUP BY 
        cust_id
),
customer_info_basic AS (
    SELECT DISTINCT
        sc.cust_id,
        oc.id_card,
        dcf.identity_info->>'name' AS name,
        dcf.credit_score
    FROM 
        src_customer sc
    JOIN 
        ods_customer_clean oc ON sc.cust_id = oc.src_cust_id
    LEFT JOIN 
        dwd_customer_full dcf ON dcf.unified_id = 'U' || oc.id_card
),
customer_full_info AS (
    SELECT 
        tac.cust_id,
        tac.alert_count,
        COALESCE(caa.alerts, '[]'::jsonb) AS alerts,
        jsonb_build_object(
            'name', COALESCE(cib.name, '未知'),
            'risk_score', COALESCE(cib.credit_score, 0)
        ) AS customer_info
    FROM 
        top_alert_customers tac
    LEFT JOIN 
        customer_alerts_agg caa ON tac.cust_id = caa.cust_id
    LEFT JOIN 
        customer_info_basic cib ON tac.cust_id = cib.cust_id
),
customer_json_agg AS (
    SELECT 
        jsonb_agg(
            jsonb_build_object(
                'cust_id', cust_id,
                'alert_count', alert_count,
                'alerts', alerts,
                'customer_info', customer_info
            )
        ) AS customers_json
    FROM 
        customer_full_info
),
risk_level_counts AS (
    SELECT 
        COUNT(*) AS high_risk_count
    FROM 
        dwd_customer_behavior
    WHERE 
        (risk_indicators->>'risk_level') = '高风险'
),
total_customers AS (
    SELECT 
        COUNT(*) AS total_count
    FROM 
        dwd_customer_behavior
)
SELECT 
    TO_CHAR(ms.report_month, 'YYYY-MM') AS report_period,
    jsonb_build_object(
        'total_alerts', ms.total_alerts,
        'affected_customers', ms.affected_customers,
        'alert_types', (SELECT json_data FROM alert_types_json),
        'high_risk_ratio', CASE 
            WHEN (SELECT total_count FROM total_customers) = 0 THEN 0
            ELSE (SELECT high_risk_count FROM risk_level_counts)::FLOAT / (SELECT total_count FROM total_customers)
        END
    ) AS risk_distribution,
    (SELECT customers_json FROM customer_json_agg) AS top_risky_customers
FROM 
    monthly_stats ms;

-- 3. 投资组合分析 (DWS_ASSET_SUMMARY → ADS_INVESTMENT_PORTFOLIO)
INSERT INTO ads_investment_portfolio (
    analysis_date, portfolio_allocation, performance_metrics, recommendation
)
WITH asset_daily_data AS (
    SELECT 
        summary_date,
        asset_category,
        total_value,
        COALESCE(daily_change, 0) AS daily_change,
        COALESCE(daily_change_pct, 0) AS daily_change_pct
    FROM 
        dws_asset_summary
    WHERE 
        summary_date >= CURRENT_DATE - INTERVAL '30 days'
),
asset_max_date AS (
    SELECT 
        asset_category,
        MAX(summary_date) AS latest_date
    FROM 
        asset_daily_data
    GROUP BY 
        asset_category
),
asset_latest_data AS (
    SELECT DISTINCT ON (add.asset_category)
        add.asset_category,
        add.total_value,
        add.daily_change,
        add.daily_change_pct,
        add.summary_date
    FROM 
        asset_daily_data add
    JOIN 
        asset_max_date amd ON add.asset_category = amd.asset_category 
        AND add.summary_date = amd.latest_date
    ORDER BY 
        add.asset_category, add.summary_date DESC
),
portfolio_total AS (
    SELECT 
        summary_date,
        SUM(total_value) AS total_value,
        SUM(daily_change) AS daily_change,
        (SUM(daily_change) * 100.0 / NULLIF(SUM(total_value) - SUM(daily_change), 0))::NUMERIC(5,2) AS daily_change_pct
    FROM 
        asset_daily_data
    GROUP BY 
        summary_date
),
asset_allocation_data AS (
    SELECT 
        add.summary_date,
        add.asset_category,
        add.total_value,
        pt.total_value,
        (add.total_value * 100.0 / pt.total_value)::NUMERIC(5,2) AS percentage
    FROM 
        asset_daily_data add
    JOIN 
        portfolio_total pt ON add.summary_date = pt.summary_date
),
allocation_json AS (
    SELECT 
        summary_date AS analysis_date,
        jsonb_object_agg(
            asset_category,
            percentage
        ) AS allocation_data
    FROM 
        asset_allocation_data
    GROUP BY 
        summary_date
),
performance_summary AS (
    SELECT 
        summary_date,
        SUM(total_value) AS total_value,
        SUM(daily_change) AS daily_change,
        (SUM(daily_change) * 100.0 / NULLIF(SUM(total_value) - SUM(daily_change), 0))::NUMERIC(5,2) AS daily_change_pct
    FROM 
        asset_daily_data
    GROUP BY 
        summary_date
),
best_asset AS (
    SELECT DISTINCT
        asset_category,
        daily_change_pct
    FROM 
        asset_daily_data
    ORDER BY 
        daily_change_pct DESC
    LIMIT 1
),
worst_asset AS (
    SELECT DISTINCT
        asset_category,
        daily_change_pct
    FROM 
        asset_daily_data
    ORDER BY 
        daily_change_pct ASC
    LIMIT 1
),
performance_json AS (
    SELECT 
        ps.summary_date,
        jsonb_build_object(
            'total_value', ps.total_value,
            'daily_change', ps.daily_change,
            'daily_change_pct', ps.daily_change_pct,
            'best_performing', (SELECT asset_category FROM best_asset),
            'worst_performing', (SELECT asset_category FROM worst_asset)
        ) AS performance_data
    FROM 
        performance_summary ps
),
high_concentration AS (
    SELECT 
        COUNT(*) > 0 AS exists
    FROM 
        asset_allocation_data
    WHERE 
        percentage > 40
),
high_risk_allocation AS (
    SELECT 
        SUM(percentage) > 60 AS exists
    FROM 
        asset_allocation_data
    WHERE 
        asset_category IN ('股票', '基金')
),
low_risk_allocation AS (
    SELECT 
        SUM(percentage) > 80 AS exists
    FROM 
        asset_allocation_data
    WHERE 
        asset_category IN ('存款', '债券')
),
opportunity_asset AS (
    SELECT 
        asset_category
    FROM 
        asset_daily_data
    WHERE 
        daily_change_pct < -2
    ORDER BY 
        daily_change_pct ASC
    LIMIT 1
),
recommendation_json AS (
    SELECT 
        jsonb_build_object(
            'rebalance_suggestion', CASE 
                WHEN (SELECT exists FROM high_concentration) THEN '建议分散投资，降低单一资产类别占比'
                ELSE '当前资产配置较为均衡'
            END,
            'risk_assessment', CASE 
                WHEN (SELECT exists FROM high_risk_allocation) THEN '当前风险较高，建议适当增加低风险资产'
                WHEN (SELECT exists FROM low_risk_allocation) THEN '当前风险较低，可考虑适当增加收益型资产'
                ELSE '当前风险适中'
            END,
            'opportunity', (SELECT asset_category FROM opportunity_asset)
        ) AS recommendation_data
    FROM 
        asset_daily_data
    LIMIT 1
)
SELECT 
    aj.analysis_date,
    aj.allocation_data AS portfolio_allocation,
    pj.performance_data AS performance_metrics,
    (SELECT recommendation_data FROM recommendation_json) AS recommendation
FROM 
    allocation_json aj
JOIN 
    performance_json pj ON aj.analysis_date = pj.summary_date;

-- 4. 客户洞察 (DWS_CUSTOMER_PROFILE + DWD_CUSTOMER_BEHAVIOR + DWD_ACCOUNT_ASSET + DWD_TRADE_ANALYSIS → ADS_CUSTOMER_INSIGHT)
-- 该报表整合了:
-- - DWS_CUSTOMER_PROFILE: 提供客户画像基础信息
-- - DWD_CUSTOMER_BEHAVIOR: 提供客户行为特征
-- - DWD_ACCOUNT_ASSET: 提供资产持仓信息
-- - DWD_TRADE_ANALYSIS: 提供交易行为分析
INSERT INTO ads_customer_insight (
    insight_date, customer_segments, behavior_patterns, cross_selling_opportunities
)
WITH current_profiles AS (
    SELECT *
    FROM dws_customer_profile
    WHERE profile_date = CURRENT_DATE
),
risk_segment_counts AS (
    SELECT 
        risk_preference,
        COUNT(*) AS count
    FROM 
        current_profiles
    GROUP BY 
        risk_preference
),
risk_segments_json AS (
    SELECT 
        jsonb_object_agg(
            risk_preference,
            count
        ) AS risk_data
    FROM 
        risk_segment_counts
),
asset_segments AS (
    SELECT 
        CASE 
            WHEN total_asset_value > 1000000 THEN '高净值'
            WHEN total_asset_value > 500000 THEN '富裕'
            WHEN total_asset_value > 100000 THEN '小康'
            ELSE '普通'
        END AS segment,
        COUNT(*) AS count
    FROM 
        current_profiles
    GROUP BY 
        CASE 
            WHEN total_asset_value > 1000000 THEN '高净值'
            WHEN total_asset_value > 500000 THEN '富裕'
            WHEN total_asset_value > 100000 THEN '小康'
            ELSE '普通'
        END
),
asset_segments_json AS (
    SELECT 
        jsonb_object_agg(
            segment,
            count
        ) AS asset_data
    FROM 
        asset_segments
),
customer_segments_json AS (
    SELECT 
        CURRENT_DATE AS profile_date,
        jsonb_build_object(
            'by_risk', (SELECT risk_data FROM risk_segments_json),
            'by_asset', (SELECT asset_data FROM asset_segments_json)
        ) AS segments_data
    FROM 
        current_profiles
    LIMIT 1
),
avg_transaction_value AS (
    SELECT 
        AVG(avg_transaction_amount) AS avg_amount
    FROM 
        current_profiles
),
product_preferences AS (
    SELECT 
        investment_style->>'preferred_products' AS product,
        COUNT(*) AS count
    FROM 
        current_profiles
    GROUP BY 
        investment_style->>'preferred_products'
),
product_preferences_json AS (
    SELECT 
        jsonb_object_agg(
            product,
            count
        ) AS product_data
    FROM 
        product_preferences
),
investment_horizons AS (
    SELECT 
        investment_style->>'investment_horizon' AS horizon,
        COUNT(*) AS count
    FROM 
        current_profiles
    GROUP BY 
        investment_style->>'investment_horizon'
),
investment_horizons_json AS (
    SELECT 
        jsonb_object_agg(
            horizon,
            count
        ) AS horizon_data
    FROM 
        investment_horizons
),
behavior_patterns_json AS (
    SELECT 
        CURRENT_DATE AS profile_date,
        jsonb_build_object(
            'avg_transaction', (SELECT avg_amount FROM avg_transaction_value),
            'preferred_products', (SELECT product_data FROM product_preferences_json),
            'investment_horizon', (SELECT horizon_data FROM investment_horizons_json)
        ) AS patterns_data
    FROM 
        current_profiles
    LIMIT 1
),
high_value_customers AS (
    SELECT 
        cust_id,
        total_asset_value,
        investment_style->>'preferred_products' AS current_products,
        risk_preference
    FROM 
        current_profiles
    WHERE 
        total_asset_value > 500000
        AND (investment_style->>'preferred_products') = '存款'
    LIMIT 20
),
suitable_products AS (
    SELECT 
        hvc.cust_id,
        hvc.total_asset_value,
        hvc.current_products,
        ops.product_name
    FROM 
        high_value_customers hvc
    JOIN 
        ods_product_std ops ON ops.risk_rating <= (
            CASE 
                WHEN hvc.risk_preference = '高风险' THEN 5
                WHEN hvc.risk_preference = '中风险' THEN 3
                ELSE 2
            END
        )
        AND ops.status = '在售'
),
customer_recommendations AS (
    SELECT 
        cust_id,
        total_asset_value,
        current_products,
        array_agg(product_name) AS product_array
    FROM (
        SELECT 
            cust_id,
            total_asset_value,
            current_products,
            product_name,
            ROW_NUMBER() OVER (PARTITION BY cust_id ORDER BY product_name) AS rn
        FROM 
            suitable_products
    ) ranked
    WHERE 
        rn <= 3
    GROUP BY 
        cust_id, total_asset_value, current_products
),
customer_recommendations_json AS (
    SELECT 
        cust_id,
        jsonb_build_object(
            'cust_id', cust_id,
            'total_asset', total_asset_value,
            'current_products', current_products,
            'recommended_products', to_jsonb(product_array)
        ) AS recommendation_data
    FROM 
        customer_recommendations
),
high_value_targets_json AS (
    SELECT 
        jsonb_agg(recommendation_data) AS targets_data
    FROM 
        customer_recommendations_json
),
product_customer_counts AS (
    SELECT 
        ops.product_category,
        COUNT(DISTINCT daa.cust_id) AS current_customers
    FROM 
        dwd_account_asset daa
    JOIN 
        ods_product_std ops ON daa.account_type = ops.product_category
    GROUP BY 
        ops.product_category
),
product_potential_counts AS (
    SELECT 
        pcc.product_category,
        pcc.current_customers,
        COUNT(dcp.cust_id) AS potential_count
    FROM 
        product_customer_counts pcc
    CROSS JOIN LATERAL (
        SELECT 
            cp.cust_id
        FROM 
            current_profiles cp
        WHERE 
            cp.risk_preference IN ('中风险', '高风险')
            AND cp.cust_id NOT IN (
                SELECT daa2.cust_id
                FROM dwd_account_asset daa2
                WHERE daa2.account_type = pcc.product_category
            )
    ) dcp
    GROUP BY 
        pcc.product_category, pcc.current_customers
),
product_opportunities_json AS (
    SELECT 
        jsonb_agg(
            jsonb_build_object(
                'product_category', product_category,
                'current_customers', current_customers,
                'potential_customers', potential_count
            )
        ) AS opportunities_data
    FROM 
        product_potential_counts
),
cross_selling_json AS (
    SELECT 
        CURRENT_DATE AS insight_date,
        jsonb_build_object(
            'high_value_targets', COALESCE((SELECT targets_data FROM high_value_targets_json), '[]'::jsonb),
            'upgrade_opportunities', COALESCE((SELECT opportunities_data FROM product_opportunities_json), '[]'::jsonb)
        ) AS opportunities_data
    FROM 
        current_profiles
    LIMIT 1
)
SELECT 
    csj1.profile_date AS insight_date,
    csj1.segments_data AS customer_segments,
    bpj.patterns_data AS behavior_patterns,
    csj2.opportunities_data AS cross_selling_opportunities
FROM 
    customer_segments_json csj1
JOIN 
    behavior_patterns_json bpj ON csj1.profile_date = bpj.profile_date
JOIN 
    cross_selling_json csj2 ON csj1.profile_date = csj2.insight_date; 