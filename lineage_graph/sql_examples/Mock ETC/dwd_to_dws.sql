-- DWD 到 DWS 层的 ETL 处理
-- 作者：数据工程团队
-- 创建日期：2023-06-01
-- 最后修改：2023-06-15

-- 1. 每日汇总报表 (ODS_TRADE_STD + ODS_EXTERNAL_TRADE_STD → DWS_DAILY_REPORT)
INSERT INTO dws_daily_report (report_date, total_customers, total_transactions, total_amount, high_risk_count)
WITH internal_stats AS (
    -- 内部交易统计
    SELECT 
        DATE_TRUNC('day', ots.trade_time)::DATE AS trade_date,
        COUNT(DISTINCT ots.cust_id) AS internal_customers,
        COUNT(*) AS internal_transactions,
        SUM(ots.amount) AS internal_amount,
        COUNT(CASE WHEN ots.risk_level >= 4 THEN 1 END) AS internal_high_risk
    FROM 
        ods_trade_std ots
    WHERE 
        DATE_TRUNC('day', ots.trade_time)::DATE = CURRENT_DATE - INTERVAL '1 day'
    GROUP BY 
        DATE_TRUNC('day', ots.trade_time)::DATE
),
external_stats AS (
    -- 外部交易统计
    SELECT 
        DATE_TRUNC('day', oets.trade_time)::DATE AS trade_date,
        COUNT(DISTINCT oets.cust_id) AS external_customers,
        COUNT(*) AS external_transactions,
        SUM(oets.amount) AS external_amount,
        COUNT(CASE WHEN oets.risk_level >= 4 THEN 1 END) AS external_high_risk
    FROM 
        ods_external_trade_std oets
    WHERE 
        DATE_TRUNC('day', oets.trade_time)::DATE = CURRENT_DATE - INTERVAL '1 day'
    GROUP BY 
        DATE_TRUNC('day', oets.trade_time)::DATE
)
SELECT 
    CURRENT_DATE - INTERVAL '1 day' AS report_date,
    -- 修复：分别处理内部和外部客户数
    COALESCE((SELECT internal_customers FROM internal_stats), 0) + 
    COALESCE((SELECT external_customers FROM external_stats), 0) AS total_customers,
    COALESCE((SELECT internal_transactions FROM internal_stats), 0) + 
    COALESCE((SELECT external_transactions FROM external_stats), 0) AS total_transactions,
    COALESCE((SELECT internal_amount FROM internal_stats), 0) + 
    COALESCE((SELECT external_amount FROM external_stats), 0) AS total_amount,
    COALESCE((SELECT internal_high_risk FROM internal_stats), 0) + 
    COALESCE((SELECT external_high_risk FROM external_stats), 0) AS high_risk_count
WHERE 
    (CURRENT_DATE - INTERVAL '1 day') NOT IN (SELECT report_date FROM dws_daily_report);

-- 2. 风险预警表 (ODS_TRADE_STD → DWS_RISK_ALERT)
INSERT INTO dws_risk_alert (cust_id, alert_type, alert_time, details)
-- 大额交易预警
SELECT 
    ots.cust_id,
    '大额交易预警' AS alert_type,
    ots.trade_time AS alert_time,
    jsonb_build_object(
        'trade_id', ots.trade_id,
        'amount', ots.amount,
        'channel', ots.channel,
        'risk_level', ots.risk_level,
        'reason', '单笔交易金额超过警戒线'
    ) AS details
FROM 
    ods_trade_std ots
WHERE 
    ots.amount > 100000
    AND ots.trade_time > CURRENT_TIMESTAMP - INTERVAL '1 day'
    AND NOT EXISTS (
        SELECT 1 FROM dws_risk_alert dra 
        WHERE dra.cust_id = ots.cust_id 
        AND dra.alert_type = '大额交易预警'
        AND dra.alert_time > CURRENT_TIMESTAMP - INTERVAL '1 day'
    )
UNION ALL
-- 频繁交易预警
SELECT 
    cust_id,
    '频繁交易预警' AS alert_type,
    MAX(trade_time) AS alert_time,
    jsonb_build_object(
        'transaction_count', COUNT(*),
        'total_amount', SUM(amount),
        'time_window', '1小时',
        'reason', '短时间内频繁交易'
    ) AS details
FROM 
    ods_trade_std
WHERE 
    trade_time > CURRENT_TIMESTAMP - INTERVAL '1 day'
GROUP BY 
    cust_id, DATE_TRUNC('hour', trade_time)
HAVING 
    COUNT(*) > 10
    AND NOT EXISTS (
        SELECT 1 FROM dws_risk_alert dra 
        WHERE dra.cust_id = cust_id 
        AND dra.alert_type = '频繁交易预警'
        AND dra.alert_time > CURRENT_TIMESTAMP - INTERVAL '1 day'
    );

-- 3. 资产汇总表 (DWD_ACCOUNT_ASSET → DWS_ASSET_SUMMARY)
INSERT INTO dws_asset_summary (
    summary_date, asset_category, total_value, 
    daily_change, daily_change_pct, customer_count
)
WITH current_assets AS (
    SELECT 
        CURRENT_DATE AS report_date,
        daa.account_type AS asset_category,
        SUM(daa.total_asset_value) AS total_value,
        COUNT(DISTINCT daa.cust_id) AS customer_count
    FROM 
        dwd_account_asset daa
    GROUP BY 
        daa.account_type
),
previous_assets AS (
    SELECT 
        das.asset_category,
        das.total_value AS previous_value
    FROM 
        dws_asset_summary das
    WHERE 
        das.summary_date = CURRENT_DATE - INTERVAL '1 day'
)
SELECT 
    ca.report_date AS summary_date,
    ca.asset_category,
    ca.total_value,
    COALESCE(ca.total_value - pa.previous_value, 0) AS daily_change,
    001 AS daily_change_pct,
    ca.customer_count
FROM 
    current_assets ca
LEFT JOIN 
    previous_assets pa ON ca.asset_category = pa.asset_category
WHERE 
    NOT EXISTS (
        SELECT 1 FROM dws_asset_summary das 
        WHERE das.summary_date = ca.report_date
        AND das.asset_category = ca.asset_category
    );

-- 4. 客户画像表 (DWD_CUSTOMER_BEHAVIOR → DWS_CUSTOMER_PROFILE)
INSERT INTO dws_customer_profile (
    cust_id, risk_preference, investment_style, 
    avg_transaction_amount, total_asset_value, profile_date
)
SELECT 
    dcb.cust_id,
    (dcb.risk_indicators->>'risk_level')::VARCHAR AS risk_preference,
    jsonb_build_object(
        'preferred_products', CASE 
            WHEN EXISTS (
                SELECT 1 FROM dwd_account_asset daa 
                WHERE daa.cust_id = dcb.cust_id AND daa.account_type = '理财'
            ) THEN '理财产品'
            ELSE '存款'
        END,
        'activity_level', dcb.behavior_pattern->>'activity_level',
        'investment_horizon', CASE 
            WHEN (dcb.risk_indicators->>'risk_level') = '高风险' THEN '短期'
            WHEN (dcb.risk_indicators->>'risk_level') = '中风险' THEN '中期'
            ELSE '长期'
        END
    ) AS investment_style,
    (dcb.trading_preference->>'avg_transaction')::NUMERIC AS avg_transaction_amount,
    COALESCE((
        SELECT SUM(daa.total_asset_value)
        FROM dwd_account_asset daa
        WHERE daa.cust_id = dcb.cust_id
    ), 0) AS total_asset_value,
    CURRENT_DATE AS profile_date
FROM 
    dwd_customer_behavior dcb
WHERE 
    NOT EXISTS (
        SELECT 1 FROM dws_customer_profile dcp 
        WHERE dcp.cust_id = dcb.cust_id 
        AND dcp.profile_date = CURRENT_DATE
    ); 