-- SRC 到 ODS 层的 ETL 处理
-- 作者：数据工程团队
-- 创建日期：2023-06-01
-- 最后修改：2023-06-15

-- 1. 客户数据清洗 (SRC_CUSTOMER → ODS_CUSTOMER_CLEAN)
INSERT INTO ods_customer_clean (legal_name, id_card, contact_phone, etl_time, batch_id, src_cust_id)
SELECT 
    (raw_data->>'name')::VARCHAR(100) AS legal_name,
    (raw_data->>'id_number')::VARCHAR(20) AS id_card,
    (raw_data->>'phone')::VARCHAR(20) AS contact_phone,
    CURRENT_TIMESTAMP AS etl_time,
    EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::BIGINT AS batch_id,
    cust_id AS src_cust_id
FROM 
    src_customer
WHERE 
    cust_id NOT IN (SELECT src_cust_id FROM ods_customer_clean WHERE src_cust_id IS NOT NULL)
    AND raw_data->>'id_number' IS NOT NULL
    AND raw_data->>'name' IS NOT NULL;

-- 2. 交易数据标准化 (SRC_TRADE → ODS_TRADE_STD)
INSERT INTO ods_trade_std (trade_id, cust_id, trade_category, amount, trade_time, channel, risk_level, etl_time)
SELECT 
    t.trade_id,
    t.cust_id,
    CASE 
        WHEN t.trade_type = '存款' THEN '存款'
        WHEN t.trade_type = '取款' THEN '取款'
        WHEN t.trade_type = '转账' THEN '转账'
        WHEN t.trade_type IN ('理财购买', '基金申购', '保险购买') THEN '理财购买'
        ELSE '转账' -- 默认分类
    END AS trade_category,
    t.amount,
    t.trade_time,
    t.channel,
    CASE
        WHEN t.amount > 50000 THEN 3 -- 大额交易风险中等
        WHEN t.trade_type = '转账' AND t.amount > 10000 THEN 2 -- 大额转账风险较低
        WHEN t.trade_time::time BETWEEN '23:00:00' AND '06:00:00' THEN 4 -- 夜间交易风险较高
        ELSE 1 -- 默认风险低
    END AS risk_level,
    CURRENT_TIMESTAMP AS etl_time
FROM 
    src_trade t
WHERE 
    t.trade_id NOT IN (SELECT trade_id FROM ods_trade_std)
    AND t.amount > 0
    AND t.cust_id IS NOT NULL;

-- 3. 外部交易数据标准化 (SRC_EXTERNAL_TRADE → ODS_EXTERNAL_TRADE_STD)
INSERT INTO ods_external_trade_std (ext_trade_id, cust_id, trade_category, amount, trade_time, source_system, risk_level, etl_time)
SELECT 
    et.ext_trade_id,
    et.customer_ref AS cust_id,
    CASE 
        WHEN et.transaction_type LIKE '%买入%' THEN '理财购买'
        WHEN et.transaction_type LIKE '%卖出%' OR et.transaction_type LIKE '%赎回%' THEN '取款'
        ELSE '转账' -- 默认分类
    END AS trade_category,
    et.transaction_amount AS amount,
    et.transaction_datetime AS trade_time,
    et.source_system,
    CASE
        WHEN et.transaction_amount > 100000 THEN 4 -- 外部大额交易风险高
        WHEN et.source_system = '第三方支付' THEN 3 -- 第三方支付风险中等
        WHEN (et.additional_info->>'status') = '处理中' THEN 2 -- 处理中交易风险较低
        ELSE 1 -- 默认风险低
    END AS risk_level,
    CURRENT_TIMESTAMP AS etl_time
FROM 
    src_external_trade et
WHERE 
    et.ext_trade_id NOT IN (SELECT ext_trade_id FROM ods_external_trade_std)
    AND et.transaction_amount > 0
    AND et.customer_ref IS NOT NULL;

-- 4. 产品数据标准化 (SRC_PRODUCT → ODS_PRODUCT_STD)
INSERT INTO ods_product_std (product_id, product_name, product_category, risk_rating, yield_rate, duration_days, status, etl_time)
SELECT 
    p.product_id,
    p.product_name,
    CASE 
        WHEN p.product_type IN ('理财', '信托', '债券') THEN p.product_type
        WHEN p.product_type = '基金' THEN '基金'
        WHEN p.product_type = '保险' THEN '保险'
        ELSE '其他' -- 默认分类
    END AS product_category,
    p.risk_level AS risk_rating,
    (p.product_details->>'expected_return')::NUMERIC(6,4) AS yield_rate,
    (p.maturity_date - p.issue_date) AS duration_days,
    CASE 
        WHEN p.maturity_date < CURRENT_DATE THEN '已到期'
        WHEN p.issue_date > CURRENT_DATE THEN '未发行'
        ELSE '在售' 
    END AS status,
    CURRENT_TIMESTAMP AS etl_time
FROM 
    src_product p
WHERE 
    p.product_id NOT IN (SELECT product_id FROM ods_product_std);

-- 5. 账户数据清洗 (SRC_ACCOUNT → ODS_ACCOUNT_CLEAN)
INSERT INTO ods_account_clean (account_id, cust_id, account_type, status, balance, open_date, update_time, etl_time)
SELECT 
    a.account_id,
    a.cust_id,
    a.account_type,
    CASE 
        WHEN a.status = '正常' THEN '正常'
        WHEN a.status = '冻结' THEN '冻结'
        WHEN a.status = '休眠' THEN '休眠'
        WHEN a.status = '注销' THEN '注销'
        ELSE '未知' -- 默认状态
    END AS status,
    CASE 
        WHEN a.balance < 0 AND a.account_type NOT IN ('贷款', '信用卡') THEN 0 -- 非贷款账户余额不应为负
        ELSE a.balance
    END AS balance,
    a.open_date,
    a.last_update_time AS update_time,
    CURRENT_TIMESTAMP AS etl_time
FROM 
    src_account a
WHERE 
    a.account_id NOT IN (SELECT account_id FROM ods_account_clean)
    AND a.cust_id IS NOT NULL;

-- 6. 市场数据标准化 (SRC_MARKET_DATA → ODS_MARKET_DATA_STD)
INSERT INTO ods_market_data_std (data_id, instrument_code, price_type, price, trading_datetime, source, etl_time)
SELECT 
    md.data_id,
    md.instrument_code,
    md.price_type,
    md.price,
    (md.trading_date + md.trading_time) AS trading_datetime,
    md.source,
    CURRENT_TIMESTAMP AS etl_time
FROM 
    src_market_data md
WHERE 
    md.data_id NOT IN (SELECT data_id FROM ods_market_data_std)
    AND md.instrument_code IS NOT NULL
    AND md.price > 0; 