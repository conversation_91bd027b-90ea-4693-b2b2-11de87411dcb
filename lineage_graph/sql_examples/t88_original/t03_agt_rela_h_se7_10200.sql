CREATE TEMPORARY TABLE tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 AS
SELECT
  *
FROM PDB.T03_AGT_RELA_H
WHERE
  1 = 0;

CREATE TEMPORARY TABLE tmp_t03_agt_rela_h_se7_10200.VT_INC_202 AS
SELECT
  *
FROM PDB.T03_AGT_RELA_H
WHERE
  1 = 0;

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  T1.CODE,
  'SE3',
  '007',
  CAST('2026-01-01' AS DATE),
  '120',
  T2.CODE,
  'SE7120',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_ACCT_LIMIT',
  202
FROM ODB.SE7_ACCT_LIMIT AS T1
INNER JOIN ODB.SE7_ACCT_LIMIT AS T2
  ON T1.PARENTID = T2.ID
  AND CAST('2026-01-01' AS DATE) BETWEEN T2.START_DT AND T2.END_DT - 1
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN T1.START_DT AND T1.END_DT - 1
  AND (
    (
      T1.CODE,
      (
        CASE T1.DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
      ) || (
        T1.VERSION
      ) || (
        CASE WHEN SUBSTRING(T1.ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
      ) || (
        CHR(TRIM(T1.ID))
      ) || T1.ID
    ) IN (
      SELECT
        CODE,
        MAX(
          (
            CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
          ) || (
            VERSION
          ) || (
            CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
          ) || (
            CHR(TRIM(ID))
          ) || ID
        )
      FROM ODB.SE7_ACCT_LIMIT
      WHERE
        CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
      GROUP BY
        1
    )
    AND T1.CODE <> ''
    AND T2.CODE <> ''
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  CODE,
  'SE7120',
  '008',
  CAST('2026-01-01' AS DATE),
  '120',
  UPLIMITCODE,
  'SE7120',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_ACCT_LIMIT',
  202
FROM ODB.SE7_ACCT_LIMIT
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  AND (
    (
      CODE,
      (
        CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
      ) || (
        VERSION
      ) || (
        CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
      ) || (
        CHR(TRIM(ID))
      ) || ID
    ) IN (
      SELECT
        CODE,
        MAX(
          (
            CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
          ) || (
            VERSION
          ) || (
            CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
          ) || (
            CHR(TRIM(ID))
          ) || ID
        )
      FROM ODB.SE7_ACCT_LIMIT
      WHERE
        CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
      GROUP BY
        1
    )
    AND CODE <> ''
    AND UPLIMITCODE <> ''
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  T1.CODE,
  'SE7130',
  '006',
  CAST('2026-01-01' AS DATE),
  '130',
  T2.CODE,
  'SE7118',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_ASSURE_CONTRACT',
  202
FROM ODB.SE7_GC_ASSURE_CONTRACT AS T1
INNER JOIN ODB.SE7_GC_MAIN_CONTRACT AS T2
  ON T1.UPCONTRACTID = T2.ID
  AND CAST('2026-01-01' AS DATE) BETWEEN T2.START_DT AND T2.END_DT - 1
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN T1.START_DT AND T1.END_DT - 1
  AND (
    (
      T1.CODE,
      (
        CASE T1.DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
      ) || (
        T1.VERSION
      ) || (
        CASE WHEN SUBSTRING(T1.ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
      ) || (
        CHR(TRIM(T1.ID))
      ) || T1.ID
    ) IN (
      SELECT
        CODE,
        MAX(
          (
            CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
          ) || (
            VERSION
          ) || (
            CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
          ) || (
            CHR(TRIM(ID))
          ) || ID
        )
      FROM ODB.SE7_GC_ASSURE_CONTRACT
      WHERE
        CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
      GROUP BY
        1
    )
    AND T1.CODE <> ''
    AND T1.UPCONTRACTID <> T1.ID
    AND T2.CODE <> ''
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  CODE,
  'SE3',
  '002',
  CAST('2026-01-01' AS DATE),
  '104',
  LIMITCODE,
  'SE7120',
  '0021',
  REALAPPLYRATE,
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_LOAN_CREDIT',
  202
FROM ODB.SE7_GC_LOAN_CREDIT
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  AND (
    (
      CODE,
      (
        CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
      ) || (
        VERSION
      ) || (
        CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
      ) || (
        CHR(TRIM(ID))
      ) || ID
    ) IN (
      SELECT
        CODE,
        MAX(
          (
            CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
          ) || (
            VERSION
          ) || (
            CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
          ) || (
            CHR(TRIM(ID))
          ) || ID
        )
      FROM ODB.SE7_GC_LOAN_CREDIT
      WHERE
        CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
      GROUP BY
        1
    )
    AND CODE <> ''
    AND LIMITCODE <> ''
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  CODE,
  'SE3',
  '005',
  CAST('2026-01-01' AS DATE),
  '104',
  UPCONTRACTCODE,
  'SE7118',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_LOAN_CREDIT',
  202
FROM ODB.SE7_GC_LOAN_CREDIT
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  AND (
    (
      CODE,
      (
        CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
      ) || (
        VERSION
      ) || (
        CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
      ) || (
        CHR(TRIM(ID))
      ) || ID
    ) IN (
      SELECT
        CODE,
        MAX(
          (
            CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
          ) || (
            VERSION
          ) || (
            CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
          ) || (
            CHR(TRIM(ID))
          ) || ID
        )
      FROM ODB.SE7_GC_LOAN_CREDIT
      WHERE
        CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
      GROUP BY
        1
    )
    AND CODE <> ''
    AND UPCONTRACTCODE <> ''
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  T1.CODE,
  'SE3',
  '022',
  CAST('2026-01-01' AS DATE),
  '104',
  COALESCE(T3.UPCONTRACTCODE, T4.UPCONTRACTCODE, ''),
  'SE7130',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_LOAN_CREDIT',
  202
FROM ODB.SE7_GC_LOAN_CREDIT AS T1
LEFT JOIN (
  SELECT
    CODE,
    UPCONTRACTCODE
  FROM ODB.SE7_GC_MAIN_CONTRACT
  WHERE
    CODE <> '' AND CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  QUALIFY
    ROW_NUMBER() OVER (PARTITION BY CODE ORDER BY CASE DEL_IND WHEN '' THEN 'Z' ELSE DEL_IND END DESC, CASE STATUS WHEN 'ACTIVE' THEN -100 ELSE 0 END, VERSION DESC, CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 100 ELSE 0 END, CHR(TRIM(ID)) DESC, ID DESC) = 1
) AS T2
  ON T1.UPCONTRACTCODE = T2.CODE
LEFT JOIN (
  SELECT
    CODE,
    UPCONTRACTCODE
  FROM ODB.SE7_GC_ASSURE_CONTRACT
  WHERE
    CODE <> '' AND CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  QUALIFY
    ROW_NUMBER() OVER (PARTITION BY CODE ORDER BY CASE DEL_IND WHEN '' THEN 'Z' ELSE DEL_IND END DESC, CASE STATUS WHEN 'ACTIVE' THEN -100 ELSE 0 END, VERSION DESC, CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 100 ELSE 0 END, CHR(TRIM(ID)) DESC, ID DESC) = 1
) AS T3
  ON T1.UPCONTRACTCODE = T3.CODE
LEFT JOIN (
  SELECT
    CODE,
    UPCONTRACTCODE
  FROM ODB.SE7_GC_ASSURE_CONTRACT
  WHERE
    CODE <> '' AND CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  QUALIFY
    ROW_NUMBER() OVER (PARTITION BY CODE ORDER BY CASE DEL_IND WHEN '' THEN 'Z' ELSE DEL_IND END DESC, CASE STATUS WHEN 'ACTIVE' THEN -100 ELSE 0 END, VERSION DESC, CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 100 ELSE 0 END, CHR(TRIM(ID)) DESC, ID DESC) = 1
) AS T4
  ON T2.UPCONTRACTCODE = T4.CODE
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN T1.START_DT AND T1.END_DT - 1
  AND (
    COALESCE(T3.UPCONTRACTCODE, T4.UPCONTRACTCODE, '') <> ''
  )
QUALIFY
  ROW_NUMBER() OVER (PARTITION BY T1.CODE ORDER BY CASE DEL_IND WHEN '' THEN 'Z' ELSE DEL_IND END DESC, CASE STATUS WHEN 'ACTIVE' THEN -100 ELSE 0 END, VERSION DESC, CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 100 ELSE 0 END, CHR(TRIM(ID)) DESC, ID DESC) = 1;

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_NEW_202 (
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
)
SELECT
  CODE,
  'SE7118',
  '004',
  CAST('2026-01-01' AS DATE),
  '118',
  LIMITCODE,
  'SE7120',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  '',
  CAST('3000-12-31' AS DATE),
  'SE7_GC_MAIN_CONTRACT',
  202
FROM ODB.SE7_GC_MAIN_CONTRACT
WHERE
  CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
  AND (
    (
      CODE,
      (
        CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
      ) || (
        VERSION
      ) || (
        CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
      ) || (
        CHR(TRIM(ID))
      ) || ID
    ) IN (
      SELECT
        CODE,
        MAX(
          (
            CASE DEL_IND WHEN 'D' THEN 'D' ELSE 'Z' END
          ) || (
            VERSION
          ) || (
            CASE WHEN SUBSTRING(ID, 1, 1) = 'Q' THEN 'Q' ELSE 'Z' END
          ) || (
            CHR(TRIM(ID))
          ) || ID
        )
      FROM ODB.SE7_GC_MAIN_CONTRACT
      WHERE
        CAST('2026-01-01' AS DATE) BETWEEN START_DT AND END_DT - 1
      GROUP BY
        1
    )
    AND CODE <> ''
    AND LIMITCODE <> ''
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_INC_202
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
FROM tmp_t03_agt_rela_h_se7_10200.VT_NEW_202
WHERE
  NOT (
    ACCT_NUM,
    AGT_MODIF_NUM,
    AGT_RELA_TYPE_CD,
    AGT_TYPE_CD,
    RELA_ACCT_NUM,
    RELA_AGT_MODIF_NUM,
    AGT_RELA_ADD_FEAT_TYPE_CD1,
    AGT_RELA_ADD_FEAT1,
    AGT_RELA_ADD_FEAT_TYPE_CD2,
    AGT_RELA_ADD_FEAT2,
    AGT_RELA_ADD_FEAT_TYPE_CD3,
    AGT_RELA_ADD_FEAT3,
    AGT_RELA_ADD_FEAT_TYPE_CD4,
    AGT_RELA_ADD_FEAT4,
    AGT_RELA_ADD_FEAT_TYPE_CD5,
    AGT_RELA_ADD_FEAT5
  ) IN (
    SELECT
      ACCT_NUM,
      AGT_MODIF_NUM,
      AGT_RELA_TYPE_CD,
      AGT_TYPE_CD,
      RELA_ACCT_NUM,
      RELA_AGT_MODIF_NUM,
      AGT_RELA_ADD_FEAT_TYPE_CD1,
      AGT_RELA_ADD_FEAT1,
      AGT_RELA_ADD_FEAT_TYPE_CD2,
      AGT_RELA_ADD_FEAT2,
      AGT_RELA_ADD_FEAT_TYPE_CD3,
      AGT_RELA_ADD_FEAT3,
      AGT_RELA_ADD_FEAT_TYPE_CD4,
      AGT_RELA_ADD_FEAT4,
      AGT_RELA_ADD_FEAT_TYPE_CD5,
      AGT_RELA_ADD_FEAT5
    FROM PDB.T03_AGT_RELA_H
    WHERE
      END_DT = CAST('3000-12-31' AS DATE)
  );

INSERT INTO tmp_t03_agt_rela_h_se7_10200.VT_INC_202
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  CAST('0001-01-01' AS DATE),
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
FROM PDB.T03_AGT_RELA_H
WHERE
  END_DT = CAST('3000-12-31' AS DATE)
  AND ETL_JOB_NUM = 202
  AND NOT (ACCT_NUM, AGT_MODIF_NUM, AGT_RELA_TYPE_CD, AGT_TYPE_CD) IN (
    SELECT
      ACCT_NUM,
      AGT_MODIF_NUM,
      AGT_RELA_TYPE_CD,
      AGT_TYPE_CD
    FROM tmp_t03_agt_rela_h_se7_10200.VT_NEW_202
  );

UPDATE PDB.T03_AGT_RELA_H SET END_DT = CAST('2026-01-01' AS DATE)
WHERE
  END_DT = CAST('3000-12-31' AS DATE)
  AND (ACCT_NUM, AGT_MODIF_NUM, AGT_RELA_TYPE_CD, AGT_TYPE_CD) IN (
    SELECT
      ACCT_NUM,
      AGT_MODIF_NUM,
      AGT_RELA_TYPE_CD,
      AGT_TYPE_CD
    FROM VT_INC_202
  );

INSERT INTO PDB.T03_AGT_RELA_H
SELECT
  ACCT_NUM,
  AGT_MODIF_NUM,
  AGT_RELA_TYPE_CD,
  START_DT,
  AGT_TYPE_CD,
  RELA_ACCT_NUM,
  RELA_AGT_MODIF_NUM,
  AGT_RELA_ADD_FEAT_TYPE_CD1,
  AGT_RELA_ADD_FEAT1,
  AGT_RELA_ADD_FEAT_TYPE_CD2,
  AGT_RELA_ADD_FEAT2,
  AGT_RELA_ADD_FEAT_TYPE_CD3,
  AGT_RELA_ADD_FEAT3,
  AGT_RELA_ADD_FEAT_TYPE_CD4,
  AGT_RELA_ADD_FEAT4,
  AGT_RELA_ADD_FEAT_TYPE_CD5,
  AGT_RELA_ADD_FEAT5,
  END_DT,
  DATA_SRC_TABLE_NAME,
  ETL_JOB_NUM
FROM tmp_t03_agt_rela_h_se7_10200.VT_INC_202
WHERE
  END_DT <> CAST('0001-01-01' AS DATE);
