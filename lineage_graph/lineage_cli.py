#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SQL行级血缘分析工具 - 命令行交互版本

该模块提供了一个命令行交互式工具，用于分析SQL语句之间的血缘关系，
并将表与表之间的数据流动关系存储到Neo4j数据库中。

主要功能:
- 从文件或目录加载和分析SQL语句
- 构建血缘关系图并存入Neo4j数据库
- 支持查询特定节点的上游血缘
- 提供交互式命令行界面
- 支持智能剪枝功能

作者: 数据血缘分析团队
版本: 1.0.0
"""

# 标准库导入
import os
import sys
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到Python路径
current_file_path = Path(__file__).resolve()
project_root = current_file_path.parent.parent
sys.path.insert(0, str(project_root))

# 本地模块导入
from lineage_graph.lineage_graph_neo4j import Neo4jLineageGraph, build_graph, calculate_lineage_with_pruning, calculate_node_path_lengths, get_upstream_subgraph
from lineage_core.lineage_analyzer import LineageAnalyzer
from lineage_core.utils import build_sql_list_from_str

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Neo4j连接配置
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "12345678"  # 应该从配置文件或环境变量获取

# ====================== 工具函数 ======================

def load_sql_from_file(file_path: Path) -> str:
    """
    从文件加载SQL语句
    
    Args:
        file_path: SQL文件路径
        
    Returns:
        str: SQL语句
    """
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            sql_content = f.read()
            logger.info(f"成功加载SQL文件: {file_path}")
            return sql_content
    except Exception as e:
        error_msg = f"加载SQL文件 {file_path} 时出错: {e}"
        logger.error(error_msg)
        print(f"错误: {error_msg}")
        return ""

def load_sql_from_directory(dir_path: Path) -> str:
    """
    从目录加载所有SQL文件并拼接
    
    Args:
        dir_path: 目录路径
        
    Returns:
        str: 拼接后的SQL语句
    """
    if not dir_path.exists() or not dir_path.is_dir():
        error_msg = f"错误：找不到目录 {dir_path}"
        logger.error(error_msg)
        print(f"错误: {error_msg}")
        return ""

    # 获取目录中所有SQL文件
    sql_files = list(dir_path.glob("**/*.sql"))
    sql_files.sort()  # 按文件名排序

    if not sql_files:
        warning_msg = f"警告：目录 {dir_path} 中没有SQL文件"
        logger.warning(warning_msg)
        print(f"警告: {warning_msg}")
        return ""

    combined_sql = ""

    for sql_file in sql_files:
        try:
            with open(sql_file, "r", encoding="utf-8") as f:
                file_content = f.read()
                # 添加文件名作为注释
                combined_sql += f"-- 文件: {sql_file.name}\n{file_content}\n\n"
                logger.info(f"成功加载文件: {sql_file}")
        except Exception as e:
            error_msg = f"加载文件 {sql_file} 时出错: {str(e)}"
            logger.error(error_msg)
            print(f"错误: {error_msg}")

    return combined_sql.strip()

def analyze_sql(sql_content: str, do_pruning: bool = False) -> Tuple[Neo4jLineageGraph, Dict[str, int], List[str]]:
    """
    分析SQL内容并构建血缘图
    
    Args:
        sql_content: SQL语句
        do_pruning: 是否应用剪枝优化
        
    Returns:
        Tuple: (血缘图, 节点路径长度, 下游节点)
    """
    try:
        # 解析SQL语句
        sql_statements = build_sql_list_from_str(sql_content)
        
        if not sql_statements:
            logger.warning("未能解析出有效的SQL语句")
            return None, {}, []
        
        # 构建血缘图
        lineage_graph = build_graph(sql_statements=sql_statements)
        
        # 应用剪枝优化
        if do_pruning:
            logger.info("启用剪枝优化...")
            analyzer = LineageAnalyzer()
            lineage_graph, pruned_nodes, downstream_nodes = calculate_lineage_with_pruning(
                lineage_graph=lineage_graph,
                analyzer=analyzer,
                with_pruning=True
            )
            logger.info(f"剪枝完成，共剪枝 {len(pruned_nodes)} 个节点")
        else:
            downstream_nodes = lineage_graph.get_downstream_nodes()
        
        # 计算节点到最下游节点的路径长度
        node_path_lengths = calculate_node_path_lengths(lineage_graph)
        
        return lineage_graph, node_path_lengths, downstream_nodes
    except Exception as e:
        logger.exception(f"分析SQL时出错: {e}")
        print(f"错误: 分析SQL时出错: {e}")
        return None, {}, []

def print_lineage_info(lineage_graph: Neo4jLineageGraph, node_path_lengths: Dict[str, int], downstream_nodes: List[str]):
    """
    打印血缘图的基本信息
    
    Args:
        lineage_graph: 血缘图
        node_path_lengths: 节点路径长度
        downstream_nodes: 下游节点
    """
    if not lineage_graph:
        print("没有可用的血缘图信息")
        return
    
    print("\n=== 血缘图信息 ===")
    print(f"节点总数: {len(lineage_graph.get_all_nodes())}")
    print(f"边总数: {len(lineage_graph.get_all_edges())}")
    print(f"下游节点数: {len(downstream_nodes)}")
    
    if downstream_nodes:
        print("\n下游节点列表:")
        for node in downstream_nodes:
            print(f"  - {node}")
    
    print("\n=== 节点路径长度 ===")
    for node, length in sorted(node_path_lengths.items(), key=lambda x: x[1], reverse=True):
        print(f"{node}: {length}")

def print_node_details(lineage_graph: Neo4jLineageGraph, node_name: str):
    """
    打印节点的详细信息
    
    Args:
        lineage_graph: 血缘图
        node_name: 节点名称
    """
    if not lineage_graph.has_node(node_name):
        print(f"错误: 血缘图中不存在节点 '{node_name}'")
        return
    
    print(f"\n=== 节点 '{node_name}' 详细信息 ===")
    
    # 获取节点属性
    node_attrs = lineage_graph.get_node_attributes(node_name)
    print("节点属性:")
    for key, value in node_attrs.items():
        print(f"  {key}: {value}")
    
    # 获取上游节点
    predecessors = lineage_graph.predecessors(node_name)
    print(f"\n上游节点 ({len(predecessors)}):")
    for pred in predecessors:
        print(f"  {pred}")
    
    # 获取入边上的SQL语句
    print("\n入边SQL语句:")
    in_edges = lineage_graph.in_edges(node_name, data=True)
    for source, target, data in in_edges:
        print(f"\n  从 {source} 到 {target}:")
        if 'sql_statements' in data:
            for i, sql_info in enumerate(data['sql_statements']):
                print(f"    SQL {i+1}: {sql_info['sql_text'][:100]}...")
        else:
            print("    没有SQL语句")

def explore_upstream_subgraph(lineage_graph: Neo4jLineageGraph, node_name: str):
    """
    探索节点的上游子图
    
    Args:
        lineage_graph: 血缘图
        node_name: 节点名称
    """
    if not lineage_graph.has_node(node_name):
        print(f"错误: 血缘图中不存在节点 '{node_name}'")
        return
    
    subgraph = get_upstream_subgraph(lineage_graph, node_name)
    
    print(f"\n=== 节点 '{node_name}' 的上游子图 ===")
    print(f"子图节点数: {len(subgraph.get_all_nodes())}")
    print(f"子图边数: {len(subgraph.get_all_edges())}")
    
    print("\n子图节点列表:")
    for node in sorted(subgraph.get_all_nodes()):
        print(f"  - {node}")
    
    print("\n子图边列表:")
    for source, target in sorted(subgraph.get_all_edges()):
        print(f"  {source} -> {target}")

# ====================== 命令行界面 ======================

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="SQL行级血缘分析工具 - 命令行版本")
    
    parser.add_argument(
        "input",
        help="输入SQL文件或目录路径"
    )
    
    parser.add_argument(
        "--pruning",
        action="store_true",
        help="启用智能剪枝优化"
    )
    
    parser.add_argument(
        "--uri",
        default=NEO4J_URI,
        help=f"Neo4j数据库URI (默认: {NEO4J_URI})"
    )
    
    parser.add_argument(
        "--user",
        default=NEO4J_USER,
        help=f"Neo4j用户名 (默认: {NEO4J_USER})"
    )
    
    parser.add_argument(
        "--password",
        default=NEO4J_PASSWORD,
        help="Neo4j密码"
    )
    
    parser.add_argument(
        "--no-interactive",
        action="store_true",
        help="非交互模式，仅分析并存储血缘关系"
    )
    
    return parser.parse_args()

def interactive_menu(lineage_graph: Neo4jLineageGraph, node_path_lengths: Dict[str, int]):
    """
    交互式菜单
    
    Args:
        lineage_graph: 血缘图
        node_path_lengths: 节点路径长度
    """
    while True:
        print("\n=== SQL血缘分析工具 - 交互式菜单 ===")
        print("1. 查看血缘图统计信息")
        print("2. 查看所有节点")
        print("3. 查看节点详细信息")
        print("4. 探索节点上游血缘")
        print("5. 导出血缘图")
        print("0. 退出")
        
        choice = input("\n请输入选项: ")
        
        if choice == "0":
            break
        
        elif choice == "1":
            nodes_count = len(lineage_graph.get_all_nodes())
            edges_count = len(lineage_graph.get_all_edges())
            downstream_nodes = lineage_graph.get_downstream_nodes()
            
            print("\n=== 血缘图统计信息 ===")
            print(f"节点总数: {nodes_count}")
            print(f"边总数: {edges_count}")
            print(f"下游节点数: {len(downstream_nodes)}")
            
            if downstream_nodes:
                print("\n下游节点:")
                for node in downstream_nodes:
                    print(f"  - {node}")
        
        elif choice == "2":
            sorted_nodes = sorted(node_path_lengths.items(), key=lambda x: x[1], reverse=True)
            
            print("\n=== 所有节点 (按路径长度排序) ===")
            for node, length in sorted_nodes:
                print(f"{node}: 路径长度 = {length}")
        
        elif choice == "3":
            node_name = input("请输入要查看的节点名称: ")
            print_node_details(lineage_graph, node_name)
        
        elif choice == "4":
            node_name = input("请输入要探索上游血缘的节点名称: ")
            explore_upstream_subgraph(lineage_graph, node_name)
        
        elif choice == "5":
            print("导出功能尚未实现")
            # 这里可以实现导出到文件的功能
        
        else:
            print("无效的选项，请重新输入")

def main():
    """主函数"""
    args = parse_arguments()
    
    # 检查输入路径
    input_path = Path(args.input)
    if not input_path.exists():
        print(f"错误: 输入路径 '{input_path}' 不存在")
        sys.exit(1)
    
    # 加载SQL
    if input_path.is_dir():
        print(f"从目录 '{input_path}' 加载SQL文件...")
        sql_content = load_sql_from_directory(input_path)
    else:
        print(f"加载SQL文件 '{input_path}'...")
        sql_content = load_sql_from_file(input_path)
    
    if not sql_content:
        print("错误: 没有有效的SQL内容")
        sys.exit(1)
    
    # 分析SQL并构建血缘图
    print("正在分析SQL并构建血缘图...")
    lineage_graph, node_path_lengths, downstream_nodes = analyze_sql(
        sql_content, 
        do_pruning=args.pruning
    )
    
    if not lineage_graph:
        print("错误: 血缘图构建失败")
        sys.exit(1)
    
    print(f"血缘图构建完成！共有 {len(lineage_graph.get_all_nodes())} 个节点，{len(lineage_graph.get_all_edges())} 条边")
    
    # 非交互模式
    if args.no_interactive:
        print("非交互模式 - 血缘图已存储到Neo4j数据库")
        return
    
    # 进入交互式菜单
    try:
        interactive_menu(lineage_graph, node_path_lengths)
    finally:
        # 关闭Neo4j连接
        lineage_graph.close()
        print("已关闭Neo4j连接")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被中断")
        sys.exit(0)
    except Exception as e:
        print(f"程序发生错误: {e}")
        logger.exception("程序发生未处理的异常")
        sys.exit(1)