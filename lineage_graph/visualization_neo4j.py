"""
SQL行级血缘分析工具 - Neo4j可视化模块

该模块提供了基于Neo4j的血缘图可视化功能，包括：
- 打印血缘图中的边及其关联的SQL语句
- 使用graphviz生成血缘图的可视化图像
- 支持节点状态（已访问、可剪枝）的样式渲染
- 支持在边上显示SQL语句

作者: 数据血缘分析团队
版本: 1.0.0
"""

from typing import Optional, Any, Dict, List, Tuple, Set
import json
from lineage_core.logger import logger
from lineage_graph.lineage_graph_neo4j import Neo4jLineageGraph

# ====================== 辅助函数 ======================
def _create_digraph(title: str, format_type: str = 'pdf', direction: str = 'LR'):
    """
    创建并配置graphviz有向图对象
    
    Args:
        title: 图表标题
        format_type: 输出格式，默认为'pdf'
        direction: 布局方向，可选'LR'（从左到右）或'TB'（从上到下），默认为'LR'
        
    Returns:
        graphviz.Digraph: 配置好的图对象
    """
    try:
        import graphviz
    except ImportError:
        logger.error("请先安装 graphviz 包: pip install graphviz")
        return None
    
    dot = graphviz.Digraph(comment=title, format=format_type)
    
    # 基本图形属性设置
    dot.attr(rankdir=direction)  # 根据direction参数设置布局方向
    dot.attr('node', fontname='SimHei')  # 设置中文字体
    dot.attr('edge', fontname='SimHei')  # 设置中文字体
    dot.attr('node', height='0.4', fixedsize='true')
    dot.attr(nodesep='0.4')  # 节点之间的最小间距
    if direction == 'LR':
        dot.attr(ranksep='0.8')  # 层级之间的最小间距
    else:
        dot.attr(ranksep='4.0')  # 层级之间的最小间距
    dot.attr(bgcolor='#FAFAFA')  # 设置背景色为浅灰色
    
    return dot

def _render_graph(dot, output_file: str, open_pdf: bool = True):
    """
    渲染并保存图形
    
    Args:
        dot: graphviz.Digraph对象
        output_file: 输出文件路径
        open_pdf: 是否在生成后自动打开文件
    """
    try:
        if open_pdf:
            dot.render(output_file.split('.')[0], view=open_pdf)
            logger.info(f"图形已保存为 {output_file}")
    except Exception as e:
        logger.error(f"保存图像时出错: {e}")

def _extract_node_label(node_name: str) -> str:
    """
    从完整节点名中提取表名（保留模式前缀）
    
    Args:
        node_name: 完整节点名
        
    Returns:
        str: 提取后的表名（包含模式前缀）
    """
    return str(node_name)  # 返回完整节点名，保留模式前缀

def _calculate_node_width(label: str) -> str:
    """
    根据标签长度计算节点宽度
    
    Args:
        label: 节点显示标签
        
    Returns:
        str: 节点宽度值字符串
    """
    # 基础宽度为1.0，每增加10个字符增加0.5的宽度
    label_length = len(label)
    width = max(1.0, 1.0 + (label_length / 10) * 1.0)
    return str(width)

def _add_styled_node(dot, node_name: str, label: str, node_type: str = 'normal', node_index: int = 0):
    """
    添加带样式的节点
    
    Args:
        dot: graphviz.Digraph对象
        node_name: 节点ID
        label: 节点显示标签
        node_type: 节点类型，可选值为'normal', 'center', 'pruned', 'unvisited', 'upstream', 'downstream'
        node_index: 用于生成循环颜色的索引
    """
    width = _calculate_node_width(label)
    
    # 定义颜色方案
    node_colors = ['#FF9999', '#99FF99', '#9999FF', '#FFFF99', '#FF99FF', '#99FFFF']
    center_color = '#FF9999'      # 中心/高亮节点颜色
    pruned_color = '#666666'      # 可剪枝节点颜色
    unvisited_color = '#DDDDDD'   # 未访问节点颜色
    upstream_color = '#99CCFF'    # 上游节点颜色
    downstream_color = '#99FF99'  # 下游节点颜色
    
    # 根据节点类型设置样式
    if node_type == 'center':
        dot.node(str(node_name), label, shape='box', style='filled,rounded', 
                fillcolor=center_color, fontcolor='black', penwidth='2',
                width=width)
    elif node_type == 'pruned':
        dot.node(str(node_name), label, shape='box', style='filled,rounded', 
                fillcolor=pruned_color, fontcolor='white', penwidth='1',
                width=width)
    elif node_type == 'unvisited':
        dot.node(str(node_name), label, shape='box', style='filled,rounded', 
                fillcolor=unvisited_color, fontcolor='#666666', penwidth='1',
                width=width)
    elif node_type == 'upstream':
        dot.node(str(node_name), label, shape='box', style='filled,rounded', 
                fillcolor=upstream_color, fontcolor='black', penwidth='1.5',
                width=width)
    elif node_type == 'downstream':
        dot.node(str(node_name), label, shape='box', style='filled,rounded', 
                fillcolor=downstream_color, fontcolor='black', penwidth='1.5',
                width=width)
    else:  # normal
        color = node_colors[node_index % len(node_colors)]
        dot.node(str(node_name), label, shape='box', style='filled,rounded', 
                fillcolor=color, fontcolor='black', penwidth='1.5',
                width=width)

def _format_sql_label(sql_text: str, sql_id: int) -> str:
    """
    格式化SQL语句为graphviz边标签
    
    Args:
        sql_text: SQL语句文本
        sql_id: SQL语句ID
        
    Returns:
        str: 格式化后的标签
    """
    try:
        import sqlglot
        
        # 使用sqlglot从语法层面美化SQL
        try:
            formatted_sql = sqlglot.transpile(sql_text, read='mysql', pretty=True)[0]
        except Exception as format_error:
            logger.warning(f"SQL美化失败: {format_error}")
            formatted_sql = sql_text  # 如果美化失败，使用原始SQL
        
        # 移除分号，它在DOT语言中是语句终止符
        formatted_sql = formatted_sql.replace(';', '')
        
        # 转义其他可能导致问题的字符
        formatted_sql = formatted_sql.replace('\\', '\\\\')  # 转义反斜杠
        formatted_sql = formatted_sql.replace('"', '\\"')    # 转义双引号
        formatted_sql = formatted_sql.replace("'", "\\'")    # 转义单引号
        formatted_sql = formatted_sql.replace('<', '\\<')    # 转义小于号
        formatted_sql = formatted_sql.replace('>', '\\>')    # 转义大于号
        
        # 处理SQL格式以确保左对齐
        lines = formatted_sql.split('\n')
        if lines:
            # 找出所有非空行的最小缩进
            non_empty_lines = [line for line in lines if line.strip()]
            if non_empty_lines:
                min_indent = min(len(line) - len(line.lstrip()) for line in non_empty_lines)
                # 移除共同的前导空格以实现左对齐
                aligned_lines = []
                for i, line in enumerate(lines):
                    if line.strip():  # 非空行
                        # 移除最小共同缩进
                        base_line = line[min_indent:] if len(line) > min_indent else line
                        # 第一行不缩进，后续行添加缩进
                        if i > 0:
                            # 使用中文全角空格来代替普通空格，这在大多数环境下不会被压缩
                            aligned_line = "　" + base_line  # 两个全角空格
                        else:
                            aligned_line = base_line
                        aligned_line = aligned_line.replace('  ', '　　')
                        aligned_lines.append(aligned_line)
                    else:
                        aligned_lines.append('')  # 保留空行
                
                formatted_sql = '\n'.join(aligned_lines)
        
        # 创建SQL标签，确保每行都左对齐，并添加明显的分隔和边框
        sql_label = f"SQL {sql_id+1}: \\l" + formatted_sql.replace('\n', '\\l') + '\\l' + "-" * 30 + "\\l"
        return sql_label
    except ImportError:
        logger.warning("sqlglot库未安装，无法格式化SQL")
        # 创建简单标签
        return f"SQL {sql_id+1}: {sql_text[:100]}..." if len(sql_text) > 100 else sql_text

def _add_styled_edge(dot, source: str, target: str, edge_type: str = 'normal', label: str = None):
    """
    添加带样式的边
    
    Args:
        dot: graphviz.Digraph对象
        source: 源节点ID
        target: 目标节点ID
        edge_type: 边类型，可选值为'normal', 'pruned', 'unvisited', 'upstream', 'downstream'
        label: 边标签
    """
    if edge_type == 'pruned':
        # 可剪枝边使用深灰色虚线
        if label:
            dot.edge(str(source), str(target), label=label, 
                   color='#666666', penwidth='1', fontsize='10', 
                   align='left', style='dashed')
        else:
            dot.edge(str(source), str(target), color='#666666', 
                   penwidth='1', style='dashed')
    elif edge_type == 'unvisited':
        # 未访问边使用浅灰色虚线
        if label:
            dot.edge(str(source), str(target), label=label, 
                   color='#CCCCCC', penwidth='1', fontsize='10', 
                   align='left', style='dashed')
        else:
            dot.edge(str(source), str(target), color='#CCCCCC', 
                   penwidth='1', style='dashed')
    elif edge_type == 'upstream':
        # 上游到中心的边
        if label:
            dot.edge(str(source), str(target), label=label, 
                   color='#0066CC', penwidth='1.5', fontsize='10', align='left')
        else:
            dot.edge(str(source), str(target), color='#0066CC', penwidth='1.5')
    elif edge_type == 'downstream':
        # 中心到下游的边
        if label:
            dot.edge(str(source), str(target), label=label, 
                   color='#CC6600', penwidth='1.5', fontsize='10', align='left')
        else:
            dot.edge(str(source), str(target), color='#CC6600', penwidth='1.5')
    else:  # normal
        # 正常边使用蓝色实线
        if label:
            dot.edge(str(source), str(target), label=label, 
                   color='#0066CC', penwidth='1.5', fontsize='10', align='left')
        else:
            dot.edge(str(source), str(target), color='#0066CC', penwidth='1.5')

# ====================== 血缘图打印函数 ======================

def print_lineage_graph_with_sql(graph: Neo4jLineageGraph) -> None:
    """
    打印血缘图中的边及其关联的SQL语句。
    
    该函数遍历Neo4j血缘图中的所有边，输出每条边的源节点和目标节点，以及关联的SQL语句。
    SQL语句过长时会被截断，以便于显示。
    
    Args:
        graph: Neo4j血缘图对象，包含关联的SQL语句信息
    
    Returns:
        无返回值，直接通过logger输出信息
    """
    try:
        logger.info("=== 血缘图边与SQL语句映射 ===")
        all_edges = graph.get_all_edges()
        
        for source, target in all_edges:
            logger.info(f"边: {source} -> {target}")
            
            # 获取边的属性
            edge_attrs = graph.get_edge_attributes(source, target)
            
            if 'sql_statements' in edge_attrs and edge_attrs['sql_statements']:
                logger.info(f"关联的SQL语句数量: {len(edge_attrs['sql_statements'])}")
                for sql_info in edge_attrs['sql_statements']:
                    sql_id = sql_info['sql_id']
                    sql_text = sql_info['sql_text']
                    # 截断过长的SQL以便于显示
                    short_sql = sql_text[:100] + "..." if len(sql_text) > 100 else sql_text
                    logger.debug(f"  SQL ID: {sql_id+1}, 语句: {short_sql}")
            else:
                logger.info("  没有关联的SQL语句")
    except Exception as e:
        logger.error(f"打印血缘图时出错: {e}")

# ====================== 血缘图可视化函数 ======================

def visualize_lineage_graph(graph: Neo4jLineageGraph, output_file: str = "output/lineage_graph_vis.pdf", open_pdf: bool = True, show_sql: bool = True, direction: str = 'LR') -> Optional[Any]:
    """
    使用 graphviz 可视化Neo4j血缘图，并保存为指定格式。
    
    该函数将Neo4j血缘图转换为graphviz格式，并根据节点状态(是否访问、是否可剪枝)进行不同的样式渲染。
    图中的节点和边会根据状态使用不同的颜色和样式，边上还会显示对应的SQL语句。
    
    Args:
        graph: Neo4j血缘图对象
        output_file: 输出文件路径，默认为 "output/lineage_graph_vis.pdf"
        open_pdf: 是否在生成后自动打开PDF文件，默认为True。如果为False，则只生成DOT文件而不生成PDF
        show_sql: 是否在边上显示SQL语句，默认为True
        direction: 布局方向，可选'LR'（从左到右）或'TB'（从上到下），默认为'LR'
    
    Returns:
        graphviz.Digraph对象，如果graphviz库未安装则返回None
    
    Raises:
        Exception: 当图像保存失败时可能引发异常
    """
    try:
        # 创建有向图
        dot = _create_digraph('表级血缘图', direction=direction)
        if dot is None:
            return None
        
        # 获取所有节点和边
        all_nodes = graph.get_all_nodes()
        all_edges = graph.get_all_edges()
        
        logger.debug(f"开始创建血缘图可视化，总节点数: {len(all_nodes)}, 总边数: {len(all_edges)}")
        
        # 添加节点
        for i, node in enumerate(all_nodes):
            try:
                # 获取表名，去除模式名
                table_name = _extract_node_label(node)
                
                # 获取节点属性
                node_attrs = graph.get_node_attributes(node)
                
                # 检查节点状态
                is_pruned = node_attrs.get('pruned', False)
                is_visited = node_attrs.get('visited', False)
                
                # 根据节点状态设置样式
                if not is_visited:
                    _add_styled_node(dot, node, table_name, 'unvisited')
                elif is_pruned:
                    _add_styled_node(dot, node, table_name, 'pruned')
                else:
                    _add_styled_node(dot, node, table_name, 'normal', i)
            except Exception as e:
                logger.error(f"添加节点 {node} 时出错: {e}")
                continue
        
        # 添加边
        for source, target in all_edges:
            try:
                # 获取边的属性
                edge_attrs = graph.get_edge_attributes(source, target)
                
                # 获取节点属性
                source_attrs = graph.get_node_attributes(source)
                target_attrs = graph.get_node_attributes(target)
                
                # 检查源节点和目标节点状态
                source_pruned = source_attrs.get('pruned', False)
                target_pruned = target_attrs.get('pruned', False)
                source_visited = source_attrs.get('visited', False)
                target_visited = target_attrs.get('visited', False)
                
                # 准备边标签
                edge_label = ""
                if show_sql and 'sql_statements' in edge_attrs and edge_attrs['sql_statements']:
                    # 为每条SQL语句创建标签
                    sql_labels = []
                    for sql_info in edge_attrs['sql_statements']:
                        sql_id = sql_info['sql_id']
                        sql_text = sql_info['sql_text']
                        sql_label = _format_sql_label(sql_text, sql_id)
                        sql_labels.append(sql_label)
                    
                    # 将所有SQL语句连接成一个标签
                    edge_label = "\\l".join(sql_labels)
                
                # 根据节点的状态设置边的样式
                if not source_visited or not target_visited:
                    _add_styled_edge(dot, source, target, 'unvisited', edge_label)
                elif source_pruned or target_pruned:
                    _add_styled_edge(dot, source, target, 'pruned', edge_label)
                else:
                    _add_styled_edge(dot, source, target, 'normal', edge_label)
            except Exception as e:
                logger.error(f"添加边 {source} -> {target} 时出错: {e}")
                continue
        
        # 渲染并保存图形
        _render_graph(dot, output_file, open_pdf)
        
        return dot
    except Exception as e:
        logger.error(f"可视化血缘图时出错: {e}")
        return None

# ====================== 路径查询和可视化 ======================

def _get_path_between_nodes(graph: Neo4jLineageGraph, source_node: str, target_node: str) -> Tuple[Set[str], Set[Tuple[str, str]]]:
    """
    查询两个节点之间的路径
    
    Args:
        graph: Neo4j血缘图对象
        source_node: 起始节点
        target_node: 目标节点
        
    Returns:
        Tuple[Set[str], Set[Tuple[str, str]]]: (路径节点集合, 路径边集合)
    """
    path_nodes = set()
    path_edges = set()
    
    try:
        # 使用Neo4j查询查找从源节点到目标节点的所有路径
        with graph.driver.session() as session:
            result = session.run(
                """
                MATCH p = (source:Table:`""" + graph.namespace + """` {name: $source})-[:FLOWS_TO*]->(target:Table:`""" + graph.namespace + """` {name: $target})
                RETURN p
                """,
                source=source_node, target=target_node
            )
            
            # 提取路径中的所有节点和边
            for record in result:
                path = record["p"]
                nodes = path.nodes
                relationships = path.relationships
                
                for node in nodes:
                    path_nodes.add(node["name"])
                
                for rel in relationships:
                    path_edges.add((rel.start_node["name"], rel.end_node["name"]))
    except Exception as e:
        logger.error(f"查询节点间路径时出错: {e}")
    
    return path_nodes, path_edges

def visualize_path_between_nodes(graph: Neo4jLineageGraph, source_node: str, target_node: str, output_file: str = "output/path_vis.pdf", open_pdf: bool = True, direction: str = 'LR') -> Optional[Any]:
    """
    可视化两个节点之间的所有路径。
    
    Args:
        graph: Neo4j血缘图对象
        source_node: 起始节点
        target_node: 目标节点
        output_file: 输出文件路径
        open_pdf: 是否在生成后自动打开PDF文件
        direction: 布局方向，可选'LR'（从左到右）或'TB'（从上到下），默认为'LR'
        
    Returns:
        graphviz.Digraph对象，如果失败则返回None
    """
    # 查询路径
    path_nodes, path_edges = _get_path_between_nodes(graph, source_node, target_node)
    
    if not path_nodes:
        logger.warning(f"没有找到从 {source_node} 到 {target_node} 的路径")
        return None
    
    try:
        # 创建有向图
        dot = _create_digraph(f'从 {source_node} 到 {target_node} 的路径', direction=direction)
        if dot is None:
            return None
        
        # 添加节点
        for node in path_nodes:
            table_name = _extract_node_label(node)
            
            if node == source_node or node == target_node:
                # 起点和终点使用高亮颜色
                _add_styled_node(dot, node, table_name, 'center')
            else:
                # 路径中的其他节点
                _add_styled_node(dot, node, table_name, 'upstream')
        
        # 添加边
        for source, target in path_edges:
            _add_styled_edge(dot, source, target)
        
        # 渲染并保存图形
        _render_graph(dot, output_file, open_pdf)
        
        return dot
    except Exception as e:
        logger.error(f"可视化路径时出错: {e}")
        return None

# ====================== 子图查询和可视化 ======================

def _get_subgraph(graph: Neo4jLineageGraph, node: str, direction: str = "upstream", depth: int = 200) -> Tuple[Set[str], Set[Tuple[str, str]]]:
    """
    查询指定节点的上游或下游子图
    
    Args:
        graph: Neo4j血缘图对象
        node: 中心节点
        direction: 方向，"upstream"表示上游，"downstream"表示下游
        depth: 遍历深度
        
    Returns:
        Tuple[Set[str], Set[Tuple[str, str]]]: (子图节点集合, 子图边集合)
    """
    subgraph_nodes = set()
    subgraph_edges = set()
    
    try:
        # 根据方向构建查询
        if direction == "upstream":
            query = f"""
            MATCH p = (source:Table:`{graph.namespace}`)-[:FLOWS_TO*1..{depth}]->(target:Table:`{graph.namespace}` {{name: $node}})
            RETURN p
            """
        else:  # downstream
            query = f"""
            MATCH p = (source:Table:`{graph.namespace}` {{name: $node}})-[:FLOWS_TO*1..{depth}]->(target:Table:`{graph.namespace}`)
            RETURN p
            """
        
        # 执行查询
        with graph.driver.session() as session:
            result = session.run(query, node=node)
            
            # 提取子图中的所有节点和边
            for record in result:
                path = record["p"]
                nodes = path.nodes
                relationships = path.relationships
                
                for n in nodes:
                    subgraph_nodes.add(n["name"])
                
                for rel in relationships:
                    subgraph_edges.add((rel.start_node["name"], rel.end_node["name"]))
    except Exception as e:
        logger.error(f"查询子图时出错: {e}")
    
    return subgraph_nodes, subgraph_edges

def visualize_subgraph(graph: Neo4jLineageGraph, node: str, direction: str = "upstream", depth: int = 200, output_file: str = "output/subgraph_vis.pdf", open_pdf: bool = True, layout_direction: str = 'LR') -> Optional[Any]:
    """
    可视化指定节点的上游或下游子图
    
    Args:
        graph: Neo4j血缘图对象
        node: 中心节点
        direction: 方向，"upstream"表示上游，"downstream"表示下游
        depth: 遍历深度
        output_file: 输出文件路径
        open_pdf: 是否在生成后自动打开PDF文件
        layout_direction: 布局方向，可选'LR'（从左到右）或'TB'（从上到下），默认为'LR'
        
    Returns:
        graphviz.Digraph对象，如果失败则返回None
    """
    # 查询子图
    subgraph_nodes, subgraph_edges = _get_subgraph(graph, node, direction, depth)
    
    if not subgraph_nodes:
        logger.warning(f"节点 {node} 的{direction}子图为空")
        return None
    
    try:
        # 创建有向图
        title = f'{node} 的{direction}子图 (深度 {depth})'
        dot = _create_digraph(title, direction=layout_direction)
        if dot is None:
            return None
        
        # 添加节点
        for i, n in enumerate(subgraph_nodes):
            table_name = _extract_node_label(n)
            
            if n == node:
                # 中心节点
                _add_styled_node(dot, n, table_name, 'center')
            else:
                # 其他节点
                _add_styled_node(dot, n, table_name, 'normal', i)
        
        # 添加边
        for source, target in subgraph_edges:
            _add_styled_edge(dot, source, target)
        
        # 渲染并保存图形
        _render_graph(dot, output_file, open_pdf)
        
        return dot
    except Exception as e:
        logger.error(f"可视化子图时出错: {e}")
        return None

# ====================== 邻居节点查询和可视化 ======================

def _get_node_neighborhood(graph: Neo4jLineageGraph, node: str) -> Tuple[List[str], List[str], List[Tuple[str, str]], List[Tuple[str, str]]]:
    """
    查询节点的邻居节点（直接相连的上游和下游节点）
    
    Args:
        graph: Neo4j血缘图对象
        node: 中心节点
        
    Returns:
        Tuple: (上游节点列表, 下游节点列表, 上游边列表, 下游边列表)
    """
    in_nodes = []
    out_nodes = []
    in_edges = []
    out_edges = []
    
    try:
        # 使用Neo4j查询获取中心节点的所有直接相连节点
        with graph.driver.session() as session:
            result = session.run(
                """
                MATCH (n:Table:`""" + graph.namespace + """` {name: $node})
                OPTIONAL MATCH (in:Table:`""" + graph.namespace + """` )-[r1:FLOWS_TO]->(n)
                OPTIONAL MATCH (n)-[r2:FLOWS_TO]->(out:Table:`""" + graph.namespace + """` )
                RETURN collect(DISTINCT in.name) AS in_nodes, 
                       collect(DISTINCT out.name) AS out_nodes,
                       collect(DISTINCT [in.name, n.name]) AS in_edges,
                       collect(DISTINCT [n.name, out.name]) AS out_edges
                """,
                node=node
            )
            
            record = result.single()
            if record:
                # 获取上游节点和下游节点
                in_nodes = [n for n in record["in_nodes"] if n]  # 过滤None值
                out_nodes = [n for n in record["out_nodes"] if n]  # 过滤None值
                
                # 获取边
                in_edges = [(edge[0], edge[1]) for edge in record["in_edges"] if edge[0]]  # 过滤None值
                out_edges = [(edge[0], edge[1]) for edge in record["out_edges"] if edge[1]]  # 过滤None值
    except Exception as e:
        logger.error(f"查询节点邻居时出错: {e}")
    
    return in_nodes, out_nodes, in_edges, out_edges

def visualize_node_neighborhood(graph: Neo4jLineageGraph, node: str, output_file: str = "output/neighborhood_vis.pdf", open_pdf: bool = True, direction: str = 'LR') -> Optional[Any]:
    """
    可视化指定节点及其直接相连的所有邻居节点组成的子图
    
    该函数会同时显示中心节点的上游节点和下游节点，即所有与中心节点有直接边连接的节点。
    这与visualize_subgraph不同，后者可以递归地向上或向下遍历多层节点。
    
    Args:
        graph: Neo4j血缘图对象
        node: 中心节点
        output_file: 输出文件路径
        open_pdf: 是否在生成后自动打开PDF文件
        direction: 布局方向，可选'LR'（从左到右）或'TB'（从上到下），默认为'LR'
        
    Returns:
        graphviz.Digraph对象，如果失败则返回None
    """
    # 查询邻居节点
    in_nodes, out_nodes, in_edges, out_edges = _get_node_neighborhood(graph, node)
    
    # 即使没有邻居节点，也创建只包含中心节点的图
    if not in_nodes and not out_nodes:
        logger.warning(f"节点 {node} 没有直接相连的邻居节点")
    
    try:
        # 创建有向图
        title = f'{node} 及其邻居节点'
        dot = _create_digraph(title, direction=direction)
        if dot is None:
            return None
        
        # 添加节点
        # 先添加中心节点
        center_name = _extract_node_label(node)
        _add_styled_node(dot, node, center_name, 'center')
        
        # 添加上游节点
        for n in in_nodes:
            table_name = _extract_node_label(n)
            _add_styled_node(dot, n, table_name, 'upstream')
        
        # 添加下游节点
        for n in out_nodes:
            table_name = _extract_node_label(n)
            _add_styled_node(dot, n, table_name, 'downstream')
        
        # 添加边
        # 上游到中心的边
        for source, target in in_edges:
            _add_styled_edge(dot, source, target, 'upstream')
        
        # 中心到下游的边
        for source, target in out_edges:
            _add_styled_edge(dot, source, target, 'downstream')
        
        # 渲染并保存图形
        _render_graph(dot, output_file, open_pdf)
        
        return dot
    except Exception as e:
        logger.error(f"可视化节点邻居时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# ====================== 连通分量查询和可视化 ======================

def _get_connected_component(graph: Neo4jLineageGraph, node: str) -> Tuple[Set[str], Set[Tuple[str, str]]]:
    """
    获取与指定节点相连的所有节点（连通分量）
    
    该函数获取给定节点所在的连通分量，即所有可以从该节点通过有向边到达
    或者可以到达该节点的所有节点，无论是直接连接还是间接连接。
    
    Args:
        graph: Neo4j血缘图对象
        node: 中心节点
        
    Returns:
        Tuple[Set[str], Set[Tuple[str, str]]]: (子图节点集合, 子图边集合)
    """
    component_nodes = set()
    component_edges = set()
    
    try:
        # 使用Neo4j查询获取与中心节点相连的所有节点
        with graph.driver.session() as session:
            # 先检查节点是否存在
            check_result = session.run(
                """
                MATCH (n:Table:`""" + graph.namespace + """` {name: $node})
                RETURN n
                """,
                node=node
            )
            
            check_record = check_result.single()
            if not check_record:
                logger.warning(f"节点 {node} 在数据库中不存在")
                component_nodes.add(node)  # 至少添加中心节点
                return component_nodes, component_edges
            else:
                logger.info(f"已找到节点 {node}")
            
            # 检查直接连接的边
            direct_edges_query = """
            MATCH (n:Table:`""" + graph.namespace + """` {name: $node})-[r]-(m)
            RETURN count(r) as edge_count
            """
            
            direct_result = session.run(direct_edges_query, node=node)
            direct_record = direct_result.single()
            
            if direct_record and direct_record["edge_count"] > 0:
                logger.info(f"节点 {node} 有 {direct_record['edge_count']} 条直接相连的边")
            else:
                logger.warning(f"节点 {node} 没有直接相连的边")
            
            # 详细查询所有入边和出边
            detailed_edges_query = """
            MATCH (n:Table:`""" + graph.namespace + """` {name: $node})-[r]->(m)
            RETURN 'outgoing' as direction, m.name as connected_node
            UNION
            MATCH (m)-[r]->(n:Table:`""" + graph.namespace + """` {name: $node})
            RETURN 'incoming' as direction, m.name as connected_node
            """
            
            detailed_result = session.run(detailed_edges_query, node=node)
            for record in detailed_result:
                direction = record["direction"]
                connected_node = record["connected_node"]
                logger.info(f"找到 {direction} 边连接到节点: {connected_node}")
            
            # 查询所有可从该节点到达或可到达该节点的节点
            path_query = """
            MATCH (center:Table:`""" + graph.namespace + """` {name: $node})
            CALL {
                // 上游路径
                MATCH path = (source:Table:`""" + graph.namespace + """`)-[:FLOWS_TO*]->(center)
                RETURN path
                UNION
                // 下游路径
                MATCH path = (center)-[:FLOWS_TO*]->(target:Table:`""" + graph.namespace + """`)
                RETURN path
            }
            RETURN path
            """
            
            logger.info(f"执行连通分量查询: {path_query.replace(graph.namespace, 'NAMESPACE')}")
            
            result = session.run(path_query, node=node)
            path_count = 0
            
            # 提取所有节点和边
            for record in result:
                path_count += 1
                if record and record.get("path"):
                    path = record["path"]
                    nodes = path.nodes
                    relationships = path.relationships
                    
                    logger.info(f"发现路径 {path_count}，包含 {len(nodes)} 个节点和 {len(relationships)} 条边")
                    
                    for n in nodes:
                        component_nodes.add(n["name"])
                    
                    for rel in relationships:
                        component_edges.add((rel.start_node["name"], rel.end_node["name"]))
            
            if path_count == 0:
                logger.warning(f"没有找到连接到节点 {node} 的任何路径")
            else:
                logger.info(f"总共找到 {path_count} 条路径")
            
            # 确保中心节点被添加（即使它没有连接）
            component_nodes.add(node)
            
    except Exception as e:
        logger.error(f"查询连通分量时出错: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info(f"连通分量查询结果: {len(component_nodes)} 个节点, {len(component_edges)} 条边")
    return component_nodes, component_edges

def visualize_connected_component(graph: Neo4jLineageGraph, node: str, output_file: str = "output/component_vis.pdf", open_pdf: bool = True, direction: str = 'LR') -> Optional[Any]:
    """
    可视化所有与指定节点相连的节点构成的子图（连通分量）
    
    该函数绘制指定节点所在的连通分量，包括所有可以从该节点到达
    或可以到达该节点的节点，而不仅仅是一阶邻居。
    
    Args:
        graph: Neo4j血缘图对象
        node: 中心节点
        output_file: 输出文件路径
        open_pdf: 是否在生成后自动打开PDF文件
        direction: 布局方向，可选'LR'（从左到右）或'TB'（从上到下），默认为'LR'
        
    Returns:
        graphviz.Digraph对象，如果失败则返回None
    """
    # 获取连通分量
    component_nodes, component_edges = _get_connected_component(graph, node)
    
    if len(component_nodes) <= 1:
        logger.warning(f"节点 {node} 没有与其他节点相连")
    
    try:
        # 创建有向图
        title = f'与 {node} 相连的子图'
        dot = _create_digraph(title, direction=direction)
        if dot is None:
            return None
        
        # 添加节点，首先添加中心节点
        center_name = _extract_node_label(node)
        _add_styled_node(dot, node, center_name, 'center')
        
        # 添加其他节点
        for i, n in enumerate(component_nodes):
            if n != node:  # 跳过中心节点，已经添加过了
                table_name = _extract_node_label(n)
                _add_styled_node(dot, n, table_name, 'normal', i)
        
        # 添加边，区分上游边和下游边
        for source, target in component_edges:
            if target == node:
                # 到中心节点的边（上游）
                _add_styled_edge(dot, source, target, 'upstream')
            elif source == node:
                # 从中心节点出发的边（下游）
                _add_styled_edge(dot, source, target, 'downstream')
            else:
                # 其他边
                _add_styled_edge(dot, source, target, 'normal')
        
        # 渲染并保存图形
        _render_graph(dot, output_file, open_pdf)
        
        return dot
    except Exception as e:
        logger.error(f"可视化连通分量时出错: {e}")
        import traceback
        traceback.print_exc()
        return None 