#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
不满足约束跟踪示例

该示例展示了如何使用TypeAwareEnhancedSolver的不满足约束跟踪功能，
用于诊断约束系统中哪些约束导致了不可满足的结果。
"""

import z3
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver
from lineage_core.logger import logger, setup_logger

def demonstrate_unsat_tracking():
    """
    展示TypeAwareEnhancedSolver的不满足约束跟踪功能
    """
    # 设置更详细的日志级别
    setup_logger(log_level="DEBUG")
    
    # 创建求解器并启用约束跟踪
    solver = TypeAwareEnhancedSolver(track_unsat=True)
    
    # 创建示例变量
    x, y = z3.Ints('x y')
    
    # 添加矛盾的约束条件
    logger.info("添加约束条件...")
    solver.add(x > 0)  # x必须大于0
    solver.add(y > 0)  # y必须大于0
    solver.add(x + y < 0)  # x+y必须小于0（与前两个约束矛盾）
    
    # 执行求解
    logger.info("开始求解...")
    result = solver.check()
    
    if result == z3.unsat:
        logger.info("求解结果: 约束不可满足")
        # 不满足约束的核心已经在solver.check()中自动输出到logger
    else:
        logger.info("求解结果: 约束可满足")
        model = solver.model()
        logger.info(f"模型: {model}")
    
    # 重置求解器并关闭跟踪
    solver.reset()
    solver.enable_unsat_tracking(False)
    
    # 添加可满足的约束
    solver.add(x > 0)
    solver.add(y > 0)
    solver.add(x + y > 0)
    
    # 再次求解
    logger.info("\n添加可满足约束后再次求解...")
    result = solver.check()
    
    if result == z3.unsat:
        logger.info("求解结果: 约束不可满足")
    else:
        logger.info("求解结果: 约束可满足")
        model = solver.model()
        logger.info(f"模型: {model}")

if __name__ == "__main__":
    demonstrate_unsat_tracking() 