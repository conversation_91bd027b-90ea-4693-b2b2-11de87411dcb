from z3 import *
import time

def main():
    datex = z3.String('datax')
    s = Solver()
    start_time = time.time()
    s.add(z3.And(datex > z3.StringVal('2024-01-01'), datex < z3.StringVal('2024-12-31')))
    s.add(datex < z3.StringVal('2023-01-01'))
    print(f"default: {s.check()}")
    end_time = time.time()
    print(f"default time: {end_time - start_time}")

    s.reset()
    z3.set_option('smt.string_solver', 'z3str3')
    start_time = time.time()
    s.add(z3.And(datex > z3.StringVal('2024-01-01'), datex < z3.StringVal('2024-12-31')))
    s.add(datex < z3.StringVal('2023-01-01'))
    print(f"z3str3: {s.check()}")
    if s.check() == sat:
        m = s.model()
        print(f"model: {m}")
    end_time = time.time()
    print(f"z3str3 time: {end_time - start_time}")

if __name__ == "__main__":
    main() 