''' 重新梳理和描述一下SQL代码的建模

    有数据表，表达为SQL后如下：

    order_mapping 存储当前订单号和历史订单号的映射关系
    INSERT INTO order_mapping (current_order_id, historical_order_id) SELECT '1001', '9001' FROM DUAL;
    INSERT INTO order_mapping (current_order_id, historical_order_id) SELECT '9001', '9002' FROM DUAL;

    current_orders 存储所有当前有效订单号
    INSERT INTO current_orders (current_order_id) SELECT current_order_id FROM order_mapping;

    现在需要查询满足条件的记录，是否存在某些 order_mapping 里的历史订单号也在 current_orders 的当前订单号列表中：
    SELECT m.current_order_id FROM order_mapping m JOIN current_orders c ON m.historical_order_id = c.current_order_id;

    以下代码体现了上述逻辑。
'''

from z3 import *
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent))
from lineage_core.type_aware_solver import TypeAwareEnhancedSolver
# 创建 Z3 求解器
solver = TypeAwareEnhancedSolver()

# 定义 id 的类型为整数
Dataset_Id = IntSort() # 记录ID

# 定义订单号类型为字符串
OrderId = StringSort()

# 定义函数：将 id 映射到当前订单号和历史订单号
current_order_id = Function('current_order_id', Dataset_Id, OrderId)
historical_order_id = Function('historical_order_id', Dataset_Id, OrderId)

# order_mapping 表中的数据
solver.add(current_order_id(1) == "1001")  # 第一条映射，当前订单号1001
solver.add(historical_order_id(1) == "9001")  # 第一条映射，历史订单号9001
solver.add(current_order_id(2) == "9001")  # 第二条映射，当前订单号9001
solver.add(historical_order_id(2) == "9002")  # 第二条映射，历史订单号9002

# order_mapping 表中的数据
order_mapping_ids = [1, 2]  # 这里用id来索引order_mapping表

current_order_id_2 = Function('current_order_id_2', Dataset_Id, OrderId)
solver.add(Or([current_order_id_2(3) == current_order_id(i) for i in order_mapping_ids]))
current_orders_ids_2 = [3]

# 定义变量，分别表示order_mapping表和current_orders表的行id
mapping_id = Int('mapping_id')
current_id = Int('current_id')

# 约束变量必须在各自表的id集合内
solver.add(Or([mapping_id == i for i in order_mapping_ids]))
solver.add(Or([current_id == i for i in current_orders_ids_2]))

# 约束：order_mapping表的历史订单号 = current_orders表的当前订单号
solver.add(current_order_id(current_id) == historical_order_id(mapping_id))

# 检查是否存在满足条件的记录
if solver.check() == sat:
    print("存在历史订单号也在当前订单号列表中的情况")
    model = solver.model()
    mapping_id_val = model[mapping_id]
    current_id_val = model[current_id]
    print(f"示例解: mapping_id = {mapping_id_val}, current_id = {current_id_val}")
    print(f"current_order_id = {model.eval(current_order_id(mapping_id))}, historical_order_id = {model.eval(historical_order_id(mapping_id))}, current_orders_id = {model.eval(current_order_id(current_id))}")
else:
    print("不存在历史订单号也在当前订单号列表中的情况")