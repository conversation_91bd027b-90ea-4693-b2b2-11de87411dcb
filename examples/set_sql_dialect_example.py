#!/usr/bin/env python3
"""
示例脚本：展示如何设置和使用全局SQL方言配置
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = str(Path(__file__).resolve().parent.parent)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入需要的模块
from lineage_core import set_sql_dialect, get_sql_dialect
from lineage_core.utils import extract_tables
import sqlglot

# 定义几个不同方言的SQL示例
postgres_sql = """
SELECT * FROM users 
WHERE created_at > CURRENT_DATE - INTERVAL '7 days'
"""

mysql_sql = """
SELECT * FROM users 
WHERE created_at > CURRENT_DATE - INTERVAL 7 DAY
"""

oracle_sql = """
SELECT * FROM users 
WHERE created_at > CURRENT_DATE - INTERVAL '7' DAY
"""

snowflake_sql = """
SELECT * FROM users 
WHERE created_at > CURRENT_DATE - INTERVAL '7 days'
"""

# 测试不同的SQL方言
def test_dialect(dialect, sql):
    print(f"\n==== 测试 {dialect} 方言 ====")
    
    # 设置全局SQL方言
    set_sql_dialect(dialect)
    current_dialect = get_sql_dialect()
    print(f"当前SQL方言: {current_dialect}")
    
    try:
        # 使用sqlglot直接解析
        print(f"\n直接使用 sqlglot 解析 {dialect} SQL:")
        parsed = sqlglot.parse_one(sql, dialect=current_dialect)
        print(f"解析成功: {parsed}")
        
        # 使用库中的函数（会使用全局配置的方言）
        print(f"\n使用库函数解析 {dialect} SQL:")
        input_tables, output_tables = extract_tables(sql)
        print(f"提取的输入表: {input_tables}")
        
    except Exception as e:
        print(f"解析失败: {e}")

if __name__ == "__main__":
    # 测试不同方言
    test_dialect("postgres", postgres_sql)
    test_dialect("mysql", mysql_sql)
    test_dialect("oracle", oracle_sql)
    test_dialect("snowflake", snowflake_sql)
    
    # 恢复默认方言
    set_sql_dialect("postgres")
    print(f"\n已恢复默认方言: {get_sql_dialect()}") 