import sqlglot
from sqlglot import exp
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
from lineage_core.config import get_sql_dialect

def extract_tables(sql: str) -> tuple[list[str], list[str]]:
    """
    从SQL字符串中提取输入表和输出表
    
    参数:
        sql: 要解析的SQL查询字符串
    
    返回:
        tuple: 包含两个列表的元组
            - 第一个列表: 排序后的输入表名称列表
            - 第二个列表: 排序后的输出表名称列表
    """
    input_tables = set()    # 存储输入表名的集合
    output_tables = set()   # 存储输出表名的集合
    cte_names = set()       # 存储CTE名称的集合
    
    try:
        # 解析SQL字符串为表达式
        expressions = sqlglot.parse(sql, dialect=get_sql_dialect())
        for expression in expressions:
            # 收集所有CTE名称
            for cte in expression.find_all(exp.CTE):
                cte_names.add(cte.alias_or_name)
            
            # 处理INSERT语句中的输出表
            if isinstance(expression, exp.Insert):
                # 获取输出表名
                if hasattr(expression, 'this') and expression.this:
                    # 处理Schema结构
                    if isinstance(expression.this, exp.Schema):
                        if hasattr(expression.this, 'this') and expression.this.this:
                            if hasattr(expression.this.this, 'name'):
                                output_tables.add(expression.this.this.name)
                    # 处理直接的Table结构
                    elif hasattr(expression.this, 'name'):
                        output_tables.add(expression.this.name)
            
            # 收集所有表引用作为潜在的输入表
            for table in expression.find_all(exp.Table):
                if hasattr(table, 'name'):
                    table_name = table.name
                    # 排除CTE定义的临时表
                    if table_name not in cte_names:
                        input_tables.add(table_name)
        
        # 从输入表中移除输出表（避免将输出表也计算为输入表）
        input_tables = input_tables - output_tables
        
    except Exception as e:
        print(f"解析SQL时出错: {e}")
        return [], []
    
    # 返回排序后的表名列表
    return sorted(input_tables), sorted(output_tables)

if __name__ == "__main__":
    sql_path = "test.sql"  # 使用当前目录下的test.sql文件
    try:
        with open(sql_path, 'r') as f:
            sql_content = f.read()
        input_tables, output_tables = extract_tables(sql_content)
    except FileNotFoundError:
        print(f"错误: 文件 {sql_path} 未找到")
        exit(1)
    
    print("SQL中使用的输入表：")
    for table in input_tables:
        print(f"- {table}")
    
    print("\nSQL中使用的输出表：")
    for table in output_tables:
        print(f"- {table}")
