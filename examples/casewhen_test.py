import sqlglot
from sqlglot import expressions as exp
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
from lineage_core.config import get_sql_dialect

def transform_case_when(sql):
    """
    将包含 CASE WHEN 的 SQL SELECT 语句转换为不含 CASE WHEN 的等价形式。
    返回多个 SELECT 语句的列表，每个分支一个 SELECT。
    如果无法处理（如聚合函数中的 CASE），则返回原始 SQL
    
    支持两种 CASE WHEN 语法:
    1. 简单型: CASE expr WHEN value THEN result ...
    2. 搜索型: CASE WHEN condition THEN result ...
    
    局限性：！！！
      - 只处理最外层的 CASE WHEN，不处理嵌套的 CASE WHEN
      - 如果有多个 CASE WHEN, 只处理第一个
    
    参数:
        sql (str): 输入的 SQL 语句
    返回:
        list: 转换后的 SQL 语句列表
    """
    try:
        # 解析 SQL 语句
        ast = sqlglot.parse_one(sql, dialect=get_sql_dialect())
        
        # 确保是 SELECT 语句
        if not isinstance(ast, exp.Select):
            return [sql]  # 非 SELECT 语句，返回原始 SQL
        
        # 找到 CASE WHEN 表达式
        case_expr = None
        case_projection = None
        for projection in ast.expressions:
            # 简单类型的 CASE WHEN 可能嵌套在一个别名表达式中
            if isinstance(projection, exp.Alias) and isinstance(projection.this, exp.Case):
                case_expr = projection.this
                case_projection = projection
                break
            elif isinstance(projection, exp.Case):
                case_expr = projection
                case_projection = projection
                break
        
        if not case_expr:
            return [sql]  # 未找到 CASE WHEN 表达式，返回原始 SQL
        
        # 检查是否在聚合函数内部
        if case_projection and hasattr(case_projection, 'parent') and any(
            isinstance(case_projection.parent, agg) 
            for agg in [exp.Sum, exp.Avg, exp.Count, exp.Max, exp.Min]
        ):
            return [sql]  # CASE WHEN 在聚合函数内，返回原始 SQL
        
        # 判断 CASE 类型
        is_simple_case = 'this' in case_expr.args and case_expr.args['this'] is not None
        
        # 获取条件列表和默认值
        case_conditions = case_expr.args.get('ifs', [])
        default = case_expr.args.get('default')  # ELSE 子句的值
        
        # 获取原始的 FROM、JOIN 和 WHERE 子句
        from_clause = ast.args.get('from')
        joins = ast.args.get('joins', [])
        where_clause = ast.args.get('where')
        
        # 生成每个分支的 SELECT 语句
        select_statements = []
        sql_list = []  # 存储最终的 SQL 字符串列表
        
        # 处理每个条件
        for if_obj in case_conditions:
            # 获取结果值
            then = if_obj.args['true']
            
            # 获取或构建条件
            if is_simple_case:
                # 简单型 CASE: 创建 expr = value 条件
                # CASE expr WHEN value THEN result
                cond = exp.EQ(
                    this=case_expr.args['this'].copy(),
                    expression=if_obj.args['this']
                )
            else:
                # 搜索型 CASE: 直接使用条件
                # CASE WHEN condition THEN result
                cond = if_obj.args['this']
                
            # 准备新的投影表达式列表
            new_expressions = []
            for proj in ast.expressions:
                if proj == case_projection:
                    # 如果是 Alias 包装的 CASE
                    if isinstance(case_projection, exp.Alias):
                        new_proj = exp.Alias(
                            this=then,
                            alias=case_projection.alias
                        )
                    else:
                        new_proj = then
                else:
                    new_proj = proj.copy()
                new_expressions.append(new_proj)
            
            # 处理 WHERE 条件
            if where_clause:
                new_where = exp.and_(where_clause.this.copy(), cond)
            else:
                new_where = cond
            
            # 创建新的 SELECT 语句
            new_select = exp.Select(
                expressions=new_expressions,
                from_=from_clause.copy() if from_clause else None,
                joins=[j.copy() for j in joins],
                where=exp.Where(this=new_where)
            )
            select_statements.append(new_select)
            sql_list.append(new_select.sql(pretty=True))
        
        # 处理 ELSE 子句（如果存在）
        if default:
            # 生成否定条件
            not_conds = []
            for if_obj in case_conditions:
                if is_simple_case:
                    # 简单型 CASE: expr != value 条件
                    not_conds.append(exp.NEQ(
                        this=case_expr.args['this'].copy(),
                        expression=if_obj.args['this']
                    ))
                else:
                    # 搜索型 CASE: NOT condition
                    not_conds.append(exp.not_(if_obj.args['this']))
                
            # 处理 ELSE 条件（处理 not_conds 为空的情况）
            if not_conds:
                else_cond = exp.and_(*not_conds)
                # 处理 WHERE 条件
                if where_clause:
                    new_where = exp.and_(where_clause.this.copy(), else_cond)
                else:
                    new_where = else_cond
            else:
                # 如果没有否定条件（没有 WHEN 子句），只用原始 WHERE
                new_where = where_clause.this.copy() if where_clause else None
            
            # 准备新的投影表达式列表
            new_expressions = []
            for proj in ast.expressions:
                if proj == case_projection:
                    # 如果是 Alias 包装的 CASE
                    if isinstance(case_projection, exp.Alias):
                        new_proj = exp.Alias(
                            this=default,
                            alias=case_projection.alias
                        )
                    else:
                        new_proj = default
                else:
                    new_proj = proj.copy()
                new_expressions.append(new_proj)
            
            # 创建 ELSE 对应的 SELECT 语句
            new_select = exp.Select(
                expressions=new_expressions,
                from_=from_clause.copy() if from_clause else None,
                joins=[j.copy() for j in joins],
                where=exp.Where(this=new_where) if new_where else None
            )
            select_statements.append(new_select)
            sql_list.append(new_select.sql(pretty=True))
        
        # 检查是否有生成的语句
        if not select_statements:
            return [sql]  # 没有生成语句，返回原始 SQL
        
        # 返回 SQL 字符串列表
        return sql_list
    
    except Exception as e:
        # 出现任何异常，返回原始 SQL
        print(f"转换 CASE WHEN 时出错: {e}")
        return [sql]

# 测试示例
def test_case_when():
    # 示例1：简单 CASE 表达式
    sql1 = '''
    SELECT
        T1.b AS code,
        CASE T1.b 
            WHEN "s001" THEN T2.amount 
            WHEN "s002" THEN T3.amount 
            ELSE T1.amount 
        END AS amount
    FROM T1
    INNER JOIN T2 ON T1.a = T2.a
    INNER JOIN T3 ON T1.a = T3.a
    WHERE T1.c > 0
    '''
    
    # 示例2：搜索 CASE 表达式
    sql2 = '''
    SELECT 
        customer_id,
        CASE 
            WHEN total_purchase > 1000 THEN 'Platinum'
            WHEN total_purchase > 500 THEN 'Gold'
            WHEN total_purchase > 100 THEN 'Silver'
            ELSE 'Regular'
        END AS customer_tier
    FROM customers
    WHERE signup_date > '2023-01-01'
    '''
    
    # 示例3：带聚合的 CASE 表达式
    sql3 = '''
    SELECT 
        department,
        SUM(CASE 
            WHEN status = 'completed' THEN amount
            ELSE 0
        END) AS completed_amount,
        COUNT(*) AS total_transactions
    FROM transactions
    GROUP BY department
    HAVING COUNT(*) > 10
    '''
    
    # 示例4：多个 CASE 表达式
    sql4 = '''
    SELECT
        order_id,
        CASE 
            WHEN payment_method = 'credit' THEN amount * 0.98
            WHEN payment_method = 'debit' THEN amount * 0.99
            ELSE amount
        END AS discounted_amount,
        CASE
            WHEN shipping_method = 'express' THEN 'Fast'
            WHEN shipping_method = 'standard' THEN 'Normal'
            ELSE 'Slow'
        END AS delivery_speed
    FROM orders
    WHERE order_date > '2023-01-01'
    '''
    
    # 示例5：无 CASE WHEN 的 SQL
    sql5 = '''
    SELECT 
        product_id, 
        product_name, 
        price 
    FROM products 
    WHERE category = 'electronics'
    '''
    
    examples = [sql1, sql2, sql3, sql4, sql5]
    
    for i, sql in enumerate(examples, 1):
        print(f"\n示例 {i}:")
        print(f"原始 SQL:\n{sql}")
        try:
            result_sqls = transform_case_when(sql)
            print(f"\n转换后的 SQL 列表（{len(result_sqls)} 条语句）:")
            for j, result_sql in enumerate(result_sqls, 1):
                print(f"\n-- 语句 {j} --")
                print(result_sql)
        except Exception as e:
            print(f"错误: {e}")
            import traceback
            traceback.print_exc()

# 执行测试
if __name__ == "__main__":
    test_case_when()