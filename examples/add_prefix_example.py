import sqlglot
from sqlglot import expressions as exp
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
from lineage_core.config import get_sql_dialect

def extract_table_mappings(parsed_sql):
    """
    从解析后的SQL AST中提取表名和别名的映射关系
    
    参数:
        parsed_sql: SQLGlot解析后的SQL AST
        
    返回:
        dict: 表别名到真实表名的映射，格式为 {"alias": "real_name"}
              如果表没有别名，则使用 {"table_name": "table_name"}
    """
    table_mappings = {}
    
    # 查找最外层查询中的所有表引用
    def process_node(node, inside_subquery=False):
        # 首先处理子查询
        if isinstance(node, exp.Subquery):
            if node.args.get("alias"):
                alias = node.args["alias"].name
                table_mappings[alias] = "__SUBQUERY__"
            # 将子查询内部标记为inside_subquery=True，跳过其中的表处理
            process_node(node.this, inside_subquery=True)
            return
        
        # 处理表引用，但仅处理不在子查询内的表
        if isinstance(node, exp.Table) and not inside_subquery:
            table_name = node.name
            
            # 检查是否有别名
            alias = None
            if node.args.get("alias"):
                alias = node.args["alias"].name
            
            if alias:
                table_mappings[alias] = table_name
            else:
                # 如果没有别名，表名映射到自身
                table_mappings[table_name] = table_name
        
        # 递归处理能包含其他节点的节点类型
        if isinstance(node, exp.Select):
            # 处理SELECT语句的FROM子句
            if node.args.get("from"):
                process_node(node.args["from"].this, inside_subquery)
            
            # 处理JOIN
            for join in node.args.get("joins", []):
                process_node(join.this, inside_subquery)
        
        # 处理INSERT语句
        elif isinstance(node, exp.Insert):
            # 处理INSERT...SELECT语句的SELECT部分
            if node.args.get("expression") and isinstance(node.args["expression"], exp.Select):
                process_node(node.args["expression"], inside_subquery)
                
        # 处理FROM子句
        elif isinstance(node, exp.From) and not inside_subquery:
            process_node(node.this, inside_subquery)
            
        # 处理JOIN子句
        elif isinstance(node, exp.Join) and not inside_subquery:
            process_node(node.this, inside_subquery)
    
    # 从根节点开始处理
    process_node(parsed_sql)
    
    return table_mappings

def add_table_prefix_to_columns(sql, table_mapping=None):
    """
    在SQL片段的where和having子句中为列添加表名前缀，
    如果是INSERT或UPDATE语句，还会为输出字段添加目标表名作为前缀
    
    参数:
        sql_fragment (str): SQL片段字符串或完整SQL语句
        table_mapping (dict, optional): 表别名到真实表名的映射
                                      如果为None，将自动从SQL中提取
    
    返回:
        str: 处理后的SQL片段
    """
    try:
        parsed = sqlglot.parse_one(sql, dialect=get_sql_dialect())
    except Exception as e:
        raise ValueError(f"无法解析SQL: {e}") from e
    
    # 如果没有提供表映射，自动提取
    if table_mapping is None:
        table_mapping = extract_table_mappings(parsed)
    
    # 获取INSERT或UPDATE语句的目标表名
    target_table = None
    if isinstance(parsed, exp.Insert) or isinstance(parsed, exp.Update):
        # 修正：从this字段下的Table节点获取目标表名
        if "this" in parsed.args and isinstance(parsed.args["this"], exp.Table):
            target_table = parsed.args["this"].name
    
    # 为INSERT语句中的SELECT部分添加目标表前缀
    if target_table and (isinstance(parsed, exp.Insert) or isinstance(parsed, exp.Update)) and "expression" in parsed.args:
        select_stmt = parsed.args["expression"]
        if isinstance(select_stmt, exp.Select):
            new_expressions = []
            for expr in select_stmt.args.get("expressions", []):
                # 处理别名表达式
                if isinstance(expr, exp.Alias):
                    alias_name = expr.args["alias"].name
                    new_alias = f"{target_table}_{alias_name}"
                    expr.args["alias"].args["this"] = new_alias
                # 处理普通列
                elif isinstance(expr, exp.Column):
                    column_name = expr.name
                    new_alias = f"{target_table}_{column_name}"
                    expr = exp.Alias(
                        this=expr,
                        alias=exp.Identifier(this=new_alias, quoted=False)
                    )
                new_expressions.append(expr)
            
            if new_expressions:
                select_stmt.args["expressions"] = new_expressions
    
    def _add_prefix(node):
        if isinstance(node, exp.Column):
            # 获取列名和可能的表别名
            column_name = node.name
            table_alias = node.table
            
            # 如果列已经有表别名前缀，直接修改表别名为真实表名
            if table_alias and table_alias in table_mapping:
                real_table = table_mapping[table_alias]
                if real_table != "__SUBQUERY__":  # 不处理子查询
                    # 直接修改表标识符的名称
                    node.args["table"].args["this"] = real_table
                    return node
            # 如果列没有表别名前缀
            elif not table_alias:
                # 如果只有一个表，直接使用它
                if len(table_mapping) == 1:
                    alias, real_name = next(iter(table_mapping.items()))
                    if real_name != "__SUBQUERY__":  # 不为子查询添加前缀
                        # 添加表前缀
                        node.args["table"] = exp.Identifier(this=real_name, quoted=False)
                        return node
                # 如果有多个表，我们需要更复杂的逻辑来确定列属于哪个表
                else:
                    # 尝试找到不是子查询的第一个表
                    for alias, real_name in table_mapping.items():
                        if real_name != "__SUBQUERY__":
                            # 添加表前缀
                            node.args["table"] = exp.Identifier(this=real_name, quoted=False)
                            return node
        
        return node
    
    # 遍历并转换AST
    transformed = parsed.transform(_add_prefix)
    
    # 提取出原始片段部分
    result = transformed.sql()
    return result

# 示例用法
if __name__ == "__main__":
    
    sql1 = """
    insert into orders_new
    SELECT id as new_id FROM orders o WHERE o.order_id = 1 AND status = 'active'
    """
    result1 = add_table_prefix_to_columns(sql1)
    print("\n示例1结果:")
    print("原始sql:", sql1)
    print("处理后sql:", result1)

    # 示例2: 完整SQL语句
    sql2 = "SELECT * FROM orders o WHERE o.order_id = 1 AND status = 'active'"
    result2 = add_table_prefix_to_columns(sql2)
    print("\n示例2结果:")
    print("原始sql:", sql2)
    print("处理后sql:", result2)
    
    sql3 = """
    SELECT t.name FROM 
    (SELECT id, value as name FROM tableA t1 WHERE value > 10) t
    JOIN tableB t1 ON t.id = t1.id
    WHERE t1.status = 'active'
    """
    print("\n示例3结果:")
    print("原始sql:", sql3)
    result3 = add_table_prefix_to_columns(sql3)
    print("处理后sql:", result3)

    # 示例4: 多表JOIN
    sql4 = """
    SELECT o.id, c.name, p.price 
    FROM orders o 
    JOIN customers c ON o.customer_id = c.id 
    JOIN products p ON o.product_id = p.id
    WHERE o.order_date > '2023-01-01' AND c.status = 'completed' AND p.price > 100
    """
    print("\n示例4结果:")
    print("原始sql:", sql4)
    result4 = add_table_prefix_to_columns(sql4)
    print("处理后sql:", result4)
    
    # 示例5: 带子查询
    sql5 = """
    SELECT * FROM (SELECT id, name FROM users) u
    WHERE age > 18 AND name LIKE 'A%'
    """
    result5 = add_table_prefix_to_columns(sql5)
    print("\n示例5结果:")
    print("原始sql:", sql5)
    print("处理后sql:", result5)
    
    # 示例6: 测试别名替换为真实表名
    sql6 = """
    SELECT * FROM customers c
    WHERE c.id = 1 AND c.name = 'Alice' AND status = 'active'
    """
    result6 = add_table_prefix_to_columns(sql6)
    print("\n示例6结果:")
    print("原始sql:", sql6)
    print("处理后sql:", result6)
    
    # 示例7: INSERT语句的列前缀添加
    sql7 = """
    INSERT INTO target_table
    SELECT id, name, value FROM source_table
    WHERE status = 'active'
    """
    result7 = add_table_prefix_to_columns(sql7)
    print("\n示例7结果:")
    print("原始sql:", sql7)
    print("处理后sql:", result7)