from z3 import *

def create_nullable_type(base_sort_name, base_sort):
    """
    创建一个可为空的数据类型
    
    Args:
        base_sort_name: 基础类型名称
        base_sort: Z3基础类型
        
    Returns:
        可为空的数据类型、构造函数和判断函数
    """
    nullable = Datatype(f'Nullable{base_sort_name}')
    nullable.declare('null')  # NULL值构造函数
    nullable.declare('some', ('value', base_sort))  # 非NULL值构造函数
    nullable = nullable.create()
    
    # 返回类型和相关函数
    return (nullable,  # 类型
            nullable.null,  # NULL构造函数
            nullable.some,  # 非NULL构造函数 
            nullable.is_null,  # NULL判断函数
            nullable.is_some)  # 非NULL判断函数

def main():
    """验证使用Z3 Datatype处理NULL值的可行性"""
    # 创建可为空的整数类型
    NullableInt, null_int, some_int, is_null_int, is_some_int = create_nullable_type('Int', IntSort())
    
    # 创建可为空的字符串类型
    NullableString, null_string, some_string, is_null_string, is_some_string = create_nullable_type('String', StringSort())

    # 创建Z3求解器
    s = Solver()
   
    print("case 1")
    # x is null
    x_n = Const('x', NullableInt)
    s.add(is_null_int(x_n))
    # x = y
    y_n = Const('y', NullableInt)
    s.add(x_n == y_n)
    print(f"可满足性: {s.check()}")
    if s.check() == sat:
        m = s.model()
        print(f"模型: {m}")
    # y = 10
    y = z3.Int('y')
    #s.add(y_n == some_int(y))
    s.add(is_some_int(y_n))
    print(f"可满足性: {s.check()}")
    if s.check() == sat:
        m = s.model()
        print(f"模型: {m}")

    s.reset()
    print("case 2")
    # y = 10
    y = z3.Int('y')
    s.add(y == 10)
    print(f"可满足性: {s.check()}")
    if s.check() == sat:
        m = s.model()
        print(f"模型: {m}")
    # x = y
    x = z3.Int('x')
    s.add(x == y)
    print(f"可满足性: {s.check()}")
    if s.check() == sat:
        m = s.model()
        print(f"模型: {m}")
    # x is null
    x_n = Const('x', NullableInt)
    s.add(is_null_int(x_n))
    s.add(x_n == some_int(x))
    print(f"可满足性: {s.check()}")
    if s.check() == sat:
        m = s.model()
        print(f"模型: {m}")
    
    
   
    print("\n----------------------------\n") 
    # # 创建可为空变量
    # x = Const('x', NullableInt)
    # y = Const('y', NullableInt)
    # name = Const('name', NullableString)
    # name1 = Const('name1', NullableString)
    
    # # 演示IS NULL条件
    # print("示例1: x IS NULL")
    # s.push()
    # s.add(is_null_int(x))  # x IS NULL
    # print(f"可满足性: {s.check()}")
    # if s.check() == sat:
    #     m = s.model()
    #     print(f"模型: {m}")
    # s.pop()
    
    # # 演示IS NOT NULL条件
    # print("\n示例2: x IS NOT NULL AND x > 10")
    # s.push()
    # # s.add(is_some_int(x))  # x IS NOT NULL
    # # s.add(is_null_int(x))  # x IS NOT NULL
    # # value_x = Const('xxx', IntSort())
    # value_x = z3.Int("vx")
    # s.add(x == some_int(value_x))  # 获取x的值
    # s.add(value_x > 10)  # x > 10
    # s.add(value_x < 20)  # x > 10
    # print(f"可满足性: {s.check()}")
    # if s.check() == sat:
    #     m = s.model()
    #     print(f"模型: {m}")
    # s.pop()
    
    # # 演示NULL值的相等比较
    # print("\n示例3: x IS NULL AND y IS NULL => x = y")
    # s.push()
    # s.add(is_null_int(x))  # x IS NULL
    # s.add(is_null_int(y))  # y IS NULL
    # s.add(x != y)  # 尝试令x != y
    # print(f"可满足性: {s.check()}")  # 应为unsat，因为NULL = NULL
    # s.pop()
    
    # # 演示字符串类型NULL
    # print("\n示例4: name IS NULL OR name = 'John'")
    # s.push()
    # # name IS NULL OR name = 'John'
    # s.add((is_null_string(name), 
    #         And(is_some_string(name), name == some_string(StringVal("John")))))
    # print(f"可满足性: {s.check()}")
    # if s.check() == sat:
    #     m = s.model()
    #     print(f"模型: {m}")

    # s.reset()
    # s.add(is_null_int(x))
    # s.add(is_null_int(y))
    # s.add(x == y)
    # s.add(y ==some_int(10))
    # print(y.sort())
    # print(some_int(10).sort())
    # # s.add(value_x > 10)
    # print("\n示例5")
    # print(f"可满足性: {s.check()}")
    # if s.check() == sat:
    #     m = s.model()
    #     print(f"模型: {m}")

    # # 新增示例6：验证三个条件的矛盾情况
    # print("\n示例6: a IS NULL AND a = b AND b = 'SSS'")
    # s.push()
    # a = Const('a', NullableString)
    # b = Const('b', NullableString)
    # s.add(is_null_string(a))  # a IS NULL
    # s.add(a == b)            # a = b
    # s.add(b == some_string(StringVal("SSS")))  # b = 'SSS'
    # print(f"可满足性: {s.check()}")  # 应输出unsat
    # if s.check() == sat:
    #     m = s.model()
    #     print(f"模型: {m}")
    # s.pop()

    # # 新增示例7：对比普通类型与可空类型的区别
    # print("\n示例7: 类型对比演示")
    # print("场景1：使用可空类型（处理NULL语义）")
    # s.push()
    # c = Const('c', NullableString)
    # d = Const('d', NullableString)
    # s.add(c == d)            # c = d
    # s.add(d == some_string(StringVal("TEST")))  # d = 'TEST'
    # s.add(is_null_string(c)) # c IS NULL
    # print(f"可空类型可满足性: {s.check()}")  # unsat
    # s.pop()

    # print("\n场景2：使用普通类型（无NULL语义）")
    # s.push()
    # e = String('e')  # 普通字符串类型
    # f = String('f')
    # s.add(e == f)     # e = f
    # s.add(f == StringVal("TEST"))  # f = 'TEST'
    # s.add(e == StringVal("OTHER")) # e = 'OTHER' 
    # print(f"普通类型可满足性: {s.check()}")  # unsat（直接冲突）
    # s.pop()
    
    # s.reset()
    # print("\n示例8：类型安全与条件计算")
    # s.push()
    # num = Const('num', NullableInt)
    # num2 = Const('num2', NullableInt)
    # normal_num = Int('normal_num')
    # s.add(z3.Or(num == some_int(normal_num), normal_num == 1))  # 解包可空值
    # s.add(is_null_int(num))
    # s.add(num == num2)
    # print(num.sort())
    # print(f"类型安全计算可满足性: {s.check()}")  # sat
    # m = s.model()
    # print(f"模型: num={m[num]}, normal_num={m[normal_num]}")
    # s.pop()

    # s.reset()
    # z3.set_option('smt.string_solver', 'z3str3')
    # datex = z3.String('s')
    # s.add(z3.And(datex > z3.StringVal('2024-01-01'), datex < z3.StringVal('2024-12-31')))
    # s.add(datex < z3.StringVal('2024-01-01'))
    # s.add(datex > z3.StringVal('2024-01-01'))
    # s.add(datex < z3.StringVal('2024-11-01'))
    # print("\n示例6")
    # print(f"可满足性: {s.check()}")
    # if s.check() == sat:
    #     m = s.model()
    #     print(f"模型: {m}")
if __name__ == "__main__":
    main()
