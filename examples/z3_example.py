from z3 import *

def tracked_solve(constraints):
    s = Solver()
    s.set(unsat_core=True)
    tracked_constraints = []
    
    # 为每个约束自动创建跟踪标识符
    for i, constraint in enumerate(constraints):
        name = f"constraint_{i}"
        s.assert_and_track(constraint, name)
        tracked_constraints.append((name, constraint))
    
    if s.check() == unsat:
        core = s.unsat_core()
        print("不可满足核心约束:")
        for name in core:
            # 找到对应的约束并打印
            for tracked_name, constraint in tracked_constraints:
                if str(tracked_name) == str(name):
                    print(f"{tracked_name}: {constraint}")
        return None
    else:
        return s.model()

# 使用示例
x, y = Ints('x y')
constraints = [x > 0, y > 0, x + y < 0]
result = tracked_solve(constraints)


from z3 import *

# 定义变量
variables = {}
def get_var(name, type_func=Int):
    if name not in variables:
        variables[name] = type_func(name)
    return variables[name]

# 构建复杂表达式
constraint = And(
    And(And(739982 >= get_var("odb_se7_gc_credit_choose_rel_start_dt"),
            739982 <= get_var("odb_se7_gc_credit_choose_rel_end_dt") - 1),
        And(get_var("odb_se7_gc_credit_choose_rel_appcode") != "",
            get_var("odb_se7_gc_loan_credit_code") != "")),
    # 添加其他约束...
)

# 使用Z3的简化功能
simplified = simplify(constraint)
print(simplified)

# 使用策略进行更强大的简化
tactic = Tactic('ctx-solver-simplify')
simplified_goal = tactic(constraint)
print(simplified_goal)


# 创建 Z3 求解器
solver = Solver()

# 定义 id 的类型为整数
Id = z3.IntSort()

# 定义 a 和 b 的值类型为字符串
Value = z3.StringSort()

# 定义函数：将 id 映射到 a 和 b 的值
a_func = z3.Function('a', Id, Value)
b_func = z3.Function('b', Id, Value)
c_func = z3.Function('c', Id, Value)

# 添加数据库中的记录
solver.add(a_func(1) == "001")  # id=1 时，a="001"
solver.add(b_func(1) == "002")  # id=1 时，b="002"
solver.add(a_func(2) == "002")  # id=2 时，a="002"
solver.add(b_func(2) == "003")  # id=2 时，b="003"
solver.add(c_func(3) == b_func(1))  

# 定义数据库中存在的 id 集合
ids = [1, 2]

# 定义两个变量 id1 和 id2
id1 = Int('id1')
id2 = Int('id2')

# 约束 id1 和 id2 必须在数据库中存在的 id 集合内
solver.add(Or([id1 == i for i in ids]))
# solver.add(Or([id2 == i for i in ids]))
solver.add(id2 == 3)

# 定义条件：id1 ≠ id2 且 b(id1) = a(id2)
# condition = And(id1 != id2, b_func(id1) == a_func(id2))

# 将条件添加到求解器
# solver.add(condition)
solver.add(a_func(id1) == c_func(id2))

# 检查是否可满足
if solver.check() == sat:
    print("存在两条记录满足 a(id1) = c(id2)")
    model = solver.model()
    id1_val = model[id1]
    id2_val = model[id2]
    print(f"示例解: id1 = {id1_val}, id2 = {id2_val}")
    print(f"a(id1) = {model.eval(a_func(id1))}, c(id2) = {model.eval(c_func(id2))}")
else:
    print("不存在满足条件的记录")

    
solver.reset()

# 定义变量
id1 = Int('id1')
a = String('a')
b = String('b')
c = String('c')

solver.add(z3.Or(z3.And(id1==1, a == '001', b == '002'), z3.And(id1==2, a == '002', b == '003')))
solver.add(c == b)
solver.add(c == a)

print("new test")
if solver.check() == sat:
    model = solver.model()
    print(f"id1 = {model[id1]}, a = {model[a]}, b = {model[b]}, c = {model[c]}")
else:
    print("不存在满足条件的记录")

